<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\StudentUserService;
use App\Student;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class StudentUserServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $studentUserService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->studentUserService = new StudentUserService();
    }

    /** @test */
    public function it_creates_user_for_student_without_user()
    {
        // Create a student without a user
        $student = Student::factory()->create([
            'full_name' => 'Test Student',
            'email' => '<EMAIL>',
            'user_id' => null
        ]);

        $result = $this->studentUserService->createUserForStudent($student);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('credentials', $result);
        $this->assertArrayHasKey('user', $result);
        
        // Verify user was created
        $this->assertNotNull($student->fresh()->user_id);
        $user = User::find($student->fresh()->user_id);
        $this->assertNotNull($user);
        $this->assertEquals($student->full_name, $user->full_name);
    }

    /** @test */
    public function it_does_not_create_user_for_student_with_existing_user()
    {
        // Create a user first
        $user = User::factory()->create();
        
        // Create a student with the user
        $student = Student::factory()->create([
            'user_id' => $user->id
        ]);

        $result = $this->studentUserService->createUserForStudent($student);

        $this->assertFalse($result['success']);
        $this->assertTrue($result['user_exists']);
    }

    /** @test */
    public function it_restores_soft_deleted_user_instead_of_creating_new_one()
    {
        // Create a user and student
        $user = User::factory()->create([
            'full_name' => 'Test User',
            'email' => '<EMAIL>'
        ]);
        
        $student = Student::factory()->create([
            'full_name' => 'Test Student',
            'email' => '<EMAIL>',
            'user_id' => $user->id
        ]);

        // Soft delete the user
        $user->delete();
        
        // Update student to remove user_id reference
        $student->update(['user_id' => null]);

        // Try to create user for student
        $result = $this->studentUserService->createUserForStudent($student);

        $this->assertTrue($result['success']);
        $this->assertTrue($result['restored'] ?? false);
        
        // Verify user was restored, not created new
        $restoredUser = User::find($user->id);
        $this->assertNotNull($restoredUser);
        $this->assertNull($restoredUser->deleted_at);
        
        // Verify student is linked to restored user
        $this->assertEquals($user->id, $student->fresh()->user_id);
    }

    /** @test */
    public function it_finds_soft_deleted_user_by_email()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        
        // Soft delete the user
        $user->delete();
        
        // Create a student with same email but no user_id
        $student = Student::factory()->create([
            'email' => '<EMAIL>',
            'user_id' => null
        ]);

        $softDeletedUser = $this->studentUserService->findSoftDeletedUser($student);

        $this->assertNotNull($softDeletedUser);
        $this->assertEquals($user->id, $softDeletedUser->id);
        $this->assertNotNull($softDeletedUser->deleted_at);
    }

    /** @test */
    public function it_handles_bulk_user_creation()
    {
        // Create multiple students without users
        $students = Student::factory()->count(3)->create([
            'user_id' => null
        ]);

        $studentIds = $students->pluck('id')->toArray();
        $result = $this->studentUserService->createUsersForStudents($studentIds);

        $this->assertEquals(3, $result['success_count']);
        $this->assertEquals(0, $result['error_count']);
        $this->assertEquals(0, $result['already_exist_count']);
        $this->assertCount(3, $result['created_users']);

        // Verify all students now have users
        foreach ($students as $student) {
            $this->assertNotNull($student->fresh()->user_id);
        }
    }
}
