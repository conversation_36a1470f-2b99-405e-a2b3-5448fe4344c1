<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sweet Navigation CSS Test</title>
    
    <!-- Bootstrap 3 for compatibility testing -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <!-- Our Sweet Navigation CSS -->
    <link rel="stylesheet" href="css/components/sweet-navigation.css">
    
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            margin: 10px;
            padding: 10px 20px;
            background: #009933;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #007a29;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sweet Navigation CSS Component Test</h1>
        <p class="lead">This page tests the extracted CSS styles for the Sweet Navigation component.</p>
        
        <div class="test-section">
            <h3>Test 1: Basic CSS Classes</h3>
            <p>Testing individual CSS classes to ensure they render correctly:</p>
            
            <!-- Search Container Test -->
            <div class="sweet-navigation-search-container">
                <input type="text" class="sweet-navigation-search-input" placeholder="Search items...">
                <i class="fa fa-search sweet-navigation-search-icon"></i>
            </div>
            
            <!-- Group Test -->
            <div class="sweet-navigation-group">
                <div class="sweet-navigation-group-header">
                    <span>Test Program Group</span>
                    <i class="fa fa-chevron-down toggle-icon"></i>
                </div>
                <div class="sweet-navigation-items-list">
                    <div class="sweet-navigation-item">
                        <div class="sweet-navigation-item-main-info">
                            <div class="sweet-navigation-item-name">Test Item 1</div>
                            <div class="sweet-navigation-item-details">
                                Teacher: <a href="#" class="sweet-navigation-link">John Doe</a> | 
                                Schedule: Mon, Wed, Fri 10:00-11:30
                            </div>
                        </div>
                        <div class="sweet-navigation-item-meta">
                            <span class="sweet-navigation-count">25</span>
                            <div class="sweet-navigation-item-actions">
                                <a href="#" class="sweet-navigation-action-btn sweet-navigation-action-report">Report</a>
                                <a href="#" class="sweet-navigation-action-btn sweet-navigation-action-show">Show</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="sweet-navigation-item current-item">
                        <div class="sweet-navigation-item-main-info">
                            <div class="sweet-navigation-item-name">Current Test Item</div>
                            <div class="sweet-navigation-item-details">
                                Teacher: <a href="#" class="sweet-navigation-link">Jane Smith</a> | 
                                Schedule: Tue, Thu 14:00-15:30
                            </div>
                        </div>
                        <div class="sweet-navigation-item-meta">
                            <span class="sweet-navigation-count">18</span>
                            <div class="sweet-navigation-item-actions">
                                <a href="#" class="sweet-navigation-action-btn sweet-navigation-action-report">Report</a>
                                <a href="#" class="sweet-navigation-action-btn sweet-navigation-action-show">Show</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="sweet-navigation-item">
                        <div class="sweet-navigation-item-main-info">
                            <div class="sweet-navigation-item-name">Test Item 3</div>
                            <div class="sweet-navigation-item-details">
                                Teacher: <a href="#" class="sweet-navigation-link">Bob Wilson</a> | 
                                Schedule: Daily 09:00-10:00
                            </div>
                        </div>
                        <div class="sweet-navigation-item-meta">
                            <span class="sweet-navigation-count">32</span>
                            <div class="sweet-navigation-item-actions">
                                <a href="#" class="sweet-navigation-action-btn sweet-navigation-action-report">Report</a>
                                <a href="#" class="sweet-navigation-action-btn sweet-navigation-action-show">Show</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: SweetAlert2 Integration Test</h3>
            <p>Click the button below to test SweetAlert2 with our custom styles:</p>
            <button class="test-button" onclick="testSweetAlert()">Test SweetAlert2 Popup</button>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Animation Test</h3>
            <p>Click the button below to test the slideInFromTop animation:</p>
            <button class="test-button" onclick="testAnimation()">Test Animation</button>
            <div id="animation-test" style="display: none; margin-top: 20px; padding: 20px; background: #e8f5e8; border-radius: 8px;">
                <p>This element uses the slideInFromTop animation!</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test 4: No Items Found State</h3>
            <div class="sweet-navigation-no-items-found">
                <i class="fa fa-search" style="font-size: 48px; color: #ccc; margin-bottom: 10px;"></i>
                <p>No items found matching your search criteria.</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <p><strong>Visual Inspection Checklist:</strong></p>
                <ul>
                    <li>✓ Search input has proper styling and focus effects</li>
                    <li>✓ Group headers have green gradient background</li>
                    <li>✓ Items have hover effects (slight movement and background change)</li>
                    <li>✓ Current item is highlighted with green background and arrow</li>
                    <li>✓ Action buttons have proper colors and hover effects</li>
                    <li>✓ Scrollbar styling is applied (if content overflows)</li>
                    <li>✓ SweetAlert2 popup works with custom styling</li>
                    <li>✓ Animation works smoothly</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- jQuery and SweetAlert2 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        function testSweetAlert() {
            Swal.fire({
                title: 'Sweet Navigation Test',
                html: `
                    <div class="sweet-navigation-search-container">
                        <input type="text" class="sweet-navigation-search-input" placeholder="Search test items...">
                        <i class="fa fa-search sweet-navigation-search-icon"></i>
                    </div>
                    <div class="sweet-navigation-group">
                        <div class="sweet-navigation-group-header">
                            <span>Test Group</span>
                            <i class="fa fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="sweet-navigation-items-list">
                            <div class="sweet-navigation-item current-item">
                                <div class="sweet-navigation-item-main-info">
                                    <div class="sweet-navigation-item-name">Current Item in Popup</div>
                                    <div class="sweet-navigation-item-details">This should be highlighted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                width: 800,
                customClass: {
                    container: 'sweet-navigation-alert'
                },
                showConfirmButton: true,
                confirmButtonText: 'Close Test'
            });
        }
        
        function testAnimation() {
            const element = document.getElementById('animation-test');
            element.style.display = 'block';
            element.style.animation = 'slideInFromTop 0.3s ease-out';
            
            setTimeout(() => {
                element.style.display = 'none';
                element.style.animation = '';
            }, 3000);
        }
        
        // Add some interactive behavior for testing
        $(document).ready(function() {
            // Test group header collapse
            $('.sweet-navigation-group-header').click(function() {
                $(this).toggleClass('collapsed');
                $(this).next('.sweet-navigation-items-list').slideToggle();
            });
            
            // Test search functionality
            $('.sweet-navigation-search-input').on('input', function() {
                const searchTerm = $(this).val().toLowerCase();
                $('.sweet-navigation-item').each(function() {
                    const itemText = $(this).text().toLowerCase();
                    if (itemText.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });
        });
    </script>
</body>
</html>
