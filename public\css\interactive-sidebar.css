/**
 * Interactive Mobile Sidebar CSS
 * Mobile-first responsive sidebar with collapse functionality
 * Compatible with Bootstrap 5 and CoreUI
 */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Sidebar dimensions */
  --sidebar-width: 280px;
  --sidebar-width-collapsed: 60px;
  --sidebar-mobile-width: 280px;
  
  /* Breakpoints */
  --mobile-breakpoint: 768px;
  
  /* Animation system - Performance optimized transitions */
  --sidebar-transition-duration: 0.3s;
  --sidebar-transition-duration-fast: 0.2s;
  --sidebar-transition-duration-slow: 0.4s;
  
  /* Easing functions for polished animations */
  --sidebar-easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --sidebar-easing-decelerate: cubic-bezier(0, 0, 0.2, 1);
  --sidebar-easing-accelerate: cubic-bezier(0.4, 0, 1, 1);
  --sidebar-easing-sharp: cubic-bezier(0.4, 0, 0.6, 1);
  --sidebar-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Transform-based animation properties */
  --sidebar-transform-slide-in: translateX(0);
  --sidebar-transform-slide-out: translateX(-100%);
  --sidebar-transform-scale-in: scale(1);
  --sidebar-transform-scale-out: scale(0.95);
  
  /* Synchronized animation timing */
  --sidebar-sync-delay: 0.05s;
  --sidebar-stagger-delay: 0.02s;
  
  /* Performance optimization properties */
  --sidebar-will-change: transform, opacity, width;
  --sidebar-backface-visibility: hidden;
  --sidebar-perspective: 1000px;
  
  /* Z-index layers */
  --sidebar-z-index: 1050;
  --backdrop-z-index: 1040;
  
  /* Colors */
  --sidebar-bg: #f8f9fa;
  --sidebar-color: #495057;
  --sidebar-active-bg: #e9ecef;
  --sidebar-active-color: #6c757d;
  --sidebar-hover-bg: #e9ecef;
  --backdrop-bg: rgba(0, 0, 0, 0.5);
  
  /* Dark mode colors */
  --sidebar-bg-dark: #1c1d22;
  --sidebar-color-dark: #f0f0f0;
  --sidebar-active-bg-dark: #2a2b30;
  --sidebar-active-color-dark: #adb5bd;
  --sidebar-hover-bg-dark: #2a2b30;
}

/* Dark mode color overrides */
[data-bs-theme="dark"] {
  --sidebar-bg: var(--sidebar-bg-dark);
  --sidebar-color: var(--sidebar-color-dark);
  --sidebar-active-bg: var(--sidebar-active-bg-dark);
  --sidebar-active-color: var(--sidebar-active-color-dark);
  --sidebar-hover-bg: var(--sidebar-hover-bg-dark);
}

/* ===== MOBILE-FIRST BASE STYLES ===== */

/* Mobile sidebar (default - hidden off-screen) */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--sidebar-mobile-width);
  height: 100vh;
  background: var(--sidebar-bg);
  color: var(--sidebar-color);
  transform: var(--sidebar-transform-slide-out);
  transition: transform var(--sidebar-transition-duration) var(--sidebar-easing-standard);
  z-index: var(--sidebar-z-index);
  overflow-y: auto;
  overflow-x: hidden;
  border-right: 1px solid var(--bs-border-color);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  
  /* Performance optimizations */
  will-change: var(--sidebar-will-change);
  backface-visibility: var(--sidebar-backface-visibility);
  perspective: var(--sidebar-perspective);
}

/* Mobile sidebar visible state */
.sidebar.show {
  transform: var(--sidebar-transform-slide-in);
}

/* Mobile backdrop overlay */
.sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--backdrop-bg);
  z-index: var(--backdrop-z-index);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--sidebar-transition-duration) var(--sidebar-easing-standard),
              visibility var(--sidebar-transition-duration) var(--sidebar-easing-standard);
  backdrop-filter: blur(2px);
  
  /* Performance optimizations */
  will-change: opacity, visibility;
  backface-visibility: var(--sidebar-backface-visibility);
}

/* Mobile backdrop visible state */
.sidebar-backdrop.show {
  opacity: 1;
  visibility: visible;
}

/* Mobile hamburger menu button */
.sidebar-toggle-mobile {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: none;
  border: none;
  color: var(--bs-body-color);
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.sidebar-toggle-mobile:hover {
  background-color: var(--bs-secondary-bg);
  color: var(--bs-primary);
  transform: scale(1.05);
}

.sidebar-toggle-mobile:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

/* Desktop sidebar toggle button (hidden on mobile) */
.sidebar-toggle-desktop {
  display: none;
}

/* Main content wrapper - no margin on mobile */
.wrapper {
  margin-left: 0;
  transition: margin-left var(--sidebar-transition-duration) var(--sidebar-easing-standard);
  min-height: 100vh;
  
  /* Performance optimizations */
  will-change: margin-left;
  backface-visibility: var(--sidebar-backface-visibility);
}

/* ===== DESKTOP RESPONSIVE STYLES ===== */
@media (min-width: 769px) {
  /* Desktop sidebar - always visible, positioned relative to content */
  .sidebar {
    position: fixed;
    transform: var(--sidebar-transform-slide-in);
    width: var(--sidebar-width);
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.08);
    transition: width var(--sidebar-transition-duration) var(--sidebar-easing-standard);
    
    /* Performance optimizations for desktop */
    will-change: width;
  }
  
  /* Desktop collapsed state with synchronized animation */
  .sidebar.collapsed {
    width: var(--sidebar-width-collapsed);
    transition: width var(--sidebar-transition-duration) var(--sidebar-easing-decelerate);
  }
  
  /* Hide mobile-specific elements on desktop */
  .sidebar-backdrop {
    display: none;
  }
  
  .sidebar-toggle-mobile {
    display: none;
  }
  
  /* Show desktop toggle button */
  .sidebar-toggle-desktop {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    color: var(--bs-body-color);
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 6px;
  }
  
  .sidebar-toggle-desktop:hover {
    background-color: var(--bs-secondary-bg);
    color: var(--bs-primary);
    transform: scale(1.05);
  }
  
  .sidebar-toggle-desktop:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
  }
  
  /* Desktop wrapper with sidebar margin */
  .wrapper {
    margin-left: var(--sidebar-width);
  }
  
  /* Desktop wrapper with collapsed sidebar */
  .wrapper.sidebar-collapsed {
    margin-left: var(--sidebar-width-collapsed);
  }
}

/* ===== SIDEBAR CONTENT STYLES ===== */

/* Sidebar brand/header */
.sidebar-brand {
  display: flex;
  align-items: center;
  padding: 1rem;
  height: 60px;
  background: var(--sidebar-bg);
  border-bottom: 1px solid var(--bs-border-color);
  position: relative;
  overflow: hidden;
}

.sidebar-brand-full {
  transition: opacity var(--sidebar-transition-duration) var(--sidebar-easing-standard),
              visibility var(--sidebar-transition-duration) var(--sidebar-easing-standard);
  
  /* Performance optimizations */
  will-change: opacity, visibility;
  backface-visibility: var(--sidebar-backface-visibility);
}

.sidebar-brand-narrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--sidebar-transition-duration) var(--sidebar-easing-standard),
              visibility var(--sidebar-transition-duration) var(--sidebar-easing-standard);
  
  /* Performance optimizations */
  will-change: opacity, visibility;
  backface-visibility: var(--sidebar-backface-visibility);
}

/* Collapsed state brand visibility */
.sidebar.collapsed .sidebar-brand-full {
  opacity: 0;
  visibility: hidden;
}

.sidebar.collapsed .sidebar-brand-narrow {
  opacity: 1;
  visibility: visible;
}

/* Sidebar navigation */
.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav .nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--sidebar-color);
  text-decoration: none;
  transition: all var(--sidebar-transition-duration-fast) var(--sidebar-easing-standard),
              transform var(--sidebar-transition-duration-fast) var(--sidebar-easing-decelerate);
  border-radius: 6px;
  margin: 2px 8px;
  white-space: nowrap;
  position: relative;
  
  /* Performance optimizations */
  will-change: background-color, color, transform;
  backface-visibility: var(--sidebar-backface-visibility);
}

.sidebar-nav .nav-link:hover {
  background-color: var(--sidebar-hover-bg);
  color: var(--sidebar-active-color);
}

.sidebar-nav .nav-link.active {
  background-color: var(--sidebar-active-bg);
  color: var(--sidebar-active-color);
  font-weight: 600;
  border-left: 3px solid var(--sidebar-active-color);
}

.sidebar-nav .nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
  font-size: 1rem;
}

.sidebar-nav .nav-link-text {
  flex: 1;
  transition: opacity var(--sidebar-transition-duration) var(--sidebar-easing-standard),
              visibility var(--sidebar-transition-duration) var(--sidebar-easing-standard);
  
  /* Performance optimizations */
  will-change: opacity, visibility;
  backface-visibility: var(--sidebar-backface-visibility);
}

/* Collapsed state text visibility */
.sidebar.collapsed .nav-link-text {
  opacity: 0;
  visibility: hidden;
}

/* ===== MENU GROUP ACCORDION STYLES ===== */

.sidebar-nav .nav-group {
  margin: 2px 8px;
}

.sidebar-nav .nav-group-toggle {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  color: var(--sidebar-color);
  text-align: left;
  cursor: pointer;
  transition: all var(--sidebar-transition-duration-fast) var(--sidebar-easing-standard),
              transform var(--sidebar-transition-duration-fast) var(--sidebar-easing-decelerate);
  border-radius: 6px;
  white-space: nowrap;
  
  /* Performance optimizations */
  will-change: background-color, color, transform;
  backface-visibility: var(--sidebar-backface-visibility);
}

.sidebar-nav .nav-group-toggle:hover {
  background-color: var(--sidebar-hover-bg);
  color: var(--sidebar-active-color);
  transform: translateX(2px);
}

.sidebar-nav .nav-group.show .nav-group-toggle {
  background-color: var(--sidebar-active-bg);
  color: var(--sidebar-active-color);
  font-weight: 600;
}

.sidebar-nav .nav-group-arrow {
  margin-left: auto;
  transition: transform var(--sidebar-transition-duration) var(--sidebar-easing-sharp);
  font-size: 0.8rem;
  
  /* Performance optimizations */
  will-change: transform;
  backface-visibility: var(--sidebar-backface-visibility);
}

.sidebar-nav .nav-group.show .nav-group-arrow {
  transform: rotate(90deg);
}

.sidebar-nav .nav-group-items {
  padding-left: 0;
  margin-left: 2rem;
  border-left: 1px solid var(--bs-border-color);
  transition: all var(--sidebar-transition-duration) var(--sidebar-easing-standard);
  
  /* Performance optimizations */
  will-change: height, opacity;
  backface-visibility: var(--sidebar-backface-visibility);
}

.sidebar-nav .nav-group-items .nav-link {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  margin: 1px 0;
}

/* Collapsed state group items */
.sidebar.collapsed .nav-group-items {
  display: none;
}

/* ===== TOOLTIP STYLES FOR COLLAPSED STATE ===== */

.sidebar-tooltip {
  position: absolute;
  left: calc(100% + 10px);
  top: 50%;
  transform: translateY(-50%);
  background: var(--bs-dark);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1060;
  pointer-events: none;
}

.sidebar-tooltip::before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 5px solid transparent;
  border-right-color: var(--bs-dark);
}

/* Show tooltip on hover when sidebar is collapsed */
.sidebar.collapsed .nav-link:hover .sidebar-tooltip {
  opacity: 1;
  visibility: visible;
}

/* ===== ACCESSIBILITY STYLES ===== */

/* Focus styles */
.sidebar-nav .nav-link:focus,
.sidebar-nav .nav-group-toggle:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== ADVANCED ANIMATION SYSTEM ===== */

/* Synchronized animations for sidebar and content area */
.sidebar.animating,
.wrapper.animating {
  transition-delay: var(--sidebar-sync-delay);
}

/* Staggered animations for menu items */
.sidebar-nav .nav-link:nth-child(1) { animation-delay: calc(var(--sidebar-stagger-delay) * 1); }
.sidebar-nav .nav-link:nth-child(2) { animation-delay: calc(var(--sidebar-stagger-delay) * 2); }
.sidebar-nav .nav-link:nth-child(3) { animation-delay: calc(var(--sidebar-stagger-delay) * 3); }
.sidebar-nav .nav-link:nth-child(4) { animation-delay: calc(var(--sidebar-stagger-delay) * 4); }
.sidebar-nav .nav-link:nth-child(5) { animation-delay: calc(var(--sidebar-stagger-delay) * 5); }
.sidebar-nav .nav-link:nth-child(6) { animation-delay: calc(var(--sidebar-stagger-delay) * 6); }
.sidebar-nav .nav-link:nth-child(7) { animation-delay: calc(var(--sidebar-stagger-delay) * 7); }
.sidebar-nav .nav-link:nth-child(8) { animation-delay: calc(var(--sidebar-stagger-delay) * 8); }

/* Slide-in animation for menu items when sidebar becomes visible */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade-in animation for collapsed state tooltips */
@keyframes fadeInTooltip {
  from {
    opacity: 0;
    transform: translateY(-50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

/* Bounce animation for interactive elements */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Apply staggered slide-in animation when sidebar becomes visible */
.sidebar.show .sidebar-nav .nav-link {
  animation: slideInFromLeft var(--sidebar-transition-duration-fast) var(--sidebar-easing-decelerate) forwards;
}

/* Enhanced tooltip animation */
.sidebar.collapsed .nav-link:hover .sidebar-tooltip {
  animation: fadeInTooltip var(--sidebar-transition-duration-fast) var(--sidebar-easing-bounce) forwards;
}

/* Bounce animation for toggle buttons */
.sidebar-toggle-mobile:active,
.sidebar-toggle-desktop:active {
  animation: bounceIn var(--sidebar-transition-duration-fast) var(--sidebar-easing-bounce);
}

/* ===== ANIMATION PERFORMANCE OPTIMIZATIONS ===== */

/* Use hardware acceleration for transforms */
.sidebar,
.sidebar-backdrop,
.wrapper {
  will-change: transform;
  backface-visibility: hidden;
}

/* Optimize rendering for 

/* ===== REDUCED MOTION ACCESSIBILITY SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
  /* Disable all transitions and animations for users who prefer reduced motion */
  .sidebar,
  .sidebar-backdrop,
  .wrapper,
  .sidebar-nav .nav-link,
  .sidebar-nav .nav-group-toggle,
  .sidebar-nav .nav-group-arrow,
  .sidebar-brand-full,
  .sidebar-brand-narrow,
  .nav-link-text,
  .sidebar-nav .nav-group-items,
  .sidebar-tooltip {
    transition: none !important;
    animation: none !important;
  }
  
  /* Provide instant state changes instead of animations */
  .sidebar.show {
    transform: var(--sidebar-transform-slide-in) !important;
  }
  
  .sidebar:not(.show) {
    transform: var(--sidebar-transform-slide-out) !important;
  }
  
  .sidebar-backdrop.show {
    opacity: 1 !important;
    visibility: visible !important;
  }
  
  .sidebar-backdrop:not(.show) {
    opacity: 0 !important;
    visibility: hidden !important;
  }
  
  /* Disable staggered animations */
  .sidebar-nav .nav-link {
    animation-delay: 0s !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .sidebar,
  .sidebar-backdrop,
  .sidebar-toggle-mobile,
  .sidebar-toggle-desktop {
    display: none !important;
  }
  
  .wrapper {
    margin-left: 0 !important;
  }
}

/* ===== HIGH CONTRAST MODE SUPPORT ===== */
@media (prefers-contrast: high) {
  .sidebar {
    border-right: 2px solid;
  }
  
  .sidebar-nav .nav-link.active {
    border-left-width: 4px;
  }
}