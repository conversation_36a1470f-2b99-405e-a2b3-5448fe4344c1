# Sweet Navigation Component - Troubleshooting Guide

## Quick Diagnostic Checklist

When the Sweet Navigation component isn't working, check these items in order:

1. **✅ Assets Loaded**: Are CSS and JS files loading without 404 errors?
2. **✅ Dependencies**: Are j<PERSON>uery and SweetAlert2 loaded before the component?
3. **✅ Initialization**: Is `initializeSweetNavigation()` being called?
4. **✅ Data Attributes**: Are required `data-ajax-url` and `data-title` present?
5. **✅ Backend Endpoint**: Is the API endpoint returning valid JSON?
6. **✅ Network**: Are there any CORS or authentication issues?
7. **✅ Console Errors**: Check browser console for JavaScript errors

## Common Issues & Solutions

### 1. Popup Not Appearing

#### Symptoms
- Hovering over trigger does nothing
- No loading state appears
- No console errors

#### Possible Causes & Solutions

**Missing Required Data Attributes**
```html
<!-- ❌ WRONG: Missing required attributes -->
<a href="#" class="sweet-navigation-trigger">Navigation</a>

<!-- ✅ CORRECT: Include required attributes -->
<a href="#" class="sweet-navigation-trigger"
   data-ajax-url="/api/navigation/classes"
   data-title="Navigation">
   Navigation
</a>
```

**Component Not Initialized**
```javascript
// Check if function exists
if (typeof initializeSweetNavigation === 'function') {
    initializeSweetNavigation();
    console.log('✅ Component initialized');
} else {
    console.error('❌ initializeSweetNavigation function not found');
}
```

**CSS Class Missing**
```html
<!-- ❌ WRONG: Missing trigger class -->
<a href="#" data-ajax-url="/api/navigation">Navigation</a>

<!-- ✅ CORRECT: Include trigger class -->
<a href="#" class="sweet-navigation-trigger" data-ajax-url="/api/navigation">Navigation</a>
```

### 2. AJAX Errors

#### Symptoms
- Loading state appears but popup shows error
- Network tab shows failed requests
- Console shows AJAX errors

#### Possible Causes & Solutions

**404 Not Found**
```javascript
// Check if endpoint exists
fetch('/api/navigation/classes')
    .then(response => {
        if (!response.ok) {
            console.error('❌ Endpoint not found:', response.status);
        }
    });
```

**CORS Issues**
```php
// In Laravel, add CORS middleware
// config/cors.php
'paths' => ['api/*', 'sanctum/csrf-cookie'],
'allowed_methods' => ['*'],
'allowed_origins' => ['*'], // Configure properly for production
'allowed_headers' => ['*'],
```

**Authentication Required**
```javascript
// Check if user is authenticated
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        'Authorization': 'Bearer ' + localStorage.getItem('token') // If using tokens
    }
});
```

**Invalid JSON Response**
```php
// Ensure controller returns proper JSON
return response()->json([
    'success' => true,
    'data' => ['groups' => $groups]
]);

// NOT just:
return $groups; // ❌ This might not be valid JSON structure
```

### 3. Styling Issues

#### Symptoms
- Popup appears but looks broken
- Elements are misaligned
- Colors or fonts are wrong

#### Possible Causes & Solutions

**CSS Not Loaded**
```html
<!-- Check if CSS file is loading -->
<link rel="stylesheet" href="{{ asset('css/components/sweet-navigation.css') }}">

<!-- Verify in browser network tab -->
```

**CSS Conflicts**
```css
/* Add higher specificity if needed */
.sweet-navigation-alert .swal2-popup {
    /* Your styles with !important if necessary */
    margin-top: 80px !important;
}
```

**Bootstrap Version Conflicts**
```html
<!-- Ensure you're using compatible Bootstrap version -->
<!-- Component tested with Bootstrap 3.4.1 -->
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
```

### 4. Search Not Working

#### Symptoms
- Search input appears but doesn't filter items
- Typing in search has no effect

#### Possible Causes & Solutions

**JavaScript Errors**
```javascript
// Check for errors in search function
$('#sweetNavigationSearchInput').on('input', function() {
    console.log('Search triggered:', $(this).val());
    // Debug search functionality
});
```

**Data Attributes Missing**
```html
<!-- Ensure items have searchable data attributes -->
<div class="sweet-navigation-item" 
     data-item-name="algebra fundamentals"
     data-item-text="teacher dr smith">
    <!-- Item content -->
</div>
```

### 5. Multiple Instances Interfering

#### Symptoms
- Multiple triggers on same page don't work independently
- One trigger affects others

#### Possible Causes & Solutions

**Event Namespace Conflicts**
```javascript
// Component uses namespaced events
$('.sweet-navigation-trigger').off('mouseenter.sweet-nav');
$('.sweet-navigation-trigger').on('mouseenter.sweet-nav', handler);
```

**Global State Issues**
```javascript
// Each trigger should work independently
// Check if isDropdownOpen state is being shared incorrectly
```

### 6. Performance Issues

#### Symptoms
- Slow popup opening
- Browser becomes unresponsive
- Memory usage increases over time

#### Possible Causes & Solutions

**Too Many Items**
```php
// Limit items in backend response
$classes = Class::limit(50)->get(); // Don't return thousands of items
```

**Memory Leaks**
```javascript
// Ensure proper cleanup
$(document).off('keydown.sweet-nav'); // Clean up event listeners
$('.swal2-container').remove(); // Clean up DOM elements
```

**Inefficient Queries**
```php
// Use eager loading to prevent N+1 queries
$programs = Program::with(['classes.teachers'])->get();
```

## Debug Mode

### Enable Debug Logging

```javascript
// Enable debug mode
window.SweetNavigationConfig = window.SweetNavigationConfig || {};
window.SweetNavigationConfig.debug = true;

// Re-initialize
initializeSweetNavigation();
```

### Browser Console Debugging

```javascript
// Check component status
console.log('Sweet Navigation Config:', window.SweetNavigationConfig);
console.log('Triggers found:', $('.sweet-navigation-trigger').length);
console.log('SweetAlert2 available:', typeof Swal !== 'undefined');
console.log('jQuery available:', typeof $ !== 'undefined');

// Test individual trigger
$('.sweet-navigation-trigger').first().trigger('mouseenter');
```

### Network Debugging

```javascript
// Monitor AJAX requests
$(document).ajaxSend(function(event, xhr, settings) {
    console.log('🔄 AJAX Request:', settings.url);
});

$(document).ajaxSuccess(function(event, xhr, settings, data) {
    console.log('✅ AJAX Success:', settings.url, data);
});

$(document).ajaxError(function(event, xhr, settings, error) {
    console.error('❌ AJAX Error:', settings.url, xhr.status, error);
});
```

## Browser-Specific Issues

### Internet Explorer (Not Supported)
- Component requires modern JavaScript features
- Use Chrome, Firefox, Safari, or Edge instead

### Safari Issues
```javascript
// Safari sometimes needs explicit initialization
$(window).on('load', function() {
    setTimeout(initializeSweetNavigation, 100);
});
```

### Mobile Browser Issues
```css
/* Ensure touch events work properly */
.sweet-navigation-trigger {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
}
```

## Laravel-Specific Issues

### Route Not Found
```php
// Check if route is defined
php artisan route:list | grep navigation

// Ensure route is in correct file (api.php for API routes)
Route::get('/navigation/classes', [NavigationController::class, 'getClasses'])
    ->name('api.navigation.classes');
```

### Middleware Issues
```php
// Check middleware configuration
Route::middleware(['auth:sanctum', 'throttle:60,1'])->group(function () {
    Route::get('/navigation/classes', [NavigationController::class, 'getClasses']);
});
```

### CSRF Token Issues
```html
<!-- Ensure CSRF token is available -->
<meta name="csrf-token" content="{{ csrf_token() }}">
```

```javascript
// Verify CSRF token is being sent
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});
```

## Testing Tools

### Manual Testing Checklist

1. **Hover Test**: Hover over trigger, verify loading state appears
2. **Data Loading**: Verify popup opens with correct data
3. **Search Test**: Type in search box, verify filtering works
4. **Navigation Test**: Click items, verify navigation works
5. **Action Buttons**: Click action buttons, verify they work
6. **Close Test**: Test ESC key, outside click, close button
7. **Multiple Triggers**: Test multiple triggers don't interfere

### Automated Testing

```javascript
// Simple test function
function testSweetNavigation() {
    const tests = [];
    
    // Test 1: Component loaded
    tests.push({
        name: 'Component Loaded',
        pass: typeof initializeSweetNavigation === 'function'
    });
    
    // Test 2: Triggers found
    tests.push({
        name: 'Triggers Found',
        pass: $('.sweet-navigation-trigger').length > 0
    });
    
    // Test 3: Dependencies loaded
    tests.push({
        name: 'Dependencies Loaded',
        pass: typeof $ !== 'undefined' && typeof Swal !== 'undefined'
    });
    
    // Report results
    tests.forEach(test => {
        console.log(test.pass ? '✅' : '❌', test.name);
    });
    
    return tests.every(test => test.pass);
}

// Run test
testSweetNavigation();
```

## Getting Help

### Information to Provide

When reporting issues, include:

1. **Browser & Version**: Chrome 91, Firefox 89, etc.
2. **Laravel Version**: Laravel 10.x
3. **Error Messages**: Exact console errors
4. **Network Requests**: Failed AJAX requests from Network tab
5. **Code Samples**: Your trigger HTML and backend controller
6. **Steps to Reproduce**: Exact steps that cause the issue

### Useful Commands

```bash
# Check Laravel logs
tail -f storage/logs/laravel.log

# Clear Laravel cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear

# Check routes
php artisan route:list | grep navigation

# Run tests
php artisan test --filter NavigationTest
```

### Debug Configuration

```php
// In .env for debugging
APP_DEBUG=true
LOG_LEVEL=debug

// In config/app.php
'debug' => env('APP_DEBUG', false),
```

Remember: Most issues are caused by missing dependencies, incorrect data attributes, or backend endpoint problems. Start with the basics and work your way up to more complex debugging.
