<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * CommandScheduleRule Entity
 * 
 * Manages dynamic scheduling rules for Laravel Artisan commands
 * 
 * @property int $id
 * @property string $name
 * @property string $command
 * @property string $schedule_expression
 * @property string $schedule_type
 * @property array|null $days_of_week
 * @property array|null $time_slots
 * @property string $timezone
 * @property bool $is_active
 * @property int $priority
 * @property string|null $description
 * @property string|null $depends_on_command
 * @property int $delay_after_dependency
 * @property int $max_execution_time
 * @property \Illuminate\Support\Carbon|null $next_run_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $created_by
 * @property string|null $updated_by
 * @property-read string $human_readable_schedule_info
 * @property-read string $human_readable_next_run
 * @property-read string $status_badge
 * @property-read string $dependency_info
 */
final class CommandScheduleRule extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'command_schedule_rules';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'command',
        'schedule_expression',
        'schedule_type',
        'days_of_week',
        'time_slots',
        'timezone',
        'is_active',
        'priority',
        'description',
        'depends_on_command',
        'delay_after_dependency',
        'max_execution_time',
        'next_run_at',
        'created_by',
        'updated_by'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'days_of_week' => 'array',
        'time_slots' => 'array',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'delay_after_dependency' => 'integer',
        'max_execution_time' => 'integer',
        'next_run_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];



    /**
     * Get human-readable schedule information
     *
     * @return string
     */
    public function getHumanReadableScheduleInfoAttribute(): string
    {
        $info = [];
        // Schedule type and expression
        switch ($this->schedule_type) {
            case 'daily_at':
                // Cron format minute hour * * * or time format HH:MM
                if (preg_match('/^(\d+)\s+(\d+)\s+\*\s+\*\s+\*$/', $this->schedule_expression, $m)) {
                    $minute = (int)$m[1];
                    $hour = (int)$m[2];
                    $time = Carbon::createFromTime($hour, $minute)->format('g:i A');
                } elseif (preg_match('/^(\d{1,2}):(\d{2})$/', $this->schedule_expression)) {
                    $time = Carbon::createFromFormat('H:i', $this->schedule_expression)->format('g:i A');
                } else {
                    $time = $this->schedule_expression;
                }
                $info[] = "Daily at {$time}";
                break;
            case 'weekly_at':
                // Cron format minute hour * * dow or raw
                if (preg_match('/^(\d+)\s+(\d+)\s+\*\s+\*\s+(\d+)$/', $this->schedule_expression, $m)) {
                    $minute = (int)$m[1];
                    $hour = (int)$m[2];
                    $dow = (int)$m[3];
                    $dayNames = ['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];
                    $dayName = $dayNames[$dow] ?? "Day {$dow}";
                    $time = Carbon::createFromTime($hour, $minute)->format('g:i A');
                    $info[] = "Weekly: {$dayName} at {$time}";
                } else {
                    $info[] = "Weekly: {$this->schedule_expression}";
                }
                break;
            case 'cron':
                $info[] = "Cron: {$this->schedule_expression}";
                break;
            default:
                $info[] = ucfirst($this->schedule_type) . ": {$this->schedule_expression}";
                break;
        }
        
        // Timezone
        $info[] = "Timezone: {$this->timezone}";
        
        // Days of week (if specified)
        if (!empty($this->days_of_week)) {
            $dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            $selectedDays = array_map(function($day) use ($dayNames) {
                return $dayNames[$day] ?? $day;
            }, $this->days_of_week);
            $info[] = "Days: " . implode(', ', $selectedDays);
        }
        
        // Time slots (if specified)
        if (!empty($this->time_slots)) {
            $slots = array_map(function($slot) {
                return "{$slot['start']}-{$slot['end']}";
            }, $this->time_slots);
            $info[] = "Time slots: " . implode(', ', $slots);
        }
        
        return implode(' | ', $info);
    }

    /**
     * Get human-readable next run information
     *
     * @return string
     */
    public function getHumanReadableNextRunAttribute(): string
    {
        if (!$this->is_active) {
            return '<span class="text-muted">Disabled</span>';
        }
        
        if ($this->next_run_at) {
            $nextRun = Carbon::parse($this->next_run_at);
            if ($nextRun->isFuture()) {
                return '<span class="text-success">Next: ' . $nextRun->format('M j, Y g:i A') . '</span>';
            } else {
                return '<span class="text-warning">Overdue: ' . $nextRun->format('M j, Y g:i A') . '</span>';
            }
        }
        
        return '<span class="text-info">Not scheduled</span>';
    }

    /**
     * Get status badge HTML
     *
     * @return string
     */
    public function getStatusBadgeAttribute(): string
    {
        if ($this->is_active) {
            return '<span class="badge bg-success">Active</span>';
        } else {
            return '<span class="badge bg-secondary">Disabled</span>';
        }
    }

    /**
     * Get dependency information HTML
     *
     * @return string
     */
    public function getDependencyInfoAttribute(): string
    {
        if (empty($this->depends_on_command)) {
            return '<span class="text-muted">None</span>';
        }
        
        $delay = $this->delay_after_dependency;
        $delayText = $delay > 0 ? " (+{$delay}s)" : '';
        
        return "<span class=\"text-info\">{$this->depends_on_command}{$delayText}</span>";
    }

    /**
     * Get all active schedule rules ordered by priority
     *
     * @return Collection<CommandScheduleRule>
     */
    public static function getActiveRules(): Collection
    {
        return static::where('is_active', true)
            ->orderBy('priority')
            ->orderBy('created_at')
            ->get();
    }

    /**
     * Get schedule rules for a specific command
     *
     * @param string $command
     * @return Collection<CommandScheduleRule>
     */
    public static function getForCommand(string $command): Collection
    {
        return static::where('command', $command)
            ->where('is_active', true)
            ->orderBy('priority')
            ->get();
    }

    /**
     * Get all available commands from schedule rules
     *
     * @return array<string>
     */
    public static function getAvailableCommands(): array
    {
        return static::select('command')
            ->distinct()
            ->pluck('command')
            ->toArray();
    }

    /**
     * Get rules that have dependencies
     *
     * @return Collection<CommandScheduleRule>
     */
    public static function getDependentRules(): Collection
    {
        return static::whereNotNull('depends_on_command')
            ->where('is_active', true)
            ->orderBy('priority')
            ->get();
    }

    /**
     * Get statistics by command
     *
     * @param string $command
     * @return array
     */
    public static function getCommandStats(string $command): array
    {
        $total = static::where('command', $command)->count();
        $active = static::where('command', $command)->where('is_active', true)->count();
        $inactive = $total - $active;
        $dependent = static::where('command', $command)->whereNotNull('depends_on_command')->count();
        
        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive,
            'dependent' => $dependent,
        ];
    }

    /**
     * Check if this rule should run on a given day
     *
     * @param int $dayOfWeek 0=Sunday, 6=Saturday
     * @return bool
     */
    public function shouldRunOnDay(int $dayOfWeek): bool
    {
        if (empty($this->days_of_week)) {
            return true; // No specific days configured, runs every day
        }

        return in_array($dayOfWeek, $this->days_of_week, true);
    }

    /**
     * Check if this rule is within its configured time slots
     *
     * @param string|null $currentTime Time in H:i format, defaults to current time
     * @return bool
     */
    public function isWithinTimeSlot(?string $currentTime = null): bool
    {
        if (empty($this->time_slots)) {
            return true; // No time slots configured, runs any time
        }

        if ($currentTime === null) {
            $currentTime = Carbon::now($this->timezone)->format('H:i');
        }

        try {
            // Convert current time to Carbon object for accurate comparison
            $currentTimeCarbon = Carbon::createFromFormat('H:i', $currentTime);

            foreach ($this->time_slots as $slot) {
                if (isset($slot['start']) && isset($slot['end'])) {
                    try {
                        // Convert slot times to Carbon objects for proper comparison
                        $startTime = Carbon::createFromFormat('H:i', $slot['start']);
                        $endTime = Carbon::createFromFormat('H:i', $slot['end']);
                        
                        // Use Carbon's between method for accurate time range checking
                        if ($currentTimeCarbon->between($startTime, $endTime, true)) {
                            return true;
                        }
                    } catch (\Exception $e) {
                        Log::warning('CommandScheduleRule: Invalid time format in time slot', [
                            'rule_id' => $this->id,
                            'slot_start' => $slot['start'] ?? 'null',
                            'slot_end' => $slot['end'] ?? 'null',
                            'error' => $e->getMessage()
                        ]);
                        continue; // Skip invalid time slot
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::warning('CommandScheduleRule: Invalid current time format', [
                'rule_id' => $this->id,
                'current_time' => $currentTime,
                'error' => $e->getMessage()
            ]);
            
            // Fallback to string comparison for malformed time
            foreach ($this->time_slots as $slot) {
                if (isset($slot['start']) && isset($slot['end'])) {
                    if ($currentTime >= $slot['start'] && $currentTime <= $slot['end']) {
                        return true;
                    }
                }
            }
            
            return false;
        }
    }

    /**
     * Get a human-readable description of when this rule runs
     *
     * @return string
     */
    public function getScheduleDescription(): string
    {
        $description = "Runs ";

        // Add day information
        if (!empty($this->days_of_week)) {
            $dayNames = [
                0 => 'Sunday',
                1 => 'Monday', 
                2 => 'Tuesday',
                3 => 'Wednesday',
                4 => 'Thursday',
                5 => 'Friday',
                6 => 'Saturday'
            ];
            
            $days = array_map(fn($day) => $dayNames[$day] ?? "Day {$day}", $this->days_of_week);
            $description .= "on " . implode(', ', $days) . " ";
        } else {
            $description .= "daily ";
        }

        // Add time information
        if (!empty($this->time_slots)) {
            $times = [];
            foreach ($this->time_slots as $slot) {
                if (isset($slot['start']) && isset($slot['end'])) {
                    $times[] = "{$slot['start']}-{$slot['end']}";
                }
            }
            if (!empty($times)) {
                $description .= "between " . implode(', ', $times) . " ";
            }
        }

        $description .= "({$this->timezone})";

        return $description;
    }

    /**
     * Enable this schedule rule
     *
     * @param string|null $updatedBy
     * @return bool
     */
    public function enable(?string $updatedBy = null): bool
    {
        $this->is_active = true;
        if ($updatedBy) {
            $this->updated_by = $updatedBy;
        }
        
        $result = $this->save();
        
        Log::info('CommandScheduleRule: Rule enabled', [
            'rule_id' => $this->id,
            'rule_name' => $this->name,
            'command' => $this->command,
            'updated_by' => $updatedBy
        ]);
        
        return $result;
    }

    /**
     * Disable this schedule rule
     *
     * @param string|null $updatedBy
     * @return bool
     */
    public function disable(?string $updatedBy = null): bool
    {
        $this->is_active = false;
        if ($updatedBy) {
            $this->updated_by = $updatedBy;
        }
        
        $result = $this->save();
        
        Log::info('CommandScheduleRule: Rule disabled', [
            'rule_id' => $this->id,
            'rule_name' => $this->name,
            'command' => $this->command,
            'updated_by' => $updatedBy
        ]);
        
        return $result;
    }

    /**
     * Validate schedule expression based on type
     *
     * @return bool
     */
    public function isValidScheduleExpression(): bool
    {
        switch ($this->schedule_type) {
            case 'cron':
                // Basic cron validation - should have 5 parts
                $parts = explode(' ', $this->schedule_expression);
                return count($parts) === 5;
                
            case 'daily_at':
                // Should be in H:i format
                return preg_match('/^\d{1,2}:\d{2}$/', $this->schedule_expression);
                
            case 'weekly_at':
                // Should be in "day H:i" format
                return preg_match('/^\d \d{1,2}:\d{2}$/', $this->schedule_expression);
                
            default:
                return true; // Custom types are assumed valid
        }
    }

    /**
     * Mark execution as started and update next_run_at
     *
     * @return bool
     */
    public function markExecutionStarted(): bool
    {
        $this->next_run_at = Carbon::now();
        
        $result = $this->save();
        
        Log::info('CommandScheduleRule: Execution started', [
            'rule_id' => $this->id,
            'rule_name' => $this->name,
            'command' => $this->command,
            'started_at' => $this->next_run_at->toDateTimeString()
        ]);
        
        return $result;
    }

    /**
     * Mark execution as completed
     *
     * @param Carbon|null $nextRunTime
     * @return bool
     */
    public function markExecutionCompleted(?Carbon $nextRunTime = null): bool
    {
        if ($nextRunTime) {
            $this->next_run_at = $nextRunTime;
        } else {
            // Clear next_run_at if no next run time provided
            $this->next_run_at = null;
        }
        
        $result = $this->save();
        
        Log::info('CommandScheduleRule: Execution completed', [
            'rule_id' => $this->id,
            'rule_name' => $this->name,
            'command' => $this->command,
            'completed_at' => Carbon::now()->toDateTimeString(),
            'next_run_at' => $this->next_run_at?->toDateTimeString()
        ]);
        
        return $result;
    }

    /**
     * Mark execution as failed
     *
     * @param string|null $errorMessage
     * @param Carbon|null $nextRunTime
     * @return bool
     */
    public function markExecutionFailed(?string $errorMessage = null, ?Carbon $nextRunTime = null): bool
    {
        if ($nextRunTime) {
            $this->next_run_at = $nextRunTime;
        }
        
        $result = $this->save();
        
        Log::error('CommandScheduleRule: Execution failed', [
            'rule_id' => $this->id,
            'rule_name' => $this->name,
            'command' => $this->command,
            'failed_at' => Carbon::now()->toDateTimeString(),
            'error_message' => $errorMessage,
            'next_run_at' => $this->next_run_at?->toDateTimeString()
        ]);
        
        return $result;
    }

    /**
     * Check if this rule is due to run
     *
     * @return bool
     */
    public function isDue(): bool
    {
        if (!$this->is_active) {
            return false;
        }
        
        // If no next_run_at is set, consider it due (first run)
        if (!$this->next_run_at) {
            return true;
        }
        
        // Check if next_run_at is in the past or now
        return Carbon::now()->greaterThanOrEqualTo($this->next_run_at);
    }

    /**
     * Get execution history for this rule
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function executions(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(CommandScheduleExecution::class, 'schedule_rule_id');
    }

    /**
     * Get the filter configuration for this schedule rule
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function filter(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(CommandScheduleFilter::class, 'schedule_rule_id');
    }

    /**
     * Get recent executions for this rule
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentExecutions(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $this->executions()
            ->orderBy('started_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get the last execution for this rule
     *
     * @return CommandScheduleExecution|null
     */
    public function getLastExecution(): ?CommandScheduleExecution
    {
        return $this->executions()
            ->orderBy('started_at', 'desc')
            ->first();
    }

    /**
     * Check if this rule is currently executing
     *
     * @return bool
     */
    public function isCurrentlyExecuting(): bool
    {
        return $this->executions()
            ->where('status', CommandScheduleExecution::STATUS_RUNNING)
            ->exists();
    }

    /**
     * Get contextual sort key based on next execution time relative to current datetime
     * Returns minutes until next execution (0 = next, higher = further away)
     * Past executions for today get pushed to end with high values
     *
     * @return int Minutes until next execution (0-based, past = high values)
     */
    public function getCronSortKey(): int
    {
        $nextExecution = $this->getNextExecutionTime();

        if (!$nextExecution) {
            return PHP_INT_MAX; // Invalid expressions go to end
        }

        $now = Carbon::now();
        $minutesUntilNext = $now->diffInMinutes($nextExecution, false);

        // If negative (past), it means next execution is in the future
        // Convert to positive minutes for sorting (0 = soonest)
        return max(0, $minutesUntilNext);
    }

    /**
     * Calculate the next execution datetime based on cron expression
     * Considers current date/time to determine if today's execution has passed
     *
     * @return Carbon|null Next execution datetime or null if invalid
     */
    public function getNextExecutionTime(): ?Carbon
    {
        if (!$this->schedule_expression) {
            return null;
        }

        try {
            $parts = explode(' ', trim($this->schedule_expression));

            if (count($parts) >= 5) {
                $minute = (int) trim($parts[0]);
                $hour = (int) trim($parts[1]);
                $cronDayOfWeek = (int) trim($parts[4]); // 0=Sunday, 1=Monday, etc.

                // Validate time ranges
                if ($hour >= 0 && $hour <= 23 && $minute >= 0 && $minute <= 59 && $cronDayOfWeek >= 0 && $cronDayOfWeek <= 6) {
                    $now = Carbon::now();
                    $today = $now->dayOfWeek; // 0=Sunday, 1=Monday, etc.

                    // Create execution time for today
                    $todayExecution = $now->copy()->startOfDay()->addHours($hour)->addMinutes($minute);

                    // If it's the right day and time hasn't passed, use today
                    if ($cronDayOfWeek === $today && $todayExecution->gt($now)) {
                        return $todayExecution;
                    }

                    // Otherwise, find next occurrence
                    $daysUntilNext = ($cronDayOfWeek - $today + 7) % 7;
                    if ($daysUntilNext === 0) {
                        $daysUntilNext = 7; // Next week if today's time has passed
                    }

                    return $now->copy()->addDays($daysUntilNext)->startOfDay()->addHours($hour)->addMinutes($minute);
                }
            }
        } catch (\Exception $e) {
            Log::warning('CommandScheduleRule: Error calculating next execution time', [
                'rule_id' => $this->id,
                'schedule_expression' => $this->schedule_expression,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Extract day of week from cron expression
     * Returns day of week (0-6) where 0=Sunday, or null for daily schedules
     *
     * @return int|null Day of week (0-6) or null
     */
    public function getCronDayOfWeek(): ?int
    {
        if (!$this->schedule_expression) {
            return null;
        }

        try {
            // Parse cron expression: "minute hour day month weekday"
            $parts = explode(' ', trim($this->schedule_expression));

            if (count($parts) >= 5) {
                $dayOfWeek = trim($parts[4]);

                // Check if it's a specific day (not *)
                if ($dayOfWeek !== '*' && is_numeric($dayOfWeek)) {
                    $day = (int) $dayOfWeek;
                    if ($day >= 0 && $day <= 6) {
                        return $day;
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('CommandScheduleRule: Error parsing day of week from cron expression', [
                'rule_id' => $this->id,
                'schedule_expression' => $this->schedule_expression,
                'error' => $e->getMessage()
            ]);
        }

        return null; // Daily schedule or invalid expression
    }

    /**
     * Get formatted time from cron expression for display
     *
     * @return string|null Time in HH:MM format or null
     */
    public function getCronDisplayTime(): ?string
    {
        if (!$this->schedule_expression) {
            return null;
        }

        try {
            // Parse cron expression: "minute hour day month weekday"
            $parts = explode(' ', trim($this->schedule_expression));

            if (count($parts) >= 2) {
                $minute = trim($parts[0]);
                $hour = trim($parts[1]);

                // Validate that minute and hour are numeric
                if (is_numeric($minute) && is_numeric($hour)) {
                    $hourInt = (int) $hour;
                    $minuteInt = (int) $minute;

                    // Validate time ranges
                    if ($hourInt >= 0 && $hourInt <= 23 && $minuteInt >= 0 && $minuteInt <= 59) {
                        return sprintf('%02d:%02d', $hourInt, $minuteInt);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('CommandScheduleRule: Error parsing time from cron expression', [
                'rule_id' => $this->id,
                'schedule_expression' => $this->schedule_expression,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }
}