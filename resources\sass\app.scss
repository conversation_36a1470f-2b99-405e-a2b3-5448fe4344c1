// Variables
@import 'variables';

// Bootstrap
@import '~bootstrap/scss/bootstrap';

// Metronic Base Styles
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 0;
    background: #f5f5f5;
}

// Metronic App Styles
.app-root {
    min-height: 100vh;
}

.app-page {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.app-header {
    background: #fff;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 0;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.app-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.app-content {
    flex: 1;
    padding: 2rem 0;
}

// Card Styles
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

// Button Styles
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background-color: #4F46E5;
    border-color: #4F46E5;
}

.btn-success {
    background-color: #10B981;
    border-color: #10B981;
}

.btn-warning {
    background-color: #F59E0B;
    border-color: #F59E0B;
}

.btn-danger {
    background-color: #EF4444;
    border-color: #EF4444;
}

// Menu Styles
.menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu-item {
    margin-right: 1rem;
}

.menu-link {
    display: block;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #6b7280;
    border-radius: 6px;
    transition: all 0.2s;
}

.menu-item.here .menu-link,
.menu-link:hover {
    background-color: #f3f4f6;
    color: #374151;
}

// Utility Classes
.text-dark {
    color: #1f2937 !important;
}

.text-gray-400 {
    color: #9ca3af !important;
}

.text-gray-600 {
    color: #6b7280 !important;
}

.text-gray-800 {
    color: #374151 !important;
}

.fw-bold {
    font-weight: 600 !important;
}

.fw-semibold {
    font-weight: 500 !important;
}

.fs-1 {
    font-size: 2.5rem !important;
}

.fs-2 {
    font-size: 2rem !important;
}

.fs-6 {
    font-size: 1rem !important;
}

.fs-7 {
    font-size: 0.875rem !important;
}

// Responsive Grid
.g-5 {
    gap: 1.5rem;
}

.g-xl-10 {
    @media (min-width: 1200px) {
        gap: 2.5rem;
    }
}

.mb-5 {
    margin-bottom: 1.5rem !important;
}

.mb-xl-10 {
    @media (min-width: 1200px) {
        margin-bottom: 2.5rem !important;
    }
}

// Badge Styles
.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-light-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-light-primary {
    background-color: #e0e7ff;
    color: #3730a3;
}

.badge-light-warning {
    background-color: #fef3c7;
    color: #92400e;
}

// Form Styles
.form-control {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    border-color: #4F46E5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-select {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    padding: 0.5rem 0.75rem;
}

// Table Styles
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
}

.table td {
    border-top: 1px solid #f3f4f6;
    vertical-align: middle;
}

// Symbol/Avatar Styles
.symbol {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    overflow: hidden;
}

.symbol-40px {
    width: 40px;
    height: 40px;
}

.symbol-50px {
    width: 50px;
    height: 50px;
}

.symbol-100px {
    width: 100px;
    height: 100px;
}

.symbol-circle {
    border-radius: 50%;
}

.symbol-label {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

// Background Colors
.bg-light-primary {
    background-color: #e0e7ff !important;
}

.bg-light-success {
    background-color: #d1fae5 !important;
}

.bg-light-warning {
    background-color: #fef3c7 !important;
}

.bg-light-info {
    background-color: #dbeafe !important;
}

.bg-primary {
    background-color: #4F46E5 !important;
}

.bg-success {
    background-color: #10B981 !important;
}

.bg-warning {
    background-color: #F59E0B !important;
}

.bg-info {
    background-color: #3B82F6 !important;
}

// Text Colors
.text-primary {
    color: #4F46E5 !important;
}

.text-success {
    color: #10B981 !important;
}

.text-warning {
    color: #F59E0B !important;
}

.text-info {
    color: #3B82F6 !important;
}

.text-white {
    color: #ffffff !important;
}

// Separator
.separator {
    height: 1px;
    background-color: #e5e7eb;
    margin: 1rem 0;
}
