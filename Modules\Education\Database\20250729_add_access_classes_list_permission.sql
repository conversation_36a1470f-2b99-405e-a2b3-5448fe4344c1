/*
 * Add "access classes list" Permission for Sweet Navigation
 * 
 * Description: Creates a new permission for controlling access to the Sweet Navigation classes list
 * Module: Education
 * Author: Augment Agent
 * Date: 2025-07-29
 * 
 * Purpose: This permission controls who can see the interactive classes dropdown in breadcrumbs.
 * Users without this permission will see a static "Classes" link instead of the Sweet Navigation dropdown.
 * 
 * IMPORTANT: This file must be reviewed and executed by authorized database administrators
 * 
 * Security Impact:
 * - Restricts access to the classes list navigation feature
 * - Prevents unauthorized users from browsing all classes
 * - Maintains data privacy and access control
 */

-- ===================================================================
-- ADD "access classes list" PERMISSION
-- ===================================================================

-- Insert the new permission
INSERT INTO permissions (
    id,
    name,
    guard_name,
    organization_id,
    created_at,
    updated_at,
    module_id,
    parent_id,
    route,
    status,
    created_by,
    updated_by,
    type,
    deleted_at,
    display_name,
    description
) VALUES (
    643,  -- Next available ID after current max (642)
    'access classes list',
    'employee',
    2,  -- Standard organization_id based on existing permissions
    NOW(),
    NOW(),
    NULL,  -- module_id (can be set later if needed)
    172,   -- parent_id: references "access classes" permission (id: 172)
    NULL,  -- route (not tied to specific route)
    1,     -- status: active
    1,     -- created_by: system user
    1,     -- updated_by: system user
    NULL,  -- type
    NULL,  -- deleted_at: not deleted
    'Access Classes List',  -- display_name: human-readable name
    'Allows access to the interactive classes navigation dropdown in breadcrumbs'  -- description
);

-- ===================================================================
-- VERIFICATION QUERY (Optional - run to verify insertion)
-- ===================================================================

/*
-- Uncomment to verify the permission was created successfully:
SELECT * FROM permissions WHERE name = 'access classes list';

-- Check parent-child relationship:
SELECT 
    p.name as permission_name,
    parent.name as parent_permission
FROM permissions p
LEFT JOIN permissions parent ON p.parent_id = parent.id
WHERE p.name = 'access classes list';
*/

-- ===================================================================
-- ROLLBACK PROCEDURE (Emergency Use Only)
-- ===================================================================

/*
-- EMERGENCY ROLLBACK: Remove the permission if needed
-- Only run if there are issues with the permission

DELETE FROM permissions WHERE name = 'access classes list' AND id = 643;

-- Also remove any role assignments for this permission:
DELETE FROM role_has_permissions WHERE permission_id = 643;
DELETE FROM model_has_permissions WHERE permission_id = 643;
*/
