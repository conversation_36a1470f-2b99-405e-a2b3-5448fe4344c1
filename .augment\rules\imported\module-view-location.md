---
type: "always_apply"
---

# Module View Location Convention

In this project, Blade views for Laravel modules (managed by `nwidart/laravel-modules` or similar) are **not** located in the default module path (`Modules/<ModuleName>/Resources/views`).

Instead, they are located within the main application's `resources` directory under:

`[resources/views/modules](mdc:resources/views/modules)/<module-name-lowercase>/`

Each module's service provider (e.g., [`Modules/HumanResource/Providers/HumanResourceServiceProvider.php`](mdc:Modules/HumanResource/Providers/HumanResourceServiceProvider.php)) is configured in its `registerViews` method to load views from this custom path using `$this->loadViewsFrom(resource_path('views/modules/<module-name-lowercase>'), '<module-name-lowercase>');`.

When working with module views, always look in `resources/views/modules/` first.

**This convention MUST be strictly followed when creating or editing any Laravel Blade views or Blade partials for a module. Under no circumstances should these files be placed in, or moved to, the `Modules/<ModuleName>/Resources/views/` directory. Always use `[resources/views/modules](mdc:resources/views/modules)/<module-name-lowercase>/` for all module-related Blade files.**
