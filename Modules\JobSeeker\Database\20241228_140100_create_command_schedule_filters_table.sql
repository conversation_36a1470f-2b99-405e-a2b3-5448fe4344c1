-- Create command_schedule_filters table for per-rule filter configuration
-- This allows each schedule rule to have custom filters for jobs.af API calls

CREATE TABLE command_schedule_filters (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    schedule_rule_id bigint unsigned NOT NULL,
    categories json DEFAULT NULL COMMENT 'Array of job categories to filter by',
    locations json DEFAULT NULL COMMENT 'Array of province/location names to filter by',
    companies json DEFAULT NULL COMMENT 'Array of company names to filter by',
    experience_levels json DEFAULT NULL COMMENT 'Array of experience levels (Entry, Mid, Senior, etc.)',
    search_term varchar(255) DEFAULT NULL COMMENT 'Search term for job title/description',
    work_type varchar(50) DEFAULT NULL COMMENT 'Work type filter (Remote, On-site, Hybrid)',
    is_default tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Marks the fallback filter configuration',
    created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY command_schedule_filters_rule_unique (schedule_rule_id),
    KEY command_schedule_filters_is_default_index (is_default),
    CONSTRAINT command_schedule_filters_schedule_rule_id_foreign 
        FOREIGN KEY (schedule_rule_id) 
        REFERENCES command_schedule_rules (id) 
        ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better query performance
CREATE INDEX command_schedule_filters_locations_index ON command_schedule_filters ((CAST(locations AS CHAR(255) ARRAY)));
CREATE INDEX command_schedule_filters_categories_index ON command_schedule_filters ((CAST(categories AS CHAR(255) ARRAY)));

-- Verify table creation
DESCRIBE command_schedule_filters; 