<?php

namespace Modules\JobSeeker\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Console\Commands\SyncJobsAfCommand;
use Modules\JobSeeker\Console\Commands\FetchJobsAfDescriptionsCommand;
use Mo<PERSON>les\JobSeeker\Console\Commands\SyncAcbarJobsCommand;
use Modules\JobSeeker\Console\Commands\DiscoverAcbarCategoriesCommand;
use Modules\JobSeeker\Console\Commands\CleanupStaleDeviceTokensCommand;
use Modules\JobSeeker\Console\Commands\NotifyJobSeekersCommand;
use Modules\JobSeeker\Console\Commands\CleanupOldJobsCommand;
use Modules\JobSeeker\Console\Commands\EncryptExistingCredentialsCommand;
use Modules\JobSeeker\Console\Commands\EmailProviderHeartbeatCommand;
use Modules\JobSeeker\Console\Commands\EmailRecoveryCommand;
use Modules\JobSeeker\Console\Commands\CleanupCommandExecutionHistoryCommand;
use Modules\JobSeeker\Http\Middleware\JobSeekerAuthThrottleMiddleware;
use Modules\JobSeeker\Http\Middleware\JobSeekerThrottleMiddleware;
use Modules\JobSeeker\Http\Middleware\JobSeekerSearchThrottleMiddleware;
use Modules\JobSeeker\Http\Middleware\JobSeekerAdminMiddleware;

class JobSeekerServiceProvider extends ServiceProvider
{
    /**
     * @var string $moduleName
     */
    protected $moduleName = 'JobSeeker';

    /**
     * @var string $moduleNameLower
     */
    protected $moduleNameLower = 'jobseeker';

    /**
     * The command classes
     *
     * @var array
     */
    protected $commands = [
        SyncJobsAfCommand::class,
        FetchJobsAfDescriptionsCommand::class,
        SyncAcbarJobsCommand::class,
        // DiscoverAcbarCategoriesCommand::class,
        CleanupStaleDeviceTokensCommand::class,
        NotifyJobSeekersCommand::class,
        CleanupOldJobsCommand::class,
        EncryptExistingCredentialsCommand::class,
        EmailProviderHeartbeatCommand::class,
        EmailRecoveryCommand::class,
        CleanupCommandExecutionHistoryCommand::class,
    ];

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot(): void
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path('JobSeeker', 'Database/Migrations'));
        $this->registerMiddleware();
        
        Log::info('JobSeekerServiceProvider: Boot method completed successfully');
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
        $this->app->register(EventServiceProvider::class);
        $this->commands($this->commands);
    }

    /**
     * Register middleware.
     *
     * @return void
     */
    protected function registerMiddleware(): void
    {
        $router = $this->app['router'];
        $router->aliasMiddleware('jobseeker.auth.throttle', JobSeekerAuthThrottleMiddleware::class);
        $router->aliasMiddleware('jobseeker.throttle', JobSeekerThrottleMiddleware::class);
        $router->aliasMiddleware('jobseeker.search.throttle', JobSeekerSearchThrottleMiddleware::class);
        $router->aliasMiddleware('jobseeker.admin', JobSeekerAdminMiddleware::class);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path($this->moduleName, 'Config/config.php') => config_path($this->moduleNameLower . '.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/config.php'), $this->moduleNameLower
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/' . $this->moduleNameLower);

        $sourcePath = module_path($this->moduleName, 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ], ['views', $this->moduleNameLower . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->moduleNameLower);
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/' . $this->moduleNameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->moduleNameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->moduleName, 'Resources/lang'), $this->moduleNameLower);
            $this->loadJsonTranslationsFrom(module_path($this->moduleName, 'Resources/lang'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (\Config::get('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->moduleNameLower)) {
                $paths[] = $path . '/modules/' . $this->moduleNameLower;
            }
        }
        return $paths;
    }
} 