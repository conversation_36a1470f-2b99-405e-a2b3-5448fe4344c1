<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Modules\JobSeeker\Entities\JobSeeker;
use Modules\JobSeeker\Services\AccountLockoutService;
use Modules\JobSeeker\Services\PasswordHistoryService;
use App\Rules\StrongPassword;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Laravel\Socialite\Facades\Socialite;

/**
 * Enhanced JobSeeker Authentication Controller
 * 
 * Implements comprehensive security features:
 * - Account lockout after failed attempts
 * - Strong password validation
 * - Password history tracking
 * - Email verification enforcement
 * - Secure social login
 * - Turnstile CAPTCHA verification
 */
final class LoginController extends Controller
{
    use AuthenticatesUsers;

    /**
     * Get the post login redirect path.
     */
    protected function redirectTo(): string
    {
        return route('jobseeker.notifications');
    }

    /**
     * Account lockout service
     */
    private AccountLockoutService $lockoutService;

    /**
     * Password history service
     */
    private PasswordHistoryService $passwordHistoryService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        AccountLockoutService $lockoutService,
        PasswordHistoryService $passwordHistoryService
    ) {
        // Apply guest middleware only to actual authentication actions, not form display
        $this->middleware('guest:job_seeker')->only(['login', 'register', 'redirectToProvider', 'handleProviderCallback']);
        $this->lockoutService = $lockoutService;
        $this->passwordHistoryService = $passwordHistoryService;
    }

    /**
     * Show the application's login form.
     */
    public function showLoginForm()
    {
        // If JobSeeker is already authenticated, redirect to intended destination
        if (Auth::guard('job_seeker')->check()) {
            $intendedUrl = session()->pull('url.intended', $this->redirectTo());
            
            Log::info('Already authenticated JobSeeker attempted to access login form', [
                'job_seeker_id' => Auth::guard('job_seeker')->id(),
                'redirect_to' => $intendedUrl,
                'ip' => request()->ip()
            ]);
            
            return redirect($intendedUrl);
        }
        
        return view('modules.jobseeker.auth.login');
    }

    /**
     * Show the application's registration form.
     */
    public function showRegistrationForm()
    {
        // If JobSeeker is already authenticated, redirect to intended destination
        if (Auth::guard('job_seeker')->check()) {
            $intendedUrl = session()->pull('url.intended', $this->redirectTo());
            
            Log::info('Already authenticated JobSeeker attempted to access registration form', [
                'job_seeker_id' => Auth::guard('job_seeker')->id(),
                'redirect_to' => $intendedUrl,
                'ip' => request()->ip()
            ]);
            
            return redirect($intendedUrl);
        }
        
        return view('modules.jobseeker.auth.register');
    }

    /**
     * Verify Turnstile CAPTCHA token.
     */
    protected function verifyTurnstileToken(Request $request): bool
    {
        $token = $request->input('cf-turnstile-response');
        
        if (!$token) {
            Log::warning('Turnstile token missing in JobSeeker request', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            return false;
        }

        try {
            $response = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
                'secret'   => config('services.turnstile.secret'),
                'response' => $token,
                'remoteip' => $request->ip(),
            ]);

            $result = $response->json();

            if (!($result['success'] ?? false)) {
                Log::warning('Turnstile verification failed for JobSeeker', [
                    'token' => substr($token, 0, 10) . '...',
                    'response' => $result,
                    'ip' => $request->ip()
                ]);
                return false;
            }

            Log::info('Turnstile verification passed for JobSeeker', [
                'ip' => $request->ip(),
                'challenge_ts' => $result['challenge_ts'] ?? null
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Turnstile verification error for JobSeeker', [
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);
            return false;
        }
    }

    /**
     * Handle a login request to the application.
     */
    public function login(Request $request)
    {
        $this->validateLogin($request);

        // Verify Turnstile CAPTCHA
        if (!$this->verifyTurnstileToken($request)) {
            return back()->withErrors([
                'cf-turnstile-response' => 'Security verification failed. Please try again.'
            ])->withInput($request->except('password'));
        }

        $email = $this->getEmailFromLogin($request->input('login'));
        $password = $request->input('password');

        // Check for account lockout first
        $jobSeeker = JobSeeker::where('email', $email)->first();
        if ($jobSeeker && $this->lockoutService->isAccountLocked($jobSeeker)) {
            $lockoutDetails = $this->lockoutService->getLockoutDetails($jobSeeker);
            
            Log::warning('Login attempt on locked JobSeeker account', [
                'email' => $email,
                'ip' => $request->ip(),
                'lockout_details' => $lockoutDetails
            ]);

            return back()->withErrors([
                'login' => 'Your account is temporarily locked due to multiple failed login attempts. Please try again in ' . $lockoutDetails['minutes_remaining'] . ' minutes.'
            ])->withInput($request->except('password'));
        }

        // Check for auto-unlock
        if ($jobSeeker) {
            $this->lockoutService->canAutoUnlock($jobSeeker);
        }

        // Attempt authentication
        if ($this->attemptLogin($request)) {
            if ($jobSeeker) {
                // Clear failed attempts on successful login
                $this->lockoutService->clearFailedAttempts($jobSeeker);
                
                // Check email verification
                if (!$this->isEmailVerified($jobSeeker)) {
                    $this->guard()->logout();
                    return $this->redirectToEmailVerification($jobSeeker);
                }

                Log::info('Successful JobSeeker login', [
                    'job_seeker_id' => $jobSeeker->id,
                    'email' => $email,
                    'ip' => $request->ip()
                ]);
            }

            return $this->sendLoginResponse($request);
        }

        // Record failed attempt and handle potential lockout
        $lockoutResult = $this->lockoutService->recordFailedAttempt(
            $email,
            $request->ip(),
            $request->userAgent()
        );

        if ($lockoutResult['locked']) {
            return back()->withErrors([
                'login' => 'Too many failed login attempts. Your account has been locked for ' . 
                          $lockoutResult['lockout_details']['minutes_remaining'] . ' minutes.'
            ])->withInput($request->except('password'));
        }

        return $this->sendFailedLoginResponse($request, $lockoutResult['attempts_remaining']);
    }

    /**
     * Handle a registration request.
     */
    public function register(Request $request)
    {
        $this->validateRegistration($request);

        // Verify Turnstile CAPTCHA
        if (!$this->verifyTurnstileToken($request)) {
            return back()->withErrors([
                'cf-turnstile-response' => 'Security verification failed. Please try again.'
            ])->withInput($request->except('password', 'password_confirmation'));
        }

        try {
            $jobSeeker = $this->createJobSeeker($request->all());

            // Send email verification
            $this->sendEmailVerification($jobSeeker);

            // Instead of auto-login, redirect to email verification notice
            // Auto-login the user temporarily so they can access the verification page
            Auth::guard('job_seeker')->login($jobSeeker);

            Log::info('New JobSeeker registration', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'ip' => $request->ip()
            ]);

            // Redirect to email verification notice page
            return redirect()->route('jobseeker.verification.notice')
                ->with('success', 'Registration successful! Please check your email to verify your account.');
        } catch (\Exception $e) {
            Log::error('JobSeeker registration failed', [
                'error' => $e->getMessage(),
                'email' => $request->input('email'),
                'ip' => $request->ip()
            ]);

            return back()->withErrors([
                'registration' => 'Registration failed. Please try again.'
            ])->withInput($request->except('password', 'password_confirmation'));
        }
    }

    /**
     * Handle password change request.
     */
    public function changePassword(Request $request)
    {
        $jobSeeker = Auth::guard('job_seeker')->user();
        
        if (!$jobSeeker) {
            return response()->json(['error' => 'Unauthenticated'], 401);
        }

        // Check if password change is allowed
        if (!$this->passwordHistoryService->canChangePassword($jobSeeker)) {
            return back()->withErrors([
                'password' => 'You must wait at least 1 hour between password changes.'
            ]);
        }

        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'password' => [
                'required',
                'string',
                'confirmed',
                new StrongPassword([
                    $jobSeeker->name,
                    $jobSeeker->email,
                    $jobSeeker->username ?? ''
                ])
            ],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Verify current password
        if (!Hash::check($request->current_password, $jobSeeker->password)) {
            return back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        // Check password history
        if ($this->passwordHistoryService->isPasswordRecentlyUsed($jobSeeker, $request->password)) {
            return back()->withErrors([
                'password' => 'You cannot reuse one of your recent passwords. Please choose a different password.'
            ]);
        }

        // Record old password in history
        $this->passwordHistoryService->recordPasswordChange($jobSeeker, $jobSeeker->password);

        // Update password
        $jobSeeker->update(['password' => Hash::make($request->password)]);

        Log::info('JobSeeker password changed successfully', [
            'job_seeker_id' => $jobSeeker->id,
            'email' => $jobSeeker->email,
            'ip' => $request->ip()
        ]);

        return back()->with('success', 'Password updated successfully!');
    }

    /**
     * Redirect to Google OAuth provider.
     */
    public function redirectToProvider(Request $request)
    {
        try {
            // Store CAPTCHA verification in session for OAuth flow
            if ($request->has('cf-turnstile-response')) {
                if ($this->verifyTurnstileToken($request)) {
                    session(['oauth_captcha_verified' => true]);
                } else {
                    return redirect()->route('jobseeker.login')
                        ->withErrors(['cf-turnstile-response' => 'Please complete security verification before using Google authentication.']);
                }
            }

            return Socialite::driver('google')->redirect();
        } catch (\Exception $e) {
            Log::error('JobSeeker Google OAuth redirect failed', [
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);
            
            return redirect()->route('jobseeker.login')
                ->withErrors(['login' => 'Unable to connect to Google. Please try again.']);
        }
    }

    /**
     * Handle Google OAuth callback.
     */
    public function handleProviderCallback(Request $request)
    {
        try {
            // Check if CAPTCHA was verified during OAuth initiation
            if (!session('oauth_captcha_verified')) {
                session()->forget('oauth_captcha_verified');
                return redirect()->route('jobseeker.login')
                    ->withErrors(['login' => 'Security verification required. Please complete CAPTCHA and try again.']);
            }

            // Clear the CAPTCHA verification flag
            session()->forget('oauth_captcha_verified');

            $googleUser = Socialite::driver('google')->user();
            
            // Validate and sanitize Google user data
            if (!$this->isValidGoogleUser($googleUser)) {
                throw new \Exception('Invalid Google user data received');
            }

            $jobSeeker = JobSeeker::where('email', $googleUser->getEmail())->first();

            if ($jobSeeker) {
                // Check account lockout
                if ($this->lockoutService->isAccountLocked($jobSeeker)) {
                    $lockoutDetails = $this->lockoutService->getLockoutDetails($jobSeeker);
                    return redirect()->route('jobseeker.login')->withErrors([
                        'login' => 'Your account is temporarily locked. Please try again in ' . 
                                  $lockoutDetails['minutes_remaining'] . ' minutes.'
                    ]);
                }

                // Update Google ID if not set
                if (!$jobSeeker->google_id) {
                    $jobSeeker->update(['google_id' => $googleUser->getId()]);
                }

                // Mark email as verified if coming from Google
                if (!$jobSeeker->email_verified_at) {
                    $jobSeeker->update(['email_verified_at' => now()]);
                }
            } else {
                // Create new user from Google data
                $jobSeeker = $this->createJobSeekerFromGoogle($googleUser);
            }

            Auth::guard('job_seeker')->login($jobSeeker);

            Log::info('Successful JobSeeker Google OAuth login', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'google_id' => $googleUser->getId(),
                'ip' => $request->ip()
            ]);

            return redirect()->intended($this->redirectTo);

        } catch (\Exception $e) {
            Log::error('JobSeeker Google OAuth callback failed', [
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return redirect()->route('jobseeker.login')->withErrors([
                'login' => 'Google authentication failed. Please try again.'
            ]);
        }
    }

    /**
     * Get the guard to be used during authentication.
     */
    protected function guard()
    {
        return Auth::guard('job_seeker');
    }

    /**
     * Get the login username to be used by the controller.
     */
    public function username(): string
    {
        $login = request()->input('login');
        $field = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
        request()->merge([$field => $login]);
        return $field;
    }

    /**
     * Validate the user login request.
     */
    protected function validateLogin(Request $request): void
    {
        $request->validate([
            'login' => 'required|string',
            'password' => 'required|string',
            'cf-turnstile-response' => 'required',
        ]);
    }

    /**
     * Validate the user registration request.
     */
    protected function validateRegistration(Request $request): void
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:job_seekers',
            'password' => [
                'required',
                'string',
                'confirmed',
                'min:8',
                new StrongPassword([
                    $request->input('name'),
                    $request->input('email')
                ])
            ],
            'terms' => 'required|accepted',
            'cf-turnstile-response' => 'required',
        ]);
    }

    /**
     * Create a new job seeker instance.
     */
    protected function createJobSeeker(array $data): JobSeeker
    {
        $username = $this->generateUniqueUsername($data['name']);
        
        return JobSeeker::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'username' => $username,
            'password' => Hash::make($data['password']),
            'email_verification_token' => \Str::random(60),
            'is_active' => false, // Require email verification
        ]);
    }

    /**
     * Create job seeker from Google OAuth data.
     */
    protected function createJobSeekerFromGoogle($googleUser): JobSeeker
    {
        return JobSeeker::create([
            'name' => $this->sanitizeGoogleData($googleUser->getName()),
            'email' => $googleUser->getEmail(),
            'username' => $this->generateUniqueUsername($googleUser->getName()),
            'google_id' => $googleUser->getId(),
            'email_verified_at' => now(), // Google emails are pre-verified
            'is_active' => true,
            'avatar' => $googleUser->getAvatar(),
        ]);
    }

    /**
     * Check if email is verified.
     */
    protected function isEmailVerified(JobSeeker $jobSeeker): bool
    {
        return $jobSeeker->email_verified_at !== null;
    }

    /**
     * Send email verification.
     */
    protected function sendEmailVerification(JobSeeker $jobSeeker): void
    {
        try {
            $jobSeeker->sendEmailVerificationNotification();
            
            Log::info('Email verification sent for JobSeeker', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send JobSeeker verification email', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'error' => $e->getMessage()
            ]);
            
            // Don't throw exception to prevent registration failure
            // User can request verification resend later
        }
    }

    /**
     * Redirect to email verification page.
     */
    protected function redirectToEmailVerification(JobSeeker $jobSeeker)
    {
        return redirect()->route('jobseeker.verification.notice')
            ->with('email', $jobSeeker->email);
    }

    /**
     * Get email from login input.
     */
    protected function getEmailFromLogin(string $login): string
    {
        return filter_var($login, FILTER_VALIDATE_EMAIL) ? $login : 
               JobSeeker::where('username', $login)->value('email') ?? $login;
    }

    /**
     * Send failed login response with remaining attempts.
     */
    protected function sendFailedLoginResponse(Request $request, int $attemptsRemaining)
    {
        $message = $attemptsRemaining > 0 
            ? "Invalid credentials. You have {$attemptsRemaining} attempts remaining before account lockout."
            : 'Invalid credentials.';

        throw ValidationException::withMessages([
            $this->username() => [$message],
        ]);
    }

    /**
     * Validate Google user data.
     */
    protected function isValidGoogleUser($googleUser): bool
    {
        return $googleUser && 
               $googleUser->getEmail() && 
               $googleUser->getId() && 
               filter_var($googleUser->getEmail(), FILTER_VALIDATE_EMAIL);
    }

    /**
     * Sanitize Google user data.
     */
    protected function sanitizeGoogleData(?string $data): string
    {
        return strip_tags(trim($data ?? ''));
    }

    /**
     * Generate unique username from name.
     */
    protected function generateUniqueUsername(string $name): string
    {
        $baseUsername = \Str::slug(strtolower($name), '');
        $username = $baseUsername;
        $counter = 1;

        while (JobSeeker::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Log the user out of the application.
     */
    public function logout(Request $request)
    {
        $jobSeeker = Auth::guard('job_seeker')->user();
        
        if ($jobSeeker) {
            Log::info('JobSeeker logout', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'ip' => $request->ip()
            ]);
        }

        $this->guard()->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('jobseeker.login');
    }
} 