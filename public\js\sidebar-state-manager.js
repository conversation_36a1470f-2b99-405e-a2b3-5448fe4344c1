/**
 * Sidebar State Manager
 * 
 * Integrates preference persistence with sidebar states
 * Handles mobile/desktop responsive behavior and menu group states
 * Includes comprehensive ARIA support and screen reader accessibility
 */
class SidebarStateManager {
    constructor() {
        this.preferenceManager = new PreferenceManager();
        this.config = {
            breakpoints: {
                mobile: 768
            },
            selectors: {
                sidebar: '#sidebar',
                wrapper: '.main-wrapper, .wrapper',
                toggleBtn: '.sidebar-toggle, #sidebarCollapse, .sidebar-toggle-mobile, .sidebar-toggle-desktop',
                closeBtn: '#close_sidebar',
                backdrop: '.sidebar-backdrop',
                menuGroups: '.nav-group-toggle, [data-bs-toggle="collapse"]',
                body: 'body'
            },
            classes: {
                sidebarCollapsed: 'collapsed',
                sidebarVisible: 'show',
                mobileView: 'mobile-view',
                desktopView: 'desktop-view',
                backdropActive: 'show'
            },
            animations: {
                duration: 300,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            },
            aria: {
                labels: {
                    sidebar: 'Main navigation',
                    toggleButton: 'Toggle navigation menu',
                    closeButton: 'Close navigation menu',
                    menuGroup: 'Menu group',
                    collapsedItem: 'Collapsed menu item',
                    expandedGroup: 'Expanded menu group',
                    collapsedGroup: 'Collapsed menu group'
                },
                liveRegion: {
                    id: 'sidebar-live-region',
                    politeness: 'polite'
                }
            }
        };
        
        this.state = {
            isMobile: window.innerWidth <= this.config.breakpoints.mobile,
            isCollapsed: false,
            isVisible: false,
            expandedGroups: new Set(),
            isInitialized: false
        };
        
        // Cached DOM elements for performance optimization
        this.elements = {};
        
        // Performance-optimized event handlers
        this.debouncedResize = this.debounce(this.handleResize.bind(this), 250);
        this.throttledScroll = this.throttle(this.handleScroll.bind(this), 16); // ~60fps
        
        // Event delegation container
        this.eventDelegationContainer = null;
        
        this.init();
    }
    
    /**
     * Initialize the sidebar state manager
     */
    init() {
        this.cacheElements();
        this.setupAriaSupport();
        this.loadPreferences();
        this.setupEventListeners();
        this.updateViewportState();
        this.applyInitialState();
        this.state.isInitialized = true;
        
        console.log('SidebarStateManager initialized', this.state);
    }
    
    /**
     * Cache DOM elements for performance
     */
    cacheElements() {
        this.elements = {
            sidebar: document.querySelector(this.config.selectors.sidebar),
            wrapper: document.querySelector(this.config.selectors.wrapper),
            toggleBtns: document.querySelectorAll(this.config.selectors.toggleBtn),
            closeBtn: document.querySelector(this.config.selectors.closeBtn),
            backdrop: document.querySelector(this.config.selectors.backdrop),
            menuGroups: document.querySelectorAll(this.config.selectors.menuGroups),
            body: document.body
        };
        
        // Create backdrop if it doesn't exist
        if (!this.elements.backdrop) {
            this.createBackdrop();
        }
    }
    
    /**
     * Create backdrop element for mobile overlay
     */
    createBackdrop() {
        const backdrop = document.createElement('div');
        backdrop.className = 'sidebar-backdrop';
        backdrop.setAttribute('aria-hidden', 'true');
        document.body.appendChild(backdrop);
        this.elements.backdrop = backdrop;
    }
    
    /**
     * Setup comprehensive ARIA support for screen readers and accessibility
     */
    setupAriaSupport() {
        this.setupLiveRegion();
        this.setupNavigationLandmarks();
        this.setupInteractiveElementLabels();
        this.setupMenuGroupAriaSupport();
        this.setupKeyboardAccessibility();
        
        console.log('ARIA support initialized');
    }
    
    /**
     * Create and setup ARIA live region for state change announcements
     */
    setupLiveRegion() {
        // Check if live region already exists
        let liveRegion = document.getElementById(this.config.aria.liveRegion.id);
        
        if (!liveRegion) {
            liveRegion = document.createElement('div');
            liveRegion.id = this.config.aria.liveRegion.id;
            liveRegion.setAttribute('aria-live', this.config.aria.liveRegion.politeness);
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.setAttribute('aria-relevant', 'additions text');
            liveRegion.className = 'sr-only'; // Screen reader only
            liveRegion.style.cssText = `
                position: absolute !important;
                width: 1px !important;
                height: 1px !important;
                padding: 0 !important;
                margin: -1px !important;
                overflow: hidden !important;
                clip: rect(0, 0, 0, 0) !important;
                white-space: nowrap !important;
                border: 0 !important;
            `;
            
            document.body.appendChild(liveRegion);
        }
        
        this.elements.liveRegion = liveRegion;
    }
    
    /**
     * Setup proper navigation landmarks and roles
     */
    setupNavigationLandmarks() {
        if (this.elements.sidebar) {
            // Set main navigation role and label
            this.elements.sidebar.setAttribute('role', 'navigation');
            this.elements.sidebar.setAttribute('aria-label', this.config.aria.labels.sidebar);
            
            // Add landmark for main content area
            if (this.elements.wrapper) {
                this.elements.wrapper.setAttribute('role', 'main');
                this.elements.wrapper.setAttribute('aria-label', 'Main content area');
            }
            
            // Setup navigation list structure
            const navList = this.elements.sidebar.querySelector('ul.list-unstyled, ul.components');
            if (navList) {
                navList.setAttribute('role', 'menubar');
                navList.setAttribute('aria-orientation', 'vertical');
                
                // Setup individual navigation items
                const navItems = navList.querySelectorAll('li');
                navItems.forEach((item, index) => {
                    const link = item.querySelector('a');
                    const hasSubmenu = item.querySelector('ul');
                    
                    if (hasSubmenu) {
                        // Menu group with submenu
                        item.setAttribute('role', 'none');
                        if (link) {
                            link.setAttribute('role', 'menuitem');
                            link.setAttribute('aria-haspopup', 'true');
                            link.setAttribute('aria-expanded', 'false');
                            link.setAttribute('tabindex', index === 0 ? '0' : '-1');
                        }
                        
                        // Setup submenu
                        const submenu = item.querySelector('ul');
                        if (submenu) {
                            submenu.setAttribute('role', 'menu');
                            submenu.setAttribute('aria-label', `${link?.textContent?.trim() || 'Menu group'} submenu`);
                            
                            const submenuItems = submenu.querySelectorAll('li');
                            submenuItems.forEach(subItem => {
                                subItem.setAttribute('role', 'none');
                                const subLink = subItem.querySelector('a');
                                if (subLink) {
                                    subLink.setAttribute('role', 'menuitem');
                                    subLink.setAttribute('tabindex', '-1');
                                }
                            });
                        }
                    } else {
                        // Regular navigation item
                        item.setAttribute('role', 'none');
                        if (link) {
                            link.setAttribute('role', 'menuitem');
                            link.setAttribute('tabindex', index === 0 ? '0' : '-1');
                        }
                    }
                });
            }
        }
    }
    
    /**
     * Setup ARIA labels and states for interactive elements
     */
    setupInteractiveElementLabels() {
        // Setup toggle buttons
        this.elements.toggleBtns.forEach(btn => {
            if (!btn.getAttribute('aria-label')) {
                btn.setAttribute('aria-label', this.config.aria.labels.toggleButton);
            }
            btn.setAttribute('aria-controls', this.elements.sidebar?.id || 'sidebar');
            btn.setAttribute('aria-expanded', 'false');
        });
        
        // Setup close button
        if (this.elements.closeBtn) {
            if (!this.elements.closeBtn.getAttribute('aria-label')) {
                this.elements.closeBtn.setAttribute('aria-label', this.config.aria.labels.closeButton);
            }
            this.elements.closeBtn.setAttribute('aria-controls', this.elements.sidebar?.id || 'sidebar');
        }
        
        // Setup backdrop
        if (this.elements.backdrop) {
            this.elements.backdrop.setAttribute('role', 'presentation');
            this.elements.backdrop.setAttribute('aria-hidden', 'true');
        }
    }
    
    /**
     * Setup ARIA support for menu groups with collapse functionality
     */
    setupMenuGroupAriaSupport() {
        this.elements.menuGroups.forEach(toggle => {
            const targetId = toggle.getAttribute('data-bs-target') || toggle.getAttribute('href');
            const targetElement = targetId ? document.querySelector(targetId) : null;
            
            if (targetElement) {
                // Setup toggle button
                toggle.setAttribute('aria-controls', targetId.replace('#', ''));
                toggle.setAttribute('aria-expanded', targetElement.classList.contains('show') ? 'true' : 'false');
                
                if (!toggle.getAttribute('aria-label')) {
                    const groupName = toggle.textContent?.trim() || this.config.aria.labels.menuGroup;
                    toggle.setAttribute('aria-label', `Toggle ${groupName} menu group`);
                }
                
                // Setup collapsible content
                targetElement.setAttribute('role', 'region');
                targetElement.setAttribute('aria-labelledby', toggle.id || `toggle-${targetId.replace('#', '')}`);
                
                // Ensure toggle has an ID for aria-labelledby
                if (!toggle.id) {
                    toggle.id = `toggle-${targetId.replace('#', '')}`;
                }
                
                // Add arrow indicator with ARIA support
                this.setupArrowIndicator(toggle);
            }
        });
    }
    
    /**
     * Setup arrow indicators for menu groups with proper ARIA support
     */
    setupArrowIndicator(toggle) {
        let arrow = toggle.querySelector('.nav-group-arrow, .arrow-indicator');
        
        if (!arrow) {
            arrow = document.createElement('i');
            arrow.className = 'nav-group-arrow fas fa-chevron-right ms-auto';
            arrow.setAttribute('aria-hidden', 'true');
            toggle.appendChild(arrow);
        }
        
        // Ensure arrow is hidden from screen readers
        arrow.setAttribute('aria-hidden', 'true');
        arrow.setAttribute('role', 'presentation');
    }
    
    /**
     * Setup comprehensive keyboard accessibility
     */
    setupKeyboardAccessibility() {
        if (!this.elements.sidebar) return;
        
        // Setup roving tabindex for navigation
        this.setupRovingTabindex();
        
        // Setup keyboard event handlers for navigation
        this.elements.sidebar.addEventListener('keydown', this.handleNavigationKeydown.bind(this));
        
        // Setup focus management
        this.setupFocusManagement();
    }
    
    /**
     * Setup roving tabindex pattern for keyboard navigation
     */
    setupRovingTabindex() {
        const menuItems = this.elements.sidebar.querySelectorAll('[role="menuitem"]');
        
        menuItems.forEach((item, index) => {
            item.setAttribute('tabindex', index === 0 ? '0' : '-1');
        });
    }
    
    /**
     * Handle keyboard navigation within the sidebar
     */
    handleNavigationKeydown(event) {
        const target = event.target;
        const isMenuItem = target.getAttribute('role') === 'menuitem';
        
        if (!isMenuItem) return;
        
        const menuItems = Array.from(this.elements.sidebar.querySelectorAll('[role="menuitem"]'));
        const currentIndex = menuItems.indexOf(target);
        
        let nextIndex = currentIndex;
        
        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                nextIndex = (currentIndex + 1) % menuItems.length;
                break;
                
            case 'ArrowUp':
                event.preventDefault();
                nextIndex = currentIndex === 0 ? menuItems.length - 1 : currentIndex - 1;
                break;
           
    
    /**
     * Setup event listeners with performance optimizations
     */
    setupEventListeners() {
        // Resize event with debouncing
        window.addEventListener('resize', this.debouncedResize, { passive: true });
        
        // Setup event delegation for better performance
        this.setupEventDelegation();
        
        // Individual event listeners for specific elements
        this.setupIndividualEventListeners();
        
        // Keyboard events
        document.addEventListener('keydown', this.handleKeydown.bind(this));
        
        // Touch events for swipe-to-close
        this.setupTouchEvents();
        
        // Scroll events for performance monitoring
        if (this.elements.sidebar) {
            this.elements.sidebar.addEventListener('scroll', this.throttledScroll, { passive: true });
        }
    }
    
    /**
     * Setup event delegation for menu interactions
     * More efficient than individual event listeners
     */
    setupEventDelegation() {
        // Use sidebar as delegation container for menu interactions
        if (this.elements.sidebar) {
            this.eventDelegationContainer = this.elements.sidebar;
            
            // Single event listener for all menu interactions
            this.elements.sidebar.addEventListener('click', (event) => {
                const target = event.target.closest('[data-bs-toggle="collapse"], .nav-group-toggle, .sidebar-toggle-desktop');
                
                if (target) {
                    // Prevent default and stop propagation
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // Handle different types of interactions
                    if (target.matches('[data-bs-toggle="collapse"], .nav-group-toggle')) {
                        this.handleMenuGroupToggle(event);
                    } else if (target.matches('.sidebar-toggle-desktop')) {
                        this.handleToggle(event);
                    }
                }
            });
        }
    }
    
    /**
     * Setup individual event listeners for specific elements
     */
    setupIndividualEventListeners() {
        // Toggle button events (mobile buttons outside sidebar)
        this.elements.toggleBtns.forEach(btn => {
            // Skip buttons that are handled by event delegation
            if (!btn.closest('#sidebar')) {
                btn.addEventListener('click', this.handleToggle.bind(this), { passive: false });
            }
        });
        
        // Close button event
        if (this.elements.closeBtn) {
            this.elements.closeBtn.addEventListener('click', this.handleClose.bind(this), { passive: false });
        }
        
        // Backdrop click event
        if (this.elements.backdrop) {
            this.elements.backdrop.addEventListener('click', this.handleBackdropClick.bind(this), { passive: false });
        }
    }
    
    /**
     * Setup touch events for mobile swipe-to-close
     */
    setupTouchEvents() {
        if (!this.elements.sidebar) return;
        
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        this.elements.sidebar.addEventListener('touchstart', (e) => {
            if (!this.state.isMobile || !this.state.isVisible) return;
            
            startX = e.touches[0].clientX;
            isDragging = true;
        }, { passive: true });
        
        this.elements.sidebar.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            
            currentX = e.touches[0].clientX;
            const deltaX = currentX - startX;
            
            // Only allow left swipe (negative delta)
            if (deltaX < 0) {
                const opacity = Math.max(0, 1 + (deltaX / 200));
                this.elements.sidebar.style.transform = `translateX(${Math.min(0, deltaX)}px)`;
                if (this.elements.backdrop) {
                    this.elements.backdrop.style.opacity = opacity;
                }
            }
        }, { passive: true });
        
        this.elements.sidebar.addEventListener('touchend', () => {
            if (!isDragging) return;
            
            const deltaX = currentX - startX;
            
            // Close if swiped more than 100px to the left
            if (deltaX < -100) {
                this.closeMobile();
            } else {
                // Reset position
                this.elements.sidebar.style.transform = '';
                if (this.elements.backdrop) {
                    this.elements.backdrop.style.opacity = '';
                }
            }
            
            isDragging = false;
        }, { passive: true });
    }
    
    /**
     * Handle window resize with responsive state management
     */
    handleResize() {
        const wasMobile = this.state.isMobile;
        this.updateViewportState();
        
        // Handle viewport transition
        if (wasMobile !== this.state.isMobile) {
            this.handleViewportTransition(wasMobile);
        }
        
        this.coordinateStates();
    }
    
    /**
     * Update viewport state based on window width
     */
    updateViewportState() {
        this.state.isMobile = window.innerWidth <= this.config.breakpoints.mobile;
        
        // Update body classes
        if (this.state.isMobile) {
            this.elements.body.classList.add(this.config.classes.mobileView);
            this.elements.body.classList.remove(this.config.classes.desktopView);
        } else {
            this.elements.body.classList.add(this.config.classes.desktopView);
            this.elements.body.classList.remove(this.config.classes.mobileView);
        }
    }
    
    /**
     * Handle transition between mobile and desktop views
     */
    handleViewportTransition(wasMobile) {
        console.log(`Viewport transition: ${wasMobile ? 'mobile' : 'desktop'} -> ${this.state.isMobile ? 'mobile' : 'desktop'}`);
        
        if (wasMobile && !this.state.isMobile) {
            // Mobile to desktop transition
            this.transitionToDesktop();
        } else if (!wasMobile && this.state.isMobile) {
            // Desktop to mobile transition
            this.transitionToMobile();
        }
    }
    
    /**
     * Transition from mobile to desktop view
     */
    transitionToDesktop() {
        // Hide mobile backdrop
        this.hideBackdrop();
        
        // Reset mobile sidebar state
        if (this.elements.sidebar) {
            this.elements.sidebar.classList.remove(this.config.classes.sidebarVisible);
        }
        
        // Apply desktop state from preferences
        const prefs = this.preferenceManager.load();
        this.state.isCollapsed = prefs.desktop.collapsed;
        this.state.isVisible = true; // Desktop sidebar is always visible
        
        this.applyDesktopState();
    }
    
    /**
     * Transition from desktop to mobile view
     */
    transitionToMobile() {
        // Hide desktop sidebar
        this.state.isVisible = false;
        this.state.isCollapsed = false; // Mobile doesn't use collapsed state
        
        // Clean up desktop classes
        if (this.elements.sidebar) {
            this.elements.sidebar.classList.remove(this.config.classes.sidebarCollapsed);
        }
        
        if (this.elements.wrapper) {
            this.elements.wrapper.classList.remove('sidebar-collapsed');
        }
        
        this.applyMobileState();
    }
    
    /**
     * Coordinate mobile and desktop sidebar states
     */
    coordinateStates() {
        if (this.state.isMobile) {
            this.applyMobileState();
        } else {
            this.applyDesktopState();
        }
    }
    
    /**
     * Apply mobile-specific state with performance optimization
     */
    applyMobileState() {
        if (!this.elements.sidebar) return;
        
        // Use performance-optimized state application
        this.applyStateWithOptimization(() => {
            // Apply visibility state
            if (this.state.isVisible) {
                this.elements.sidebar.classList.add(this.config.classes.sidebarVisible);
                this.showBackdrop();
                this.trapFocus();
            } else {
                this.elements.sidebar.classList.remove(this.config.classes.sidebarVisible);
                this.hideBackdrop();
                this.releaseFocus();
            }
        });
    }
    
    /**
     * Apply desktop-specific state with performance optimization
     */
    applyDesktopState() {
        if (!this.elements.sidebar || !this.elements.wrapper) return;
        
        // Use performance-optimized state application
        this.applyStateWithOptimization(() => {
            // Apply collapse state
            if (this.state.isCollapsed) {
                this.elements.sidebar.classList.add(this.config.classes.sidebarCollapsed);
                this.elements.wrapper.classList.add('sidebar-collapsed');
                this.showTooltips();
            } else {
                this.elements.sidebar.classList.remove(this.config.classes.sidebarCollapsed);
                this.elements.wrapper.classList.remove('sidebar-collapsed');
                this.hideTooltips();
            }
        });
    }
    
    /**
     * Handle toggle button click
     */
    handleToggle(event) {
        event.preventDefault();
        event.stopPropagation();
        
        if (this.state.isMobile) {
            this.toggleMobile();
        } else {
            this.toggleDesktop();
        }
    }
    
    /**
     * Toggle mobile sidebar visibility
     */
    toggleMobile() {
        this.state.isVisible = !this.state.isVisible;
        this.applyMobileState();
        
        console.log('Mobile sidebar toggled:', this.state.isVisible);
    }
    
    /**
     * Toggle desktop sidebar collapse state
     */
    toggleDesktop() {
        this.state.isCollapsed = !this.state.isCollapsed;
        this.applyDesktopState();
        this.savePreferences();
        
        console.log('Desktop sidebar toggled:', this.state.isCollapsed);
    }
    
    /**
     * Close mobile sidebar
     */
    closeMobile() {
        if (this.state.isMobile && this.state.isVisible) {
            this.state.isVisible = false;
            this.applyMobileState();
        }
    }
    
    /**
     * Handle close button click
     */
    handleClose(event) {
        event.preventDefault();
        this.closeMobile();
    }
    
    /**
     * Handle backdrop click
     */
    handleBackdropClick(event) {
        event.preventDefault();
        this.closeMobile();
    }
    
    /**
     * Handle menu group toggle
     */
    handleMenuGroupToggle(event) {
        const target = event.currentTarget;
        const groupId = target.getAttribute('data-bs-target') || target.getAttribute('href');
        
        if (groupId) {
            const cleanId = groupId.replace('#', '');
            
            if (this.state.expandedGroups.has(cleanId)) {
                this.state.expandedGroups.delete(cleanId);
            } else {
                this.state.expandedGroups.add(cleanId);
            }
            
            this.savePreferences();
        }
    }
    
    /**
     * Handle keyboard events
     */
    handleKeydown(event) {
        // Close mobile sidebar on Escape key
        if (event.key === 'Escape' && this.state.isMobile && this.state.isVisible) {
            this.closeMobile();
        }
        
        // Toggle sidebar on Ctrl+B (optional keyboard shortcut)
        if (event.ctrlKey && event.key === 'b') {
            event.preventDefault();
            if (this.state.isMobile) {
                this.toggleMobile();
            } else {
                this.toggleDesktop();
            }
        }
    }
    
    /**
     * Show backdrop overlay
     */
    showBackdrop() {
        if (this.elements.backdrop) {
            this.elements.backdrop.classList.add(this.config.classes.backdropActive);
        }
    }
    
    /**
     * Hide backdrop overlay
     */
    hideBackdrop() {
        if (this.elements.backdrop) {
            this.elements.backdrop.classList.remove(this.config.classes.backdropActive);
        }
    }
    
    /**
     * Show tooltips for collapsed sidebar using cached selectors
     */
    showTooltips() {
        if (!this.elements.sidebar) return;
        
        const cached = this.optimizeDOMQueries();
        cached.navLinks.forEach(link => {
            const textElement = link.querySelector('.nav-link-text');
            if (textElement && !link.querySelector('.sidebar-tooltip')) {
                const tooltip = this.createTooltip(textElement.textContent.trim());
                link.appendChild(tooltip);
            }
        });
        
        // Refresh cached selectors since we added tooltips
        this.refreshCachedSelectors();
    }
    
    /**
     * Hide tooltips when sidebar is expanded using cached selectors
     */
    hideTooltips() {
        if (!this.elements.sidebar) return;
        
        const cached = this.optimizeDOMQueries();
        cached.tooltips.forEach(tooltip => tooltip.remove());
        
        // Refresh cached selectors since we removed tooltips
        this.refreshCachedSelectors();
    }
    
    /**
     * Create tooltip element
     */
    createTooltip(text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'sidebar-tooltip';
        tooltip.textContent = text;
        tooltip.setAttribute('role', 'tooltip');
        tooltip.setAttribute('aria-hidden', 'true');
        return tooltip;
    }
    
    /**
     * Trap focus within sidebar for accessibility using cached selectors
     */
    trapFocus() {
        if (!this.elements.sidebar) return;
        
        const cached = this.optimizeDOMQueries();
        
        if (cached.focusableElements.length > 0) {
            cached.focusableElements[0].focus();
        }
    }
    
    /**
     * Release focus trap
     */
    releaseFocus() {
        // Focus can return to the toggle button or main content
        const toggleBtn = document.querySelector(this.config.selectors.toggleBtn);
        if (toggleBtn) {
            toggleBtn.focus();
        }
    }
    
    /**
     * Load user preferences from storage
     */
    loadPreferences() {
        const prefs = this.preferenceManager.load();
        if (prefs) {
            this.state.isCollapsed = prefs.desktop?.collapsed || false;
            this.state.expandedGroups = new Set(prefs.menuGroups ? Object.keys(prefs.menuGroups).filter(key => prefs.menuGroups[key]) : []);
        }
    }
    
    /**
     * Save current state to preferences
     */
    savePreferences() {
        const preferences = {
            desktop: {
                collapsed: this.state.isCollapsed
            },
            menuGroups: {}
        };
        
        // Save menu group states
        this.state.expandedGroups.forEach(groupId => {
            preferences.menuGroups[groupId] = true;
        });
        
        this.preferenceManager.save(preferences);
    }
    
    /**
     * Apply initial state on page load
     */
    applyInitialState() {
        this.coordinateStates();
        
        // Restore menu group states
        this.state.expandedGroups.forEach(groupId => {
            const element = document.getElementById(groupId);
            if (element) {
                element.classList.add('show');
                const toggle = document.querySelector(`[data-bs-target="#${groupId}"], [href="#${groupId}"]`);
                if (toggle) {
                    toggle.setAttribute('aria-expanded', 'true');
                    toggle.classList.remove('collapsed');
                }
            }
        });
    }
    
    /**
     * Utility function to debounce function calls
     * Optimized for performance with proper cleanup
     */
    debounce(func, wait) {
        let timeout;
        let lastCallTime = 0;
        
        return function executedFunction(...args) {
            const now = Date.now();
            const timeSinceLastCall = now - lastCallTime;
            
            const later = () => {
                lastCallTime = Date.now();
                timeout = null;
                func.apply(this, args);
            };
            
            // Clear existing timeout
            if (timeout) {
                clearTimeout(timeout);
            }
            
            // If enough time has passed, execute immediately
            if (timeSinceLastCall >= wait) {
                later();
            } else {
                // Otherwise, schedule for later
                timeout = setTimeout(later, wait - timeSinceLastCall);
            }
        };
    }
    
    /**
     * Throttle function for high-frequency events
     * More efficient than debounce for continuous events
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    /**
     * Handle scroll events for performance monitoring
     * Throttled to maintain 60fps performance
     */
    handleScroll(event) {
        // Monitor scroll performance and adjust animations if needed
        if (this.state.isMobile && this.state.isVisible) {
            // Optimize mobile scrolling by reducing backdrop blur on scroll
            const scrollTop = event.target.scrollTop;
            if (this.elements.backdrop && scrollTop > 50) {
                this.elements.backdrop.style.backdropFilter = 'blur(1px)';
            } else if (this.elements.backdrop) {
                this.elements.backdrop.style.backdropFilter = 'blur(2px)';
            }
        }
    }
    
    /**
     * Optimize DOM queries with cached selectors
     * Reduces repeated DOM traversal for better performance
     */
    optimizeDOMQueries() {
        // Cache frequently accessed elements
        if (!this.cachedSelectors) {
            this.cachedSelectors = {
                navLinks: this.elements.sidebar?.querySelectorAll('.nav-link') || [],
                menuGroupToggles: this.elements.sidebar?.querySelectorAll('.nav-group-toggle, [data-bs-toggle="collapse"]') || [],
                tooltips: this.elements.sidebar?.querySelectorAll('.sidebar-tooltip') || [],
                focusableElements: this.elements.sidebar?.querySelectorAll(
                    'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
                ) || []
            };
        }
        
        return this.cachedSelectors;
    }
    
    /**
     * Refresh cached selectors when DOM changes
     */
    refreshCachedSelectors() {
        this.cachedSelectors = null;
        this.optimizeDOMQueries();
    }
    
    /**
     * Use hardware-accelerated CSS transforms for animations
     * More performant than changing layout properties
     */
    applyHardwareAcceleratedAnimations() {
        if (!this.elements.sidebar) return;
        
        // Enable hardware acceleration for sidebar
        this.elements.sidebar.style.willChange = 'transform';
        this.elements.sidebar.style.backfaceVisibility = 'hidden';
        this.elements.sidebar.style.perspective = '1000px';
        
        // Enable hardware acceleration for wrapper
        if (this.elements.wrapper) {
            this.elements.wrapper.style.willChange = 'margin-left';
            this.elements.wrapper.style.backfaceVisibility = 'hidden';
        }
        
        // Enable hardware acceleration for backdrop
        if (this.elements.backdrop) {
            this.elements.backdrop.style.willChange = 'opacity, visibility';
            this.elements.backdrop.style.backfaceVisibility = 'hidden';
        }
    }
    
    /**
     * Clean up hardware acceleration when not needed
     */
    cleanupHardwareAcceleration() {
        const elements = [this.elements.sidebar, this.elements.wrapper, this.elements.backdrop];
        
        elements.forEach(element => {
            if (element) {
                element.style.willChange = 'auto';
                element.style.backfaceVisibility = '';
                element.style.perspective = '';
            }
        });
    }
    
    /**
     * Performance-optimized state application
     * Uses requestAnimationFrame for smooth animations
     */
    applyStateWithOptimization(stateFunction) {
        // Use requestAnimationFrame for smooth state changes
        requestAnimationFrame(() => {
            this.applyHardwareAcceleratedAnimations();
            stateFunction.call(this);
            
            // Clean up after animation completes
            setTimeout(() => {
                this.cleanupHardwareAcceleration();
            }, this.config.animations.duration + 50);
        });
    }
    
    /**
     * Destroy the sidebar state manager and clean up resources
     */
    destroy() {
        // Remove event listeners
        window.removeEventListener('resize', this.debouncedResize);
        
        if (this.elements.sidebar) {
            this.elements.sidebar.removeEventListener('scroll', this.throttledScroll);
        }
        
        // Clean up cached elements
        this.elements = {};
        this.cachedSelectors = null;
        
        // Clean up hardware acceleration
        this.cleanupHardwareAcceleration();
        
        console.log('SidebarStateManager destroyed');
    }