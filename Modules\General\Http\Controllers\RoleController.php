<?php

namespace Modules\General\Http\Controllers;

use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Role;
use App\Permission;
use App\Authorizable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\RolePermission\Entities\PermissionAssign;


class RoleController extends Controller
{
    // use Authorizable;

    public function index()
    {
        $roles = Role::all();
        $permissions = Permission::all();

        return view('general::roles.index', compact('roles', 'permissions'));
    }

    public function create()
    {

        $messages = [];// Message::limit(5)->get();
        return view('general::roles.create', compact('messages'));

    }

    public function store(Request $request)
    {
        try {
            // Validate the request
            $this->validate($request, ['description' => 'required']);
            // Modify the request data
            $request->merge(['name' => \Illuminate\Support\Str::slug($request->description) . '_' . config('organization_id') . '_']);
            // Validate the modified request data
            $this->validate($request, ['name' => 'required|unique:roles']);

            // Create an empty role array
            $role = [];

            // Debugging: dump the request data
            // Populate the role array
            $role['name'] = $request->name;
            $role['description'] = $request->description;
            $role['organization_id'] = config('organization_id');
            $newRole = Role::create($role);
            // Attempt to create a new role
            if ($newRole) {
                // Flash a success message
                flash('Role Added');

                // Get the selected permissions from the request
                $permissions = $request->get('permissions', []);

                foreach ($permissions as $permission) {
                    // Check if the permission already exists in the database
                    if (!Permission::where('name', $permission)->where('guard_name', 'employee')->first()) {
                        // Create a new permission if it does not exist
                        Permission::create(['name' => $permission, 'organization_id' => 2]);
                    }
                }

                // Synchronize the new role's permissions
                $newRole->syncPermissions($permissions);

            } else {
                // Flash an error message
                flash('Error occurred while adding role');
            }

            // Redirect to the roles page
            return redirect('workplace/general/roles');
        } catch (\Exception $e) {
            // Handle any exceptions
            flash('Error occured while adding role');
            return redirect()->back()->withErrors(['error' => $e->getMessage()]);        }
    }



    public function edit($id)
    {

        $role = Role::findOrFail($id);

        return view('general::roles.edit', compact('role'));
    }

    public function update(Request $request, $id)
    {






        try {
            DB::beginTransaction();

            Schema::disableForeignKeyConstraints();
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            PermissionAssign::where('role_id', $request->role_id)->delete();



            if ($request->module_id) {

                foreach ($request->module_id as $module) {

                    $assign = new PermissionAssign();
                    $assign->module_id = $module;
                    $assign->role_id = $request->role_id;
                    $assign->organization_id = Auth::user()->organization_id;
                    $assign->save();
                }
            }

        $role = Role::findOrFail($id);
        if($role) {
            //     $role->syncPermissions(Permission::all());
            //     return redirect()->route('roles.index');
            $this->validate($request, ['description' => 'required']);
            
            
            $role->description = $request->description;
            $role->save();
//            $permissions = $request->get('permissions', []);

            $permissions = collect($request->permissions)->filter(function($value, $key) {
                return  $value != null;
            })->toArray();



//            foreach ($request->permissions as $permission ) {
            foreach ($permissions as $key => $permission ) {

                if(Permission::where('name' , $permission)->where('guard_name' , 'employee')->doesntExist()){
                    Permission::create(['name' => $permission , 'organization_id' => config('organization_id')]);
                }
            }


            $role->syncPermissions($permissions);
            flash( $role->description . ' permissions has been updated.');
            DB::commit();

        } else {
            flash()->error( 'Role with id '. $id .' note found.');
        }

        return redirect()->back();
        } catch (\Exception $e) {
            DB::rollback();
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }
    public function destroy($id)
    {
        // find the role by id
        $role = Role::findOrFail($id);

        // delete the role
        $role->delete();
        $roles = Role::all();
        $permissions = Permission::all();
        return view('general::roles.index', compact('roles', 'permissions'))->with('success', 'Role has been deleted.');

        // redirect to the index page with success message
        return redirect()->route('general::roles.index')->with('success', 'Role has been deleted.');
    }

}
