/**
 * PreferenceManager Class
 * 
 * Handles localStorage operations for sidebar and menu preferences
 * with error handling, versioning, and expiry management
 */
class PreferenceManager {
    constructor() {
        this.storageKey = 'jobseeker_sidebar_prefs';
        this.version = '1.0';
        this.expiryDays = 30; // Preferences expire after 30 days
        this.fallbackStorage = new Map(); // In-memory fallback when localStorage unavailable
        
        // Default preference schema
        this.defaultPreferences = {
            version: this.version,
            timestamp: Date.now(),
            desktop: {
                collapsed: false
            },
            menuGroups: {},
            accessibility: {
                reducedMotion: this.detectReducedMotion()
            }
        };
        
        this.init();
    }
    
    /**
     * Initialize the preference manager
     */
    init() {
        this.isLocalStorageAvailable = this.checkLocalStorageAvailability();
        
        if (!this.isLocalStorageAvailable) {
            console.warn('localStorage is not available, using in-memory storage');
        }
        
        // Clean up expired preferences on init
        this.cleanupExpiredPreferences();
    }
    
    /**
     * Check if localStorage is available
     * @returns {boolean}
     */
    checkLocalStorageAvailability() {
        try {
            const testKey = '__localStorage_test__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (e) {
            return false;
        }
    }
    
    /**
     * Detect if user prefers reduced motion
     * @returns {boolean}
     */
    detectReducedMotion() {
        if (typeof window !== 'undefined' && window.matchMedia) {
            return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        }
        return false;
    }
    
    /**
     * Save preferences to storage
     * @param {Object} preferences - Preferences object to save
     * @returns {boolean} - Success status
     */
    save(preferences) {
        try {
            const prefsToSave = {
                ...this.defaultPreferences,
                ...preferences,
                version: this.version,
                timestamp: Date.now()
            };
            
            if (this.isLocalStorageAvailable) {
                localStorage.setItem(this.storageKey, JSON.stringify(prefsToSave));
            } else {
                this.fallbackStorage.set(this.storageKey, prefsToSave);
            }
            
            return true;
        } catch (error) {
            console.error('Failed to save preferences:', error);
            
            // Try fallback storage if localStorage fails
            if (this.isLocalStorageAvailable) {
                try {
                    this.fallbackStorage.set(this.storageKey, {
                        ...this.defaultPreferences,
                        ...preferences,
                        version: this.version,
                        timestamp: Date.now()
                    });
                    return true;
                } catch (fallbackError) {
                    console.error('Fallback storage also failed:', fallbackError);
                }
            }
            
            return false;
        }
    }
    
    /**
     * Load preferences from storage
     * @returns {Object|null} - Loaded preferences or null if not found/invalid
     */
    load() {
        try {
            let storedData = null;
            
            if (this.isLocalStorageAvailable) {
                const stored = localStorage.getItem(this.storageKey);
                storedData = stored ? JSON.parse(stored) : null;
            } else {
                storedData = this.fallbackStorage.get(this.storageKey) || null;
            }
            
            if (!storedData) {
                return this.getDefaultPreferences();
            }
            
            // Check if preferences are expired
            if (this.isExpired(storedData.timestamp)) {
                console.log('Preferences expired, returning defaults');
                this.remove(); // Clean up expired data
                return this.getDefaultPreferences();
            }
            
            // Check version compatibility
            if (storedData.version !== this.version) {
                console.log('Preference version mismatch, migrating...');
                return this.migratePreferences(storedData);
            }
            
            // Merge with defaults to ensure all properties exist
            return {
                ...this.defaultPreferences,
                ...storedData,
                accessibility: {
                    ...this.defaultPreferences.accessibility,
                    ...(storedData.accessibility || {})
                }
            };
            
        } catch (error) {
            console.error('Failed to load preferences:', error);
            return this.getDefaultPreferences();
        }
    }
    
    /**
     * Get default preferences
     * @returns {Object}
     */
    getDefaultPreferences() {
        return {
            ...this.defaultPreferences,
            accessibility: {
                ...this.defaultPreferences.accessibility,
                reducedMotion: this.detectReducedMotion()
            }
        };
    }
    
    /**
     * Update specific preference section
     * @param {string} section - Section name (e.g., 'desktop', 'menuGroups')
     * @param {Object} data - Data to update
     * @returns {boolean} - Success status
     */
    updateSection(section, data) {
        const currentPrefs = this.load();
        
        if (!currentPrefs[section]) {
            currentPrefs[section] = {};
        }
        
        currentPrefs[section] = {
            ...currentPrefs[section],
            ...data
        };
        
        return this.save(currentPrefs);
    }
    
    /**
     * Update desktop collapse state
     * @param {boolean} collapsed - Collapse state
     * @returns {boolean} - Success status
     */
    updateDesktopCollapsed(collapsed) {
        return this.updateSection('desktop', { collapsed });
    }
    
    /**
     * Update menu group state
     * @param {string} groupId - Menu group identifier
     * @param {boolean} expanded - Expanded state
     * @returns {boolean} - Success status
     */
    updateMenuGroup(groupId, expanded) {
        const currentPrefs = this.load();
        
        if (!currentPrefs.menuGroups) {
            currentPrefs.menuGroups = {};
        }
        
        currentPrefs.menuGroups[groupId] = expanded;
        
        return this.save(currentPrefs);
    }
    
    /**
     * Get menu group state
     * @param {string} groupId - Menu group identifier
     * @returns {boolean} - Expanded state (defaults to false)
     */
    getMenuGroupState(groupId) {
        const prefs = this.load();
        return prefs.menuGroups && prefs.menuGroups[groupId] === true;
    }
    
    /**
     * Get desktop collapse state
     * @returns {boolean} - Collapse state
     */
    getDesktopCollapsed() {
        const prefs = this.load();
        return prefs.desktop && prefs.desktop.collapsed === true;
    }
    
    /**
     * Check if preferences are expired
     * @param {number} timestamp - Timestamp to check
     * @returns {boolean}
     */
    isExpired(timestamp) {
        if (!timestamp) return true;
        
        const now = Date.now();
        const expiryTime = this.expiryDays * 24 * 60 * 60 * 1000; // Convert days to milliseconds
        
        return (now - timestamp) > expiryTime;
    }
    
    /**
     * Migrate preferences from older versions
     * @param {Object} oldPrefs - Old preferences object
     * @returns {Object} - Migrated preferences
     */
    migratePreferences(oldPrefs) {
        console.log(`Migrating preferences from version ${oldPrefs.version || 'unknown'} to ${this.version}`);
        
        const migratedPrefs = {
            ...this.defaultPreferences,
            timestamp: Date.now()
        };
        
        // Migrate desktop settings
        if (oldPrefs.desktop) {
            migratedPrefs.desktop = {
                ...migratedPrefs.desktop,
                ...oldPrefs.desktop
            };
        }
        
        // Migrate menu groups
        if (oldPrefs.menuGroups) {
            migratedPrefs.menuGroups = { ...oldPrefs.menuGroups };
        }
        
        // Migrate accessibility settings
        if (oldPrefs.accessibility) {
            migratedPrefs.accessibility = {
                ...migratedPrefs.accessibility,
                ...oldPrefs.accessibility
            };
        }
        
        // Save migrated preferences
        this.save(migratedPrefs);
        
        return migratedPrefs;
    }
    
    /**
     * Remove preferences from storage
     * @returns {boolean} - Success status
     */
    remove() {
        try {
            if (this.isLocalStorageAvailable) {
                localStorage.removeItem(this.storageKey);
            }
            
            this.fallbackStorage.delete(this.storageKey);
            return true;
        } catch (error) {
            console.error('Failed to remove preferences:', error);
            return false;
        }
    }
    
    /**
     * Clean up expired preferences
     */
    cleanupExpiredPreferences() {
        const prefs = this.load();
        
        if (prefs && this.isExpired(prefs.timestamp)) {
            console.log('Cleaning up expired preferences');
            this.remove();
        }
    }
    
    /**
     * Reset preferences to defaults
     * @returns {boolean} - Success status
     */
    reset() {
        return this.save(this.getDefaultPreferences());
    }
    
    /**
     * Get storage information for debugging
     * @returns {Object}
     */
    getStorageInfo() {
        return {
            isLocalStorageAvailable: this.isLocalStorageAvailable,
            storageKey: this.storageKey,
            version: this.version,
            expiryDays: this.expiryDays,
            fallbackStorageSize: this.fallbackStorage.size
        };
    }
    
    /**
     * Export preferences for backup
     * @returns {Object|null}
     */
    export() {
        const prefs = this.load();
        
        if (prefs) {
            return {
                ...prefs,
                exportedAt: Date.now(),
                exportVersion: this.version
            };
        }
        
        return null;
    }
    
    /**
     * Import preferences from backup
     * @param {Object} importedPrefs - Preferences to import
     * @returns {boolean} - Success status
     */
    import(importedPrefs) {
        if (!importedPrefs || typeof importedPrefs !== 'object') {
            console.error('Invalid preferences data for import');
            return false;
        }
        
        try {
            // Remove export metadata
            const { exportedAt, exportVersion, ...prefsToImport } = importedPrefs;
            
            // Validate and migrate if necessary
            let validatedPrefs = prefsToImport;
            
            if (prefsToImport.version !== this.version) {
                validatedPrefs = this.migratePreferences(prefsToImport);
            }
            
            return this.save(validatedPrefs);
        } catch (error) {
            console.error('Failed to import preferences:', error);
            return false;
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PreferenceManager;
} else if (typeof window !== 'undefined') {
    window.PreferenceManager = PreferenceManager;
}