# AGENT.md - Itqan Development Guide

## Commands
**Build/Test:** `php artisan test` (PHPUnit), `php artisan test --filter=MethodName` (single test), `composer run psalm` (static analysis)
**Dev Tools:** `php artisan serve`, `npm run dev`, `npm run watch`, `php artisan queue:work`, `php artisan horizon`
**Database:** SQL files in `database/sql_files/` (NO Laravel migrations), `php artisan migrate:status`

## Architecture
**Laravel 10 + PHP 8.1+** modular application using `nwidart/laravel-modules`. Core features in `app/`, modules in `Modules/` (35 modules: JobSeeker, HumanResource, EducationalReports, etc.). Views in `resources/views/modules/{module-name}/`. Database uses Redis, queues, Horizon for jobs. Authentication via Sanctum + Spatie permissions.

## Code Standards  
**PSR-12 + strict typing** (`declare(strict_types=1)`). Controllers are final/readonly, models in `Entities/` as final classes. Bootstrap 3 + jQuery frontend. Form Requests for validation. Repository/Service patterns. Eloquent ORM preferred over raw SQL. Log critical actions with context (user IDs, model IDs).

## Module Structure
Controllers: `Modules/{Name}/Http/Controllers/`, Entities: `Modules/{Name}/Entities/`, Routes: `Modules/{Name}/Http/routes.php`, Views: `resources/views/modules/{name}/`, Console: `Modules/{Name}/Console/Commands/` (register in ServiceProvider)

## Critical Rules
- **NO Laravel migrations** - use SQL files in `database/sql_files/` with timestamp prefix
- Views stored in main `resources/` directory, NOT in module Resources
- Maintain exact AJAX payload structures and URL patterns for mobile/desktop compatibility
- Address ALL issues in multi-issue requests completely, not incrementally
