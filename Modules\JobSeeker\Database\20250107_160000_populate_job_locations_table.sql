--
-- Populate job_locations table with Afghanistan provinces
-- Then update provider_job_locations with proper canonical mappings
--

-- Insert canonical Afghanistan provinces
INSERT INTO job_locations (name, country, province) VALUES
('Kabul Province', 'Afghanistan', 'Kabul'),
('Herat Province', 'Afghanistan', 'Herat'),
('Kandahar Province', 'Afghanistan', 'Kandahar'),
('Balkh Province', 'Afghanistan', 'Balkh'),
('Nangarhar Province', 'Afghanistan', 'Nangarhar'),
('Kunduz Province', 'Afghanistan', 'Kunduz'),
('Takhar Province', 'Afghanistan', 'Takhar'),
('Baghlan Province', 'Afghanistan', 'Baghlan'),
('Ghazni Province', 'Afghanistan', 'Ghazni'),
('Faryab Province', 'Afghanistan', 'Faryab'),
('Helmand Province', 'Afghanistan', 'Helmand'),
('Badakhshan Province', 'Afghanistan', 'Badakhshan'),
('Sar-e Pol Province', 'Afghanistan', 'Sar-e Pol'),
('Parwan Province', 'Afghanistan', 'Parwan'),
('Wardak Province', 'Afghanistan', 'Wardak'),
('Logar Province', 'Afghanistan', 'Logar'),
('Bamyan Province', 'Afghanistan', 'Bamyan'),
('Badghis Province', 'Afghanistan', 'Badghis'),
('Farah Province', 'Afghanistan', 'Farah'),
('Ghor Province', 'Afghanistan', 'Ghor'),
('Jawzjan Province', 'Afghanistan', 'Jawzjan'),
('Kapisa Province', 'Afghanistan', 'Kapisa'),
('Khost Province', 'Afghanistan', 'Khost'),
('Kunar Province', 'Afghanistan', 'Kunar'),
('Laghman Province', 'Afghanistan', 'Laghman'),
('Nimroz Province', 'Afghanistan', 'Nimroz'),
('Paktia Province', 'Afghanistan', 'Paktia'),
('Paktika Province', 'Afghanistan', 'Paktika'),
('Panjshir Province', 'Afghanistan', 'Panjshir'),
('Samangan Province', 'Afghanistan', 'Samangan'),
('Uruzgan Province', 'Afghanistan', 'Uruzgan'),
('Zabul Province', 'Afghanistan', 'Zabul'),
('Daykundi Province', 'Afghanistan', 'Daykundi'),
('Nuristan Province', 'Afghanistan', 'Nuristan');

-- Clear existing provider_job_locations data to rebuild with canonical mappings
DELETE FROM provider_job_locations;

-- Insert Jobs.af location mappings (using location names as identifiers)
INSERT INTO provider_job_locations (provider_name, location_name, provider_identifier, canonical_location_id) VALUES
('jobs.af', 'Kabul Province', 'Kabul', (SELECT id FROM job_locations WHERE province = 'Kabul')),
('jobs.af', 'Herat Province', 'Herat', (SELECT id FROM job_locations WHERE province = 'Herat')),
('jobs.af', 'Kandahar Province', 'Kandahar', (SELECT id FROM job_locations WHERE province = 'Kandahar')),
('jobs.af', 'Balkh Province', 'Balkh', (SELECT id FROM job_locations WHERE province = 'Balkh')),
('jobs.af', 'Nangarhar Province', 'Nangarhar', (SELECT id FROM job_locations WHERE province = 'Nangarhar')),
('jobs.af', 'Kunduz Province', 'Kunduz', (SELECT id FROM job_locations WHERE province = 'Kunduz')),
('jobs.af', 'Takhar Province', 'Takhar', (SELECT id FROM job_locations WHERE province = 'Takhar')),
('jobs.af', 'Baghlan Province', 'Baghlan', (SELECT id FROM job_locations WHERE province = 'Baghlan')),
('jobs.af', 'Ghazni Province', 'Ghazni', (SELECT id FROM job_locations WHERE province = 'Ghazni')),
('jobs.af', 'Faryab Province', 'Faryab', (SELECT id FROM job_locations WHERE province = 'Faryab')),
('jobs.af', 'Helmand Province', 'Helmand', (SELECT id FROM job_locations WHERE province = 'Helmand')),
('jobs.af', 'Badakhshan Province', 'Badakhshan', (SELECT id FROM job_locations WHERE province = 'Badakhshan')),
('jobs.af', 'Parwan Province', 'Parwan', (SELECT id FROM job_locations WHERE province = 'Parwan')),
('jobs.af', 'Wardak Province', 'Wardak', (SELECT id FROM job_locations WHERE province = 'Wardak')),
('jobs.af', 'Logar Province', 'Logar', (SELECT id FROM job_locations WHERE province = 'Logar'));

-- Insert ACBAR location mappings (using numeric IDs as identifiers)
INSERT INTO provider_job_locations (provider_name, location_name, provider_identifier, canonical_location_id) VALUES
('acbar', 'Kabul Province', '14', (SELECT id FROM job_locations WHERE province = 'Kabul')),
('acbar', 'Herat Province', '15', (SELECT id FROM job_locations WHERE province = 'Herat')),
('acbar', 'Kandahar Province', '16', (SELECT id FROM job_locations WHERE province = 'Kandahar')),
('acbar', 'Balkh Province', '17', (SELECT id FROM job_locations WHERE province = 'Balkh')),
('acbar', 'Nangarhar Province', '18', (SELECT id FROM job_locations WHERE province = 'Nangarhar')),
('acbar', 'Kunduz Province', '19', (SELECT id FROM job_locations WHERE province = 'Kunduz')),
('acbar', 'Takhar Province', '20', (SELECT id FROM job_locations WHERE province = 'Takhar')),
('acbar', 'Baghlan Province', '21', (SELECT id FROM job_locations WHERE province = 'Baghlan')),
('acbar', 'Ghazni Province', '22', (SELECT id FROM job_locations WHERE province = 'Ghazni')),
('acbar', 'Faryab Province', '23', (SELECT id FROM job_locations WHERE province = 'Faryab')),
('acbar', 'Helmand Province', '24', (SELECT id FROM job_locations WHERE province = 'Helmand')),
('acbar', 'Badakhshan Province', '25', (SELECT id FROM job_locations WHERE province = 'Badakhshan')),
('acbar', 'Parwan Province', '26', (SELECT id FROM job_locations WHERE province = 'Parwan')),
('acbar', 'Wardak Province', '27', (SELECT id FROM job_locations WHERE province = 'Wardak')),
('acbar', 'Logar Province', '28', (SELECT id FROM job_locations WHERE province = 'Logar'));

-- Verify the data
SELECT 'job_locations count' as table_name, COUNT(*) as count FROM job_locations
UNION ALL
SELECT 'provider_job_locations count', COUNT(*) FROM provider_job_locations;

-- Show sample mappings
SELECT 
    jl.name as canonical_name,
    pjl.provider_name,
    pjl.provider_identifier,
    pjl.name as provider_name
FROM job_locations jl
JOIN provider_job_locations pjl ON jl.id = pjl.canonical_location_id
WHERE jl.province IN ('Kabul', 'Herat', 'Kandahar')
ORDER BY jl.province, pjl.provider_name; 