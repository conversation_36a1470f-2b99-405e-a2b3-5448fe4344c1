<?php

namespace App\Http\Controllers\Student;

use App\BaseSetup;
use App\Employee;
use App\Guardian;
use File;
use App\Role;
use App\User;
use DateTime;
use App\Book;
use App\Exam;
use Illuminate\Support\Facades\Session;
use Intervention\Image\Facades\Image;
use ZipArchive;
use App\Classes;
use App\Event;
use App\Route;
use App\Holiday;
use App\Section;
use App\Student;
use App\Subject;
use App\Vehicle;
use App\Weekend;
use App\YearCheck;
use App\ExamType;
use App\Homework;
use App\RoomList;
use App\RoomType;
use App\BookIssue;
use App\ClassTime;
use App\LeaveType;
use App\FeesAssign;
use App\MarksGrade;
use App\OnlineExam;
use App\ApiBaseMethod;
use App\LeaveDefine;
use App\NoticeBoard;
use App\AcademicYear;
use App\ExamSchedule;
use App\LeaveRequest;
use App\Notification;
use App\AssignSubject;
use App\AssignVehicle;
use App\DormitoryList;
use App\LibraryMember;
use App\GeneralSettings;
use App\Document;
use App\StudentTimeline;
use App\StudentAttendance;
use Illuminate\Http\Request;
use App\FeesAssignDiscount;
use App\ExamScheduleSubject;
use Illuminate\Support\Carbon;
use App\TeacherUploadContent;
use App\StudentTakeOnlineExam;
use App\UploadHomeworkContent;
use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;


class StudentPanelController extends Controller
{

    public function __construct()
    {


//        $this->middleware('PM');
//         User::checkAuth();
    }

    public function studentMyAttendanceSearchAPI(Request $request, $id = null)
    {

        $input = $request->all();

        $validator = Validator::make($input, [
            'month' => "required",
            'year' => "required",
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $student_detail = Student::where('user_id', $id)->first();

            $year = $request->year;
            $month = $request->month;
            if ($month < 10) {
                $month = '0' . $month;
            }
            $current_day = date('d');

            $days = cal_days_in_month(CAL_GREGORIAN, $month, $request->year);
            $days2 = '';
            if ($month != 1) {
                $days2 = cal_days_in_month(CAL_GREGORIAN, $month - 1, $request->year);
            } else {
                $days2 = cal_days_in_month(CAL_GREGORIAN, $month, $request->year);
            }
            // return  $days2;
            $previous_month = $month - 1;
            $previous_date = $year . '-' . $previous_month . '-' . $days2;
            $previousMonthDetails['date'] = $previous_date;
            $previousMonthDetails['day'] = $days2;
            $previousMonthDetails['week_name'] = date('D', strtotime($previous_date));
            $attendances = StudentAttendance::where('student_id', $student_detail->id)
                ->where('class_time', 'like', '%' . $request->year . '-' . $month . '%')
                ->select('attendance_type', 'class_time')
                ->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['attendances'] = $attendances;
                $data['previousMonthDetails'] = $previousMonthDetails;
                $data['days'] = $days;
                $data['year'] = $year;
                $data['month'] = $month;
                $data['current_day'] = $current_day;
                $data['status'] = 'Present: P, Late: L, Absent: A, Holiday: H, Half Day: F';
                return ApiBaseMethod::sendResponse($data, null);
            }
            //Test
            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_attendance', compact('attendances', 'days', 'year', 'month', 'current_day'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function studentMyAttendanceSearch(Request $request, $id = null)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'month' => "required",
            'year' => "required",
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $login_id = $id;
            } else {
                $login_id = Auth::guard('web')->user()->id;
            }
            $student_detail = Student::where('user_id', $login_id)->first();

            $year = $request->year;
            $month = $request->month;
            $current_day = date('d');

            $days = cal_days_in_month(CAL_GREGORIAN, $request->month, $request->year);

            $attendances = StudentAttendance::where('student_id', $student_detail->id)->where('class_time', 'like', $request->year . '-' . $request->month . '%')->get();
            $academic_years = AcademicYear::all();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['attendances'] = $attendances;
                $data['days'] = $days;
                $data['year'] = $year;
                $data['month'] = $month;
                $data['current_day'] = $current_day;
                return ApiBaseMethod::sendResponse($data, null);
            }

            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_attendance', compact('attendances', 'days', 'year', 'month', 'current_day', 'student_detail', 'academic_years'));
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentMyAttendancePrint($month, $year)
    {
        try {
            $login_id = Auth::guard('web')->user()->id;
            $student_detail = Student::where('user_id', $login_id)->first();
            $current_day = date('d');
            $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);
            $attendances = StudentAttendance::where('student_id', $student_detail->id)->where('class_time', 'like', $year . '-' . $month . '%')->get();
            $customPaper = array(0, 0, 700.00, 1000.80);
            $pdf = PDF::loadView(
                'backEnd.studentPanel.my_attendance_print',
                [
                    'attendances' => $attendances,
                    'days' => $days,
                    'year' => $year,
                    'month' => $month,
                    'current_day' => $current_day,
                    'student_detail' => $student_detail
                ]
            )->setPaper('A4', 'landscape');
            return $pdf->stream('my_attendance.pdf');
            //return view('modules.site.templates.wajeha.backEnd.studentPanel.student_attendance', compact('attendances', 'days', 'year', 'month', 'current_day', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentDashboard(Request $request, $id = null)
    {
        try {
            $user = Auth::guard('web')->user();
            $user_id = $user->id;
            $student_detail = Student::where('user_id', $user_id)->with('admissions.class')->first();



            if (is_null($student_detail->guardian_id))
            {
                $siblings = 0;


            }else{
                $siblings = Student::where('guardian_id','=', $student_detail->guardian_id)->with('admissions.class')->get();


            }
            $fees_assigneds = FeesAssign::where('student_id', $student_detail->id)->get();
            $fees_discounts = FeesAssignDiscount::where('student_id', $student_detail->id)->get();
            $documents = Document::where('documentable_id', $student_detail->id)->where('type', 'stu')->get();
            $timelines = StudentTimeline::where('staff_student_id', $student_detail->id)->where('type', 'stu')->where('visible_to_student', 1)->get();

            if ($student_detail != null) {
                $classIds = $student_detail->joint_classes()->get();
                // rest of your code here
            } else {
                // handle the error, for example by redirecting to an error page or logging the error

                $classIds = 1;
                // ...
            }
            $studentClasses = collect($classIds)->map(function ($item, $key) {
                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $exams = ExamSchedule::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            $grades = MarksGrade::get();
//            $exam_terms = ExamType::where('academic_id', YearCheck::getAcademicId())->get();
            $exam_terms = ExamType::get();
            // return $exam_terms;


            $result_views = StudentTakeOnlineExam::
            where('active_status', 1)->where('status', 2)
                ->where('student_id', @Auth::guard('web')->user()->student->id)
                ->get();

            $academic_year = AcademicYear::find($student_detail->session_id);


            return view('modules.site.templates.wajeha.backEnd.studentPanel.my_profile', compact('driver', 'academic_year', 'student_detail', 'fees_assigneds', 'fees_discounts', 'exams', 'documents', 'timelines', 'siblings', 'grades', 'exam_terms', 'result_views'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentProfile(Request $request, $id = null)
    {


        try {


            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $user_id = $id;
            } else {
                $user = Auth::guard('web')->user();


                if ($user) {
                    $user_id = $user->id;
                } else {
                    $user_id = $request->user_id;
                }
            }


            $student_detail = Student::where('user_id', $user_id)->first();

            $siblings = Student::where('guardian_id', $student_detail->guardian_id)->get();
            $fees_assigneds = FeesAssign::where('student_id', $student_detail->id)->get();
            $fees_discounts = FeesAssignDiscount::where('student_id', $student_detail->id)->get();
            $documents = Document::where('documentable_id', $student_detail->id)->where('type', 'stu')->get();
            $timelines = StudentTimeline::where('staff_student_id', $student_detail->id)->where('type', 'stu')->where('visible_to_student', 1)->get();
            if ($student_detail != null) {
                $classIds = $student_detail->joint_classes()->get();
                // rest of your code here
            } else {
                // handle the error, for example by redirecting to an error page or logging the error

                $classIds = 1;
                // ...
            }

//            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $exams = ExamSchedule::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            $grades = MarksGrade::get();


//            $classIds = $student_detail->joint_classes()->get();

            if ($student_detail != null) {
                $classIds = $student_detail->joint_classes()->get();
                // rest of your code here
            } else {
                // handle the error, for example by redirecting to an error page or logging the error

                $classIds = 1;
                // ...
            }


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });


            if ($student_detail != null) {
                $classIds = $student_detail->joint_classes()->get();
                // rest of your code here
            } else {
                // handle the error, for example by redirecting to an error page or logging the error

                $classIds = 1;
                // ...
            }

            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $totalSubjects = AssignSubject::whereIn('class_id', $studentClasses)->get();


//            $totalNotices = NoticeBoard::where('active_status', '=', 1)->where('is_published', '=', 1)->where('academic_id', YearCheck::getAcademicId())->get();
            $totalNotices = NoticeBoard::where('active_status', '=', 1)->where('is_published', '=', 1)->get();

            $time_zone_setup = GeneralSettings::join('time_zones', 'time_zones.id', '=', 'general_settings.time_zone_id')
                ->first();
            date_default_timezone_set($time_zone_setup->time_zone);


            $now = date('H:i:s');


            $online_exams = OnlineExam::where('status', 1
            )->whereIn('class_id', $studentClasses)
                // ->where('date', 'like', date('Y-m-d'))->where('start_time', '<', $now)->where('end_time', '>', $now)
                ->get();


//            $teachers = AssignSubject::select('employee_id')->whereIn('class_id', $studentClasses)
            $teachers = AssignSubject::select('teacher_id')->whereIn('class_id', $studentClasses)
//                ->distinct('employee_id')->get();
                ->distinct('teacher_id')->get();

//            $issueBooks = BookIssue::where('member_id', $student_detail->user_id)->where('issue_status', 'I')->get();
            $issueBooks = NULL;

            $homeworkLists = Homework::whereIn('class_id', $studentClasses)
//                ->where('section_id', $student_detail->section_id)
                ->where('evaluation_date', '=', null)
                ->where('submission_date', '>', $now)
//                ->where('academic_id', YearCheck::getAcademicId())
                ->get();

            $month = date('m');
            $year = date('Y');
            // return $year;

            $attendances = StudentAttendance::where('student_id', $student_detail->id)
//                ->where('class_time', 'like', $year . '-' . $month . '%')
                ->whereMonth('attendance_date', $month)
                ->whereYear('attendance_date', $year)
                ->where('attendance_type', '=', 'P')->get();
            // return $attendances;


            $holidays = Holiday::get();


            $events = Event::where('active_status', 1)
//                ->where('academic_id', YearCheck::getAcademicId())
//
                ->where(function ($q) {
                    $q->where('for_whom', 'All')->orWhere('for_whom', 'Student');
                })
                ->get();


//            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
//                $data = [];
//                $data['student_detail'] = $student_detail->toArray();
//                $data['fees_assigneds'] = $fees_assigneds->toArray();
//                $data['fees_discounts'] = $fees_discounts->toArray();
//                $data['exams'] = $exams->toArray();
//                $data['documents'] = $documents->toArray();
//                $data['timelines'] = $timelines->toArray();
//                $data['siblings'] = $siblings->toArray();
//                $data['grades'] = $grades->toArray();
//                return ApiBaseMethod::sendResponse($data, null);
//            }


            return view('modules.site.templates.wajeha.backEnd.studentPanel.studentProfile', compact('totalSubjects', 'totalNotices', 'online_exams', 'teachers', 'issueBooks', 'homeworkLists', 'attendances', 'driver', 'student_detail', 'fees_assigneds', 'fees_discounts', 'exams', 'documents', 'timelines', 'siblings', 'grades', 'events', 'holidays'));
        } catch (\Exception $e) {
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }

    public function studentsDocumentApi(Request $request, $id)
    {
        try {
            $student_detail = Student::where('user_id', $id)->first();
            $documents = Document::where('documentable_id', $student_detail->id)->where('type', 'stu')
                ->select('title', 'file')
                ->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['student_detail'] = $student_detail->toArray();
                $data['documents'] = $documents->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function classRoutine(Request $request, $id = null)
    {
        try {


            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $user_id = $id;
            } else {
                $user = Auth::guard('web')->user();

                if ($user) {
                    $user_id = $user->id;
                } else {
                    $user_id = $request->user_id;
                }
            }

            $student_detail = Student::where('user_id', $user_id)->first();
            //return $student_detail;
            $class_id = $student_detail->admissions->first()->class_id;


//            $section_id = $student_detail->section_id;
            $section_id = '222';

            $weekends = Weekend::orderBy('order', 'ASC')->all();

            $class_times = ClassTime::where('type', 'class')->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['student_detail'] = $student_detail->toArray();
                // $data['class_id'] = $class_id;
                // $data['section_id'] = $section_id;
                // $data['weekends'] = $weekends->toArray();
                // $data['class_times'] = $class_times->toArray();

                $weekenD = Weekend::get();
                foreach ($weekenD as $row) {
                    $data[$row->name] = DB::table('class_routine_updates')
                        ->select('class_times.period', 'class_times.start_time', 'class_times.end_time', 'subjects.subject_name', 'class_rooms.room_no')
                        ->join('classes', 'classes.id', '=', 'class_routine_updates.class_id')
                        ->join('sections', 'sections.id', '=', 'class_routine_updates.section_id')
                        ->join('class_times', 'class_times.id', '=', 'class_routine_updates.class_period_id')
                        ->join('subjects', 'subjects.id', '=', 'class_routine_updates.subject_id')
                        ->join('class_rooms', 'class_rooms.id', '=', 'class_routine_updates.room_id')
                        ->where([
                            ['class_routine_updates.class_id', $class_id], ['class_routine_updates.section_id', $section_id], ['class_routine_updates.day', $row->id],
                        ])->where('class_routine_updates.academic_id', YearCheck::getAcademicId())->where('classesorganization_id', Auth::guard('web')->user()->organization_id)->get();
                }

                return ApiBaseMethod::sendResponse($data, null);
            }

            return view('modules.site.templates.wajeha.backEnd.studentPanel.class_routine', compact('class_times', 'class_id', 'section_id', 'weekends'));
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentResult()
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $exams = ExamSchedule::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            $grades = MarksGrade::get();
            //dd($exams);
            $exam_terms = ExamType::get();

            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_result', compact('student_detail', 'exams', 'grades', 'exam_terms'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentExamSchedule()
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();
            $exam_types = ExamType::where('organization_id', Auth::guard('web')->user()->organization_id)->all();
            return view('modules.site.templates.wajeha.backEnd.studentPanel.exam_schedule', compact('exam_types'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentExamScheduleSearch(Request $request)
    {

        $this->validate($request, [
            'exam' => 'required',
        ]);

        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $assign_subjects = AssignSubject::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();

            if ($assign_subjects->count() == 0) {
                Toastr::error('No Subject Assigned.', 'Failed');
                return redirect('student-exam-schedule');
            }

            $exams = Exam::get();
            $class_id = $student_detail->class_id;
            $section_id = $student_detail->section_id;
            $exam_id = $request->exam;

            $exam_types = ExamType::all();
            $exam_periods = ClassTime::where('type', 'exam')->get();
            $exam_schedule_subjects = "";
            $assign_subject_check = "";

            return view('modules.site.templates.wajeha.backEnd.studentPanel.exam_schedule', compact('exams', 'assign_subjects', 'class_id', 'section_id', 'exam_id', 'exam_schedule_subjects', 'assign_subject_check', 'exam_types', 'exam_periods'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentExamScheduleApi(Request $request, $id)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $student_detail = Student::where('user_id', $id)->first();
                // $assign_subjects = AssignSubject::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->where('academic_id', YearCheck::getAcademicId())->get();
                $exam_schedule = DB::table('exam_schedules')
                    ->join('students', 'students.class_id', '=', 'exam_schedules.class_id')
                    ->join('exam_types', 'exam_types.id', '=', 'exam_schedules.exam_term_id')
                    ->join('exam_schedule_subjects', 'exam_schedule_subjects.exam_schedule_id', '=', 'exam_schedules.id')
                    ->join('subjects', 'subjects.id', '=', 'exam_schedules.subject_id')
                    ->select('subjects.subject_name', 'exam_schedule_subjects.start_time', 'exam_schedule_subjects.end_time', 'exam_schedule_subjects.date', 'exam_schedule_subjects.room', 'exam_schedules.class_id', 'exam_schedules.section_id')
                    //->where('students.class_id', '=', 'exam_schedules.class_id')

                    ->where('exam_schedules.section_id', '=', $student_detail->section_id)
                    ->where('exam_schedulesacademic_id', YearCheck::getAcademicId())->where('exam_schedules.organization_id', Auth::guard('web')->user()->organization_id)->get();
                return ApiBaseMethod::sendResponse($exam_schedule, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentViewExamSchedule($id)
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();
            $class = Classes::find($student_detail->class_id);
            $section = Section::find($student_detail->section_id);
            $assign_subjects = ExamScheduleSubject::where('exam_schedule_id', $id)->get();

            return view('modules.site.templates.wajeha.backEnd.examination.view_exam_schedule_modal', compact('class', 'section', 'assign_subjects'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentMyAttendance()
    {
        try {
            $academic_years = AcademicYear::all();
            // return $academic_years;
            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_attendance', compact('academic_years'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentHomework(Request $request, $id = null)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $student_detail = Student::where('user_id', $id)->first();
                $classIds = $student_detail->joint_classes()->get();


                $studentClasses = collect($classIds)->map(function ($item, $key) {

                    return $item->id;
                });
                $studentClasses = $studentClasses->toArray();

                $class_id = $student_detail->class->id;
                $subject_list = AssignSubject::where([['class_id', $class_id], ['section_id', $student_detail->section_id]])->get();

                $i = 0;
                foreach ($subject_list as $subject) {
                    $homework_subject_list[$subject->subject->subject_name] = $subject->subject->subject_name;
                    $allList[$subject->subject->subject_name] = DB::table('homeworks')
                        ->leftjoin('subjects', 'subjects.id', '=', 'homeworks.subject_id')
                        ->whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)
                        ->where('subject_id', $subject->subject_id)->where('homeworks.organization_id', Auth::guard('web')->user()->organization_id)->get()->toArray();;
                }

                foreach ($allList as $single) {
                    foreach ($single as $singleHw) {
                        $std_homework = DB::table('homework_students')
                            ->select('homework_id', 'complete_status')
                            ->where('homework_id', '=', $singleHw->id)
                            ->where('student_id', '=', $student_detail->id)
                            ->where('complete_status', 'C')
                            ->where('homework_students.organization_id', Auth::guard('web')->user()->organization_id)
                            ->first();

                        $d['description'] = $singleHw->description;
                        $d['subject_name'] = $singleHw->subject_name;
                        $d['homework_date'] = $singleHw->homework_date;
                        $d['submission_date'] = $singleHw->submission_date;
                        $d['evaluation_date'] = $singleHw->evaluation_date;
                        $d['file'] = $singleHw->file;
                        $d['marks'] = $singleHw->marks;

                        if (!empty($std_homework)) {
                            $d['status'] = 'C';
                        } else {
                            $d['status'] = 'I';
                        }
                        $kijanidibo[] = $d;
                    }
                }
                // return $kijanidibo;
                $classIds = $student_detail->joint_classes()->get();


                $studentClasses = collect($classIds)->map(function ($item, $key) {

                    return $item->id;
                });
                $studentClasses = $studentClasses->toArray();

                $homeworkLists = Homework::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)
                    ->get();
            } else {
                $user = Auth::guard('web')->user();
                $student_detail = Student::where('user_id', $user->id)->first();
                $classIds = $student_detail->joint_classes()->get();


                $studentClasses = collect($classIds)->map(function ($item, $key) {

                    return $item->id;
                });
                $studentClasses = $studentClasses->toArray();

                $homeworkLists = Homework::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)
                    ->where('homeworks.academic_id', YearCheck::getAcademicId())->get();
            }
            $data = [];

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $data = $kijanidibo;
                return ApiBaseMethod::sendResponse($data, null);
            }
            // return YearCheck::getAcademicId();
            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_homework', compact('homeworkLists', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentHomeworkView($class_id, $section_id, $homework_id)
    {
        try {
            $homeworkDetails = Homework::where('class_id', '=', $class_id)->where('section_id', '=', $section_id)->where('id', '=', $homework_id)->first();
            return view('modules.site.templates.wajeha.backEnd.studentPanel.studentHomeworkView', compact('homeworkDetails', 'homework_id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function addHomeworkContent($homework_id)
    {
        try {

            return view('modules.site.templates.wajeha.backEnd.studentPanel.addHomeworkContent', compact('homework_id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function deleteViewHomeworkContent($homework_id)
    {
        try {

            return view('modules.site.templates.wajeha.backEnd.studentPanel.deleteHomeworkContent', compact('homework_id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function deleteHomeworkContent($homework_id)
    {
        try {

            $user = Auth::guard('web')->user();

            $student_detail = Student::where('user_id', $user->id)->first();


            $content = UploadHomeworkContent::where('student_id', $student_detail->id)->where('homework_id', $homework_id)->first();
            if ($content->file != "") {
                if (file_exists($content->file)) {
                    unlink($content->file);
                }
            }
            $content->delete();

            Toastr::success('Operation successful', 'Success');
            return redirect()->back();


        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function uploadHomeworkContent(Request $request)
    {

        if ($request->file('files') == "") {
            Toastr::error('No file uploaded', 'Failed');
            return redirect()->back();
        }


        try {

            $user = Auth::guard('web')->user();

            $student_detail = Student::where('user_id', $user->id)->first();
            $data = [];

            foreach ($request->file('files') as $key => $file) {
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/homeworkcontent/', $fileName);
                $fileName = 'public/uploads/homeworkcontent/' . $fileName;
                $data[$key] = $fileName;
            }
            $all_filename = json_encode($data);
            $content = new UploadHomeworkContent();
            $content->file = $all_filename;
            $content->student_id = $student_detail->id;
            $content->homework_id = $request->id;
            $content->organization_id = Auth::guard('web')->user()->organization_id;
            $content->academic_id = YearCheck::getAcademicId();
            $content->save();

            Toastr::success('Operation successful', 'Success');
            return redirect()->back();


        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }

    }


    public function studentAssignment()
    {
        try {
            $user = Auth::guard('web')->user();

            $student_detail = Student::where('user_id', $user->id)->first();

            $uploadContents = TeacherUploadContent::where('content_type', 'as')
                ->where(function ($query) use ($student_detail) {
                    $query->where('available_for_all_classes', 1)
                        ->orWhere([['class', $student_detail->class_id], ['section', $student_detail->section_id]]);
                })->get();
            if (Auth()->user()->role_id != 1) {
                if ($user->hasRole("student")) {
                    Notification::where('user_id', $user->student->id)->where('role_id', 2)->update(['is_read' => 1]);
                }
            }
            return view('modules.site.templates.wajeha.backEnd.studentPanel.assignmentList', compact('uploadContents'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentAssignmentApi(Request $request, $id)
    {
        try {
            $student_detail = Student::where('user_id', $id)->first();
            $uploadContents = TeacherUploadContent::where('content_type', 'as')
                ->select('content_title', 'upload_date', 'description', 'upload_file')
                ->where(function ($query) use ($student_detail) {
                    $query->where('available_for_all_classes', 1)
                        ->orWhere([['class', $student_detail->class_id], ['section', $student_detail->section_id]]);
                })->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['student_detail'] = $student_detail->toArray();
                $data['uploadContents'] = $uploadContents->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentStudyMaterial()
    {

        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();

            $uploadContents = TeacherUploadContent::where('content_type', 'st')
                ->where(function ($query) use ($student_detail) {
                    $query->where('available_for_all_classes', 1)
                        ->orWhere([['class', $student_detail->class_id], ['section', $student_detail->section_id]]);
                })->get();

            return view('modules.site.templates.wajeha.backEnd.studentPanel.studyMetarialList', compact('uploadContents'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentSyllabus()
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();

            $uploadContents = TeacherUploadContent::where('content_type', 'sy')
                ->where(function ($query) use ($student_detail) {
                    $query->where('available_for_all_classes', 1)
                        ->orWhere([['class', $student_detail->class_id], ['section', $student_detail->section_id]]);
                })->get();

            return view('modules.site.templates.wajeha.backEnd.studentPanel.studentSyllabus', compact('uploadContents'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function othersDownload()
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();

            $uploadContents = TeacherUploadContent::where('content_type', 'ot')
                ->where(function ($query) use ($student_detail) {
                    $query->where('available_for_all_classes', 1)
                        ->orWhere([['class', $student_detail->class_id], ['section', $student_detail->section_id]]);
                })->get();

            return view('modules.site.templates.wajeha.backEnd.studentPanel.othersDownload', compact('uploadContents'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentSubject()
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $assignSubjects = AssignSubject::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_subject', compact('assignSubjects'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    //Student Subject API
    public function studentSubjectApi(Request $request, $id)
    {
        try {
            $student = Student::where('user_id', $id)->first();
            $assignSubjects = DB::table('assign_subjects')
                ->leftjoin('subjects', 'subjects.id', '=', 'assign_subjects.subject_id')
                ->leftjoin('employees', 'employees.id', '=', 'assign_subjects.employee_id')
                ->select('subjects.subject_name', 'subjects.code', 'subjects.subject_type', 'employees.full_name as teacher_name')
                ->where('assign_subjects.class_id', '=', $student->class_id)
                ->where('assign_subjects.section_id', '=', $student->section_id)
                ->where('assign_subjects.academic_id', YearCheck::getAcademicId())->where('assign_subjects.organization_id', Auth::guard('web')->user()->organization_id)->get();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['student_subjects'] = $assignSubjects->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    //student panel Transport
    public function studentTransport()
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();

            // $routes = AssignVehicle::get();
            $routes = AssignVehicle::join('vehicles', 'assign_vehicles.vehicle_id', 'vehicles.id')
                ->join('students', 'vehicles.id', 'students.vechile_id')
                ->where('assign_vehicles.active_status', 1)
                ->where('students.user_id', Auth::guard('web')->user()->id)
                ->where('assign_vehicles.organization_id', Auth::guard('web')->user()->organization_id)
                ->get();


            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_transport', compact('routes', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentTransportViewModal($r_id, $v_id)
    {
        try {
            $vehicle = Vehicle::find($v_id);
            $route = Route::find($r_id);
            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_transport_view_modal', compact('route', 'vehicle'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentDormitory()
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();
            // $room_lists = RoomList::where('organization_id',Auth::guard('web')->user()->organization_id)->get();
            $room_lists = RoomList::join('students', 'students.room_id', 'room_lists.id')
                ->where('room_lists.active_status', 1)->where('students.user_id', Auth::guard('web')->user()->id)->where('room_lists.organization_id', Auth::guard('web')->user()->organization_id)->get();

            $room_lists = $room_lists->groupBy('dormitory_id');
            $room_types = RoomType::get();
            $dormitory_lists = DormitoryList::get();
            return view('modules.site.templates.wajeha.backEnd.studentPanel.student_dormitory', compact('room_lists', 'room_types', 'dormitory_lists', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentBookList()
    {
        try {
            $books = Book::where('active_status', 1)
                ->orderBy('id', 'DESC')
                ->get();
            return view('modules.site.templates.wajeha.backEnd.studentPanel.studentBookList', compact('books'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentBookIssue()
    {
        try {
            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();
            $books = Book::select('id', 'book_title')->all();
            $subjects = Subject::select('id', 'subject_name')->all();
            $library_member = LibraryMember::where('member_type', 2)->where('documentable_id', $student_detail->user_id)->first();
            if (empty($library_member)) {
                Toastr::error('You are not library member ! Please contact with librarian', 'Failed');
                return redirect()->back();
                // return redirect()->back()->with('message-danger', 'You are not library member ! Please contact with librarian');
            }
            $issueBooks = BookIssue::where('member_id', $library_member->documentable_id)->where('issue_status', 'I')->get();

            return view('modules.site.templates.wajeha.backEnd.studentPanel.studentBookIssue', compact('books', 'subjects', 'issueBooks'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentNoticeboard(Request $request)
    {
        try {
            $data = [];
            $allNotices = NoticeBoard::where('inform_to', 'LIKE', '%2%')
                ->orderBy('id', 'DESC')
                ->get();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['allNotices'] = $allNotices->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.studentPanel.studentNoticeboard', compact('allNotices'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentTeacher()
    {
        try {

            $user = Auth::guard('web')->user();
            $student_detail = Student::where('user_id', $user->id)->first();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $teachers = AssignSubject::select('employee_id')->whereIn('class_id', $studentClasses)
                ->where('section_id', $student_detail->section_id)->distinct('employee_id')->get();
            return view('modules.site.templates.wajeha.backEnd.studentPanel.studentTeacher', compact('teachers'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentTeacherApi(Request $request, $id)
    {
        try {
            $student = Student::where('user_id', $id)->first();

            $assignTeacher = DB::table('assign_subjects')
                ->leftjoin('subjects', 'subjects.id', '=', 'assign_subjects.subject_id')
                ->leftjoin('employees', 'employees.id', '=', 'assign_subjects.employee_id')
                //->select('subjects.subject_name', 'subjects.code', 'subjects.subject_type', 'employees.full_name')
                ->select('employees.full_name', 'employees.email', 'employees.mobile')
                ->where('assign_subjects.class_id', '=', $student->class_id)
                ->where('assign_subjects.section_id', '=', $student->section_id)
                ->where('assign_subjects.organization_id', Auth::guard('web')->user()->organization_id)->get();

            $class_teacher = DB::table('class_teachers')
                ->join('assign_class_teachers', 'assign_class_teachers.id', '=', 'class_teachers.assign_class_employee_id')
                ->join('employees', 'class_teachers.employee_id', '=', 'employees.id')
                ->where('assign_class_teachers.class_id', '=', $student->class_id)
                ->where('assign_class_teachers.section_id', '=', $student->section_id)
                ->where('assign_class_teachers.active_status', '=', 1)
                ->select('full_name')
                ->first();
            $settings = GeneralSettings::find(1);
            if (@$settings->phone_number_privacy == 1) {
                $permission = 1;
            } else {
                $permission = 0;
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['teacher_list'] = $assignTeacher->toArray();
                $data['class_teacher'] = $class_teacher;
                $data['permission'] = $permission;
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function studentLibrary(Request $request, $id)
    {
        try {
            $student = Student::where('user_id', $id)->first();
            $issueBooks = DB::table('book_issues')
                ->leftjoin('books', 'books.id', '=', 'book_issues.book_id')
                ->where('book_issues.member_id', '=', $student->user_id)
                ->where('book_issues.organization_id', Auth::guard('web')->user()->organization_id)->get();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['issueBooks'] = $issueBooks->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentDormitoryApi(Request $request)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $studentDormitory = DB::table('room_lists')
                    ->join('dormitory_lists', 'room_lists.dormitory_id', '=', 'dormitory_lists.id')
                    ->join('room_types', 'room_lists.room_type_id', '=', 'room_types.id')
                    ->select('dormitory_lists.dormitory_name', 'room_lists.name as room_number', 'room_lists.number_of_bed', 'room_lists.cost_per_bed', 'room_lists.active_status')->where('room_lists.organization_id', Auth::guard('web')->user()->organization_id)->get();

                return ApiBaseMethod::sendResponse($studentDormitory, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentTimelineApi(Request $request, $id)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                //$timelines = StudentTimeline::where('staff_student_id', $id)->first();
                $timelines = DB::table('student_timelines')
                    ->leftjoin('students', 'students.id', '=', 'student_timelines.staff_student_id')
                    ->where('student_timelines.type', '=', 'stu')
                    ->where('student_timelines.active_status', '=', 1)
                    ->where('students.user_id', '=', $id)
                    ->select('title', 'date', 'description', 'file', 'student_timelines.active_status')
                    ->where('student_timelines.academic_id', YearCheck::getAcademicId())->where('students.organization_id', Auth::guard('web')->user()->organization_id)->get();
                return ApiBaseMethod::sendResponse($timelines, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function examListApi(Request $request, $id)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $student = Student::where('user_id', $id)->first();
                // return  $student;
                $exam_List = DB::table('exam_types')
                    ->join('exams', 'exams.exam_type_id', '=', 'exam_types.id')
                    ->where('exams.class_id', '=', $student->class_id)
                    ->where('exams.section_id', '=', $student->section_id)
                    ->distinct()
                    ->select('exam_types.id as exam_id', 'exam_types.title as exam_name')
                    ->where('exam_types.organization_id', Auth::guard('web')->user()->organization_id)->get();
                return ApiBaseMethod::sendResponse($exam_List, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function examScheduleApi(Request $request, $id, $exam_id)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $student = Student::where('user_id', $id)->first();
                $exam_schedule = DB::table('exam_schedules')
                    ->join('exam_types', 'exam_types.id', '=', 'exam_schedules.exam_term_id')
                    // ->join('exam_types','exam_types.id','=','exam_schedules.exam_term_id' )
                    ->join('subjects', 'subjects.id', '=', 'exam_schedules.subject_id')
                    ->join('class_rooms', 'class_rooms.id', '=', 'exam_schedules.room_id')
                    ->join('class_times', 'class_times.id', '=', 'exam_schedules.exam_period_id')
                    ->where('exam_schedules.exam_term_id', '=', $exam_id)
                    ->where('exam_schedules.organization_id', '=', $student->organization_id)
                    ->where('exam_schedules.class_id', '=', $student->class_id)
                    ->where('exam_schedules.section_id', '=', $student->section_id)
                    ->where('exam_schedules.active_status', '=', 1)
                    ->select('exam_types.id', 'exam_types.title as exam_name', 'subjects.subject_name', 'date', 'class_rooms.room_no', 'class_times.start_time', 'class_times.end_time')
                    ->where('exam_schedules.organization_id', Auth::guard('web')->user()->organization_id)->get();
                return ApiBaseMethod::sendResponse($exam_schedule, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function examResultApi(Request $request, $id, $exam_id)
    {
        try {
            $data = [];

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $student = Student::where('user_id', $id)->first();
                $exam_result = DB::table('result_stores')
                    ->join('exam_types', 'exam_types.id', '=', 'result_stores.exam_type_id')
                    ->join('exams', 'exams.id', '=', 'exam_types.id')
                    ->join('subjects', 'subjects.id', '=', 'result_stores.subject_id')
                    ->where('exams.id', '=', $exam_id)
                    ->where('result_stores.organization_id', '=', $student->organization_id)
                    ->where('result_stores.class_id', '=', $student->class_id)
                    ->where('result_stores.section_id', '=', $student->section_id)
                    ->where('result_stores.student_id', '=', $student->id)
                    ->select('exams.id', 'exam_types.title as exam_name', 'subjects.subject_name', 'result_stores.total_marks as obtained_marks', 'exams.exam_mark as total_marks', 'result_stores.total_gpa_grade as grade')
                    ->where('exams.organization_id', Auth::guard('web')->user()->organization_id)->get();

                $data['exam_result'] = $exam_result->toArray();
                $data['pass_marks'] = 0;

                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function updatePassowrdStoreApi(Request $request)
    {
        try {
            $user = User::find($request->id);

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {


                if (Hash::check($request->current_password, $user->password)) {

                    $user->password = Hash::make($request->new_password);
                    $result = $user->save();
                    $msg = "Password Changed Successfully ";
                    return ApiBaseMethod::sendResponse(null, $msg);
                } else {
                    $msg = "You Entered Wrong Current Password";
                    return ApiBaseMethod::sendError(null, $msg);
                }
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function leaveApply(Request $request)
    {
        try {
            $user = Auth::guard('web')->user();

            if ($user) {
                $my_leaves = LeaveDefine::where('role_id', $user->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $user->role_id)->all();
                // return $apply_leaves;
                $leave_types = LeaveDefine::where('role_id', $user->role_id)->all();
            } else {
                $my_leaves = LeaveDefine::where('role_id', $request->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $request->role_id)->all();
                $leave_types = LeaveDefine::where('role_id', $request->role_id)->all();
            }

            return view('modules.site.templates.wajeha.backEnd.student_leave.apply_leave', compact('apply_leaves', 'leave_types', 'my_leaves'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function leaveStore(Request $request)
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        $this->validate($request, [
            'apply_date' => "required",
            'leave_type' => "required",
            'leave_from' => 'required|before_or_equal:leave_to',
            'leave_to' => "required",
            'attach_file' => "sometimes|nullable|mimes:pdf,doc,docx,jpg,jpeg,png|max:10000",
        ]);
        try {
            $input = $request->all();
            $fileName = "";
            if ($request->file('attach_file') != "") {
                $file = $request->file('attach_file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }

            $user = Auth()->user();

            if ($user) {
                $login_id = $user->id;
                $role_id = $user->role_id;
            } else {
                $login_id = $request->login_id;
                $role_id = $request->role_id;
            }
            $apply_leave = new LeaveRequest();
            $apply_leave->employee_id = $login_id;
            $apply_leave->role_id = $role_id;
            $apply_leave->apply_date = date('Y-m-d', strtotime($request->apply_date));
            $apply_leave->leave_define_id = $request->leave_type;
            $apply_leave->type_id = $request->leave_type;
            $apply_leave->leave_from = date('Y-m-d', strtotime($request->leave_from));
            $apply_leave->leave_to = date('Y-m-d', strtotime($request->leave_to));
            $apply_leave->approve_status = 'P';
            $apply_leave->reason = $request->reason;
            $apply_leave->file = $fileName;
            $apply_leave->academic_id = YearCheck::getAcademicId();
            $apply_leave->organization_id = Auth::guard('web')->user()->organization_id;
            $result = $apply_leave->save();

            if ($result) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function leaveEdit($id)
    {
    }

    public function pendingLeave(Request $request)
    {
        try {
            $apply_leaves = LeaveRequest::where([['active_status', 1], ['approve_status', 'P']])->get();
            $leave_types = LeaveType::get();
            $roles = Role::where('id', 2)->where(function ($q) {
                $q->orWhere('type', 'System');
            })->get();
            $pendingRequest = LeaveRequest::where('leave_requests.active_status', 1)
                ->select('leave_requests.id', 'full_name', 'apply_date', 'leave_from', 'leave_to', 'reason', 'file', 'leave_types.type', 'approve_status')
                ->join('leave_defines', 'leave_requests.leave_define_id', '=', 'leave_defines.id')
                ->join('employees', 'leave_requests.employee_id', '=', 'employees.id')
                ->leftjoin('leave_types', 'leave_requests.type_id', '=', 'leave_types.id')
                ->where('leave_requests.approve_status', '=', 'P')
                ->where('leave_requests.academic_id', YearCheck::getAcademicId())->where('leave_requests.organization_id', Auth::guard('web')->user()->organization_id)->get();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['pending_request'] = $pendingRequest->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.student_leave.pending_leave', compact('apply_leaves', 'leave_types', 'roles'));
        } catch (\Exception $e) {
            // dd($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentLeaveEdit(request $request, $id)
    {
        try {
            $user = Auth::guard('web')->user();
            if ($user) {
                $my_leaves = LeaveDefine::where('role_id', $user->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $user->role_id)->all();
                $leave_types = LeaveDefine::where('role_id', $user->role_id)->all();
            } else {
                $my_leaves = LeaveDefine::where('role_id', $request->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $request->role_id)->all();
                $leave_types = LeaveDefine::where('role_id', $request->role_id)->all();
            }
            $apply_leave = LeaveRequest::find($id);
            return view('modules.site.templates.wajeha.backEnd.student_leave.apply_leave', compact('apply_leave', 'apply_leaves', 'leave_types', 'my_leaves'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentUpdate(Request $request)
    {

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $student_detail = Student::find($request->id);










        //always happen start
         $student_photo = "";
         if ($request->file('photo') != "") {
             if ($student_detail->student_photo != "") {
                 if (file_exists($student_detail->student_photo)) {
                     unlink($student_detail->student_photo);
                 }
             }
             $file = $request->file('photo');
             $student_photo = 'stu-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
             $file->move('public/uploads/student/', $student_photo);
             $student_photo =  'public/uploads/student/' . $student_photo;
         }


        // always happen start

        $document_file_1 = "";
        if ($request->file('document_file_1') != "") {
            if ($student_detail->document_file_1 != "") {
                if (file_exists($student_detail->document_file_1)) {
                    unlink($student_detail->document_file_1);
                }
            }
            $file = $request->file('document_file_1');
            $document_file_1 = 'doc1-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_1);
            $document_file_1 =  'public/uploads/student/document/' . $document_file_1;
        }

        $document_file_2 = "";
        if ($request->file('document_file_2') != "") {
            if ($student_detail->document_file_2 != "") {
                if (file_exists($student_detail->document_file_2)) {
                    unlink($student_detail->document_file_2);
                }
            }
            $file = $request->file('document_file_2');
            $document_file_2 = 'doc2-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_2);
            $document_file_2 =  'public/uploads/student/document/' . $document_file_2;
        }

        $document_file_3 = "";
        if ($request->file('document_file_3') != "") {
            if ($student_detail->document_file_3 != "") {
                if (file_exists($student_detail->document_file_3)) {
                    unlink($student_detail->document_file_3);
                }
            }
            $file = $request->file('document_file_3');
            $document_file_3 = 'doc3-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_3);
            $document_file_3 =  'public/uploads/student/document/' . $document_file_3;
        }

        $document_file_4 = "";
        if ($request->file('document_file_4') != "") {
            if ($student_detail->document_file_4 != "") {
                if (file_exists($student_detail->document_file_4)) {
                    unlink($student_detail->document_file_4);
                }
            }
            $file = $request->file('document_file_4');
            $document_file_4 = 'doc4-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_4);
            $document_file_4 =  'public/uploads/student/document/' . $document_file_4;
        }


//        if ($request->relation == 'Father') {
//            $guardians_photo = "";
//
//            if ($request->file('fathers_photo') != "") {
//                $student = Student::find($request->id);
//
//                if (@$student->parents->guardians_photo != "") {
//                    if (file_exists(@$student->parents->guardians_photo)) {
//                        unlink(@$student->parents->guardians_photo);
//                    }
//                }
//
//
//                $guardians_photo =  Session::get('fathers_photo');
//            }
//        }
//        elseif ($request->relation == 'Mother') {
//            $guardians_photo = "";
//            if ($request->file('mothers_photo') != "") {
//                $student = Student::find($request->id);
//
//                if (@$student->parents->guardians_photo != "") {
//                    if (file_exists(@$student->parents->guardians_photo)) {
//                        unlink(@$student->parents->guardians_photo);
//                    }
//                }
//
//                $guardians_photo =  Session::get('mothers_photo');
//            }
//        }
//        elseif ($request->relation == 'Other') {
//            $guardians_photo = "";
//            if ($request->file('guardians_photo') != "") {
//                $student = Student::find($request->id);
//
//                if (@$student->parents->guardians_photo != "") {
//                    if (file_exists(@$student->parents->guardians_photo)) {
//                        unlink(@$student->parents->guardians_photo);
//                    }
//                }
//
//                $guardians_photo =  Session::get('guardians_photo');
//            }
//        }






        DB::beginTransaction();

        try {



            $user_stu = User::find($student_detail->user_id);




            $user_stu->full_name = $request->full_name;


            $user_stu->email = $request->email_address;
//            $user_stu->password = Hash::make(123456);

            $user_stu->save();
            $user_stu->toArray();







                    try {
                        $student = Student::find($request->id);



                        $student->full_name = $request->full_name;
                        $student->gender = BaseSetup::find($request->gender)->base_setup_name;
//                        $student->date_of_birth = date('Y-m-d', strtotime($request->date_of_birth));
//                        if (@$request->category != "") {
//                            $student->student_category_id = $request->category;
//                        }

//                        $student->age = $request->age;


                        $student->email = $request->email_address;
                        $student->mobile = $request->phone_number;

                        if (Session::get('student_photo') != "") {
                            $student->student_photo = Session::get('student_photo');
                        }

                        /* if ($student_photo != "") {
                        } */
                        if (@$request->blood_group != "") {
                            $student->bloodgroup_id = $request->blood_group;
                        }
                        if (@$request->religion != "") {
                            $student->religion_id = $request->religion;
                        }

                        $student->height = $request->height;
                        $student->weight = $request->weight;
//                        $student->current_address = $request->current_address;
//                        $student->permanent_address = $request->permanent_address;



//                        $student->national_id_no = $request->national_id_number;
                        $student->bank_account_no = $request->bank_account_number;
                        $student->bank_name = $request->bank_name;
                        $student->aditional_notes = $request->additional_notes;
                        $student->document_title_1 = $request->document_title_1;
                        if ($document_file_1 != "") {
                            $student->document_file_1 =  $document_file_1;
                        }

                        $student->document_title_2 = $request->document_title_2;
                        if ($document_file_2 != "") {
                            $student->document_file_2 =  $document_file_2;
                        }

                        $student->document_title_3 = $request->document_title_3;
                        if ($document_file_3 != "") {
                            $student->document_file_3 = $document_file_3;
                        }

                        $student->document_title_4 = $request->document_title_4;

                        if ($document_file_4 != "") {
                            $student->document_file_4 = $document_file_4;
                        }
                        $student->save();
                        DB::commit();


                        // session null

                        Session::put('student_photo', '');
                        Session::put('fathers_photo', '');
                        Session::put('mothers_photo', '');
                        Session::put('guardians_photo', '');



                        Toastr::success('Operation successful', 'Success');
                        return redirect()->back();
                    } catch (\Exception $e) {
                        DB::rollback();

                        Toastr::error('Operation Failed', 'Failed');
                        return redirect()->back();
                    }

                catch (\Exception $e) {
                    DB::rollback();

                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }


        } catch (\Exception $e) {
            DB::rollback();

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function studentUpdatePic(Request $r, $id)
    {

        // try {
        $validator = Validator::make($r->all(), [
            'logo_pic' => 'sometimes|required|mimes:jpg,png|max:40000',
            'fathers_photo' => 'sometimes|required|mimes:jpg,png|max:40000',
            'mothers_photo' => 'sometimes|required|mimes:jpg,png|max:40000',
            'guardians_photo' => 'sometimes|required|mimes:jpg,png|max:40000',

        ]);
        if ($validator->fails()) {
            return response()->json(['error' => 'error'], 201);
        }
        try {
            $data = Student::find($id);

            $data_parent = $data->parents;
            if ($r->hasFile('logo_pic')) {
                $file = $r->file('logo_pic');
                $images = Image::make($file)->insert($file);
                $pathImage = 'public/uploads/student/';
                if (!file_exists($pathImage)) {
                    mkdir($pathImage, 0777, true);
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->staff_photo =  $imageName;
                    Session::put('student_photo', $imageName);
                } else {
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    if (file_exists(Session::get('student_photo')) || file_exists($data->student_photo)) {
                        \Illuminate\Support\Facades\File::delete($data->student_photo);
                        File::delete(Session::get('student_photo'));
                    }
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->student_photo =  $imageName;
                    Session::put('student_photo', $imageName);
                }
            }
            // parent
            if ($r->hasFile('fathers_photo')) {
                $file = $r->file('fathers_photo');
                $images = Image::make($file)->insert($file);
                $pathImage = 'public/uploads/student/';
                if (!file_exists($pathImage)) {
                    mkdir($pathImage, 0777, true);
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->staff_photo =  $imageName;
                    Session::put('fathers_photo', $imageName);
                } else {
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    if (file_exists(Session::get('fathers_photo')) || file_exists($data_parent->fathers_photo)) {
                        File::delete(Session::get('fathers_photo'));
                        File::delete($data_parent->fathers_photo);
                    }
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->fathers_photo =  $imageName;
                    Session::put('fathers_photo', $imageName);
                }
            }
            //mother
            if ($r->hasFile('mothers_photo')) {
                $file = $r->file('mothers_photo');
                $images = Image::make($file)->insert($file);
                $pathImage = 'public/uploads/student/';
                if (!file_exists($pathImage)) {
                    mkdir($pathImage, 0777, true);
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->staff_photo =  $imageName;
                    Session::put('mothers_photo', $imageName);
                } else {
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    if (file_exists(Session::get('mothers_photo')) || file_exists($data_parent->mothers_photo)) {
                        File::delete(Session::get('mothers_photo'));
                        File::delete($data_parent->mothers_photo);
                    }
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->mothers_photo =  $imageName;
                    Session::put('mothers_photo', $imageName);
                }
            }
            // guardians_photo
            if ($r->hasFile('guardians_photo')) {
                $file = $r->file('guardians_photo');
                $images = Image::make($file)->insert($file);
                $pathImage = 'public/uploads/student/';
                if (!file_exists($pathImage)) {
                    mkdir($pathImage, 0777, true);
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->staff_photo =  $imageName;
                    Session::put('guardians_photo', $imageName);
                } else {
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    if (file_exists(Session::get('guardians_photo')) || file_exists($data_parent->guardians_photo)) {
                        File::delete(Session::get('guardians_photo'));
                        File::delete($data_parent->guardians_photo);
                    }
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->guardians_photo =  $imageName;
                    Session::put('guardians_photo', $imageName);
                }
            }

            return response()->json('success', 200);
        } catch (\Exception $e) {

            dd($e->getMessage());
            return response()->json(['error' => 'error'], 201);
        }
    }

    public function update(Request $request)
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        $this->validate($request, [
            'apply_date' => "required",
            'leave_type' => "required",
            'leave_from' => 'required|before_or_equal:leave_to',
            'leave_to' => "required",
            'attach_file' => "sometimes|nullable|mimes:pdf,doc,docx,jpg,jpeg,png|max:10000",
        ]);
        try {
            $fileName = "";
            if ($request->file('file') != "") {
                $apply_leave = LeaveRequest::find($request->id);
                if (file_exists($apply_leave->file)) unlink($apply_leave->file);
                $file = $request->file('file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }


            $user = Auth()->user();

            if ($user) {
                $login_id = $user->id;
                $role_id = $user->role_id;
            } else {
                $login_id = $request->login_id;
                $role_id = $request->role_id;
            }

            $apply_leave = LeaveRequest::find($request->id);
            $apply_leave->employee_id = $login_id;
            $apply_leave->role_id = $role_id;
            $apply_leave->apply_date = date('Y-m-d', strtotime($request->apply_date));
            $apply_leave->leave_define_id = $request->leave_type;
            $apply_leave->leave_from = date('Y-m-d', strtotime($request->leave_from));
            $apply_leave->leave_to = date('Y-m-d', strtotime($request->leave_to));
            $apply_leave->approve_status = 'P';
            $apply_leave->reason = $request->reason;
            if ($fileName != "") {
                $apply_leave->file = $fileName;
            }
            $result = $apply_leave->save();
            if ($result) {
                Toastr::success('Operation successful', 'Success');
                return redirect('student-apply-leave');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function DownlodTimeline($file_name)
    {

        try {
            $file = public_path() . '/uploads/student/timeline/' . $file_name;
            if (file_exists($file)) {
                return Response::download($file);
            } else {
                Toastr::error('File not found', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function DownlodDocument($file_name)
    {

        try {
            $file = public_path() . '/uploads/homework/' . $file_name;
            if (file_exists($file)) {
                return Response::download($file);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function DownlodContent($file_name)
    {
        try {
            $file = public_path() . '/uploads/upload_contents/' . $file_name;
            if (file_exists($file)) {
                return Response::download($file);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function DownlodStudentDocument($file_name)
    {


        try {
            $file = public_path() . '/uploads/student/document/' . $file_name;

       
            if (file_exists($file)) {
                return response()->download($file);
                return Response::download($file);
            }
//            Toastr::error('Hashmat, check this feature - why it is not working!', 'Failed');
//            return redirect()->back();
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function studentEdit(Request $request, $id)
    {

        try {

            $student = Student::find($id);


            $classes = Classes::all();

            $blood_groups = BaseSetup::where('base_group_id', '=', '3')->get();
            $genders = BaseSetup::where('base_group_id', '=', '1')->get();
            $siblings = Student::where('guardian_id', $student->guardian_id)->get();
            if (is_null($student->guardian_id))
            {
                $siblings = 0;


            }else{
                $siblings = Student::where('guardian_id','=', $student->guardian_id)->with('admissions.class')->get();


            }
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['student'] = $student;
                $data['classes'] = $classes->toArray();
                $data['religions'] = $religions->toArray();
                $data['blood_groups'] = $blood_groups->toArray();
                $data['genders'] = $genders->toArray();
                $data['route_lists'] = $route_lists->toArray();
                $data['vehicles'] = $vehicles->toArray();
                $data['dormitory_lists'] = $dormitory_lists->toArray();
                $data['categories'] = $categories->toArray();
                $data['sessions'] = $sessions->toArray();
                $data['siblings'] = $siblings->toArray();
                $data['driver_lists'] = $driver_lists->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.studentInformation.student_edit', compact('student', 'classes', 'blood_groups', 'genders','siblings', 'driver_lists'));
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function downloadHomeWorkContent($id, $student_id)
    {

        try {

            $student = Student::where('id', $student_id)->first();
            if (Auth::guard('web')->user()->role_id == 2) {
                $student = Student::where('user_id', $student_id)->first();
            }
            $hwContent = UploadHomeworkContent::where('student_id', $student->id)->where('homework_id', $id)->first();

            $file_array = json_decode($hwContent->file, true);


            $files = $file_array;
            $zipname = 'Homework_Content_' . time() . '.zip';
            $zip = new ZipArchive();
            $zip->open($zipname, ZipArchive::CREATE);
            foreach ($files as $file) {
                $zip->addFile($file);
            }
            $zip->close();
            header('Content-Type: application/zip');
            header('Content-disposition: attachment; filename=' . $zipname);
            header('Content-Length: ' . filesize($zipname));
            readfile($zipname);
            File::delete($zipname);

        } catch (\Exception $e) {
            // return $e;
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }


    }
}