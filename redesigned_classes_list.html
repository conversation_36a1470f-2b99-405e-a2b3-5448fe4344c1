<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redesigned Classes List</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --bs-primary-rgb: 60, 179, 113; /* Medium Sea Green */
            --bs-body-bg: #f8f9fa;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            width: 250px;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            background-color: #2c3e50;
            color: white;
            padding-top: 20px;
        }
        .sidebar .nav-link {
            color: #bdc3c7;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: #34495e;
            color: white;
            border-left: 3px solid #2ecc71;
        }
        .sidebar .nav-link .fa-fw {
            margin-right: 10px;
        }
        .main-content {
            margin-left: 250px;
            padding: 30px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        .header h1 {
            font-weight: 600;
        }
        .profile-dropdown img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }
        .filter-card {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: .5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .table-responsive {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: .5rem;
            overflow: hidden;
        }
        .table thead {
            background-color: #e9ecef;
        }
        .table th {
            font-weight: 600;
            color: #495057;
        }
        .table td {
            vertical-align: middle;
        }
        .class-title {
            font-weight: 500;
            color: #212529;
        }
        .class-code {
            font-size: 0.85em;
            color: #6c757d;
        }
        .teacher-badge {
            display: inline-block;
            padding: .35em .65em;
            font-size: .75em;
            font-weight: 700;
            line-height: 1;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: .25rem;
            background-color: #3498db;
        }
        .actions-dropdown .dropdown-toggle::after {
            display: none;
        }
        .chart-container {
            min-width: 150px;
        }
        .legend {
            display: flex;
            align-items: center;
            font-size: 0.8rem;
            margin-top: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 5px;
        }
    </style>
</head>
<body>

    <div class="sidebar">
        <h4 class="text-center mb-4">ITQAN</h4>
        <ul class="nav flex-column">
            <li class="nav-item"><a href="#" class="nav-link"><i class="fa fa-fw fa-tachometer-alt"></i> Dashboard</a></li>
            <li class="nav-item"><a href="#" class="nav-link active"><i class="fa fa-fw fa-chalkboard-teacher"></i> Classes</a></li>
            <li class="nav-item"><a href="#" class="nav-link"><i class="fa fa-fw fa-user-graduate"></i> Students</a></li>
            <li class="nav-item"><a href="#" class="nav-link"><i class="fa fa-fw fa-users"></i> Teachers</a></li>
            <li class="nav-item"><a href="#" class="nav-link"><i class="fa fa-fw fa-book"></i> Programs</a></li>
            <li class="nav-item"><a href="#" class="nav-link"><i class="fa fa-fw fa-building"></i> Centers</a></li>
            <li class="nav-item"><a href="#" class="nav-link"><i class="fa fa-fw fa-chart-bar"></i> Reports</a></li>
            <li class="nav-item"><a href="#" class="nav-link"><i class="fa fa-fw fa-cog"></i> Settings</a></li>
        </ul>
    </div>

    <div class="main-content">
        <header class="header">
            <h1>Classes</h1>
            <div class="d-flex align-items-center">
                <button class="btn btn-primary me-3"><i class="fa fa-plus me-2"></i> Add New Class</button>
                <div class="dropdown profile-dropdown">
                    <a href="#" class="d-block link-dark text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="https://i.pravatar.cc/40" alt="mdo" class="rounded-circle">
                    </a>
                    <ul class="dropdown-menu text-small dropdown-menu-end" aria-labelledby="dropdownUser1">
                        <li><a class="dropdown-item" href="#">Profile</a></li>
                        <li><a class="dropdown-item" href="#">Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">Sign out</a></li>
                    </ul>
                </div>
            </div>
        </header>

        <div class="filter-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">Filters</h5>
                <button class="btn btn-link text-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                    <i class="fa fa-chevron-up"></i>
                </button>
            </div>
            <div class="collapse show" id="filterCollapse">
                <div class="row g-3">
                    <div class="col-md-6 col-lg-3">
                        <label for="searchKeyword" class="form-label">Keyword</label>
                        <input type="text" class="form-control" id="searchKeyword" placeholder="Search by class name, code...">
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <label for="filterProgram" class="form-label">Program</label>
                        <select id="filterProgram" class="form-select">
                            <option selected>All Programs</option>
                            <option>Memorization and Revision</option>
                            <option>Arabic Language</option>
                        </select>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <label for="filterCenter" class="form-label">Center</label>
                        <select id="filterCenter" class="form-select">
                            <option selected>All Centers</option>
                            <option>1 - Idaman Male</option>
                            <option>2 - Idaman Female</option>
                            <option>3 - Wilayat</option>
                        </select>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <label for="filterTeacher" class="form-label">Teacher</label>
                        <select id="filterTeacher" class="form-select">
                            <option selected>All Teachers</option>
                            <option>Anesh Ahmed</option>
                            <option>Eissa Mohamm</option>
                        </select>
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <button class="btn btn-secondary me-2" type="button">Clear Filters</button>
                    <button class="btn btn-primary" type="button"><i class="fa fa-search me-2"></i> Apply Filters</button>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted">Showing 25 of 150 classes</div>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" role="switch" id="toggleStatistics" checked>
                <label class="form-check-label" for="toggleStatistics">Show Weekly Activity</label>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Class Details</th>
                        <th>Timetable</th>
                        <th>Center</th>
                        <th>Program</th>
                        <th>Teacher(s)</th>
                        <th class="text-center">Students</th>
                        <th class="statistics-col">Weekly Activity</th>
                        <th class="text-end">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Sample Row 1 -->
                    <tr>
                        <td>
                            <div class="class-title">Safiyya bint Abd al-Muttalib</div>
                            <div class="class-code">C-004-5</div>
                        </td>
                        <td>Mon-Fri (5)</td>
                        <td>3 - Wilayat</td>
                        <td>Memorization and Revision</td>
                        <td><span class="teacher-badge">Anesh Ahmed</span></td>
                        <td class="text-center">14</td>
                        <td class="statistics-col">
                            <div class="chart-container" id="chart1"></div>
                        </td>
                        <td class="text-end">
                            <div class="dropdown actions-dropdown">
                                <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-eye me-2"></i> View Details</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-chart-line me-2"></i> View Report</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-users me-2"></i> Manage Students</a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <!-- Sample Row 2 -->
                    <tr>
                        <td>
                            <div class="class-title">Sumayya bint Khayyat</div>
                            <div class="class-code">C-0068</div>
                        </td>
                        <td>Mon-Thu (3)</td>
                        <td>8 - IIUM</td>
                        <td>Memorization and Revision</td>
                        <td><span class="teacher-badge">Imaan Nazim</span></td>
                        <td class="text-center">8</td>
                        <td class="statistics-col">
                            <div class="chart-container" id="chart2"></div>
                        </td>
                        <td class="text-end">
                            <div class="dropdown actions-dropdown">
                                <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-eye me-2"></i> View Details</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-chart-line me-2"></i> View Report</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-users me-2"></i> Manage Students</a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                     <!-- Sample Row 3 -->
                    <tr>
                        <td>
                            <div class="class-title">Uqbah ibn Naafi</div>
                            <div class="class-code">C-0108</div>
                        </td>
                        <td>Mon-Fri (5)</td>
                        <td>1 - Idaman Male</td>
                        <td>Arabic Language</td>
                        <td><span class="teacher-badge">Mohamed Aime</span></td>
                        <td class="text-center">21</td>
                        <td class="statistics-col">
                            <div class="chart-container" id="chart3"></div>
                        </td>
                        <td class="text-end">
                            <div class="dropdown actions-dropdown">
                                <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-eye me-2"></i> View Details</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-chart-line me-2"></i> View Report</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fa fa-fw fa-users me-2"></i> Manage Students</a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="8">
                             <div class="legend">
                                <div class="fw-bold me-3">Activity Legend:</div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #2ecc71;"></div> Attendance
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color" style="background-color: #e74c3c;"></div> Absences
                                </div>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
            <ul class="pagination">
                <li class="page-item disabled"><a class="page-link" href="#">Previous</a></li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item"><a class="page-link" href="#">Next</a></li>
            </ul>
        </nav>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Toggle statistics column
            const toggle = document.getElementById('toggleStatistics');
            const statisticsCols = document.querySelectorAll('.statistics-col');
            toggle.addEventListener('change', function() {
                statisticsCols.forEach(col => {
                    col.style.display = this.checked ? '' : 'none';
                });
            });

            // ECharts options
            function getChartOption(data, color) {
                return {
                    grid: { top: 8, right: 8, bottom: 8, left: 8 },
                    xAxis: {
                        type: 'category',
                        show: false,
                        data: data.map((_, i) => i)
                    },
                    yAxis: {
                        type: 'value',
                        show: false
                    },
                    series: [{
                        data: data,
                        type: 'line',
                        smooth: true,
                        symbol: 'none',
                        lineStyle: {
                            color: color,
                            width: 2
                        },
                        areaStyle: {
                            color: color,
                            opacity: 0.3
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        formatter: 'Activity: {c}'
                    }
                };
            }

            // Render charts
            const chart1El = document.getElementById('chart1');
            chart1El.style.height = '60px';
            const chart1 = echarts.init(chart1El);
            chart1.setOption(getChartOption([12, 14, 2, 47, 42, 15, 47, 75, 65, 19, 14], '#e74c3c'));

            const chart2El = document.getElementById('chart2');
            chart2El.style.height = '60px';
            const chart2 = echarts.init(chart2El);
            chart2.setOption(getChartOption([15, 47, 75, 65, 19, 14, 12, 14, 2, 47, 42], '#2ecc71'));

            const chart3El = document.getElementById('chart3');
            chart3El.style.height = '60px';
            const chart3 = echarts.init(chart3El);
            chart3.setOption(getChartOption([42, 15, 47, 75, 65, 19, 14, 12, 14, 2, 47], '#2ecc71'));
            
            // Resize charts with window
            window.addEventListener('resize', function() {
                chart1.resize();
                chart2.resize();
                chart3.resize();
            });
        });
    </script>

</body>
</html>
