---
title: Laravel Backend Development Standards
---

# Laravel Backend Development Standards

This document outlines the mandatory standards and best practices for all backend development. It combines general Laravel principles with project-specific conventions.

## 0. Zero Tolerance Quality Standard

**🚨 CRITICAL: No Imperfections Policy**
- Every piece of code MUST be flawless and production-ready
- No compromises on code quality, security, or performance
- Zero tolerance for:
  - Unhandled edge cases
  - Security vulnerabilities
  - Performance bottlenecks
  - Code duplication
  - Undocumented functionality
  - Test coverage gaps
  - Inconsistent error handling
  - Incomplete validation
  - Unoptimized database queries

## 1. Core Principles & Coding Standards

-   **Strict Typing**: All PHP files MUST start with `declare(strict_types=1);`.
-   **PSR-12**: All code MUST adhere to the [PSR-12 coding standard](mdc:https:/www.php-fig.org/psr/psr-12).
-   **Final by Default**: All classes (Controllers, Models, Services, etc.) MUST be declared as `final` unless specifically designed for extension.
-   **SOLID Principles**: Code MUST follow SOLID principles to ensure maintainability, scalability, and testability.
-   **Clarity Over Brevity**: Write clean, readable code with descriptive names. No cryptic or unclear code allowed.
-   **100% Test Coverage**: All business logic MUST be covered by unit and integration tests.
-   **Complete Documentation**: All public methods and classes MUST be fully documented.
-   **Performance Optimization**: Every endpoint MUST be optimized for maximum performance.

## 2. Modular Architecture (`nwidart/laravel-modules`)

This project uses the `nwidart/laravel-modules` package. All new functionality must be encapsulated within a module.

### Module Directory Structure

Adhere strictly to the following structure within each module's directory (`Modules/<ModuleName>/`):

-   **Configuration**: `Config/` - Module-specific configuration files.
-   **Console Commands**: `Console/` - Custom Artisan commands.
-   **Database Schema**: `Database/` - Directory for raw SQL schema files.
-   **Models (`Entities`)**: `Entities/` - All Eloquent models reside here.
    -   Example: `[Modules/Admission/Entities/Admission.php](mdc:Modules/Admission/Entities/Admission.php)`
-   **Controllers**: `Http/Controllers/` - All module-specific controllers.
-   **Routes**:
    -   Web: `Http/routes.php`
    -   API: `Http/api.php`
-   **Service Providers**: `Providers/` - Where module services, routes, and views are registered.
-   **Repositories**: `Repositories/` - Data access layer logic.
-   **Tests**: `Tests/` - Unit and Feature tests for the module.

### **CRITICAL: Module View Location**

-   Module Blade views are **NOT** located in the default `Modules/<ModuleName>/Resources/views/` directory.
-   All module views **MUST** be placed in: `[resources/views/modules/<module-name-lowercase>/](mdc:resources/views/modules)`
-   The module's service provider (e.g., `[HumanResourceServiceProvider.php](mdc:Modules/HumanResource/Providers/HumanResourceServiceProvider.php)`) is configured to load views from this path.
-   **This is a non-negotiable project standard.**

## 3. Database Interactions

### Critical Database Safety Rules

- **🚨 NEVER Execute Direct MySQL Commands**: 
  - **STRICTLY PROHIBITED**: Running direct MySQL commands (e.g., `mysql -u root itqan`) for:
    - **DELETE** operations ⛔️ - Can cause irreversible data loss
    - **UPDATE** operations ⛔️ - Bypasses application validation and logic
    - **INSERT** operations ⛔️ - May violate data integrity rules
    - Any other database operations
  - **REASON**: Direct database access bypasses application safeguards and can lead to:
    - Untracked schema changes
    - Data inconsistencies
    - Security vulnerabilities
    - Lack of audit trail
    - Unrecoverable data loss
    - Violation of business rules
  - **CORRECT APPROACH**:
    - Write all schema changes as SQL files with ZERO margin for error
    - Store in `Modules/<ModuleName>/Database/` directory with perfect organization
    - Follow strict naming conventions (YYYYMMDD_HHMMSS_description.sql)
    - Include comprehensive documentation and foolproof rollback procedures
    - Mandatory review by authorized database administrators
    - Use Eloquent ORM exclusively for all data manipulation (CRUD)

### General Database Guidelines

- **NO MIGRATIONS**: Do not use Laravel's migration system.
- **Raw SQL for Schema**: All database schema changes (new tables, columns, indexes) **MUST** be written as raw SQL files.
- **SQL File Location**: Store schema change files in the module's `Modules/<ModuleName>/Database/` directory.
- **Eloquent for Data**: Use the Eloquent ORM for all application-level data manipulation (CRUD). Leverage Eloquent relationships, scopes, and accessors/mutators. Avoid raw SQL queries for data access unless absolutely necessary for performance.

### SQL File Requirements

- **File Structure**:
  ```sql
  /*
   * Description: [Clear description of changes]
   * Module: [Module name]
   * Author: [Developer name]
   * Date: YYYY-MM-DD
   * 
   * IMPORTANT: This file must be reviewed and executed by authorized database administrators
   */

  -- Schema changes
  ALTER TABLE example ADD COLUMN new_field VARCHAR(255);

  -- Rollback procedure
  -- ALTER TABLE example DROP COLUMN new_field;
  ```

- **Naming Convention**: YYYYMMDD_HHMMSS_descriptive_name.sql
- **Documentation**: Include clear descriptions, purpose, and rollback procedures
- **Review Process**: Must be reviewed by database administrators before execution

## 4. Critical Safety & Project Rules

-   **NEVER use `RefreshDatabase`**: The `Illuminate\Foundation\Testing\RefreshDatabase` trait is **STRICTLY FORBIDDEN**. It can cause irreversible data loss. Use database transactions for tests instead.
-   **NEVER modify `.env` programmatically**: The `.env` file must not be modified by application code. Use `config()` files for configuration management.
-   **Use `EmailService`**: All email sending **MUST** go through the centralized `[EmailService](mdc:app/Services/EmailService.php)`. Do not use Laravel's `Mail` facade directly.

## 5. Key Patterns & Practices

-   **Validation**: Use Form Request validation classes for all incoming data.
-   **Services & Repositories**: Employ the Service and Repository patterns to separate business logic from data access and controller layers.
-   **Error Handling & Logging**: Use Laravel's exception handling and logging facilities. Write meaningful log messages with context to aid in debugging.
-   **Dependency Injection**: Use constructor and method injection to manage dependencies. Let Laravel's service container do the work.

## 6. Quality Assurance Process

- **Mandatory Code Review**:
  - All code changes MUST undergo thorough peer review
  - Zero tolerance for bypassing review process
  - Use automated tools for static analysis and code quality

- **Testing Requirements**:
  - 100% test coverage for business logic
  - Comprehensive integration testing
  - Performance testing under load
  - Security vulnerability scanning
  - Database query optimization verification

- **Documentation Standards**:
  - Complete API documentation
  - Thorough code comments
  - Up-to-date README files
  - Detailed deployment guides
  - Comprehensive troubleshooting documentation

- **Performance Standards**:
  - Sub-200ms response time for API endpoints
  - Optimized database queries
  - Efficient caching implementation
  - Minimal memory usage
  - Regular performance audits

- **Security Requirements**:
  - Regular security audits
  - OWASP compliance
  - Input validation on all endpoints
  - Proper authentication and authorization
  - Secure data handling practices

- **Monitoring and Maintenance**:
  - Comprehensive error logging
  - Performance monitoring
  - Regular dependency updates
  - Proactive issue detection
  - Zero downtime deployments




















