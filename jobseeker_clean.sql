/*
 * JobSeeker Module Database Schema - CLEAN VERSION
 * 
 * This script contains ONLY the actively used tables in the JobSeeker module
 * based on codebase analysis.
 * 
 * Author: System Administrator
 * Date: 2025-01-26
 */

-- =============================================================================
-- SECTION 1: DATABASE SETUP
-- =============================================================================

CREATE DATABASE IF NOT EXISTS `jobseeker` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `jobseeker`;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================================================
-- SECTION 2: DROP EXISTING TABLES
-- =============================================================================

-- Core tables
DROP TABLE IF EXISTS `job_category_pivot`;
DROP TABLE IF EXISTS `job_notification_category`;
DROP TABLE IF EXISTS `job_notification_recipients`;
DROP TABLE IF EXISTS `job_notification_recipient_emails`;
DROP TABLE IF EXISTS `job_notification_setups`;
DROP TABLE IF EXISTS `user_device_tokens`;
DROP TABLE IF EXISTS `outgoing_emails`;
DROP TABLE IF EXISTS `jobseeker_settings`;
DROP TABLE IF EXISTS `job_seeker_personal_contacts`;
DROP TABLE IF EXISTS `job_sync_alert_events`;
DROP TABLE IF EXISTS `job_sync_alert_rules`;
DROP TABLE IF EXISTS `command_schedule_executions`;
DROP TABLE IF EXISTS `command_schedule_filters`;
DROP TABLE IF EXISTS `command_schedule_rules`;
DROP TABLE IF EXISTS `provider_job_categories`;
DROP TABLE IF EXISTS `provider_job_locations`;
DROP TABLE IF EXISTS `provider_circuit_breaker_states`;
DROP TABLE IF EXISTS `jobs`;
DROP TABLE IF EXISTS `job_categories`;
DROP TABLE IF EXISTS `job_locations`;
DROP TABLE IF EXISTS `job_seekers`;
DROP TABLE IF EXISTS `failed_jobs`;
DROP TABLE IF EXISTS `job_batches`;
DROP TABLE IF EXISTS `queue_jobs`;

-- =============================================================================
-- SECTION 3: CREATE TABLES
-- =============================================================================

-- Table: job_seekers
CREATE TABLE `job_seekers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_notified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `job_seekers_email_unique` (`email`),
  UNIQUE KEY `job_seekers_username_unique` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_categories
CREATE TABLE `job_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Category name',
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'URL-friendly version of name',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional description of the category',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Whether this category is active',
  `is_canonical` tinyint(1) NOT NULL DEFAULT '1',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Source system (e.g., acbar, jobs.af)',
  `source_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ID in the source system',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_archived` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Indicates if this category has been archived by an administrator',
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_name` (`name`),
  KEY `idx_is_active` (`is_active`),
  KEY `parent_id` (`parent_id`),
  KEY `idx_job_categories_source_id` (`source`,`source_id`),
  KEY `idx_job_categories_canonical` (`is_canonical`),
  KEY `idx_job_categories_is_archived` (`is_archived`),
  KEY `idx_job_categories_active_not_archived` (`is_active`,`is_archived`),
  KEY `idx_job_categories_archived_active` (`is_archived`,`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_locations
CREATE TABLE `job_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Human-readable location name (e.g., Kabul Province)',
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Country name (e.g., Afghanistan)',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Province/state name (e.g., Kabul)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Whether this location is active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `job_locations_country_province_unique` (`country`,`province`),
  KEY `job_locations_country_index` (`country`),
  KEY `job_locations_province_index` (`province`),
  KEY `job_locations_is_active_index` (`is_active`),
  KEY `job_locations_name_index` (`name`),
  KEY `job_locations_compound_country_active` (`country`,`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Canonical job locations for job seeker interface - country and province focused';

-- Table: jobs
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `slug` varchar(255) DEFAULT NULL,
  `position` varchar(255) DEFAULT NULL,
  `number_of_vacancy` int DEFAULT NULL,
  `vacancy_number` varchar(100) DEFAULT NULL,
  `is_featured` tinyint(1) DEFAULT '0',
  `locations` varchar(255) DEFAULT NULL,
  `contract_type` varchar(100) DEFAULT NULL,
  `work_type` varchar(100) DEFAULT NULL,
  `gender` varchar(50) DEFAULT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `company_logo` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `company_profile_link` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `expire_date` date DEFAULT NULL,
  `raw_data` json DEFAULT NULL,
  `publish_date` date DEFAULT NULL,
  `salary` varchar(255) DEFAULT NULL,
  `views_count` int DEFAULT '0',
  `can_apply_online` tinyint(1) DEFAULT '0',
  `description` longtext,
  `about_company` longtext,
  `job_summary` longtext,
  `duties_responsibilities` longtext,
  `job_requirements` longtext,
  `experience` varchar(255) DEFAULT NULL COMMENT 'Experience requirements for the job position',
  `submission_guideline` longtext,
  `is_new` tinyint(1) DEFAULT '0',
  `applications_count` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_notified_at` timestamp NULL DEFAULT NULL,
  `source` varchar(50) DEFAULT NULL,
  `normalized_company_name` varchar(255) DEFAULT NULL COMMENT 'Normalized company name for de-duplication',
  `normalized_job_title` varchar(255) DEFAULT NULL COMMENT 'Normalized job title for de-duplication',
  `normalized_location` varchar(255) DEFAULT NULL COMMENT 'Normalized location for de-duplication',
  `job_fingerprint` varchar(255) DEFAULT NULL COMMENT 'Unique fingerprint based on normalized data for de-duplication',
  `master_job_id` bigint unsigned DEFAULT NULL COMMENT 'Reference to master job if this is a duplicate',
  `source_ids` json DEFAULT NULL COMMENT 'Array of source information for this job posting',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_jobs_job_fingerprint` (`job_fingerprint`),
  KEY `idx_jobs_slug` (`slug`),
  KEY `idx_jobs_normalized_company` (`normalized_company_name`),
  KEY `idx_jobs_normalized_title` (`normalized_job_title`),
  KEY `idx_jobs_normalized_location` (`normalized_location`),
  KEY `idx_jobs_master_job_id` (`master_job_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Jobs table with de-duplication functionality';

-- Table: job_category_pivot
CREATE TABLE `job_category_pivot` (
  `job_id` bigint unsigned NOT NULL,
  `category_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`job_id`,`category_id`),
  KEY `job_category_pivot_job_id_index` (`job_id`),
  KEY `job_category_pivot_category_id_index` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 

-- Table: provider_job_categories
CREATE TABLE `provider_job_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `provider_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider_identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `canonical_category_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `provider_job_categories_unique_mapping` (`provider_name`,`provider_identifier`),
  KEY `provider_job_categories_provider_name_index` (`provider_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: provider_job_locations
CREATE TABLE `provider_job_locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `provider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `location_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider_identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `canonical_location_id` bigint unsigned NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `provider_job_locations_unique_mapping` (`provider_name`,`provider_identifier`),
  KEY `provider_job_locations_provider_name_index` (`provider_name`),
  KEY `provider_job_locations_location_name_index` (`location_name`),
  KEY `provider_job_locations_is_active_index` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: provider_circuit_breaker_states
CREATE TABLE `provider_circuit_breaker_states` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `provider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` enum('closed','open','half_open') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'closed',
  `failure_count` int unsigned NOT NULL DEFAULT '0',
  `last_failure_at` timestamp NULL DEFAULT NULL,
  `recovery_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `provider_circuit_breaker_states_provider_name_unique` (`provider_name`),
  KEY `provider_circuit_breaker_states_state_index` (`state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 

-- Table: command_schedule_rules
CREATE TABLE `command_schedule_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `command` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `schedule_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `schedule_type` enum('cron','daily_at','weekly_at','custom') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'cron',
  `days_of_week` json DEFAULT NULL,
  `time_slots` json DEFAULT NULL,
  `timezone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Asia/Kabul',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `priority` int NOT NULL DEFAULT '100',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `depends_on_command` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `delay_after_dependency` int NOT NULL DEFAULT '900',
  `max_execution_time` int NOT NULL DEFAULT '3600',
  `last_executed_at` datetime DEFAULT NULL,
  `execution_timeout` int NOT NULL DEFAULT '7200' COMMENT 'Timeout in seconds for command execution',
  `concurrent_executions` int NOT NULL DEFAULT '1' COMMENT 'Maximum concurrent executions allowed',
  `next_run_at` datetime DEFAULT NULL,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `provider_job_category_ids` json DEFAULT NULL COMMENT 'Stores an array of IDs from the provider_job_categories table.',
  PRIMARY KEY (`id`),
  KEY `idx_command` (`command`),
  KEY `idx_schedule_type` (`schedule_type`),
  KEY `idx_timezone` (`timezone`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_depends_on_command` (`depends_on_command`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_last_executed_at` (`last_executed_at`),
  KEY `idx_priority_active` (`priority`,`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Generic command scheduling rules';

-- Table: command_schedule_filters
CREATE TABLE `command_schedule_filters` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `schedule_rule_id` bigint unsigned NOT NULL,
  `categories` json DEFAULT NULL,
  `locations` json DEFAULT NULL,
  `companies` json DEFAULT NULL,
  `experience_levels` json DEFAULT NULL,
  `search_term` varchar(255) DEFAULT NULL,
  `work_type` varchar(50) DEFAULT NULL,
  `min_page` int DEFAULT NULL,
  `max_page` int DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `max_retries` int DEFAULT NULL,
  `timeout` int DEFAULT NULL,
  `base_delay` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `schedule_rule_id` (`schedule_rule_id`),
  KEY `command_schedule_filters_is_default_index` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: command_schedule_executions
CREATE TABLE `command_schedule_executions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `schedule_rule_id` bigint unsigned DEFAULT NULL,
  `command` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `started_at` datetime NOT NULL,
  `completed_at` datetime DEFAULT NULL,
  `status` enum('running','completed','failed','timeout') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'running',
  `exit_code` int DEFAULT NULL,
  `output` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `error_output` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `execution_time_seconds` int DEFAULT NULL,
  `memory_usage_mb` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `jobs_fetched` int DEFAULT NULL,
  `jobs_by_category` json DEFAULT NULL,
  `error_type` enum('none','network','api','timeout','data','unknown') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'none',
  `error_details` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `command_schedule_executions_schedule_rule_id_index` (`schedule_rule_id`),
  KEY `command_schedule_executions_command_index` (`command`),
  KEY `command_schedule_executions_status_index` (`status`),
  KEY `command_schedule_executions_started_at_index` (`started_at`),
  KEY `command_schedule_executions_created_at_index` (`created_at`),
  KEY `command_schedule_executions_error_type_index` (`error_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 

-- Table: job_notification_setups
CREATE TABLE `job_notification_setups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_seeker_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_count` int DEFAULT '0' COMMENT 'Number of categories subscribed to in this setup',
  `sent_count` int unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `receive_push_notifications` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether this setup should send push notifications',
  `last_notified_at` timestamp NULL DEFAULT NULL,
  `requires_review_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'Reason why this setup requires review (e.g., category removed)',
  `last_activity_check_at` timestamp NULL DEFAULT NULL COMMENT 'Last time this setup was checked for category activity',
  PRIMARY KEY (`id`),
  KEY `fk_job_notification_setups_job_seeker` (`job_seeker_id`),
  KEY `idx_job_notification_setups_requires_review` (`requires_review_reason`(50)),
  KEY `idx_job_notification_setups_activity_check` (`last_activity_check_at`),
  KEY `job_notification_setups_receive_push_notifications_index` (`receive_push_notifications`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_notification_recipients
CREATE TABLE `job_notification_recipients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `setup_id` bigint unsigned DEFAULT NULL,
  `recipient_email_id` bigint unsigned DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `job_notification_recipients_setup_id_index` (`setup_id`),
  KEY `job_notification_recipients_recipient_email_id_index` (`recipient_email_id`),
  KEY `job_notification_recipients_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_notification_recipient_emails
CREATE TABLE `job_notification_recipient_emails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `job_notification_recipient_emails_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_notification_category
CREATE TABLE `job_notification_category` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `setup_id` bigint unsigned NOT NULL,
  `category_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `job_notification_category_setup_id_index` (`setup_id`),
  KEY `job_notification_category_category_id_index` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: user_device_tokens
CREATE TABLE `user_device_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_seeker_id` bigint unsigned NOT NULL,
  `device_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_device_tokens_device_token_unique` (`device_token`(255)),
  KEY `user_device_tokens_job_seeker_id_index` (`job_seeker_id`),
  KEY `user_device_tokens_platform_index` (`platform`),
  KEY `user_device_tokens_last_used_at_index` (`last_used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: outgoing_emails
CREATE TABLE `outgoing_emails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `recipient` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'sync',
  `priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal',
  `email_data` json DEFAULT NULL,
  `status` enum('pending','queued','sending','sent','failed','cancelled','retry_scheduled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `send_attempts` int unsigned NOT NULL DEFAULT '0',
  `max_attempts` int unsigned NOT NULL DEFAULT '3',
  `last_error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `error_history` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `scheduled_at` timestamp NULL DEFAULT NULL,
  `first_attempt_at` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `failed_at` timestamp NULL DEFAULT NULL,
  `recovery_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `exported_at` timestamp NULL DEFAULT NULL,
  `exported_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `outgoing_emails_recipient_index` (`recipient`),
  KEY `outgoing_emails_provider_index` (`provider`),
  KEY `outgoing_emails_status_index` (`status`),
  KEY `outgoing_emails_created_at_index` (`created_at`),
  KEY `outgoing_emails_scheduled_at_index` (`scheduled_at`),
  KEY `outgoing_emails_failed_at_index` (`failed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 

-- Table: jobseeker_settings
CREATE TABLE `jobseeker_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `jobseeker_settings_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_seeker_account_lockouts
CREATE TABLE `job_seeker_account_lockouts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_seeker_id` bigint unsigned NOT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `locked_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `unlocked_at` timestamp NULL DEFAULT NULL,
  `unlock_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `job_seeker_account_lockouts_job_seeker_id_index` (`job_seeker_id`),
  KEY `job_seeker_account_lockouts_locked_at_index` (`locked_at`),
  KEY `job_seeker_account_lockouts_unlocked_at_index` (`unlocked_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_seeker_password_history
CREATE TABLE `job_seeker_password_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_seeker_id` bigint unsigned NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `changed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `job_seeker_password_history_job_seeker_id_index` (`job_seeker_id`),
  KEY `job_seeker_password_history_changed_at_index` (`changed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_seeker_personal_contacts
CREATE TABLE `job_seeker_personal_contacts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `job_seeker_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `job_seeker_personal_contacts_job_seeker_id_index` (`job_seeker_id`),
  KEY `job_seeker_personal_contacts_email_index` (`email`),
  KEY `job_seeker_personal_contacts_is_active_index` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_sync_alert_events
CREATE TABLE `job_sync_alert_events` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` bigint unsigned NOT NULL,
  `event_type` enum('success','warning','error') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `context` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `job_sync_alert_events_rule_id_index` (`rule_id`),
  KEY `job_sync_alert_events_event_type_index` (`event_type`),
  KEY `job_sync_alert_events_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_sync_alert_rules
CREATE TABLE `job_sync_alert_rules` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `conditions` json NOT NULL,
  `notification_channels` json NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `job_sync_alert_rules_is_active_index` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: failed_jobs
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_batches
CREATE TABLE `job_batches` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: queue_jobs
CREATE TABLE `queue_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint unsigned NOT NULL,
  `reserved_at` int unsigned DEFAULT NULL,
  `available_at` int unsigned NOT NULL,
  `created_at` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `queue_jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================================================
-- SECTION 4: ADD FOREIGN KEY CONSTRAINTS
-- =============================================================================

SET FOREIGN_KEY_CHECKS = 1;

-- Self-referencing constraints
ALTER TABLE `job_categories` ADD CONSTRAINT `job_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `job_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `jobs` ADD CONSTRAINT `fk_jobs_master_job_id` FOREIGN KEY (`master_job_id`) REFERENCES `jobs` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Command scheduling constraints
ALTER TABLE `command_schedule_filters` ADD CONSTRAINT `fk_command_schedule_filters_rule` FOREIGN KEY (`schedule_rule_id`) REFERENCES `command_schedule_rules` (`id`) ON DELETE CASCADE;
ALTER TABLE `command_schedule_executions` ADD CONSTRAINT `fk_command_schedule_executions_rule` FOREIGN KEY (`schedule_rule_id`) REFERENCES `command_schedule_rules` (`id`) ON DELETE SET NULL;

-- Provider mapping constraints
ALTER TABLE `provider_job_categories` ADD CONSTRAINT `fk_provider_job_categories_canonical_category` FOREIGN KEY (`canonical_category_id`) REFERENCES `job_categories` (`id`) ON DELETE RESTRICT;
ALTER TABLE `provider_job_locations` ADD CONSTRAINT `fk_provider_job_locations_canonical_location_id` FOREIGN KEY (`canonical_location_id`) REFERENCES `job_locations` (`id`) ON DELETE RESTRICT;

-- Notification system constraints
ALTER TABLE `job_notification_setups` ADD CONSTRAINT `fk_job_notification_setups_job_seeker` FOREIGN KEY (`job_seeker_id`) REFERENCES `job_seekers` (`id`) ON DELETE CASCADE;
ALTER TABLE `job_notification_recipients` ADD CONSTRAINT `fk_job_notification_recipients_setup` FOREIGN KEY (`setup_id`) REFERENCES `job_notification_setups` (`id`) ON DELETE CASCADE;
ALTER TABLE `job_notification_recipients` ADD CONSTRAINT `fk_job_notification_recipients_email` FOREIGN KEY (`recipient_email_id`) REFERENCES `job_notification_recipient_emails` (`id`) ON DELETE CASCADE;
ALTER TABLE `job_notification_category` ADD CONSTRAINT `fk_job_notification_category_setup` FOREIGN KEY (`setup_id`) REFERENCES `job_notification_setups` (`id`) ON DELETE CASCADE;
ALTER TABLE `job_notification_category` ADD CONSTRAINT `fk_job_notification_category_category` FOREIGN KEY (`category_id`) REFERENCES `job_categories` (`id`) ON DELETE CASCADE;

-- Pivot table constraints
ALTER TABLE `job_category_pivot` ADD CONSTRAINT `fk_job_category_pivot_job` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE;
ALTER TABLE `job_category_pivot` ADD CONSTRAINT `fk_job_category_pivot_category` FOREIGN KEY (`category_id`) REFERENCES `job_categories` (`id`) ON DELETE CASCADE;

-- Device and user constraints
ALTER TABLE `user_device_tokens` ADD CONSTRAINT `fk_user_device_tokens_job_seeker` FOREIGN KEY (`job_seeker_id`) REFERENCES `job_seekers` (`id`) ON DELETE CASCADE;
ALTER TABLE `job_seeker_account_lockouts` ADD CONSTRAINT `fk_job_seeker_account_lockouts_job_seeker` FOREIGN KEY (`job_seeker_id`) REFERENCES `job_seekers` (`id`) ON DELETE CASCADE;
ALTER TABLE `job_seeker_password_history` ADD CONSTRAINT `fk_job_seeker_password_history_job_seeker` FOREIGN KEY (`job_seeker_id`) REFERENCES `job_seekers` (`id`) ON DELETE CASCADE;
ALTER TABLE `job_seeker_personal_contacts` ADD CONSTRAINT `fk_job_seeker_personal_contacts_job_seeker` FOREIGN KEY (`job_seeker_id`) REFERENCES `job_seekers` (`id`) ON DELETE CASCADE;

-- Job sync alert constraints
ALTER TABLE `job_sync_alert_events` ADD CONSTRAINT `fk_job_sync_alert_events_rule` FOREIGN KEY (`rule_id`) REFERENCES `job_sync_alert_rules` (`id`) ON DELETE CASCADE; 