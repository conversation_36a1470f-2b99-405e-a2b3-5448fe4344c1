---
type: "always_apply"
---

- **File Organization & Architecture**
  - Never create files over **200-300 lines** – split into smaller, focused files.
  - Keep **one component or piece of functionality per file** with clear separation of concerns.

- **Design & UI Standards**
  - Aim for **Apple-level design aesthetics** with meticulous attention to detail.
  - Implement a **comprehensive colour system** (primary, secondary, accent, success, warning, error) with multiple shades.
  - Use an **8-px spacing system** and **150 % line-height** for body text.
  - Ensure readable **contrast ratios across all colour combinations** (WCAG AA+ where feasible).
  - Add **thoughtful micro-interactions, hover states, and transitions**.
  - Follow a **mobile-first responsive approach** with well-defined breakpoints.
  - **Avoid indigo/blue hues** unless explicitly requested.

- **Technology Preferences**
  - Use **Pexels URLs** for stock images – do **not** download or embed binary assets in the repo.

- **Code Quality**
  - Write **clean, readable code** with descriptive names.
  - Provide **proper error handling and loading states**.
  - Employ **progressive disclosure** techniques to manage complexity.
  - Keep functions **focused on a single responsibility**.
  - Supply **alt text** for images unless purely decorative.
  - Comment complex logic when necessary, but strive for **self-documenting code**.

- **Common Issue Patterns**
  - **CORS errors** → usually require correct headers or a proxy configuration.
  - **Hydration mismatches** → examine differences between server- and client-rendered markup.
  - **State management bugs** → trace data flow through components/stores.
  - **Performance issues** → look for unnecessary re-renders or large bundle sizes.
  - **Routing problems** → verify route names, prefixes, and navigation logic.





