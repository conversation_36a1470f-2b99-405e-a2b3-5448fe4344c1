# Sweet Navigation Component - JSON Response Format Specification

## Overview

This document defines the standardized JSON response format that backend endpoints must return for the Sweet Navigation component to function correctly. The format is designed to be flexible while maintaining consistency across different types of navigation data.

## Response Structure

### Success Response

```json
{
    "success": true,
    "data": {
        "groups": [
            {
                "name": "Group Name",
                "items": [
                    {
                        "id": "unique_identifier",
                        "name": "Display Name",
                        "url": "/path/to/item",
                        "is_current": false,
                        "subtitle": "Optional subtitle text",
                        "count": "25",
                        "actions": [
                            {
                                "type": "action_type",
                                "url": "/action/url",
                                "title": "Tooltip text",
                                "label": "Button text"
                            }
                        ]
                    }
                ]
            }
        ]
    },
    "message": "Optional success message"
}
```

### Error Response

```json
{
    "success": false,
    "message": "Error description",
    "error": "Optional detailed error information"
}
```

## Field Specifications

### Root Level Fields

#### `success` (Required)
- **Type**: Boolean
- **Description**: Indicates whether the request was successful
- **Values**: `true` for success, `false` for error

#### `data` (Required for success)
- **Type**: Object
- **Description**: Contains the navigation data
- **Required when**: `success` is `true`

#### `message` (Optional)
- **Type**: String
- **Description**: Human-readable message (used for errors or notifications)
- **Usage**: Displayed in error popups or console logs

#### `error` (Optional)
- **Type**: String
- **Description**: Detailed error information for debugging
- **Usage**: Logged to console in debug mode

### Data Object Fields

#### `groups` (Required)
- **Type**: Array of Group objects
- **Description**: Array of navigation groups/categories
- **Minimum**: 0 items (empty array for no results)
- **Maximum**: Recommended 20 groups for performance

### Group Object Fields

#### `name` (Required)
- **Type**: String
- **Description**: Display name for the group/category
- **Example**: `"Mathematics Program"`, `"Science Classes"`
- **Notes**: Supports plain text only (HTML will be escaped)

#### `items` (Required)
- **Type**: Array of Item objects
- **Description**: Array of navigation items within this group
- **Minimum**: 0 items (empty array for empty groups)
- **Maximum**: Recommended 50 items per group for performance

### Item Object Fields

#### `id` (Required)
- **Type**: String or Number
- **Description**: Unique identifier for the item
- **Example**: `"123"`, `456`
- **Notes**: Used for highlighting current item via `data-current-id`

#### `name` (Required)
- **Type**: String
- **Description**: Display name for the item
- **Example**: `"Algebra 101"`, `"Advanced Physics"`
- **Notes**: Primary text shown in navigation

#### `url` (Required)
- **Type**: String (URL)
- **Description**: URL to navigate to when item is clicked
- **Example**: `"/classes/123"`, `"https://example.com/item"`
- **Notes**: 
  - Relative URLs navigate within same window
  - Absolute URLs (starting with http) open in new tab
  - Use `"#"` to disable navigation

#### `is_current` (Optional)
- **Type**: Boolean
- **Default**: `false`
- **Description**: Whether this item is the currently active item
- **Notes**: 
  - Overrides `data-current-id` matching
  - Only one item should be `true` per response

#### `subtitle` (Optional)
- **Type**: String
- **Description**: Additional descriptive text shown below the name
- **Example**: `"Teacher: John Doe | Schedule: Mon, Wed, Fri"`
- **Notes**: 
  - Supports HTML content
  - Keep concise for better UX
  - Commonly includes icons and metadata

#### `count` (Optional)
- **Type**: String or Number
- **Description**: Numeric indicator (e.g., student count, item count)
- **Example**: `"25"`, `42`
- **Notes**: 
  - Displayed as a badge/pill
  - Usually represents quantity or status

#### `actions` (Optional)
- **Type**: Array of Action objects
- **Description**: Array of action buttons for this item
- **Maximum**: Recommended 5 actions for UX

### Action Object Fields

#### `type` (Required)
- **Type**: String
- **Description**: Action type identifier (used for CSS styling)
- **Example**: `"report"`, `"show"`, `"edit"`, `"delete"`
- **Notes**: 
  - Maps to CSS class `sweet-navigation-action-{type}`
  - Common types: `report`, `show`, `edit`, `delete`, `download`

#### `url` (Required)
- **Type**: String (URL)
- **Description**: URL for the action
- **Example**: `"/reports/123"`, `"/classes/123/edit"`
- **Notes**: Actions typically open in new tab/window

#### `title` (Required)
- **Type**: String
- **Description**: Tooltip text for the action button
- **Example**: `"View Class Report"`, `"Edit Class Details"`

#### `label` (Required)
- **Type**: String
- **Description**: Text/icon displayed on the button
- **Example**: `"R"`, `"Edit"`, `"<i class='fa fa-download'></i>"`
- **Notes**: 
  - Keep very short (1-2 characters) for space
  - Supports HTML for icons

## Example Responses

### Classes Navigation Response
```json
{
    "success": true,
    "data": {
        "groups": [
            {
                "name": "Mathematics Program",
                "items": [
                    {
                        "id": "101",
                        "name": "Algebra Fundamentals",
                        "url": "/classes/101",
                        "is_current": false,
                        "subtitle": "Teacher: <a href='/teachers/5'>Dr. Smith</a> | Mon, Wed, Fri 10:00-11:30",
                        "count": "28",
                        "actions": [
                            {
                                "type": "report",
                                "url": "/classes/101/report",
                                "title": "View Class Report",
                                "label": "R"
                            },
                            {
                                "type": "show",
                                "url": "/classes/101",
                                "title": "View Class Details",
                                "label": "V"
                            },
                            {
                                "type": "edit",
                                "url": "/classes/101/edit",
                                "title": "Edit Class",
                                "label": "E"
                            }
                        ]
                    },
                    {
                        "id": "102",
                        "name": "Advanced Calculus",
                        "url": "/classes/102",
                        "is_current": true,
                        "subtitle": "Teacher: <a href='/teachers/8'>Prof. Johnson</a> | Tue, Thu 14:00-16:00",
                        "count": "15",
                        "actions": [
                            {
                                "type": "report",
                                "url": "/classes/102/report",
                                "title": "View Class Report",
                                "label": "R"
                            },
                            {
                                "type": "show",
                                "url": "/classes/102",
                                "title": "View Class Details",
                                "label": "V"
                            }
                        ]
                    }
                ]
            },
            {
                "name": "Science Program",
                "items": [
                    {
                        "id": "201",
                        "name": "Physics Laboratory",
                        "url": "/classes/201",
                        "is_current": false,
                        "subtitle": "Teacher: <a href='/teachers/12'>Dr. Wilson</a> | Wed, Fri 16:00-18:00",
                        "count": "20"
                    }
                ]
            }
        ]
    },
    "message": "Navigation data loaded successfully"
}
```

### Empty Results Response
```json
{
    "success": true,
    "data": {
        "groups": []
    },
    "message": "No items found"
}
```

### Error Response Examples

#### Authentication Error
```json
{
    "success": false,
    "message": "Authentication required",
    "error": "User not authenticated"
}
```

#### Not Found Error
```json
{
    "success": false,
    "message": "Navigation data not found",
    "error": "No classes found for the specified criteria"
}
```

#### Server Error
```json
{
    "success": false,
    "message": "Internal server error occurred",
    "error": "Database connection failed"
}
```

## Laravel Implementation Examples

### Basic Controller Method
```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class NavigationController extends Controller
{
    public function getClassesNavigation(Request $request)
    {
        try {
            $currentClassId = $request->get('current_id');
            
            // Fetch classes grouped by programs
            $programs = Program::with(['classes.teachers', 'classes.students'])
                ->whereHas('classes')
                ->get();
            
            $groups = [];
            
            foreach ($programs as $program) {
                $items = [];
                
                foreach ($program->classes as $class) {
                    $items[] = [
                        'id' => (string) $class->id,
                        'name' => $class->name,
                        'url' => route('classes.show', $class->id),
                        'is_current' => $currentClassId == $class->id,
                        'subtitle' => $this->buildClassSubtitle($class),
                        'count' => (string) $class->students->count(),
                        'actions' => [
                            [
                                'type' => 'report',
                                'url' => route('classes.report', $class->id),
                                'title' => 'View Class Report',
                                'label' => 'R'
                            ],
                            [
                                'type' => 'show',
                                'url' => route('classes.show', $class->id),
                                'title' => 'View Class Details',
                                'label' => 'V'
                            ]
                        ]
                    ];
                }
                
                $groups[] = [
                    'name' => $program->name,
                    'items' => $items
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => [
                    'groups' => $groups
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load navigation data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
    
    private function buildClassSubtitle($class)
    {
        $teachers = $class->teachers->take(2)->map(function($teacher) {
            return '<a href="' . route('teachers.show', $teacher->id) . '">' . $teacher->name . '</a>';
        })->implode(', ');
        
        if ($class->teachers->count() > 2) {
            $teachers .= ' +' . ($class->teachers->count() - 2);
        }
        
        return "Teacher: {$teachers} | Schedule: {$class->schedule}";
    }
}
```

### Route Definition
```php
// routes/api.php or module routes
Route::get('/navigation/classes', [NavigationController::class, 'getClassesNavigation'])
    ->name('api.navigation.classes')
    ->middleware(['auth', 'throttle:60,1']);
```

## Validation Rules

### Server-Side Validation (Laravel)
```php
// Request validation rules
$rules = [
    'success' => 'required|boolean',
    'data' => 'required_if:success,true|array',
    'data.groups' => 'required|array',
    'data.groups.*.name' => 'required|string|max:255',
    'data.groups.*.items' => 'required|array',
    'data.groups.*.items.*.id' => 'required|string|max:255',
    'data.groups.*.items.*.name' => 'required|string|max:255',
    'data.groups.*.items.*.url' => 'required|string|max:2048',
    'data.groups.*.items.*.is_current' => 'boolean',
    'data.groups.*.items.*.subtitle' => 'nullable|string|max:1000',
    'data.groups.*.items.*.count' => 'nullable|string|max:50',
    'data.groups.*.items.*.actions' => 'array',
    'data.groups.*.items.*.actions.*.type' => 'required|string|max:50',
    'data.groups.*.items.*.actions.*.url' => 'required|string|max:2048',
    'data.groups.*.items.*.actions.*.title' => 'required|string|max:255',
    'data.groups.*.items.*.actions.*.label' => 'required|string|max:50',
    'message' => 'nullable|string|max:1000',
    'error' => 'nullable|string|max:2000'
];
```

## Performance Considerations

### Response Size Optimization
1. **Limit Items**: Keep total items under 500 for good performance
2. **Pagination**: Consider pagination for large datasets
3. **Caching**: Cache responses when data doesn't change frequently
4. **Compression**: Enable gzip compression on server

### Database Optimization
1. **Eager Loading**: Use eager loading to prevent N+1 queries
2. **Indexing**: Index frequently queried fields
3. **Query Optimization**: Optimize database queries for navigation data

### Client-Side Handling
1. **Loading States**: Component shows loading indicators automatically
2. **Error Handling**: Component handles errors gracefully
3. **Memory Management**: Large responses are cleaned up automatically

## Security Considerations

### Data Sanitization
1. **HTML Escaping**: Escape HTML in names and subtitles unless intentionally allowing HTML
2. **URL Validation**: Validate all URLs to prevent XSS
3. **Input Validation**: Validate all input parameters

### Authorization
1. **Access Control**: Ensure users can only access authorized navigation data
2. **Rate Limiting**: Implement rate limiting on navigation endpoints
3. **CSRF Protection**: Include CSRF tokens in AJAX requests

### Privacy
1. **Data Minimization**: Only return necessary data
2. **Sensitive Information**: Avoid exposing sensitive information in navigation
3. **Audit Logging**: Log navigation access for security auditing
