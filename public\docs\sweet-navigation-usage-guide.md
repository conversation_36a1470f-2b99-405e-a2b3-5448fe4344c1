# Sweet Navigation Component - Complete Usage Guide

## Overview

The Sweet Navigation Component is a reusable Laravel Blade component that provides elegant dropdown navigation functionality using SweetAlert2 popups. It was extracted from the original `nouranya.blade.php` file and generalized for use across any Laravel application.

## Quick Start

### 1. Include Assets (Once Per Page)

```blade
{{-- In your Blade template --}}
@include('components.sweet-navigation-assets')
```

### 2. Add Navigation Trigger

```html
<a href="#" class="sweet-navigation-trigger"
   data-ajax-url="{{ route('api.sweet-navigation.classes') }}"
   data-title="Class Navigation"
   data-current-id="{{ $currentClassId }}">
    <i class="fa fa-list-ul"></i> Classes
    <i class="fa fa-caret-down"></i>
</a>
```

### 3. Create Backend Endpoint

```php
// In your controller
public function getClassesNavigation(Request $request): JsonResponse
{
    $groups = [
        [
            'name' => 'Mathematics Program',
            'items' => [
                [
                    'id' => '1',
                    'name' => 'Algebra 101',
                    'url' => route('classes.show', 1),
                    'is_current' => $request->get('current_id') === '1',
                    'subtitle' => 'Teacher: Dr. Smith | Mon, Wed, Fri',
                    'count' => '28',
                    'actions' => [
                        ['type' => 'report', 'url' => '/report/1', 'title' => 'Report', 'label' => 'R']
                    ]
                ]
            ]
        ]
    ];
    
    return response()->json(['success' => true, 'data' => ['groups' => $groups]]);
}
```

That's it! Your navigation is ready to use.

## Detailed Implementation Guide

### Asset Inclusion Options

The `sweet-navigation-assets.blade.php` component supports several configuration options:

```blade
{{-- Basic inclusion (recommended) --}}
@include('components.sweet-navigation-assets')

{{-- Advanced configuration --}}
@include('components.sweet-navigation-assets', [
    'includeSweetAlert2' => false,  // Don't include if already loaded
    'includeJQuery' => false,       // Don't include if already loaded  
    'autoInitialize' => true        // Auto-initialize on DOM ready
])
```

### Data Attributes Reference

#### Required Attributes

- **`data-ajax-url`**: The endpoint URL that returns navigation data
- **`data-title`**: Title displayed in the popup header

#### Optional Attributes

- **`data-current-id`**: ID of currently selected item (for highlighting)
- **`data-confirm-button-text`**: Custom confirm button text
- **`data-confirm-button-url`**: URL for confirm button action
- **`data-width`**: Custom popup width (default: 800px)

### Complete Example

```html
<a href="#" class="sweet-navigation-trigger"
   data-ajax-url="{{ route('api.navigation.students') }}"
   data-title="<i class='fa fa-users'></i> Student Navigation"
   data-current-id="{{ $currentStudentId }}"
   data-confirm-button-text="<i class='fa fa-plus'></i> Add New Student"
   data-confirm-button-url="{{ route('students.create') }}"
   data-width="900px">
    <i class="fa fa-users"></i> Browse Students
</a>
```

## Integration Patterns

### 1. Breadcrumb Navigation (Original Use Case)

```blade
<ol class="breadcrumb">
    <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="dropdown-container">
        <a href="#" class="sweet-navigation-trigger"
           data-ajax-url="{{ route('api.navigation.classes', ['current' => $class->id]) }}"
           data-title="Class Navigation"
           data-current-id="{{ $class->id }}"
           data-confirm-button-url="{{ route('classes.index') }}">
            <i class="fa fa-list-ul"></i> Classes
            <i class="fa fa-caret-down"></i>
        </a>
    </li>
    <li class="active">{{ $class->name }}</li>
</ol>
```

### 2. Sidebar Navigation

```blade
<div class="sidebar-nav">
    <ul class="nav nav-pills nav-stacked">
        <li>
            <a href="#" class="sweet-navigation-trigger"
               data-ajax-url="{{ route('api.navigation.programs') }}"
               data-title="Programs">
                <i class="fa fa-graduation-cap"></i> Programs
            </a>
        </li>
        <li>
            <a href="#" class="sweet-navigation-trigger"
               data-ajax-url="{{ route('api.navigation.teachers') }}"
               data-title="Teachers">
                <i class="fa fa-user"></i> Teachers
            </a>
        </li>
    </ul>
</div>
```

### 3. Toolbar Navigation

```blade
<div class="btn-toolbar">
    <div class="btn-group">
        <a href="#" class="btn btn-primary sweet-navigation-trigger"
           data-ajax-url="{{ route('api.navigation.classes') }}"
           data-title="Quick Class Access">
            <i class="fa fa-list"></i> Classes
        </a>
        <a href="#" class="btn btn-success sweet-navigation-trigger"
           data-ajax-url="{{ route('api.navigation.students') }}"
           data-title="Quick Student Access">
            <i class="fa fa-users"></i> Students
        </a>
    </div>
</div>
```

### 4. Card-Based Navigation

```blade
<div class="row">
    <div class="col-md-4">
        <div class="panel panel-default">
            <div class="panel-body text-center">
                <a href="#" class="sweet-navigation-trigger"
                   data-ajax-url="{{ route('api.navigation.classes') }}"
                   data-title="Class Management">
                    <i class="fa fa-list fa-3x text-primary"></i>
                    <h4>Classes</h4>
                    <p>Manage and navigate classes</p>
                </a>
            </div>
        </div>
    </div>
</div>
```

## Backend Implementation

### Laravel Controller Example

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NavigationController extends Controller
{
    public function getClassesNavigation(Request $request): JsonResponse
    {
        try {
            $currentId = $request->get('current_id');
            
            // Fetch your data (replace with actual queries)
            $programs = Program::with(['classes.teachers', 'classes.students'])
                ->whereHas('classes')
                ->get();
            
            $groups = [];
            
            foreach ($programs as $program) {
                $items = [];
                
                foreach ($program->classes as $class) {
                    $items[] = [
                        'id' => (string) $class->id,
                        'name' => $class->name,
                        'url' => route('classes.show', $class->id),
                        'is_current' => $currentId == $class->id,
                        'subtitle' => $this->buildClassSubtitle($class),
                        'count' => (string) $class->students->count(),
                        'actions' => [
                            [
                                'type' => 'report',
                                'url' => route('classes.report', $class->id),
                                'title' => 'View Class Report',
                                'label' => 'R'
                            ],
                            [
                                'type' => 'show',
                                'url' => route('classes.show', $class->id),
                                'title' => 'View Class Details',
                                'label' => 'V'
                            ]
                        ]
                    ];
                }
                
                $groups[] = [
                    'name' => $program->name,
                    'items' => $items
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => ['groups' => $groups]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load navigation data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
    
    private function buildClassSubtitle($class): string
    {
        $teachers = $class->teachers->take(2)->map(function($teacher) {
            return '<a href="' . route('teachers.show', $teacher->id) . '" class="sweet-navigation-link">' . $teacher->name . '</a>';
        })->implode(', ');
        
        if ($class->teachers->count() > 2) {
            $teachers .= ' +' . ($class->teachers->count() - 2);
        }
        
        return "Teacher: {$teachers} | Schedule: {$class->schedule}";
    }
}
```

### Route Definition

```php
// routes/api.php
Route::get('/navigation/classes', [NavigationController::class, 'getClassesNavigation'])
    ->name('api.navigation.classes')
    ->middleware(['auth', 'throttle:60,1']);
```

## JSON Response Format

### Success Response Structure

```json
{
    "success": true,
    "data": {
        "groups": [
            {
                "name": "Group Name",
                "items": [
                    {
                        "id": "unique_identifier",
                        "name": "Display Name",
                        "url": "/path/to/item",
                        "is_current": false,
                        "subtitle": "Optional subtitle with HTML",
                        "count": "Badge text/number",
                        "actions": [
                            {
                                "type": "action_type",
                                "url": "/action/url",
                                "title": "Tooltip text",
                                "label": "Button text"
                            }
                        ]
                    }
                ]
            }
        ]
    },
    "message": "Optional success message"
}
```

### Error Response Structure

```json
{
    "success": false,
    "message": "Human-readable error message",
    "error": "Detailed error for debugging (optional)"
}
```

## Configuration Options

### Global Configuration

```javascript
// Override global configuration
window.SweetNavigationConfig = {
    defaultWidth: '800px',
    animationDuration: 300,
    searchPlaceholder: 'Search...',
    hoverDelay: 200,
    debug: false
};

// Re-initialize after config changes
initializeSweetNavigation();
```

### Per-Instance Configuration

Use data attributes for per-instance configuration:

```html
<a href="#" class="sweet-navigation-trigger"
   data-ajax-url="/api/navigation"
   data-title="Navigation"
   data-width="1000px">  <!-- Custom width for this instance -->
   Navigation Link
</a>
```

## Advanced Features

### Custom Styling

Override CSS variables for custom theming:

```css
:root {
    --sweet-nav-primary-color: #009933;
    --sweet-nav-popup-width: 800px;
    --sweet-nav-animation-duration: 0.3s;
}
```

### Manual Initialization

```javascript
// Manual initialization (if auto-initialization is disabled)
$(document).ready(function() {
    initializeSweetNavigation();
});

// Re-initialize after dynamic content changes
$('#dynamic-container').html(newContent);
initializeSweetNavigation();
```

### Event Handling

```javascript
// Listen for navigation events
$(document).on('mouseenter', '.sweet-navigation-trigger', function() {
    console.log('Navigation trigger hovered:', $(this).data('title'));
});

$(document).on('sweet-navigation-opened', function(event, config) {
    console.log('Navigation opened:', config);
});
```

## Browser Compatibility

- **Chrome**: 60+
- **Firefox**: 55+
- **Safari**: 12+
- **Edge**: 79+
- **Internet Explorer**: Not supported

## Dependencies

- **jQuery**: 3.6.0+
- **SweetAlert2**: 11.0+
- **Font Awesome**: 4.7+ (for icons)
- **Bootstrap**: 3.4+ (optional, for styling)
