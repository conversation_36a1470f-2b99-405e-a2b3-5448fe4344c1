<?php
Route::get('/', function () {

    return redirect(config('app.locale'));
});

if (config('domain')) {

    /* Fix redirect after login - Refactoring needed  */
    Route::get('student/home', function () {
        return redirect(config('app.locale') . '/student/home');
    });
    Route::get('guardian/home', function () {
        return redirect(config('app.locale') . '/guardian/home');
    });
    /* end */


  
    Route::group(['prefix' => config('app.locale'), 'domain' => config('domain')], function () {
        Route::group(['middleware' => 'web', 'namespace' => 'Modules\Site\Http\Controllers'], function ($router) {
            Route::get('/', 'SiteController@index');
           
            
            Route::get('/registeration', 'SiteController@registeration')->name('registeration');

            Route::get('/contact-us', 'SiteController@contact')->name('contact');
            Route::post('/contact-us', 'SiteController@sendContact')->name('sendContact');


//            Route::get('login', function () {
//                return redirect('login-board');
//            })->name('loginboard');



//            Route::group(['prefix' => 'guardian'], function () {
//                Route::get('/login', 'GuardianAuth\LoginController@showLoginForm')->name('guardian.login');
//                ;
//                Route::post('/login', 'GuardianAuth\LoginController@login');
//                Route::post('/logout', 'GuardianAuth\LoginController@logout');
//
//                Route::get('/register', 'GuardianAuth\RegisterController@showRegistrationForm');
//                Route::post('/register', 'GuardianAuth\RegisterController@register');
//
//                Route::post('/password/email', 'GuardianAuth\ForgotPasswordController@sendResetLinkEmail');
//                Route::post('/password/reset', 'GuardianAuth\ResetPasswordController@reset');
//                Route::get('/password/reset', 'GuardianAuth\ForgotPasswordController@showLinkRequestForm');
//                Route::get('/password/reset/{token}', 'GuardianAuth\ResetPasswordController@showResetForm');
//                Route::group(['middleware' => 'auth:guardian'], function () {
//                    Route::get('/', 'GuardianController@index');
//                    Route::get('home', 'GuardianController@index');
//                    // Route::get('register-student', 'GuardianController@register')->name('guardian.register_student');
//                    Route::get('register-student/{id?}', 'GuardianController@register')->name('guardian.register_student');
//
//                    Route::get('hefz/{id}', 'hefzreportController@showforguardian')->name('student.hefz.{id}');
//
//                  Route::post('gurdianaction', 'hefzreportController@gurdianaction')->name('hefzreport.gurdianaction');
//
//                  Route::get('update', 'GuardianController@edit')->name('guardian.edit');
//                  Route::post('update/{id}', 'GuardianController@update')->name('guardian.update');
//                  Route::get('profile', 'GuardianController@profile')->name('guardian.profile');
//                });
//            });




            Route::group(['prefix' => 'student'], function () {
                Route::get('/login', 'StudentAuth\LoginController@showLoginForm')->name('student.login');
                Route::post('/login', 'StudentAuth\LoginController@login');
                Route::post('/logout', 'StudentAuth\LoginController@logout');

                Route::get('/register', 'StudentAuth\RegisterController@showRegistrationForm');
                Route::post('/register', 'StudentAuth\RegisterController@register');

                Route::post('/password/email', 'StudentAuth\ForgotPasswordController@sendResetLinkEmail');
                Route::post('/password/reset', 'StudentAuth\ResetPasswordController@reset');
                Route::get('/password/reset', 'StudentAuth\ForgotPasswordController@showLinkRequestForm');
                Route::get('/password/reset/{token}', 'StudentAuth\ResetPasswordController@showResetForm');

                Route::group(['middleware' => 'auth:student'], function () {
                    Route::get('home', 'StudentController@index');
                    Route::get('setting', 'StudentController@studentsetting')->name('student.setting');
                    Route::post('guardian/create', 'GuardianController@store')->name('guardian.add');

                    Route::get('hefz', 'hefzreportController@index')->name('hefz');

                    Route::post('action', 'hefzreportController@action')->name('hefzreport.action');

                });
            });


      
            // Route::group(['prefix' => 'sponsor'], function () {
            ////     Route::get('/login', 'SponsorAuth\LoginController@showLoginForm')->name('sponsor.login');
            //     Route::post('/login', 'SponsorAuth\LoginController@login');
            //     Route::post('/logout', 'SponsorAuth\LoginController@logout');

            //     Route::get('/register', 'SponsorAuth\RegisterController@showRegistrationForm');
            //     Route::post('/register', 'SponsorAuth\RegisterController@register');

            //     Route::post('/password/email', 'SponsorAuth\ForgotPasswordController@sendResetLinkEmail');
            //     Route::post('/password/reset', 'SponsorAuth\ResetPasswordController@reset');
            //     Route::get('/password/reset', 'SponsorAuth\ForgotPasswordController@showLinkRequestForm');
            //     Route::get('/password/reset/{token}', 'SponsorAuth\ResetPasswordController@showResetForm');

            //     Route::group(['middleware' => 'auth:sponsor'], function () {
            //         Route::get('home', 'SponsorController@index');
            //     });
            // });


            Route::group(['middleware' => 'auth:student,guardian,employee'], function () {
//                Route::resource('students', 'StudentController');
                Route::resource('guardians', 'GuardianController');
                Route::resource('admission', 'AdmissionController');
//                Route::post('change-archived-student-status-to-new-admission', 'AdmissionController@changeStatusToNewAdmission')->name("change.status.to.new.admission");
                Route::resource('sponsors', 'StudentController');

                Route::post('admission/offer-response', 'AdmissionController@offer_response')->name('admission.offer_response');

                Route::post('admission/upload-payment-proof', 'AdmissionController@payment_proof')->name('admission.upload_payment_proof');
            });

            

            Route::get('classes/{center_id}', function ($center_id) {
                return App\Classes::where('center_id', '=', $center_id)
                    ->where('status', '=', 'active')
                    ->get()->pluck('name', 'id');
            });

            Route::get('program-classes/{program_id}', function ($program_id) {
                $program = App\Program::findOrFail($program_id);

                $centers = [];
                foreach ($program->classes as $class) {
                    if (!isset($centers[$class->center_id]['center'])) {
                        $centers[$class->center_id]['center']['id'] = $class->center_id;
                        $centers[$class->center_id]['center']['name'] = $class->center->name;
                    }
                    $centers[$class->center_id]['classes_with_program'][] = ['id' => $class->id, 'name' => $class->name];
                }
                return $centers;
            });
            Route::get('news', 'NewsController@list');

            Route::get('news/{slug}', 'NewsController@show');
            

            /* Have to be the last route in this group */
            Route::get('{slug}', 'MenuController@show');
        });

        Route::group(['prefix' => 'edit-mode', 'middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'namespace' => 'Modules\Site\Http\Controllers'], function () {
            if (Auth::guard('employee')) {
                view()->share('edit_mode', true);
            }
            
            Route::get('/', 'SiteController@index')->middleware('permission:manage homepage content');
            Route::get('add-menu', 'MenuController@create')->middleware('permission:create menu');

            Route::get('widgets', 'WidgetsController@available')->name('get_widgets')->middleware('permission:view homepage widgets');
            Route::get('edit-widget/{widget_name}', 'WidgetsController@edit')->name('edit_widget')->middleware('permission:edit homepage widgets');
            Route::post('update-widget', 'WidgetsController@update')->name('update_widget')->middleware('permission:edit homepage widgets');
            Route::post('sort-widgets', 'WidgetsController@sort')->name('sort_widgets')->middleware('permission:sort homepage widgets');
            Route::post('widget-blocks', 'WidgetsController@blocks')->name('get_widget_blocks')->middleware('permission:view homepage widgets');
        });
    });


    
    
    /* Workplace Route [Website Admin Panel] */
    Route::group(['prefix' => 'workplace/site', 'middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'namespace' => 'Modules\\Site\\Http\\Controllers'], function () {
        Route::resource('menu', 'MenuController');
        Route::post('menu/sort', 'MenuController@sort')->name('menu.sort')->middleware('permission:edit menu');

        Route::resource('news', 'NewsController');
        Route::post('news/reorder', 'NewsController@reorder')->name('news.reorder')->middleware('permission:edit news');

        Route::resource('slider', 'SliderController');
        Route::post('slider/sort', 'SliderController@sort')->name('sort_slides')->middleware('permission:edit slider');

        Route::resource('testimonials', 'TestimonialsController');

        // Settings are not a resource controller, so permissions are inline.
        Route::get('settings', 'SettingsController@index')->name('settings')->middleware('permission:view site settings');
        Route::post('settings', 'SettingsController@update')->name('settings.update')->middleware('permission:update site settings');
    });

    Route::group(['middleware' => ['web']], function () {

        Route::get('/', 'SiteController@index')->name('site.index');

        // Public facing routes
        Route::get('register', 'SiteController@registeration');

        Route::get('contact', 'SiteController@contact');

        Route::post('contact', 'SiteController@sendContact')->name('send_contact');

        Route::get('payment', 'SiteController@payment')->name('payment');
        Route::post('payment', 'SiteController@addpayment')->name('addpayment');

        Route::get('{slug}', 'MenuController@show')->name('show_page');

    });

    Route::group(['prefix' => 'edit-mode', 'middleware' => ['web', 'auth:employee','missedClockOutMiddleware', 'permission:manage homepage content']], function () {
        if (Auth::guard('employee')) {
            view()->share('edit_mode', true);
        }

        Route::get('/', 'SiteController@index');
        Route::get('add-menu', 'MenuController@create')->middleware('permission:create menu');

        // Widget Management
        Route::get('widgets', 'WidgetsController@available')->name('get_widgets')->middleware('permission:view homepage widgets');
        Route::get('edit-widget/{widget_name}', 'WidgetsController@edit')->name('edit_widget')->middleware('permission:edit homepage widgets');
        Route::post('update-widget', 'WidgetsController@update')->name('update_widget')->middleware('permission:edit homepage widgets');
        Route::post('sort-widgets', 'WidgetsController@sort')->name('sort_widgets')->middleware('permission:sort homepage widgets');
        Route::post('widget-blocks', 'WidgetsController@blocks')->name('get_widget_blocks')->middleware('permission:view homepage widgets');
    });
}
