--
-- Populate provider_locations table with Afghanistan location mappings
-- This enables the dynamic provider location mapping system - purely database-driven approach
-- Focus: Afghanistan locations only, no hardcoded dependencies
--

-- First, let's clear any existing data to avoid duplicates
DELETE FROM provider_locations WHERE provider_name IN ('jobs.af', 'acbar');

--
-- Jobs.af Location Mappings
-- Jobs.af uses location names as strings sent directly to their API
-- These are common Afghanistan locations that Jobs.af recognizes
--

INSERT INTO provider_locations (provider_name, location_name, provider_identifier, is_active) VALUES
-- Major Afghanistan Cities and Provinces for Jobs.af
('jobs.af', 'Kabul', 'Kabul', 1),
('jobs.af', 'Herat', 'Herat', 1),
('jobs.af', 'Mazar-i-Sharif', 'Mazar-i-Sharif', 1),
('jobs.af', 'Kandahar', 'Kandahar', 1),
('jobs.af', 'Jalalabad', 'Jalalabad', 1),
('jobs.af', 'Kunduz', 'Kunduz', 1),
('jobs.af', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1),
('jobs.af', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 1),
('jobs.af', 'Farah', 'Farah', 1),
('jobs.af', 'Gardez', 'Gardez', 1),
('jobs.af', 'Khost', '<PERSON>host', 1),
('jobs.af', '<PERSON>hkar <PERSON>ah', '<PERSON>hkar <PERSON>ah', 1),
('jobs.af', '<PERSON>mana', '<PERSON>mana', 1),
('jobs.af', 'Sheberghan', 'Sheberghan', 1),
('jobs.af', 'Taloqan', 'Taloqan', 1),
('jobs.af', 'Zaranj', 'Zaranj', 1),
('jobs.af', 'Baghlan', 'Baghlan', 1),
('jobs.af', 'Parwan', 'Parwan', 1),
('jobs.af', 'Logar', 'Logar', 1),
('jobs.af', 'Wardak', 'Wardak', 1),
('jobs.af', 'Nangarhar', 'Nangarhar', 1),
('jobs.af', 'Laghman', 'Laghman', 1),
('jobs.af', 'Kunar', 'Kunar', 1),
('jobs.af', 'Nuristan', 'Nuristan', 1),
('jobs.af', 'Badakhshan', 'Badakhshan', 1),
('jobs.af', 'Takhar', 'Takhar', 1),
('jobs.af', 'Badghis', 'Badghis', 1),
('jobs.af', 'Ghor', 'Ghor', 1),
('jobs.af', 'Daykundi', 'Daykundi', 1),
('jobs.af', 'Uruzgan', 'Uruzgan', 1),
('jobs.af', 'Zabul', 'Zabul', 1),
('jobs.af', 'Paktika', 'Paktika', 1),
('jobs.af', 'Paktia', 'Paktia', 1),
('jobs.af', 'Kapisa', 'Kapisa', 1),
('jobs.af', 'Panjshir', 'Panjshir', 1),
('jobs.af', 'Samangan', 'Samangan', 1),
('jobs.af', 'Sar-e Pol', 'Sar-e Pol', 1),
('jobs.af', 'Jawzjan', 'Jawzjan', 1),
('jobs.af', 'Faryab', 'Faryab', 1),
('jobs.af', 'Nimroz', 'Nimroz', 1);

--
-- ACBAR Location Mappings
-- ACBAR uses numeric province IDs in their API
-- These correspond to Afghanistan provinces with their specific numeric identifiers
--

INSERT INTO provider_locations (provider_name, location_name, provider_identifier, is_active) VALUES
-- Afghanistan Provinces for ACBAR (using numeric IDs as they expect)
('acbar', 'Kabul', '1', 1),
('acbar', 'Kapisa', '2', 1),
('acbar', 'Parwan', '3', 1),
('acbar', 'Wardak', '4', 1),
('acbar', 'Logar', '5', 1),
('acbar', 'Nangarhar', '6', 1),
('acbar', 'Laghman', '7', 1),
('acbar', 'Kunar', '8', 1),
('acbar', 'Nuristan', '9', 1),
('acbar', 'Badakhshan', '10', 1),
('acbar', 'Takhar', '11', 1),
('acbar', 'Baghlan', '12', 1),
('acbar', 'Kunduz', '13', 1),
('acbar', 'Samangan', '14', 1),
('acbar', 'Balkh', '15', 1),
('acbar', 'Sar-e Pol', '16', 1),
('acbar', 'Jawzjan', '17', 1),
('acbar', 'Faryab', '18', 1),
('acbar', 'Badghis', '19', 1),
('acbar', 'Herat', '20', 1),
('acbar', 'Farah', '21', 1),
('acbar', 'Nimroz', '22', 1),
('acbar', 'Helmand', '23', 1),
('acbar', 'Kandahar', '24', 1),
('acbar', 'Zabul', '25', 1),
('acbar', 'Uruzgan', '26', 1),
('acbar', 'Daykundi', '27', 1),
('acbar', 'Ghor', '28', 1),
('acbar', 'Bamyan', '29', 1),
('acbar', 'Panjshir', '30', 1),
('acbar', 'Ghazni', '31', 1),
('acbar', 'Paktika', '32', 1),
('acbar', 'Paktia', '33', 1),
('acbar', 'Khost', '34', 1);

--
-- Verify the data insertion
--
SELECT 
    provider_name,
    COUNT(*) as location_count,
    GROUP_CONCAT(location_name ORDER BY location_name SEPARATOR ', ') as sample_locations
FROM provider_locations 
WHERE provider_name IN ('jobs.af', 'acbar')
GROUP BY provider_name;

-- Show sample of each provider's data
SELECT * FROM provider_locations WHERE provider_name = 'jobs.af' LIMIT 5;
SELECT * FROM provider_locations WHERE provider_name = 'acbar' LIMIT 5; 