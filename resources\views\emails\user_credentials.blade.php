<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Login Credentials</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-top: 4px solid #28a745;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #28a745;
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header .subtitle {
            color: #6c757d;
            font-size: 16px;
            margin-top: 8px;
        }
        .welcome-message {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #28a745;
        }
        .credentials-section {
            background-color: #fff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            text-align: center;
        }
        .credentials-title {
            color: #495057;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .credential-item {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .credential-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        .credential-value {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #212529;
            background-color: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ced4da;
            word-break: break-all;
        }
        .login-button {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            transition: background-color 0.3s ease;
        }
        .login-button:hover {
            background-color: #218838;
            color: white;
            text-decoration: none;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 20px;
            margin: 25px 0;
        }
        .instructions h3 {
            color: #004085;
            margin-top: 0;
            font-size: 16px;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
            color: #004085;
        }
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .security-notice strong {
            color: #856404;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .footer .organization-name {
            font-weight: 600;
            color: #28a745;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .credential-value {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Your Account is Ready!</h1>
            <div class="subtitle">Welcome to {{ $organization_name }}</div>
        </div>

        <div class="welcome-message">
            <p><strong>Dear {{ $student_name }},</strong></p>
            <p>Your user account has been successfully created! You can now access the system using the credentials provided below.</p>
        </div>

        <div class="credentials-section">
            <div class="credentials-title">
                🔑 Your Login Credentials
            </div>
            
            <div class="credential-item">
                <div class="credential-label">Username</div>
                <div class="credential-value">{{ $username }}</div>
            </div>
            
            <div class="credential-item">
                <div class="credential-label">Password</div>
                <div class="credential-value">{{ $password }}</div>
            </div>

            <a href="{{ $login_url }}" class="login-button">
                🚀 Login Now
            </a>
        </div>

        <div class="instructions">
            <h3>📋 Getting Started:</h3>
            <ul>
                <li>Click the "Login Now" button above or visit: <strong>{{ $login_url }}</strong></li>
                <li>Enter your username and password exactly as shown above</li>
                <li>We recommend changing your password after your first login</li>
                <li>Keep your credentials secure and do not share them with others</li>
            </ul>
        </div>

        <div class="security-notice">
            <strong>🛡️ Security Notice:</strong> For your security, please change your password after logging in for the first time. If you have any issues accessing your account, please contact our support team.
        </div>

        <div class="footer">
            <p>This email was sent by <span class="organization-name">{{ $organization_name }}</span></p>
            <p>If you did not request this account or have any questions, please contact our support team.</p>
            <p><small>This is an automated message. Please do not reply to this email.</small></p>
        </div>
    </div>
</body>
</html>
