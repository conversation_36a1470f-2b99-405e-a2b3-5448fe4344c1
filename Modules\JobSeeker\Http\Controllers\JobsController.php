<?php

namespace Modules\JobSeeker\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Imports\RecipientsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use Modules\JobSeeker\Entities\JobCategory;
use Modules\JobSeeker\Entities\JobNotificationRecipient;
use Modules\JobSeeker\Entities\JobNotificationSetup;
use Modules\JobSeeker\Entities\JobSeeker;
use Modules\JobSeeker\Entities\JobSeekerPersonalContact;
use Modules\JobSeeker\Repositories\JobRepository;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Mail\VerifyJobSubscription;
use Modules\JobSeeker\Services\JobService;
use Illuminate\Support\Facades\DB;
use Modules\JobSeeker\Entities\JobNotificationRecipientEmail;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\URL;
use App\Services\EmailService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Modules\JobSeeker\Entities\Job;

class JobsController extends Controller
{
    protected $jobRepository;
    protected JobService $jobService;

    public function __construct(JobRepository $jobRepository, JobService $jobService)
    {
       
        $this->jobRepository = $jobRepository;
        $this->jobService = $jobService;
    }

    /**
     * Display a listing of the jobs.
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
public function index(Request $request)
 {
    $jobs = Job::where('publish_date', '>=', now()->subDays(7))
        ->orderBy('publish_date', 'desc')
        ->paginate(20);
     
     return view('jobseeker::jobs.index', compact('jobs'));
 }
    
    
    /**
     * Get jobs data for DataTables
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJobsData(Request $request)
    {
        $days = $request->input('days', 2);
        
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        
        $defaultLocation = config('jobseeker.default_location', 'Kabul');
        $jobs = \Modules\JobSeeker\Entities\Job::where('publish_date', '>=', $startDate)
            ->where('locations', 'like', '%' . $defaultLocation . '%')
            ->orderBy('position')
            ->get();
        
        // Format data for DataTables
        $data = [];
        
        foreach ($jobs as $job) {
            // Determine job category for prioritization
            $priority = $this->determineJobPriority($job->position);
            
            $data[] = [
                'id' => $job->id,
                'position' => $job->position,
                'company_name' => $job->company_name,
                'locations' => $job->locations,
                'contract_type' => $job->contract_type,
                'work_type' => $job->work_type,
                'publish_date' => Carbon::parse($job->publish_date)->format('Y-m-d'),
                'expire_date' => Carbon::parse($job->expire_date)->format('Y-m-d'),
                'time_ago' => Carbon::parse($job->publish_date)->diffForHumans(),
                'slug' => $job->slug,
                'priority' => $priority
            ];
        }
        
        // Sort by priority (IT first, Leadership second, Management third)
        usort($data, function($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });
        
        return response()->json([
            'data' => $data
        ]);
    }
    
    /**
     * Determine job priority based on position
     * 
     * @param string $position
     * @return int
     */
    private function determineJobPriority($position)
    {
        $position = strtolower($position);
        
        // IT jobs (highest priority)
        if (strpos($position, 'developer') !== false || 
            strpos($position, 'engineer') !== false || 
            strpos($position, 'software') !== false || 
            strpos($position, 'it') !== false) {
            return 1;
        }
        
        // Leadership roles
        if (strpos($position, 'lead') !== false || 
            strpos($position, 'chief') !== false || 
            strpos($position, 'head') !== false || 
            strpos($position, 'director') !== false) {
            return 2;
        }
        
        // Management roles
        if (strpos($position, 'manager') !== false || 
            strpos($position, 'management') !== false) {
            return 3;
        }
        
        // All other jobs
        return 10;
    }

    /**
     * Display a public listing of the jobs without authentication.
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function publicIndex(Request $request)
    {
        $days = $request->input('days', 2); // Default to 2 days
        
        return view('jobseeker::jobs.public', [
            'days' => $days
        ]);
    }

    /**
     * Get the dashboard widget with job statistics
     * 
     * @param int $days
     * @return \Illuminate\View\View
     */
public function dashboardWidget($days = 2)
 {
     $startDate = Carbon::now()->subDays($days)->startOfDay();
    $defaultLocation = config('jobseeker.default_location', 'Kabul');
     
     // Get IT jobs count
     $itJobsCount = \Modules\JobSeeker\Entities\Job::where('publish_date', '>=', $startDate)
        ->where('locations', 'like', '%' . $defaultLocation . '%')
         ->where(function($query) {
             $query->where('position', 'like', '%developer%')
                 ->orWhere('position', 'like', '%engineer%')
                 ->orWhere('position', 'like', '%software%')
                 ->orWhere('position', 'like', '%it%');
         })
         ->count();
     
     // Get leadership jobs count
     $leadershipJobsCount = \Modules\JobSeeker\Entities\Job::where('publish_date', '>=', $startDate)
        ->where('locations', 'like', '%' . $defaultLocation . '%')
         ->where(function($query) {
             $query->where('position', 'like', '%lead%')
                 ->orWhere('position', 'like', '%chief%')
                 ->orWhere('position', 'like', '%head%')
                 ->orWhere('position', 'like', '%director%');
         })
         ->count();
     
     // Get management jobs count
     $managementJobsCount = \Modules\JobSeeker\Entities\Job::where('publish_date', '>=', $startDate)
        ->where('locations', 'like', '%' . $defaultLocation . '%')
         ->where(function($query) {
             $query->where('position', 'like', '%manager%')
                 ->orWhere('position', 'like', '%management%');
         })
         ->count();
        
        return view('jobseeker::widgets.latest_jobs', [
            'itJobsCount' => $itJobsCount,
            'leadershipJobsCount' => $leadershipJobsCount,
            'managementJobsCount' => $managementJobsCount,
            'days' => $days
        ]);
    }

    /**
     * List the authenticated user's job notification setups
     */
    public function notificationSetups(Request $request)
    {
        $user = $request->user();
        $notifications = $user->jobNotifications()->get();
        return response()->json(['notifications' => $notifications]);
    }

    /**
     * Display the notification management view
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function notifications(Request $request)
    {
        $authenticatedUser = Auth::guard('job_seeker')->user();
        
        // Add null safety check for authenticated user
        if (!$authenticatedUser) {
            Log::warning('JobsController: Attempted to access notifications without authentication', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'requested_route' => $request->getPathInfo()
            ]);
            
            // Return error response for unauthenticated access
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required to access job notifications.',
                    'redirect' => route('jobseeker.login')
                ], 401);
            }
            
            // Redirect to login for web requests
            return redirect()->route('jobseeker.login')
                ->with('error', 'Please log in to access your job notifications.')
                ->with('intended', $request->fullUrl());
        }
        
        $email = $authenticatedUser->email;
        $jobSeeker = null;
        $notificationSetups = collect();
        $isSubscribed = false;
        
        // Since we have a guaranteed authenticated user, find their job seeker record
        $jobSeeker = JobSeeker::where('email', $email)->first();
        if ($jobSeeker) {
            $notificationSetups = $jobSeeker->notificationSetups()->with('categories')->get();
            $isSubscribed = $jobSeeker->is_active;
        }

        // Fetch recent job counts for each category (last 5 days)
        $categoryJobCounts = $this->jobService->getCategoryJobCounts(5, true);
        
        Log::debug('JobsController: Fetched category job counts', ['counts' => $categoryJobCounts]);

        // Check if user wants to see archived categories
        $showArchived = $request->get('show_archived', false);
        
        // Get provider-specific categories for notification setup
        $provider = $request->get('provider', 'all');

        if ($provider === 'all') {
            // Show canonical categories for multi-provider setups
            $categoriesQuery = JobCategory::where('is_active', true);

            if (!$showArchived) {
                $categoriesQuery->where('is_archived', false);
            }

            $categories = $categoriesQuery->get();
        } else {
            // Show provider-specific categories
            $providerCategories = ProviderJobCategory::where('provider_name', $provider)
                ->whereNotNull('canonical_category_id')
                ->with('canonicalCategory')
                ->get();

            // Group by canonical category for display
            $categories = $providerCategories->groupBy('canonical_category_id')
                ->map(function ($group) {
                    return $group->first()->canonicalCategory;
                })
                ->filter()
                ->values();
        }
        
        // Sort categories by job count (highest to lowest)
        $sortedCategories = $categories->sortBy(function($category) use ($categoryJobCounts) {
            // Get the count for this category, default to 0 if not found
            return -1 * ($categoryJobCounts[$category->id] ?? 0); // Negative to sort in descending order
        });

        return view('jobseeker::jobs.notifications', [
            'categories' => $sortedCategories,
            'jobSeeker' => $jobSeeker,
            'notificationSetups' => $notificationSetups,
            'categoryJobCounts' => $categoryJobCounts,
            'showArchived' => $showArchived
        ]);
    }
    
    /**
     * Subscribe to job notifications
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function subscribe(Request $request, EmailService $emailService)
    {
        try {
            // Throttle subscriptions to prevent abuse
            $throttleKey = 'job_subscription_' . ($request->ip() ?? 'unknown');
            $maxAttempts = config('jobseeker.job_notifications.throttle_attempts', 5);
            $decayMinutes = config('jobseeker.job_notifications.throttle_minutes', 30);
            
            if (RateLimiter::tooManyAttempts($throttleKey, $maxAttempts)) {
                $seconds = RateLimiter::availableIn($throttleKey);
                
                Log::warning('Job notification subscription rate limited', [
                    'ip' => $request->ip(),
                    'available_in_seconds' => $seconds
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => 'Too many subscription attempts. Please try again in ' . ceil($seconds / 60) . ' minutes.',
                ], 429);
            }
            
            // Hit the rate limiter
            RateLimiter::hit($throttleKey, $decayMinutes * 60);
            
            $data = $request->validate([
                'email' => 'required|email',
                'name' => 'nullable|string|max:255',
            ]);
            
            $jobSeeker = JobSeeker::where('email', $data['email'])->first();
            
            if (!$jobSeeker) {
                $token = Str::random(60);
                $verificationUrl = URL::temporarySignedRoute(
                    'jobseeker.verify-subscription',
                    now()->addMinutes(config('jobseeker.job_notifications.verification_expiry_minutes', 60)),
                    [
                        'email' => $data['email'],
                        'token' => $token
                    ]
                );
                
                // Send verification email using EmailService
                $emailService = app(\App\Services\EmailService::class);
                $emailResult = $emailService->sendEmail(
                    ['email' => $data['email'], 'name' => $data['name'] ?? explode('@', $data['email'])[0]],
                    'Verify Your Job Notification Subscription',
                    'jobseeker::emails.jobs.verify-subscription', // Use the module-namespaced view
                    [
                        'name' => $data['name'] ?? explode('@', $data['email'])[0],
                        'verificationUrl' => $verificationUrl
                    ]
                );

                if (!$emailResult['success']) {
                    Log::error('Failed to send verification email', [
                        'email' => $data['email'], 
                        'error' => $emailResult['message']
                    ]);
                    // Decide if you want to stop the process or allow creation anyway
                    // For now, let's log and continue, user can request new verification later
                }
                
                Log::info('Job notification verification email dispatch attempted', ['email' => $data['email'], 'success' => $emailResult['success']]);
                
                $jobSeeker = JobSeeker::create([
                    'email' => $data['email'],
                    'name' => $data['name'] ?? explode('@', $data['email'])[0],
                    'is_active' => false, 
                    'verification_token' => $token,
                ]);
                Log::info('New job notification job seeker created', ['email' => $data['email']]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Please check your email to verify your subscription.',
                    'jobSeeker' => $jobSeeker,
                    'is_verified' => false,
                    'requires_verification' => true,
                ]);
            } else {
                // Update existing job seeker (subscriber) if needed
                $jobSeeker->update([
                    'is_active' => true, // Re-subscribing or already active
                    'name' => $data['name'] ?? $jobSeeker->name,
                    'verification_token' => null, // Clear token if re-subscribing
                ]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Successfully subscribed to job notifications',
                    'jobSeeker' => $jobSeeker,
                    'is_verified' => true,
                    'requires_verification' => false,
                ]);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error subscribing to job notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while subscribing',
            ], 500);
        }
    }
    
    /**
     * Unsubscribe from job notifications
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unsubscribe(Request $request)
    {
        try {
            $data = $request->validate([
                'email' => 'required|email',
            ]);
            
            $jobSeeker = JobSeeker::where('email', $data['email'])->first();
            
            if ($jobSeeker) {
                // Deactivate job seeker and their notification setups
                $jobSeeker->update(['is_active' => false]);
                $jobSeeker->notificationSetups()->update(['is_active' => false]);
                
                Log::info('User unsubscribed from job notifications', ['email' => $data['email']]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Successfully unsubscribed from job notifications',
                ]);
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Subscription not found',
            ], 404);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error unsubscribing from job notifications', [
                'error' => $e->getMessage(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while unsubscribing',
            ], 500);
        }
    }
    
    /**
     * Check for duplicate notification setup for a job seeker.
     *
     * @param int $jobSeekerId
     * @param array $categoryIds
     * @param array $recipientEmails
     * @param int|null $excludeSetupId
     * @return string|null Name of the duplicate setup if found, otherwise null
     */
        private function checkForDuplicateNotificationSetup(int $jobSeekerId, array $categoryIds, array $recipientEmails, ?int $excludeSetupId = null): ?string
    {
        $existingSetupsQuery = JobSeeker::findOrFail($jobSeekerId)
            ->notificationSetups()
            ->with('categories', 'recipients')
            ->where('is_active', true);
        if ($excludeSetupId) {
            $existingSetupsQuery->where('id', '!=', $excludeSetupId);
        }
        $existingSetups = $existingSetupsQuery->get();
        $normalizedRecipients = array_map('strtolower', $recipientEmails);
        
        Log::info('JobsController: Starting duplicate check', [
            'job_seeker_id' => $jobSeekerId,
            'new_categories' => $categoryIds,
            'new_recipients' => $recipientEmails,
            'existing_setups_count' => $existingSetups->count()
        ]);
        
        foreach ($existingSetups as $setup) {
            $existingCategoryIds = $setup->categories->pluck('id')->map(function($id) {
                return (int) $id;
            })->toArray();
            $existingRecipientEmails = $setup->recipients->pluck('email')->toArray();
            if (empty($existingRecipientEmails)) {
                $existingRecipientEmails = [$setup->jobSeeker->email];
            }
            $existingRecipientEmails = array_map('strtolower', $existingRecipientEmails);
            
            // Ensure new category IDs are also integers for proper comparison
            $newCategoryIds = array_map('intval', $categoryIds);
            
            // Exact duplicate detection: require identical categories AND identical recipients
            $isExactCategoryMatch = count($newCategoryIds) === count($existingCategoryIds) && 
                                   count(array_intersect($newCategoryIds, $existingCategoryIds)) === count($newCategoryIds);
            $isExactRecipientMatch = count($normalizedRecipients) === count($existingRecipientEmails) && 
                                    count(array_intersect($normalizedRecipients, $existingRecipientEmails)) === count($normalizedRecipients);
            
            Log::debug('JobsController: Comparing with existing setup', [
                'existing_setup_id' => $setup->id,
                'existing_setup_name' => $setup->name,
                'new_categories' => $newCategoryIds,
                'existing_categories' => $existingCategoryIds,
                'new_recipients' => $normalizedRecipients,
                'existing_recipients' => $existingRecipientEmails,
                'is_exact_category_match' => $isExactCategoryMatch,
                'is_exact_recipient_match' => $isExactRecipientMatch,
                'new_category_count' => count($newCategoryIds),
                'existing_category_count' => count($existingCategoryIds),
                'new_recipient_count' => count($normalizedRecipients),
                'existing_recipient_count' => count($existingRecipientEmails),
            ]);
            
            if ($isExactCategoryMatch && $isExactRecipientMatch) {
                Log::info('Exact duplicate notification setup detected', [
                    'job_seeker_id' => $jobSeekerId,
                    'duplicate_setup_id' => $setup->id,
                    'duplicate_setup_name' => $setup->name,
                    'is_exact_category_match' => $isExactCategoryMatch,
                    'is_exact_recipient_match' => $isExactRecipientMatch,
                    'new_categories' => $newCategoryIds,
                    'existing_categories' => $existingCategoryIds,
                    'new_recipients' => $recipientEmails,
                    'existing_recipients' => $existingRecipientEmails,
                ]);
                return $setup->name;
            }
        }
        
        Log::info('JobsController: No exact duplicate found');
        return null;
    }
    
    /**
     * Store a new notification setup
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeNotificationSetup(Request $request): JsonResponse
    {
        Log::info('JobsController: storeNotificationSetup method entry.');
        Log::debug('JobsController: storeNotificationSetup request data.', $request->all());

        try {
            $validatedData = $request->validate([
                'job_seeker_id' => 'required|integer|exists:job_seekers,id',
                'name' => 'required|string|max:255',
                'provider_name' => 'required|string|in:acbar,jobs.af,all',
                'categories' => 'required|array|min:1',
                'categories.*' => 'integer|exists:provider_job_categories,id',
                'recipients' => 'required|array|min:1',
                'recipients.*.email' => 'required|email',
                'recipients.*.name' => 'nullable|string|max:255',
                'bypass_duplicate_check' => 'sometimes|boolean',
                'receive_push_notifications' => 'sometimes|boolean',
            ]);
            Log::info('JobsController: storeNotificationSetup validation passed.');
        } catch (ValidationException $e) {
            Log::error('JobsController: storeNotificationSetup validation failed.', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['success' => false, 'message' => 'Validation failed.', 'errors' => $e->errors()], 422);
        }

        $jobSeekerId = (int) $validatedData['job_seeker_id'];
        $categoryIds = $validatedData['categories'];
        $recipientEmailsFromRequest = array_column($validatedData['recipients'], 'email');
        
        // Check if this is the user's first notification setup
        $existingSetupsCount = JobNotificationSetup::where('job_seeker_id', $jobSeekerId)->count();
        $isFirstSetup = $existingSetupsCount === 0;
        Log::info('JobsController: Checked existing setups count.', [
            'job_seeker_id' => $jobSeekerId,
            'existing_setups_count' => $existingSetupsCount,
            'is_first_setup' => $isFirstSetup
        ]);
       
        // Check for duplicate setup before creating a new one (unless bypassed)
        $bypassDuplicateCheck = $validatedData['bypass_duplicate_check'] ?? false;
        if (!$bypassDuplicateCheck) {
            $duplicateName = $this->checkForDuplicateNotificationSetup($jobSeekerId, $categoryIds, $recipientEmailsFromRequest);
            
            if ($duplicateName) {
                Log::warning('JobsController: Attempt to create a duplicate notification setup.', [
                    'job_seeker_id' => $jobSeekerId,
                    'categories' => $categoryIds,
                    'recipients' => $recipientEmailsFromRequest,
                    'existing_setup_name' => $duplicateName
                ]);
                return response()->json([
                    'success' => false,
                    'message' => "A similar notification setup named '{$duplicateName}' already exists. Please modify your selection or name to create a unique setup.",
                    'allow_override' => true,
                    'duplicate_setup_name' => $duplicateName
                ], 409); // 409 Conflict
            }
        } else {
            Log::info('JobsController: Duplicate check bypassed by user request.', [
                'job_seeker_id' => $jobSeekerId,
                'setup_name' => $validatedData['name']
            ]);
        }
    
        DB::beginTransaction();
        try {
            $setup = new JobNotificationSetup([
                'job_seeker_id' => $jobSeekerId,
                'name' => $validatedData['name'],
                'category_count' => count($categoryIds), // Save category count
                'is_active' => true, // New setups are active by default
                'last_notified_at' => null, // New setup, not notified yet
                'receive_push_notifications' => $validatedData['receive_push_notifications'] ?? false,
            ]);
            $setup->save();
            Log::info('JobsController: Notification setup core details saved.', ['setup_id' => $setup->id, 'name' => $setup->name, 'category_count' => $setup->category_count]);

            // Associate provider categories
            $setup->providerCategories()->sync($categoryIds);
            Log::info('JobsController: Provider categories synced for setup.', ['setup_id' => $setup->id, 'provider_category_ids' => $categoryIds]);

            // Also sync legacy categories for backward compatibility
            $canonicalCategoryIds = ProviderJobCategory::whereIn('id', $categoryIds)
                ->whereNotNull('canonical_category_id')
                ->pluck('canonical_category_id')
                ->unique()
                ->toArray();
            $setup->categories()->sync($canonicalCategoryIds);
            Log::info('JobsController: Legacy categories synced for setup.', ['setup_id' => $setup->id, 'canonical_category_ids' => $canonicalCategoryIds]);

            // Create or update recipients and associate them
            $recipientModels = [];
            foreach ($validatedData['recipients'] as $recipientData) {
                Log::debug('JobsController: Processing recipient for store.', ['setup_id' => $setup->id, 'recipient_data' => $recipientData]);
                $recipientEmailModel = JobNotificationRecipientEmail::firstOrCreate(
                    ['email' => strtolower(trim($recipientData['email']))],
                    ['name' => $recipientData['name'] ?? null]
                );

                $recipient = JobNotificationRecipient::firstOrCreate(
                    ['setup_id' => $setup->id, 'recipient_email_id' => $recipientEmailModel->id],
                    [
                        'email' => $recipientEmailModel->email, // Store email directly for convenience
                        'name' => $recipientData['name'] ?? null, 
                        'is_active' => true
                    ]
                );
                $recipientModels[] = $recipient;
                Log::info('JobsController: Recipient linked to setup.', ['setup_id' => $setup->id, 'recipient_id' => $recipient->id, 'email' => $recipient->email]);
            }
            Log::info('JobsController: All recipients processed for setup.', ['setup_id' => $setup->id, 'recipient_count' => count($recipientModels)]);

            // Persist recipients as personal contacts
            $this->persistRecipientsAsPersonalContacts($jobSeekerId, $validatedData['recipients']);
            Log::info('JobsController: Recipients persisted as personal contacts.', ['setup_id' => $setup->id, 'job_seeker_id' => $jobSeekerId]);

            DB::commit();
            Log::info('JobsController: Notification setup created successfully.', ['setup_id' => $setup->id]);

            // Dispatch a job to send initial notifications (e.g., jobs from last 5 days)
            // Ensure JobService is injected or resolved
            $jobService = app(JobService::class);
            $jobService->sendInitialNotificationForSetup($setup);
            Log::info('JobsController: Dispatched initial notification job for setup.', ['setup_id' => $setup->id]);

            return response()->json([
                'success' => true,
                'message' => 'Notification setup created successfully!',
                'setup' => $setup->load('categories', 'recipients.recipientEmail'), // Load relations for response
                'is_first_setup' => $isFirstSetup // Include whether this was the first setup
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('JobsController: Error storing notification setup.', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to create notification setup: ' . $e->getMessage()
            ], 500);
        }
    }
    


    /**
     * Persist recipient emails as personal contacts for the job seeker
     * 
     * @param int $jobSeekerId
     * @param array $recipients
     * @return void
     */
    private function persistRecipientsAsPersonalContacts(int $jobSeekerId, array $recipients): void
    {
        Log::info('JobsController: persistRecipientsAsPersonalContacts method entry.', [
            'job_seeker_id' => $jobSeekerId,
            'recipients_count' => count($recipients)
        ]);

        foreach ($recipients as $recipientData) {
            $email = strtolower(trim($recipientData['email']));
            
            Log::debug('JobsController: Processing recipient for personal contact persistence.', [
                'job_seeker_id' => $jobSeekerId,
                'email' => $email
            ]);

            try {
                // First ensure the recipient email exists
                $recipientEmailModel = JobNotificationRecipientEmail::firstOrCreate(
                    ['email' => $email],
                    ['name' => $recipientData['name'] ?? null]
                );

                // Check if personal contact already exists for this job seeker and email
                $existingPersonalContact = JobSeekerPersonalContact::where('job_seeker_id', $jobSeekerId)
                    ->where('recipient_email_id', $recipientEmailModel->id)
                    ->first();

                if (!$existingPersonalContact) {
                    // Create new personal contact
                    $personalContact = JobSeekerPersonalContact::create([
                        'job_seeker_id' => $jobSeekerId,
                        'recipient_email_id' => $recipientEmailModel->id,
                        'display_name' => $recipientData['name'] ?? null,
                        'notes' => 'Added from notification setup',
                    ]);

                    Log::info('JobsController: Created new personal contact.', [
                        'job_seeker_id' => $jobSeekerId,
                        'email' => $email,
                        'personal_contact_id' => $personalContact->id
                    ]);
                } else {
                    // Update existing personal contact if name is provided and different
                    if (isset($recipientData['name']) && 
                        $recipientData['name'] && 
                        $existingPersonalContact->display_name !== $recipientData['name']) {
                        
                        $existingPersonalContact->display_name = $recipientData['name'];
                        $existingPersonalContact->save();

                        Log::info('JobsController: Updated existing personal contact display name.', [
                            'job_seeker_id' => $jobSeekerId,
                            'email' => $email,
                            'personal_contact_id' => $existingPersonalContact->id,
                            'new_display_name' => $recipientData['name']
                        ]);
                    } else {
                        Log::debug('JobsController: Personal contact already exists, no update needed.', [
                            'job_seeker_id' => $jobSeekerId,
                            'email' => $email,
                            'personal_contact_id' => $existingPersonalContact->id
                        ]);
                    }
                }
            } catch (\Exception $e) {
                Log::error('JobsController: Error persisting recipient as personal contact.', [
                    'job_seeker_id' => $jobSeekerId,
                    'email' => $email,
                    'error' => $e->getMessage()
                ]);
                // Continue processing other recipients
            }
        }

        Log::info('JobsController: Finished persisting recipients as personal contacts.', [
            'job_seeker_id' => $jobSeekerId,
            'recipients_processed' => count($recipients)
        ]);
    }
    
    /**
     * Delete a notification setup
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteNotificationSetup(Request $request, $id): JsonResponse
    {
        Log::info('JobsController: deleteNotificationSetup method entry.', ['setup_id' => $id]);
        try {
            $jobSeekerId = Auth::guard('job_seeker')->id();
            if (!$jobSeekerId) {
                Log::warning('JobsController: deleteNotificationSetup - Unauthenticated access attempt.');
                return response()->json(['success' => false, 'message' => 'Unauthenticated.'], 401);
            }

            $setup = JobNotificationSetup::where('id', $id)
                                       ->where('job_seeker_id', $jobSeekerId)
                                       ->firstOrFail();
            
            Log::debug('JobsController: Found notification setup for deletion.', ['setup_id' => $setup->id, 'job_seeker_id' => $jobSeekerId]);

            DB::beginTransaction();
            // Delete related recipients first to avoid foreign key constraints if any are set up that way.
            $setup->recipients()->delete();
            Log::info('JobsController: Deleted recipients for setup.', ['setup_id' => $setup->id]);
            // Then delete the setup itself.
            $setup->delete();
            DB::commit();

            // Check if there are any remaining setups for this job seeker
            $remainingSetups = JobNotificationSetup::where('job_seeker_id', $jobSeekerId)->count();
            
            Log::info('JobsController: Notification setup deleted successfully.', [
                'setup_id' => $id, 
                'job_seeker_id' => $jobSeekerId,
                'remaining_setups' => $remainingSetups
            ]);
            
            return response()->json([
                'success' => true, 
                'message' => 'Notification setup deleted successfully.',
                'remaining_setups' => $remainingSetups,
                'no_setups_left' => $remainingSetups === 0
            ]);

        } catch (ModelNotFoundException $e) {
            Log::warning('JobsController: Notification setup not found or not owned by user for deletion.', [
                'setup_id' => $id,
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Notification setup not found or you do not have permission to delete it.'], 404);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('JobsController: Error deleting notification setup.', [
                'setup_id' => $id,
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to delete notification setup: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Bulk delete notification setups
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkDeleteNotificationSetup(Request $request): JsonResponse
    {
        Log::info('JobsController: bulkDeleteNotificationSetup method entry.');
        
        try {
            $jobSeekerId = Auth::guard('job_seeker')->id();
            if (!$jobSeekerId) {
                Log::warning('JobsController: bulkDeleteNotificationSetup - Unauthenticated access attempt.');
                return response()->json(['success' => false, 'message' => 'Unauthenticated.'], 401);
            }

            $validatedData = $request->validate([
                'setup_ids' => 'required|array|min:1',
                'setup_ids.*' => 'integer|exists:job_notification_setups,id',
            ]);
            
            $setupIds = $validatedData['setup_ids'];
            Log::info('JobsController: bulkDeleteNotificationSetup validation passed.', ['setup_ids' => $setupIds, 'job_seeker_id' => $jobSeekerId]);

            // Verify all setups belong to the authenticated user
            $setups = JobNotificationSetup::where('job_seeker_id', $jobSeekerId)
                                        ->whereIn('id', $setupIds)
                                        ->get();
            
            if ($setups->count() !== count($setupIds)) {
                Log::warning('JobsController: bulkDeleteNotificationSetup - Some setups not found or not owned by user.', [
                    'requested_ids' => $setupIds,
                    'found_ids' => $setups->pluck('id')->toArray(),
                    'job_seeker_id' => $jobSeekerId
                ]);
                return response()->json(['success' => false, 'message' => 'Some notification setups not found or you do not have permission to delete them.'], 404);
            }

            DB::beginTransaction();
            
            $deletedCount = 0;
            foreach ($setups as $setup) {
                try {
                    // Delete related recipients first
                    $setup->recipients()->delete();
                    // Delete the setup
                    $setup->delete();
                    $deletedCount++;
                    Log::debug('JobsController: Deleted notification setup.', ['setup_id' => $setup->id]);
                } catch (\Exception $e) {
                    Log::error('JobsController: Error deleting individual setup during bulk operation.', [
                        'setup_id' => $setup->id,
                        'error' => $e->getMessage()
                    ]);
                    // Continue with other setups, don't fail the entire operation
                }
            }
            
            DB::commit();

            Log::info('JobsController: Bulk delete notification setups completed successfully.', [
                'deleted_count' => $deletedCount,
                'requested_count' => count($setupIds),
                'job_seeker_id' => $jobSeekerId
            ]);
            
            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deletedCount} notification setup(s).",
                'deleted_count' => $deletedCount
            ]);

        } catch (ValidationException $e) {
            Log::error('JobsController: bulkDeleteNotificationSetup validation failed.', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['success' => false, 'message' => 'Validation failed.', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('JobsController: Error during bulk delete notification setups.', [
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to delete notification setups: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get a notification setup by ID
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNotificationSetup(Request $request, $id): JsonResponse
    {
        Log::info('JobsController: getNotificationSetup method entry.', ['setup_id' => $id]);
        try {
            $jobSeekerId = Auth::guard('job_seeker')->id();
            if (!$jobSeekerId) {
                Log::warning('JobsController: getNotificationSetup - Unauthenticated access attempt.');
                return response()->json(['success' => false, 'message' => 'Unauthenticated.'], 401);
            }

            $setup = JobNotificationSetup::with(['categories', 'recipients.recipientEmail']) // Eager load recipientEmail relation from JobNotificationRecipient
                ->where('job_seeker_id', $jobSeekerId)
                ->findOrFail($id);
            
            Log::info('JobsController: Notification setup retrieved successfully.', ['setup_id' => $setup->id, 'job_seeker_id' => $jobSeekerId]);
            
            // Transform recipients to include email directly for frontend convenience
            $transformedSetup = $setup->toArray();
            $transformedSetup['recipients'] = $setup->recipients->map(function ($recipient) {
                Log::debug('JobsController: Transforming recipient for getNotificationSetup response.', ['recipient_id' => $recipient->id, 'email' => $recipient->email]);
                return [
                    'id' => $recipient->id,
                    'setup_id' => $recipient->setup_id,
                    'recipient_email_id' => $recipient->recipient_email_id,
                    'email' => $recipient->email, // email is already on JobNotificationRecipient
                    'name' => $recipient->name,
                    'is_active' => $recipient->is_active,
                    'created_at' => $recipient->created_at,
                    'updated_at' => $recipient->updated_at,
                    // 'recipient_email' is not needed if 'email' is directly available
                ];
            })->toArray();


            return response()->json([
                'success' => true,
                'setup' => $transformedSetup
            ]);
        } catch (ModelNotFoundException $e) {
            Log::warning('JobsController: Notification setup not found or not owned by user.', [
                'setup_id' => $id,
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Notification setup not found or you do not have permission to access it.'], 404);
        } catch (\Exception $e) {
            Log::error('JobsController: Error getting notification setup.', [
                'setup_id' => $id,
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the notification setup.'
            ], 500);
        }
    }
    
    /**
     * Update a notification setup
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateNotificationSetup(Request $request, $id): JsonResponse
    {
        Log::info("JobsController: updateNotificationSetup method entry. ID: {$id}");
        Log::debug('JobsController: updateNotificationSetup request data.', $request->all());

        try {
            $setup = JobNotificationSetup::findOrFail($id);
        } catch (ModelNotFoundException $e) {
            Log::error('JobsController: updateNotificationSetup - Setup not found.', ['setup_id' => $id, 'error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Notification setup not found.'], 404);
        }
        
        $jobSeeker = Auth::guard('job_seeker')->user();
        if (!$jobSeeker || $setup->job_seeker_id !== $jobSeeker->id) {
            Log::warning('JobsController: updateNotificationSetup - Unauthorized attempt.', [
                'setup_id' => $id,
                'setup_owner_id' => $setup->job_seeker_id,
                'attempted_by_job_seeker_id' => $jobSeeker ? $jobSeeker->id : 'Guest'
            ]);
            return response()->json(['success' => false, 'message' => 'Unauthorized to update this setup.'], 403);
        }


        try {
            $validatedData = $request->validate([
                'job_seeker_id' => 'required|integer|exists:job_seekers,id', // Should match authenticated user
                'name' => 'sometimes|required|string|max:255',
                'provider_name' => 'sometimes|string|in:acbar,jobs.af,all',
                'categories' => 'sometimes|required|array|min:1',
                'categories.*' => 'integer|exists:provider_job_categories,id',
                'recipients' => 'sometimes|required|array|min:1',
                'recipients.*.email' => 'required|email',
                'recipients.*.name' => 'nullable|string|max:255',
                'is_active' => 'sometimes|boolean',
                'bypass_duplicate_check' => 'sometimes|boolean',
                'receive_push_notifications' => 'sometimes|boolean',
            ]);
            Log::info('JobsController: updateNotificationSetup validation passed.', ['setup_id' => $id]);
        } catch (ValidationException $e) {
            Log::error('JobsController: updateNotificationSetup validation failed.', [
                'setup_id' => $id,
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['success' => false, 'message' => 'Validation failed.', 'errors' => $e->errors()], 422);
        }
        
        if ((int)$validatedData['job_seeker_id'] !== $jobSeeker->id) {
            Log::error('JobsController: updateNotificationSetup - Mismatch in job_seeker_id.', [
                'setup_id' => $id,
                'validated_job_seeker_id' => $validatedData['job_seeker_id'],
                'authenticated_job_seeker_id' => $jobSeeker->id
            ]);
            return response()->json(['success' => false, 'message' => 'Invalid job seeker ID provided.'], 400);
        }


        $categoryIds = $validatedData['categories'] ?? $setup->categories->pluck('id')->toArray();
        $recipientEmailsFromRequest = isset($validatedData['recipients']) ? array_column($validatedData['recipients'], 'email') : $setup->recipients->pluck('email')->toArray();

        // Check for duplicate setup before updating (unless bypassed)
        $bypassDuplicateCheck = $validatedData['bypass_duplicate_check'] ?? false;
        if (!$bypassDuplicateCheck) {
            $duplicateName = $this->checkForDuplicateNotificationSetup($jobSeeker->id, $categoryIds, $recipientEmailsFromRequest, $setup->id);
            if ($duplicateName) {
                Log::warning('JobsController: Attempt to update to a duplicate notification setup.', [
                    'setup_id_to_update' => $setup->id,
                    'job_seeker_id' => $jobSeeker->id,
                    'categories' => $categoryIds,
                    'recipients' => $recipientEmailsFromRequest,
                    'existing_setup_name' => $duplicateName
                ]);
                return response()->json([
                    'success' => false,
                    'message' => "A similar notification setup named '{$duplicateName}' already exists. Please modify your selection or name to create a unique setup.",
                    'allow_override' => true,
                    'duplicate_setup_name' => $duplicateName
                ], 409); // 409 Conflict
            }
        } else {
            Log::info('JobsController: Duplicate check bypassed during update.', [
                'setup_id' => $setup->id,
                'job_seeker_id' => $jobSeeker->id
            ]);
        }

        DB::beginTransaction();
        try {
            $setup->name = $validatedData['name'] ?? $setup->name;
            $setup->is_active = $validatedData['is_active'] ?? $setup->is_active;
            $setup->receive_push_notifications = $validatedData['receive_push_notifications'] ?? $setup->receive_push_notifications;
            $setup->category_count = count($categoryIds); // Update category count
            $setup->save();
            Log::info('JobsController: Notification setup core details updated.', ['setup_id' => $setup->id, 'name' => $setup->name, 'category_count' => $setup->category_count, 'is_active' => $setup->is_active]);

            // Sync categories if provided
            if (isset($validatedData['categories'])) {
                $setup->categories()->sync($categoryIds);
                Log::info('JobsController: Categories synced for updated setup.', ['setup_id' => $setup->id, 'category_ids' => $categoryIds]);
            }

            // Handle recipients update if provided
            if (isset($validatedData['recipients'])) {
                $recipientEmailsFromRequest = array_column($validatedData['recipients'], 'email');
                $recipientDataMap = collect($validatedData['recipients'])->keyBy(fn($item) => strtolower(trim($item['email'])));

                // Get current recipient emails for this setup
                $currentRecipientEmails = $setup->recipients()->pluck('email')->map('strtolower')->all();
                
                Log::debug('JobsController: Current vs New Recipients for Update', [
                    'setup_id' => $setup->id,
                    'current_emails' => $currentRecipientEmails,
                    'new_emails_from_request' => $recipientEmailsFromRequest
                ]);

                // Emails to add: in new but not in current
                $emailsToAdd = array_diff($recipientEmailsFromRequest, $currentRecipientEmails);
                // Emails to remove: in current but not in new
                $emailsToRemove = array_diff($currentRecipientEmails, $recipientEmailsFromRequest);

                Log::debug('JobsController: Recipient Emails to Add/Remove', [
                    'setup_id' => $setup->id,
                    'emails_to_add' => $emailsToAdd,
                    'emails_to_remove' => $emailsToRemove
                ]);

                // Remove recipients no longer in the list
                if (!empty($emailsToRemove)) {
                    JobNotificationRecipient::where('setup_id', $setup->id)
                        ->whereIn('email', $emailsToRemove)
                        ->delete();
                    Log::info('JobsController: Recipients removed from setup.', ['setup_id' => $setup->id, 'removed_emails' => $emailsToRemove]);
                }

                // Add new recipients
                foreach ($emailsToAdd as $email) {
                    $email = strtolower(trim($email));
                    $recipientDetails = $recipientDataMap->get($email);
                    if ($recipientDetails) {
                        $recipientEmailModel = JobNotificationRecipientEmail::firstOrCreate(
                            ['email' => $email],
                            ['name' => $recipientDetails['name'] ?? null]
                        );
                        JobNotificationRecipient::create([
                            'setup_id' => $setup->id,
                            'recipient_email_id' => $recipientEmailModel->id,
                            'email' => $email, // Store email directly
                            'name' => $recipientDetails['name'] ?? null,
                            'is_active' => true,
                        ]);
                        Log::info('JobsController: Recipient added to setup.', ['setup_id' => $setup->id, 'added_email' => $email]);
                    } else {
                        Log::warning('JobsController: Recipient details not found in map for email to add.', ['setup_id' => $setup->id, 'email' => $email]);
                    }
                }
                
                // Update existing recipients (name might change)
                foreach ($setup->recipients()->whereIn('email', $recipientEmailsFromRequest)->get() as $existingRecipient) {
                    $emailKey = strtolower(trim($existingRecipient->email));
                    if ($recipientDataMap->has($emailKey)) {
                        $newName = $recipientDataMap->get($emailKey)['name'] ?? null;
                        if ($existingRecipient->name !== $newName) {
                            $existingRecipient->name = $newName;
                            $existingRecipient->save();
                            Log::info('JobsController: Recipient name updated.', ['setup_id' => $setup->id, 'recipient_id' => $existingRecipient->id, 'new_name' => $newName]);
                        }
                    }
                }

            }

            // Persist recipients as personal contacts (if recipients are being updated)
            if (isset($validatedData['recipients'])) {
                $this->persistRecipientsAsPersonalContacts($jobSeeker->id, $validatedData['recipients']);
                Log::info('JobsController: Recipients persisted as personal contacts during update.', ['setup_id' => $setup->id, 'job_seeker_id' => $jobSeeker->id]);
            }

            DB::commit();
            Log::info('JobsController: Notification setup updated successfully.', ['setup_id' => $setup->id]);
            return response()->json([
                'success' => true,
                'message' => 'Notification setup updated successfully!',
                'setup' => $setup->fresh()->load('categories', 'recipients.recipientEmail')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('JobsController: Error updating notification setup.', [
                'setup_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update notification setup: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display recipients management page
     * 
     * @return \Illuminate\View\View
     */
public function recipients()
     {
        return view('jobseeker::jobs.recipients');
     }

    /**
     * Get personal contacts as JSON for datatable (Personal Contacts Hub)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecipients(Request $request)
    {
        $draw = $request->get('draw');
        $start = $request->get('start', 0);
        $length = $request->get('length', 10);
        $search = $request->get('search.value', '');
        
        $jobSeeker = Auth::guard('job_seeker')->user();
        if (!$jobSeeker) {
            return response()->json([
                'draw' => $draw,
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ]);
        }
        
        // Build query for personal contacts with email data
        $query = JobSeekerPersonalContact::with('recipientEmail')
            ->where('job_seeker_id', $jobSeeker->id);
        
        // Apply search if provided
        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('display_name', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('recipientEmail', function($subQ) use ($search) {
                      $subQ->where('email', 'like', "%{$search}%")
                           ->orWhere('name', 'like', "%{$search}%");
                  });
            });
        }
        
        // Count total records
        $totalRecords = $query->count();
        
        // Apply pagination
        $personalContacts = $query->skip($start)
                                 ->take($length)
                                 ->orderBy('created_at', 'desc')
                                 ->get();
        
        $data = [];
        foreach ($personalContacts as $contact) {
            $recipientEmail = $contact->recipientEmail;
            $displayName = $contact->display_name ?: $recipientEmail->name ?: '-';
            
            $data[] = [
                'id' => $contact->id,
                'email' => $recipientEmail->email,
                'name' => $displayName,
                'original_name' => $recipientEmail->name ?? '-',
                'display_name' => $contact->display_name ?? '-',
                'notes' => $contact->notes ?? '-',
                'phone' => '-', // Keeping for compatibility with existing table structure
                'created_at' => $contact->created_at->format('Y-m-d H:i'),
                'actions' => '<div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-primary edit-personal-contact" 
                                        data-id="'.$contact->id.'" title="Edit Personal Details">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-personal-contact" 
                                        data-id="'.$contact->id.'" title="Remove from Personal Contacts">
                                    <i class="fa fa-trash"></i>
                                </button>
                              </div>'
            ];
        }
        
        return response()->json([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }
    
    /**
     * Search recipients for Select2
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchRecipients(Request $request)
    {
        try {
            $jobSeeker = Auth::guard('job_seeker')->user();
            if (!$jobSeeker) {
                return response()->json([
                    'items' => [],
                    'pagination' => ['more' => false]
                ]);
            }

            $search = $request->get('q', '');
            $page = $request->get('page', 1);
            $perPage = 10;

            // Search through the job seeker's personal contacts
            $query = JobSeekerPersonalContact::with('recipientEmail')
                ->where('job_seeker_id', $jobSeeker->id);

            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('display_name', 'like', "%{$search}%")
                      ->orWhere('notes', 'like', "%{$search}%")
                      ->orWhereHas('recipientEmail', function($subQ) use ($search) {
                          $subQ->where('email', 'like', "%{$search}%")
                               ->orWhere('name', 'like', "%{$search}%");
                      });
                });
            }

            $total = $query->count();

            $contacts = $query->skip(($page - 1) * $perPage)
                             ->take($perPage)
                             ->orderBy('created_at', 'desc')
                             ->get();

            $items = [];
            foreach ($contacts as $contact) {
                $displayName = $contact->display_name ?: $contact->recipientEmail->name ?: '';
                $items[] = [
                    'id' => $contact->recipientEmail->email,
                    'email' => $contact->recipientEmail->email,
                    'name' => $displayName,
                    'text' => $contact->recipientEmail->email . ($displayName ? ' (' . $displayName . ')' : '')
                ];
            }

            return response()->json([
                'items' => $items,
                'pagination' => [
                    'more' => ($page * $perPage) < $total
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error searching recipients for job seeker', [
                'error' => $e->getMessage(),
                'job_seeker_id' => Auth::guard('job_seeker')->id()
            ]);

            return response()->json([
                'items' => [],
                'pagination' => ['more' => false]
            ]);
        }
    }
    
    /**
     * Store a new recipient
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeRecipient(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|unique:job_notification_recipients,email',
                'name' => 'nullable|string|max:255',
                'setup_id' => 'nullable|exists:job_notification_setups,id'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 422);
            }
            
            $recipient = new JobNotificationRecipient();
            $recipient->email = $request->email;
            $recipient->name = $request->name;
            $recipient->setup_id = $request->setup_id;
            $recipient->is_active = true;
            $recipient->save();
            
            Log::info('Created new job notification recipient', [
                'id' => $recipient->id,
                'email' => $recipient->email,
                'setup_id' => $recipient->setup_id
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Recipient added successfully',
                'recipient' => $recipient
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating job notification recipient', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the recipient: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Update a recipient
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateRecipient(Request $request, $id)
    {
        $recipient = JobNotificationRecipient::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:job_notification_recipients,email,' . $id,
            'name' => 'nullable|string|max:255',
            'is_active' => 'boolean'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }
        
        $recipient->email = $request->email;
        $recipient->name = $request->name;
        
        if ($request->has('is_active')) {
            $recipient->is_active = $request->is_active;
        }
        
        $recipient->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Recipient updated successfully',
            'recipient' => $recipient
        ]);
    }
    
    /**
     * Delete a recipient
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteRecipient($id)
    {
        $recipient = JobNotificationRecipient::findOrFail($id);
        $recipient->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Recipient deleted successfully'
        ]);
    }
    
    /**
     * Import recipients from Excel file
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function importRecipients(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls,csv|max:2048',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }
        
        try {
            Excel::import(new RecipientsImport, $request->file('file'));
            
            return response()->json([
                'success' => true,
                'message' => 'Recipients imported successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error importing recipients: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Error importing recipients: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download the template file for importing recipients
     */
    public function downloadRecipientsTemplate()
    {
        try {
            // Define the headers for the CSV file
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="recipients_template.csv"',
                'Pragma' => 'no-cache',
                'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
                'Expires' => '0'
            ];

            // Create the CSV content with headers
            $content = "name,email,status\n"; // Header row
            $content .= "John Doe,<EMAIL>,active\n"; // Example row
            $content .= "Jane Smith,<EMAIL>,active\n"; // Example row

            // Log the template download
            Log::info('User downloaded recipients import template', [
                'user_id' => auth()->id()
            ]);

            // Return the CSV file
            return response($content, 200, $headers);
        } catch (\Exception $e) {
            Log::error('Error downloading recipients template', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error downloading template: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a single recipient by ID
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecipient($id)
    {
        try {
            $recipient = JobNotificationRecipient::findOrFail($id);
            
            return response()->json([
                'success' => true,
                'recipient' => $recipient
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching recipient: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Recipient not found'
            ], 404);
        }
    }

    // ========== PERSONAL CONTACTS HUB METHODS ==========

    /**
     * Store a new personal contact
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storePersonalContact(Request $request): JsonResponse
    {
        try {
            $jobSeeker = Auth::guard('job_seeker')->user();
            if (!$jobSeeker) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 401);
            }

            $validator = Validator::make($request->all(), [
                'email' => 'required|email|max:255',
                'name' => 'nullable|string|max:255',
                'display_name' => 'nullable|string|max:255',
                'notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 422);
            }

            DB::beginTransaction();

            // First, ensure the email exists in job_notification_recipient_emails
            $recipientEmail = JobNotificationRecipientEmail::firstOrCreate(
                ['email' => $request->email],
                [
                    'name' => $request->name,
                    'is_active' => true,
                ]
            );

            // Update name if provided and current name is empty
            if ($request->name && !$recipientEmail->name) {
                $recipientEmail->name = $request->name;
                $recipientEmail->save();
            }

            // Check if this email is already in the JobSeeker's personal contacts
            $existingContact = JobSeekerPersonalContact::where('job_seeker_id', $jobSeeker->id)
                ->where('recipient_email_id', $recipientEmail->id)
                ->first();

            if ($existingContact) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'This email is already in your personal contacts'
                ], 422);
            }

            // Create the personal contact link
            $personalContact = JobSeekerPersonalContact::create([
                'job_seeker_id' => $jobSeeker->id,
                'recipient_email_id' => $recipientEmail->id,
                'display_name' => $request->display_name,
                'notes' => $request->notes,
            ]);

            DB::commit();

            Log::info('JobSeeker added new personal contact', [
                'job_seeker_id' => $jobSeeker->id,
                'personal_contact_id' => $personalContact->id,
                'email' => $request->email
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Contact added to your personal contacts successfully',
                'contact' => $personalContact->load('recipientEmail')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error adding personal contact', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'job_seeker_id' => Auth::guard('job_seeker')->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding the contact: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a personal contact
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePersonalContact(Request $request, int $id): JsonResponse
    {
        try {
            $jobSeeker = Auth::guard('job_seeker')->user();
            if (!$jobSeeker) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 401);
            }

            $personalContact = JobSeekerPersonalContact::where('id', $id)
                ->where('job_seeker_id', $jobSeeker->id)
                ->first();

            if (!$personalContact) {
                return response()->json([
                    'success' => false,
                    'message' => 'Personal contact not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'display_name' => 'nullable|string|max:255',
                'notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 422);
            }

            $personalContact->update([
                'display_name' => $request->display_name,
                'notes' => $request->notes,
            ]);

            Log::info('JobSeeker updated personal contact', [
                'job_seeker_id' => $jobSeeker->id,
                'personal_contact_id' => $personalContact->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Personal contact updated successfully',
                'contact' => $personalContact->load('recipientEmail')
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating personal contact', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'job_seeker_id' => Auth::guard('job_seeker')->id(),
                'contact_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the contact: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a personal contact
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyPersonalContact(int $id): JsonResponse
    {
        try {
            $jobSeeker = Auth::guard('job_seeker')->user();
            if (!$jobSeeker) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 401);
            }

            $personalContact = JobSeekerPersonalContact::where('id', $id)
                ->where('job_seeker_id', $jobSeeker->id)
                ->first();

            if (!$personalContact) {
                return response()->json([
                    'success' => false,
                    'message' => 'Personal contact not found'
                ], 404);
            }

            $email = $personalContact->recipientEmail->email ?? 'Unknown';
            $personalContact->delete();

            Log::info('JobSeeker removed personal contact', [
                'job_seeker_id' => $jobSeeker->id,
                'personal_contact_id' => $id,
                'email' => $email
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Contact removed from your personal contacts successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting personal contact', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'job_seeker_id' => Auth::guard('job_seeker')->id(),
                'contact_id' => $id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while removing the contact: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a single personal contact by ID
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPersonalContact(int $id): JsonResponse
    {
        try {
            $jobSeeker = Auth::guard('job_seeker')->user();
            if (!$jobSeeker) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 401);
            }

            $personalContact = JobSeekerPersonalContact::with('recipientEmail')
                ->where('id', $id)
                ->where('job_seeker_id', $jobSeeker->id)
                ->first();

            if (!$personalContact) {
                return response()->json([
                    'success' => false,
                    'message' => 'Personal contact not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'contact' => [
                    'id' => $personalContact->id,
                    'email' => $personalContact->recipientEmail->email,
                    'original_name' => $personalContact->recipientEmail->name,
                    'display_name' => $personalContact->display_name,
                    'notes' => $personalContact->notes,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching personal contact', [
                'error' => $e->getMessage(),
                'contact_id' => $id,
                'job_seeker_id' => Auth::guard('job_seeker')->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Personal contact not found'
            ], 404);
        }
    }

    /**
     * Search personal contacts for Select2 (for notification setup)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchPersonalContacts(Request $request): JsonResponse
    {
        try {
            $jobSeeker = Auth::guard('job_seeker')->user();
            if (!$jobSeeker) {
                return response()->json([
                    'items' => [],
                    'pagination' => ['more' => false]
                ]);
            }

            // Validate that the requested job_seeker_id matches the authenticated user (if provided)
            $requestedJobSeekerId = $request->get('job_seeker_id');
            if ($requestedJobSeekerId && (int)$requestedJobSeekerId !== $jobSeeker->id) {
                Log::warning('Unauthorized attempt to access personal contacts', [
                    'authenticated_job_seeker_id' => $jobSeeker->id,
                    'requested_job_seeker_id' => $requestedJobSeekerId,
                    'ip' => $request->ip()
                ]);
                
                return response()->json([
                    'items' => [],
                    'pagination' => ['more' => false]
                ], 403);
            }

            $search = $request->get('q', '');
            $page = $request->get('page', 1);
            $perPage = 10;

            $query = JobSeekerPersonalContact::with('recipientEmail')
                ->where('job_seeker_id', $jobSeeker->id);

            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('display_name', 'like', "%{$search}%")
                      ->orWhere('notes', 'like', "%{$search}%")
                      ->orWhereHas('recipientEmail', function($subQ) use ($search) {
                          $subQ->where('email', 'like', "%{$search}%")
                               ->orWhere('name', 'like', "%{$search}%");
                      });
                });
            }

            $total = $query->count();

            $contacts = $query->skip(($page - 1) * $perPage)
                             ->take($perPage)
                             ->orderBy('created_at', 'desc')
                             ->get();

            $items = [];
            foreach ($contacts as $contact) {
                $displayName = $contact->display_name ?: $contact->recipientEmail->name ?: '';
                $items[] = [
                    'id' => $contact->recipientEmail->email,
                    'email' => $contact->recipientEmail->email,
                    'name' => $displayName,
                    'text' => $contact->recipientEmail->email . ($displayName ? ' (' . $displayName . ')' : '')
                ];
            }

            return response()->json([
                'items' => $items,
                'pagination' => [
                    'more' => ($page * $perPage) < $total
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error searching personal contacts', [
                'error' => $e->getMessage(),
                'job_seeker_id' => Auth::guard('job_seeker')->id()
            ]);

            return response()->json([
                'items' => [],
                'pagination' => ['more' => false]
            ]);
        }
    }

    /**
     * Verify email subscription with token
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verifySubscription(Request $request)
    {
        if (!$request->hasValidSignature()) {
            return view('jobseeker::auth.invalid_token', ['message' => 'Invalid or expired verification link.']);
        }

        $jobSeeker = JobSeeker::where('email', $request->email)
                           ->where('verification_token', $request->token)
                           ->first();

        if (!$jobSeeker) {
            return view('jobseeker::auth.invalid_token', ['message' => 'Verification token not found or already used.']);
        }

        $jobSeeker->is_active = true;
        $jobSeeker->email_verified_at = now();
        $jobSeeker->verification_token = null;
        $jobSeeker->save();
        
        // Log successful verification
        Log::info('Job notification subscription verified', [
            'email' => $request->email,
            'job_seeker_id' => $jobSeeker->id
        ]);
        
        return redirect()->route('jobseeker.notifications')
            ->with('success', 'Your subscription has been verified! You will now receive job notifications.');
    }

    /**
     * Fetch jobs by category for AJAX modal (with pagination and search)
     *
     * @param \Illuminate\Http\Request $request
     * @param int $categoryId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJobsByCategory(Request $request, int $categoryId)
    {
        $perPage = (int) $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = (int) $request->input('page', 1);

        $query = \Modules\JobSeeker\Entities\Job::query()
            ->whereHas('categories', function ($q) use ($categoryId) {
                $q->where('job_categories.id', $categoryId);
            });

        // Only include published jobs (optionally filter by recentness if needed)
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('position', 'like', "%$search%")
                  ->orWhere('description', 'like', "%$search%")
                  ->orWhere('company_name', 'like', "%$search%")
                  ->orWhere('locations', 'like', "%$search%")
                  ->orWhere('job_summary', 'like', "%$search%")
                  ->orWhere('duties_responsibilities', 'like', "%$search%")
                  ->orWhere('job_requirements', 'like', "%$search%");
            });
        }

        $query->orderByDesc('publish_date');

        $jobs = $query->paginate($perPage, ['*'], 'page', $page);

        $results = $jobs->map(function ($job) {
            return [
                'id' => $job->id,
                'title' => $job->position,
                'slug' => $job->slug,
                'company_name' => $job->company_name,
                'posted_date' => $job->publish_date ? Carbon::parse($job->publish_date)->format('Y-m-d') : null,
                // Include both short preview and full description fields
                'description' => $job->description,
                'about_company' => $job->about_company,
                'job_summary' => $job->job_summary,
                'duties_responsibilities' => $job->duties_responsibilities,
                'job_requirements' => $job->job_requirements,
                'submission_guideline' => $job->submission_guideline
            ];
        });

        return response()->json([
            'data' => $results,
            'current_page' => $jobs->currentPage(),
            'last_page' => $jobs->lastPage(),
            'total' => $jobs->total(),
            'per_page' => $jobs->perPage(),
        ]);
    }

    /**
     * Display the job operations page.
     *
     * @return \Illuminate\View\View
     */
    public function showJobOperationsPage()
    {
        Log::info('JobsController: Displaying job operations page.');
        return view('jobseeker::jobs.operations');
    }

    /**
     * Execute the SyncJobsCommand.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function executeSyncJobsCommand(Request $request)
    {
        return $this->executeSyncCommand(
            'jobseeker:sync-jobs-af',
            'Jobs.af',
            'SyncJobsAfCommand'
        );
    }

    /**
     * Execute the SyncAcbarJobsCommand.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function executeAcbarSyncCommand(Request $request)
    {
        return $this->executeSyncCommand(
            'jobseeker:sync-acbar-jobs',
            'ACBAR.org',
            'SyncAcbarJobsCommand'
        );
    }

    /**
     * Execute a sync command and handle its response.
     *
     * @param string $command The Artisan command to execute
     * @param string $source The source name (e.g., 'Jobs.af', 'ACBAR.org')
     * @param string $commandName The name of the command for logging
     * @return \Illuminate\Http\JsonResponse
     */
private function executeSyncCommand(string $command, string $source, string $commandName): \Illuminate\Http\JsonResponse
 {
     Log::info("JobsController: Attempting to execute {$commandName}.");
     $startTime = microtime(true);
     
     try {
        $user = Auth::guard('job_seeker')->user();
        // if (!$user || !$user->is_active) {
        //     Log::warning("JobsController: Unauthorized attempt to execute {$commandName}.", [
        //         'user_id' => $user ? $user->id : 'guest',
        //         'is_active' => $user ? $user->is_active : false
        //     ]);
        //     return response()->json(['success' => false, 'message' => 'Unauthorized. Only active job seekers can perform this action.'], 403);
        // }

            $exitCode = \Illuminate\Support\Facades\Artisan::call($command);
            $output = \Illuminate\Support\Facades\Artisan::output();
            $executionTime = round((microtime(true) - $startTime), 2);

            // Filter out SMTP communication from output for cleaner display
            $cleanOutput = $this->filterSmtpOutput($output);

            // Extract statistics from the output if available
            $stats = $this->extractStatsFromOutput($output, $source);

            if ($exitCode === 0) {
                Log::info("JobsController: {$commandName} executed successfully.", [
                    'output' => $output,
                    'execution_time' => $executionTime,
                    'stats' => $stats
                ]);
                
                return response()->json([
                    'success' => true, 
                    'message' => "{$source} synchronization command executed successfully.",
                    'output' => $cleanOutput,
                    'stats' => $stats,
                    'execution_time_seconds' => $executionTime
                ]);
            } else {
                Log::error("JobsController: {$commandName} failed.", [
                    'exitCode' => $exitCode, 
                    'output' => $output,
                    'execution_time' => $executionTime
                ]);
                
                return response()->json([
                    'success' => false, 
                    'message' => "{$source} synchronization command failed to execute.",
                    'output' => $cleanOutput,
                    'exit_code' => $exitCode,
                    'execution_time_seconds' => $executionTime
                ], 500);
            }
        } catch (\Exception $e) {
            $executionTime = round((microtime(true) - $startTime), 2);
            
            Log::error("JobsController: Exception while executing {$commandName}.", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'execution_time' => $executionTime
            ]);
            
            return response()->json([
                'success' => false, 
                'message' => 'An error occurred: ' . $e->getMessage(),
                'execution_time_seconds' => $executionTime
            ], 500);
        }
    }

    /**
     * Extract statistics from command output
     *
     * @param string $output
     * @param string $source
     * @return array
     */
    private function extractStatsFromOutput(string $output, string $source): array
    {
        $stats = [];
        
        try {
            if ($source === 'ACBAR.org') {
                // Extract ACBAR statistics
                if (preg_match('/Created:\s*(\d+)/', $output, $matches)) {
                    $stats['jobs_created'] = (int) $matches[1];
                }
                if (preg_match('/Updated:\s*(\d+)/', $output, $matches)) {
                    $stats['jobs_updated'] = (int) $matches[1];
                }
                if (preg_match('/Errors:\s*(\d+)/', $output, $matches)) {
                    $stats['errors'] = (int) $matches[1];
                }
                if (preg_match('/Skipped \(no category mapping\):\s*(\d+)/', $output, $matches)) {
                    $stats['skipped_no_category_mapping'] = (int) $matches[1];
                }
                if (preg_match('/Categories processed:\s*(\d+)/', $output, $matches)) {
                    $stats['categories_processed'] = (int) $matches[1];
                }
                if (preg_match('/Total ACBAR jobs in database:\s*(\d+)/', $output, $matches)) {
                    $stats['total_jobs_in_database'] = (int) $matches[1];
                }
                if (preg_match('/Jobs published in last 7 days:\s*(\d+)/', $output, $matches)) {
                    $stats['recent_jobs_count'] = (int) $matches[1];
                }
                if (preg_match('/Execution time:\s*(\d+)\s*seconds/', $output, $matches)) {
                    $stats['execution_time_seconds'] = (int) $matches[1];
                }
                if (preg_match('/Peak memory usage:\s*([\d.]+)\s*MB/', $output, $matches)) {
                    $stats['peak_memory_mb'] = (float) $matches[1];
                }
            } elseif ($source === 'Jobs.af') {
                // Extract Jobs.af API statistics
                if (preg_match('/Jobs created:\s*(\d+)/', $output, $matches)) {
                    $stats['api_jobs_created'] = (int) $matches[1];
                }
                if (preg_match('/Jobs updated:\s*(\d+)/', $output, $matches)) {
                    $stats['api_jobs_updated'] = (int) $matches[1];
                }
                if (preg_match('/Jobs processed:\s*(\d+)/', $output, $matches)) {
                    $stats['api_jobs_processed'] = (int) $matches[1];
                }
                if (preg_match('/Non-English skipped:\s*(\d+)/', $output, $matches)) {
                    $stats['api_non_english_skipped'] = (int) $matches[1];
                }
                if (preg_match('/Location filtered:\s*(\d+)/', $output, $matches)) {
                    $stats['api_location_filtered'] = (int) $matches[1];
                }
                if (preg_match('/Missed jobs found:\s*(\d+)/', $output, $matches)) {
                    $stats['api_missed_jobs'] = (int) $matches[1];
                }
                if (preg_match('/New jobs for notification:\s*(\d+)/', $output, $matches)) {
                    $stats['api_new_jobs_for_notification'] = (int) $matches[1];
                }
                
                // Extract Jobs.af description statistics
                if (preg_match('/Total jobs processed:\s*(\d+)/', $output, $matches)) {
                    $stats['description_jobs_processed'] = (int) $matches[1];
                }
                if (preg_match('/Successfully updated:\s*(\d+)/', $output, $matches)) {
                    $stats['descriptions_updated'] = (int) $matches[1];
                }
                if (preg_match('/Already complete:\s*(\d+)/', $output, $matches)) {
                    $stats['descriptions_already_complete'] = (int) $matches[1];
                }
                if (preg_match('/Failed to update:\s*(\d+)/', $output, $matches)) {
                    $stats['descriptions_failed'] = (int) $matches[1];
                }
                if (preg_match('/Skipped \(no data\/slug\):\s*(\d+)/', $output, $matches)) {
                    $stats['descriptions_skipped'] = (int) $matches[1];
                }
                if (preg_match('/Network errors:\s*(\d+)/', $output, $matches)) {
                    $stats['network_errors'] = (int) $matches[1];
                }
                if (preg_match('/Total jobs\.af jobs in database:\s*(\d+)/', $output, $matches)) {
                    $stats['total_jobs_in_database'] = (int) $matches[1];
                }
                if (preg_match('/Jobs published in last 7 days:\s*(\d+)/', $output, $matches)) {
                    $stats['recent_jobs_count'] = (int) $matches[1];
                }
            }
            
            // Calculate totals
            if ($source === 'ACBAR.org') {
                $stats['total_processed'] = ($stats['jobs_created'] ?? 0) + ($stats['jobs_updated'] ?? 0);
                $stats['total_issues'] = ($stats['errors'] ?? 0) + ($stats['skipped_no_category_mapping'] ?? 0);
            } elseif ($source === 'Jobs.af') {
                $stats['total_api_processed'] = ($stats['api_jobs_created'] ?? 0) + ($stats['api_jobs_updated'] ?? 0);
                $stats['total_description_processed'] = ($stats['descriptions_updated'] ?? 0) + 
                                                       ($stats['descriptions_already_complete'] ?? 0) + 
                                                       ($stats['descriptions_failed'] ?? 0) + 
                                                       ($stats['descriptions_skipped'] ?? 0);
                $stats['total_api_filtered'] = ($stats['api_non_english_skipped'] ?? 0) + ($stats['api_location_filtered'] ?? 0);
            }
            
        } catch (\Exception $e) {
            Log::warning("JobsController: Error extracting stats from output", [
                'error' => $e->getMessage(),
                'output' => substr($output, 0, 500) // Only log first 500 chars to avoid log spam
            ]);
        }
        
        return $stats;
    }

    /**
     * Filter out SMTP communication from command output for cleaner display
     *
     * @param string $output
     * @return string
     */
    private function filterSmtpOutput(string $output): string
    {
        // Split output into lines
        $lines = explode("\n", $output);
        $filteredLines = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip SMTP communication lines
            if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} (SERVER|CLIENT) ->(SERVER|CLIENT):/', $line)) {
                continue;
            }
            
            // Skip lines that look like SMTP responses
            if (preg_match('/^(220|250|334|235|354|221)\s/', $line)) {
                continue;
            }
            
            // Skip lines with email headers and content
            if (preg_match('/^(Date:|To:|From:|Subject:|Message-ID:|X-Mailer:|MIME-Version:|Content-Type:|Content-Transfer-Encoding:)/', $line)) {
                continue;
            }
            
            // Skip HTML email content
            if (preg_match('/^(<html|<head|<body|<div|<style|<\/|&lt;|&gt;|&amp;)/', $line)) {
                continue;
            }
            
            // Skip boundary markers
            if (preg_match('/^--[a-zA-Z0-9_=]+/', $line)) {
                continue;
            }
            
            // Keep the line if it doesn't match SMTP patterns
            $filteredLines[] = $line;
        }
        
        return implode("\n", $filteredLines);
    }

    /**
     * Display the job seeker dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        $jobSeeker = Auth::guard('job_seeker')->user();
        return view('jobseeker::dashboard', compact('jobSeeker'));
    }

    /**
     * Get notification setup data for DataTables.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getNotificationSetupData(Request $request): JsonResponse
    {
        $startTime = microtime(true);
        $jobSeekerId = Auth::guard('job_seeker')->id();
        
        // Tracking counters for aggregated logging
        $processedRows = 0;
        $inactiveSetupCount = 0;
        $activeSetupCount = 0;
        $errorCount = 0;
        $processingTimes = [];
        
        Log::info('JobsController: ENTRY - Getting notification setup data for DataTables', [
            'job_seeker_id' => $jobSeekerId,
            'request_params' => $request->all(),
            'timestamp' => now()->toDateTimeString()
        ]);
        
        try {
            $query = JobNotificationSetup::with(['categories', 'recipients'])
                ->where('job_seeker_id', $jobSeekerId);
            
            // Only log query details in development or when debug is enabled
            Log::when(app()->environment('local', 'development') || config('app.debug'), function () use ($jobSeekerId, $query) {
                Log::debug('JobsController: Base query created for notification setups', [
                    'job_seeker_id' => $jobSeekerId,
                    'query_sql' => $query->toSql(),
                    'query_bindings' => $query->getBindings()
                ]);
            });
            
            $dataTablesResponse = DataTables::of($query)
                ->addColumn('notifications_sent', function (JobNotificationSetup $setup) use (&$processedRows) {
                    $processedRows++;
                    $sentCount = $setup->sent_count ?? 0;
                    
                    // Only log detailed information in development or debug mode
                    Log::when(app()->environment('local', 'development') || config('app.debug'), function () use ($setup, $sentCount) {
                        Log::debug('JobsController: Processing notifications_sent column', [
                            'setup_id' => $setup->id,
                            'sent_count' => $sentCount
                        ]);
                    });
                    
                    return $sentCount;
                })
                ->addColumn('actions', function (JobNotificationSetup $setup) {
                    // Only log in development or debug mode
                    Log::when(app()->environment('local', 'development') || config('app.debug'), function () use ($setup) {
                        Log::debug('JobsController: Processing actions column', [
                            'setup_id' => $setup->id
                        ]);
                    });
                    
                    return ''; // Actions are handled by DataTables render function
                })
                ->addColumn('activity_summary', function (JobNotificationSetup $setup) use (&$inactiveSetupCount, &$activeSetupCount, &$errorCount, &$processingTimes) {
                    $activityStartTime = microtime(true);
                    
                    // Only log entry in development or debug mode
                    Log::when(app()->environment('local', 'development') || config('app.debug'), function () use ($setup) {
                        Log::debug('JobsController: ENTRY - Processing activity_summary column', [
                            'setup_id' => $setup->id,
                            'setup_name' => $setup->name
                        ]);
                    });
                    
                    try {
                        // Get activity summary for this setup
                        $jobService = app(JobService::class);
                        $activitySummary = $jobService->getSetupActivitySummary($setup);
                        
                        $inactiveCategoriesCount = $activitySummary['inactive_categories_count'];
                        $totalCategoriesCount = $activitySummary['total_categories_count'];
                        
                        $activityProcessingTime = round((microtime(true) - $activityStartTime) * 1000, 2);
                        $processingTimes[] = $activityProcessingTime;
                        
                        if ($inactiveCategoriesCount > 0) {
                            $inactiveSetupCount++;
                            $result = [
                                'has_inactive' => true,
                                'inactive_count' => $inactiveCategoriesCount,
                                'total_count' => $totalCategoriesCount,
                                'message' => $inactiveCategoriesCount === 1 
                                    ? "1 category has not had recent job activity"
                                    : "{$inactiveCategoriesCount} categories have not had recent job activity"
                            ];
                            
                            // Only log warnings for inactive categories in production, detailed logs only in dev/debug
                            if (app()->environment('local', 'development') || config('app.debug')) {
                                Log::warning('JobsController: Setup has inactive categories', [
                                    'setup_id' => $setup->id,
                                    'setup_name' => $setup->name,
                                    'inactive_count' => $inactiveCategoriesCount,
                                    'total_count' => $totalCategoriesCount,
                                    'processing_time_ms' => $activityProcessingTime
                                ]);
                            }
                        } else {
                            $activeSetupCount++;
                            $result = [
                                'has_inactive' => false,
                                'inactive_count' => 0,
                                'total_count' => $totalCategoriesCount,
                                'message' => 'All categories are active'
                            ];
                            
                            // Only log active category details in development or debug mode
                            Log::when(app()->environment('local', 'development') || config('app.debug'), function () use ($setup, $totalCategoriesCount, $activityProcessingTime) {
                                Log::debug('JobsController: Setup has all active categories', [
                                    'setup_id' => $setup->id,
                                    'setup_name' => $setup->name,
                                    'total_count' => $totalCategoriesCount,
                                    'processing_time_ms' => $activityProcessingTime
                                ]);
                            });
                        }
                        
                        // Only log exit details in development or debug mode
                        Log::when(app()->environment('local', 'development') || config('app.debug'), function () use ($setup, $result, $activityProcessingTime) {
                            Log::debug('JobsController: EXIT - Completed activity_summary processing', [
                                'setup_id' => $setup->id,
                                'result' => $result,
                                'processing_time_ms' => $activityProcessingTime
                            ]);
                        });
                        
                        return $result;
                        
                    } catch (\Exception $e) {
                        $errorCount++;
                        $activityProcessingTime = round((microtime(true) - $activityStartTime) * 1000, 2);
                        $processingTimes[] = $activityProcessingTime;
                        
                        Log::error('JobsController: EXCEPTION - Error processing activity_summary', [
                            'setup_id' => $setup->id,
                            'setup_name' => $setup->name ?? 'Unknown',
                            'error' => $e->getMessage(),
                            'error_code' => $e->getCode(),
                            'file' => $e->getFile(),
                            'line' => $e->getLine(),
                            'processing_time_ms' => $activityProcessingTime
                        ]);
                        
                        // Return safe fallback data
                        return [
                            'has_inactive' => false,
                            'inactive_count' => 0,
                            'total_count' => 0,
                            'message' => 'Unable to check activity status'
                        ];
                    }
                })
                ->rawColumns(['categories', 'recipients', 'actions'])
                ->make(true);
            
            $totalExecutionTime = round((microtime(true) - $startTime) * 1000, 2);
            $avgProcessingTime = !empty($processingTimes) ? round(array_sum($processingTimes) / count($processingTimes), 2) : 0;
            $maxProcessingTime = !empty($processingTimes) ? max($processingTimes) : 0;
            
            // Aggregated summary log instead of per-row logs
            Log::info('JobsController: EXIT - Successfully generated DataTables response', [
                'job_seeker_id' => $jobSeekerId,
                'execution_time_ms' => $totalExecutionTime,
                'memory_usage_mb' => round(memory_get_usage() / 1024 / 1024, 2),
                'response_size_estimate' => strlen(json_encode($dataTablesResponse->getData())),
                'processing_summary' => [
                    'total_rows_processed' => $processedRows,
                    'active_setups' => $activeSetupCount,
                    'inactive_setups' => $inactiveSetupCount,
                    'errors_encountered' => $errorCount,
                    'avg_activity_processing_ms' => $avgProcessingTime,
                    'max_activity_processing_ms' => $maxProcessingTime
                ]
            ]);
            
            return $dataTablesResponse;
            
        } catch (\Exception $e) {
            $totalExecutionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            Log::error('JobsController: EXCEPTION - Error generating notification setup data', [
                'job_seeker_id' => $jobSeekerId,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'execution_time_ms' => $totalExecutionTime,
                'processing_summary' => [
                    'total_rows_processed' => $processedRows,
                    'active_setups' => $activeSetupCount,
                    'inactive_setups' => $inactiveSetupCount,
                    'errors_encountered' => $errorCount
                ],
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'error' => 'Unable to load notification setup data',
                'message' => 'An error occurred while processing your request. Please try again.'
            ], 500);
        }
    }

    /**
     * Check push notification status for the current job seeker.
     * Used by frontend to determine what prompts to show.
     *
     * @param Request $request
     * @return JsonResponse
     */
    /**
     * Get job categories for API
     *
     * @return JsonResponse
     */
    public function getCategories(): JsonResponse
    {
        try {
            Log::info('JobsController: ENTRY - Getting job categories for API');

            $categories = JobCategory::where('is_active', true)
                ->orderBy('name')
                ->get(['id', 'name', 'is_canonical', 'parent_id', 'is_archived']);

            Log::info('JobsController: Successfully retrieved job categories', [
                'total_categories' => $categories->count(),
                'canonical_count' => $categories->where('is_canonical', true)->count(),
                'archived_count' => $categories->where('is_archived', true)->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $categories,
                'message' => 'Categories retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('JobsController: EXCEPTION - Error retrieving job categories', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve categories',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    public function checkPushNotificationStatus(Request $request): JsonResponse
    {
        $jobSeekerId = Auth::guard('job_seeker')->id();
        
        Log::info('JobsController: Checking push notification status', [
            'job_seeker_id' => $jobSeekerId
        ]);
        
        try {
            // Check if user has any active notification setups with push notifications enabled
            $hasActivePushSetups = JobNotificationSetup::where('job_seeker_id', $jobSeekerId)
                ->where('is_active', true)
                ->where('receive_push_notifications', true)
                ->exists();
            
            // Check if user has any device tokens registered
            $jobSeeker = JobSeeker::find($jobSeekerId);
            $hasDeviceTokens = $jobSeeker && $jobSeeker->deviceTokens()->exists();
            
            // Get fresh device tokens count (used within last 30 days)
            $freshDeviceTokensCount = $jobSeeker ? $jobSeeker->deviceTokens()
                ->where('last_used_at', '>=', now()->subDays(30))
                ->count() : 0;
            
            $response = [
                'success' => true,
                'data' => [
                    'has_active_push_setups' => $hasActivePushSetups,
                    'has_device_tokens' => $hasDeviceTokens,
                    'fresh_device_tokens_count' => $freshDeviceTokensCount,
                    'job_seeker_id' => $jobSeekerId
                ]
            ];
            
            Log::info('JobsController: Push notification status checked successfully', [
                'job_seeker_id' => $jobSeekerId,
                'has_active_push_setups' => $hasActivePushSetups,
                'has_device_tokens' => $hasDeviceTokens,
                'fresh_device_tokens_count' => $freshDeviceTokensCount
            ]);
            
            return response()->json($response);
            
        } catch (\Exception $e) {
            Log::error('JobsController: Error checking push notification status', [
                'job_seeker_id' => $jobSeekerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Unable to check push notification status',
                'error' => 'An error occurred while checking your notification preferences.'
            ], 500);
        }
    }

    /**
     * Test the notification system by simulating job retrieval and sending notifications
     *
     * @param Request $request
     * @param int $setupId
     * @return JsonResponse
     */
    public function testNotificationSystem(Request $request, int $setupId): JsonResponse
    {
        try {
            Log::info('JobsController: ENTRY - Testing notification system', [
                'setup_id' => $setupId,
                'job_seeker_id' => Auth::guard('job_seeker')->id()
            ]);

            // Get the notification setup
            $setup = JobNotificationSetup::with(['categories', 'jobSeeker'])
                ->where('id', $setupId)
                ->where('job_seeker_id', Auth::guard('job_seeker')->id())
                ->where('is_active', true)
                ->first();

            if (!$setup) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification setup not found or not active'
                ], 404);
            }

            // Get category IDs from the setup
            $categoryIds = $setup->categories->pluck('id')->toArray();
            
            // Find recent jobs in these categories (last 7 days for testing)
            $testJobs = Job::whereHas('categories', function ($q) use ($categoryIds) {
                    $q->whereIn('job_categories.id', $categoryIds);
                })
                ->where('created_at', '>=', now()->subDays(7))
                ->where('is_active', true)
                ->limit(3) // Limit for testing
                ->get();

            if ($testJobs->isEmpty()) {
                // Create a mock job for testing if no real jobs found
                $mockJobData = [
                    'title' => 'Test Job Notification - ' . $setup->name,
                    'company_name' => 'Test Company',
                    'location' => 'Test Location',
                    'category_name' => $setup->categories->first()->name ?? 'Test Category',
                    'posted_date' => now()->format('Y-m-d H:i:s'),
                    'description' => 'This is a test notification for your job alert setup: ' . $setup->name
                ];
                
                $testResults = [
                    'email_sent' => false,
                    'push_sent' => false,
                    'mock_job_created' => true,
                    'job_data' => $mockJobData
                ];
            } else {
                $testResults = [
                    'jobs_found' => $testJobs->count(),
                    'jobs' => $testJobs->map(function($job) {
                        return [
                            'id' => $job->id,
                            'title' => $job->position, // use correct attribute name
                            'company_name' => $job->company_name,
                            'location' => $job->locations,
                            'posted_date' => $job->created_at->format('Y-m-d H:i:s')
                        ];
                    })
                ];
            }

            // Test email notification
            if ($setup->recipients && count($setup->recipients) > 0) {
                try {
                    $emailService = app(EmailService::class);
                    $recipientEmail = $setup->recipients[0]['email'] ?? null;
                    
                    if ($recipientEmail) {
                        $emailContent = [
                            'subject' => 'Test Job Alert - ' . $setup->name,
                            'body' => 'This is a test email for your job notification setup. If you are receiving this, your email notifications are working correctly.',
                            'setup_name' => $setup->name,
                            'job_count' => $testJobs->count()
                        ];

                        // Send test email
                        $emailResult = $emailService->sendEmail(
                            $recipientEmail,
                            $emailContent['subject'],
                            'jobseeker::emails.test-notification',
                            $emailContent
                        );

                        $testResults['email_sent'] = $emailResult;
                        $testResults['email_recipient'] = $recipientEmail;
                    }
                } catch (\Exception $e) {
                    $testResults['email_error'] = $e->getMessage();
                    Log::error('Email test failed', ['error' => $e->getMessage()]);
                }
            }

            // Test push notification
            if ($setup->receive_push_notifications && $setup->jobSeeker->deviceTokens()->exists()) {
                try {
                    $pushNotificationService = app(PushNotificationService::class);
                    $deviceTokens = $setup->jobSeeker->deviceTokens()
                        ->where('is_active', true)
                        ->pluck('device_token')
                        ->toArray();

                    if (!empty($deviceTokens)) {
                        $pushResult = $pushNotificationService->sendNotification(
                            $deviceTokens,
                            'Test Job Alert',
                            'This is a test push notification for: ' . $setup->name,
                            [
                                'type' => 'test_job_alert',
                                'setup_id' => $setup->id,
                                'job_count' => $testJobs->count()
                            ]
                        );

                        $testResults['push_sent'] = $pushResult['success'] ?? false;
                        $testResults['push_details'] = $pushResult;
                    }
                } catch (\Exception $e) {
                    $testResults['push_error'] = $e->getMessage();
                    Log::error('Push notification test failed', ['error' => $e->getMessage()]);
                }
            }

            Log::info('JobsController: EXIT - Notification system test completed', [
                'setup_id' => $setupId,
                'results' => $testResults
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Notification system test completed',
                'setup_name' => $setup->name,
                'test_results' => $testResults,
                'summary' => [
                    'setup_active' => true,
                    'categories_count' => count($categoryIds),
                    'recipients_count' => count($setup->recipients ?? []),
                    'push_enabled' => $setup->receive_push_notifications,
                    'device_tokens_count' => $setup->jobSeeker->deviceTokens()->count()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('JobsController: EXCEPTION - Notification system test failed', [
                'setup_id' => $setupId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Notification system test failed',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
} 