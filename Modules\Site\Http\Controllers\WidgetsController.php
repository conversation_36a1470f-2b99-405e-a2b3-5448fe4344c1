<?php
namespace Modules\Site\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Setting;

class WidgetsController extends Controller
{
    private $theme_config;

    public function __construct()
    {
        $this->theme_config = require(module_path('Site') . '/Resources/views/templates/' . config('website_theme') . '/config.php');
        if (!auth()->user() || !auth()->guard('employee')->check()) {//|| ! auth()->user()->can('update widget')
            return 'Not Authorized';
        }
    }
    public function available()
    {
        $all_widgets = $this->theme_config['widgets'];

        $used_widgets = unserialize(config('settings.homepage_widgets'));
        if (!$used_widgets) $used_widgets = [];

        $widgets = [];
        $widgets_vars = [];

        foreach ($all_widgets as $widget) {
            if (!in_array($widget['name'], $used_widgets)) {
                $widgets[] = $widget;
                if (isset($widget['number_of_blocks']['options'])) {
                    $number_of_blocks = \Illuminate\Support\Arr::first($widget['number_of_blocks']['options']);
                } else {
                    $number_of_blocks = 1;
                }
                $widgets_vars[$widget['name'] . '_number_of_blocks'] = $number_of_blocks;

                if (isset($widget['widget_settings'])) {
                    foreach ($widget['widget_settings'] as $setting => $type) {
                        $widgets_vars[$widget['name'] . '_' . $setting] = $this->getPlaceholderValue($type);
                    }
                }
                if (isset($widget['block_elements'])) {
                    foreach ($widget['block_elements'] as $setting => $type) {
                        for ($i = 1; $i <= $number_of_blocks; $i++) {
                            $widgets_vars[$widget['name'] . '_' . $setting . '_' . $i] = $this->getPlaceholderValue($type);
                        }
                    }
                }
                if(isset($widget['data_source'])){
                    $widgets_vars[$widget['name'] . '_special_data'] = app('Modules\Site\Http\Controllers\\'.ucwords($widget['data_source']).'Controller')->widget_data();
                }
            }
        }


        // dd($widgets_vars);
        extract($widgets_vars);
        return view(theme_path('widgets'), compact('widgets' ,array_keys($widgets_vars)));

    }
    /**
     * @param string $widget_name
     * @return redirect
     */

    public function edit($widget_name)
    {
        $widgets = unserialize(config('settings.homepage_widgets'));

        if (in_array($widget_name, $widgets)) {
            $widget_elements = $this->theme_config['widgets'][$widget_name] ?? null;
            $widget = $widget_elements;
            if (isset($widget['type'])) {
                $type = $widget['type'];

                $elements = [];

                if ($type == 'blocks') {
                    $number_of_blocks = $widget['number_of_blocks'];

                    $elements['number_of_blocks'] = $this->prepareElement($widget['name'], 'number_of_blocks', $number_of_blocks['type'], $number_of_blocks['options']);
                    // $elements['block'][$element] = $this->prepareElement($widget['name'], $element, $setting);

                } elseif ($type == 'dynamic_content') {

                } elseif ($type == 'one_block') {

                    $number_of_blocks = 1;

                } else {
                    $number_of_blocks = 1;

                }
                foreach ($widget['widget_settings'] as $element => $setting) {
                    $elements['settings'][$element] = [];
                    if (is_array($setting)) {

                        $elements['settings'][$element] = $this->prepareElement($widget['name'], $element, $setting['type'], $setting['options']);

                    } else {
                        $elements['settings'][$element] = $this->prepareElement($widget['name'], $element, $setting);
                    }
                }
                if ($number_of_blocks != 1) {
                    $blocks = config('setting.' . $widget['name'] . '_number_of_blocks', $number_of_blocks['options'][0] ?? 2);

                    $elements['blocks'] = [];
                    for ($i = 1; $i <= $blocks; $i++) {
                        $elements['blocks'][$i] = [];
                        foreach ($widget['block_elements'] as $element => $setting) {
                            $element = $element . '_' . $i;
                            if (is_array($setting)) {
                                $elements['blocks'][$i][$element] = $this->prepareElement($widget['name'], $element, $setting['type'], $setting['options']);
                            } else {
                                $elements['blocks'][$i][$element] = $this->prepareElement($widget['name'], $element, $setting);
                            }
                        }
                    }
                }
                return view('site::edit_mode.widget_form', compact('widget', 'elements'));

            } else

                if ($widget_elements) {
                return view('site::edit_mode.widget_form', compact('widget_elements', 'widget_name'));
            }
        }

        return redirect()->back();
    }

    public function update()
    {
        // auth


        foreach (request()->all() as $name => $value) {
            $setting = Setting::where('name', '=', $name)
                ->where('organization_id', '=', config('organization_id'))->first();
            if ($setting) {
                $setting->value = $value;
            } else {
                $setting = new Setting();
                $setting->name = $name;
                $setting->value = $value;
                $setting->organization_id = config('organization_id');
            }
            $setting->save();
        }

        return response()->json([
            'status' => 'success'
        ]);
    }

    public function sort()
    {
        $widgets = request('widgets');
        $setting = Setting::where('name', '=', 'homepage_widgets')
            ->where('organization_id', '=', config('organization_id'))->first();
        if ($setting) {
            $setting->value = serialize($widgets);
        } else {
            $setting = new Setting();
            $setting->name = 'homepage_widgets';
            $setting->value = serialize($widgets);
            $setting->organization_id = config('organization_id');
        }
        $setting->save();

        return $widgets;
    }

    public function blocks()
    {
        $widget = $this->theme_config['widgets'][request('widget_name')] ?? null;
        $starting_block = request('starting_block');
        $last_block = request('last_block');
        $blocks = [];
        // $blocks = '';
        
        if (isset($widget['block_elements'])) {
            for ($i = $starting_block; $i <= $last_block; $i++) {
                foreach ($widget['block_elements'] as $element => $type) {
                    $blocks[$i][$widget['name'] . '_' . $element . '_' . $i] = $this->prepareElement($widget['name'] , $element.'_'.$i , $type);
                }
            }
            // $blocks = $widget['block_elements'];
        }

        if($widget)
            return view('site::edit_mode.widget_blocks' , compact('starting_block','last_block', 'blocks'));
        return false;
    }


    private function getPlaceholderValue($field_type)
    {
        if ($field_type == 'string' || $field_type == 'multilang_string') {
            return 'Try this Widget. It is Awesome!!';
        }
        if ($field_type == 'text') {
            return 'Try this Widget. It is Awesome!! you can try it now and edit it to fit your propose';
        }
        if ($field_type == 'number') {
            return 3999;
        }
        if ($field_type == 'icon') {
            return 'fa-random';
        }
        if ($field_type == 'image') {
            return url('image_placeholder.jpg');
        }
        return '';
    }


    private function prepareElement($widget_name, $element, $type, $options = [])
    {
        if ($type == 'multilang_string') {
            $field = 'string';
            $setting = [
                'label' => ucwords(str_replace('_', ' ', $element)),
                'name' => $widget_name . '_' . $element . '_' . config('app.locale'),
                'value' => config('settings.' . $widget_name . '_' . $element . '_' . config('app.locale'), '')
            ];
        } elseif ($type == 'string' || $type == 'number') {
            $field = 'string';
            $setting = [
                'label' => ucwords(str_replace('_', ' ', $element)),
                'name' => $widget_name . '_' . $element,
                'value' => config('settings.' . $widget_name . '_' . $element, '')
            ];
        } elseif ($type == 'color') {
            $field = 'color';
            $setting = [
                'label' => ucwords(str_replace('_', ' ', $element)),
                'name' => $widget_name . '_' . $element,
                'value' => config('settings.' . $widget_name . '_' . $element, '')
            ];
        } elseif ($type == 'icon') {
            $field = 'icon';
            $setting = [
                'label' => ucwords(str_replace('_', ' ', $element)),
                'name' => $widget_name . '_' . $element,
                'value' => config('settings.' . $widget_name . '_' . $element, '')
            ];
        } elseif ($type == 'image') {
            $field = 'image';
            $setting = [
                'label' => ucwords(str_replace('_', ' ', $element)),
                'name' => $widget_name . '_' . $element,
                'value' => config('settings.' . $widget_name . '_' . $element, '')
            ];
        } elseif ($type == 'text') {
            $field = 'text';
            $setting = [
                'label' => ucwords(str_replace('_', ' ', $element)),
                'name' => $widget_name . '_' . $element . '_' . config('app.locale'),
                'value' => config('settings.' . $widget_name . '_' . $element . '_' . config('app.locale'), '')
            ];
        } elseif ($type == 'boolean') {
            $field = 'boolean';
            $setting = [
                'label' => ucwords(str_replace('_', ' ', $element)),
                'name' => $widget_name . '_' . $element,
                'value' => config('settings.' . $widget_name . '_' . $element, '')
            ];
        } elseif ($type == 'select') {
            $field = 'select';
            $setting = [
                'label' => ucwords(str_replace('_', ' ', $element)),
                'name' => $widget_name . '_' . $element,
                'options' => $options,
                'value' => config('settings.' . $widget_name . '_' . $element, '')
            ];
        }
        return ['field' => $field, 'setting' => $setting];
    }

}
