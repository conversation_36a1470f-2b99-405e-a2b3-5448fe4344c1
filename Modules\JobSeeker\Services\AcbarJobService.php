<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Repositories\JobRepository;
use Mo<PERSON><PERSON>\JobSeeker\Repositories\FilterRepository;
use <PERSON><PERSON>les\JobSeeker\Entities\JobCategory;
use Modules\JobSeeker\Entities\ProviderJobCategory;
use Mo<PERSON>les\JobSeeker\Entities\JobNotificationSetup;
use Modules\JobSeeker\Entities\JobNotificationSentJob;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use DOMDocument;
use DOMXPath;
use Modules\JobSeeker\Services\SystemErrorNotificationService;
use App\Services\EmailService;

class AcbarJobService
{
    /**
     * @var string
     */
    protected string $baseUrl;

    /**
     * @var int
     */
    protected int $locationId;

    /**
     * @var JobRepository
     */
    protected $jobRepository;

    /**
     * @var FilterRepository
     */
    protected FilterRepository $filterRepository;

    /**
     * @var array
     */
    protected array $categoryCache = [];

    /**
     * @var int Maximum number of retries for rate-limited requests
     */
    protected int $maxRetries;

    /**
     * @var int Base delay in microseconds (1 second)
     */
    protected int $baseDelay;

    /**
     * @var int Timeout in seconds for HTTP requests
     */
    protected int $timeout;

    /**
     * @var SystemErrorNotificationService
     */
    protected SystemErrorNotificationService $errorNotificationService;

    /**
     * @var EmailService
     */
    protected EmailService $emailService;

    /**
     * Static cache for canonical categories
     * @deprecated This will be removed after migration to provider categories
     * @var array<int, JobCategory>
     */
    private static $canonicalCategoriesCache = [];

    /**
     * AcbarJobService constructor.
     *
     * @param JobRepository $jobRepository
     * @param FilterRepository $filterRepository
     * @param SystemErrorNotificationService $errorNotificationService
     * @param EmailService $emailService
     */
    public function __construct(
        JobRepository $jobRepository,
        FilterRepository $filterRepository,
        SystemErrorNotificationService $errorNotificationService,
        EmailService $emailService
    ) {
        $this->jobRepository = $jobRepository;
        $this->filterRepository = $filterRepository;
        $this->errorNotificationService = $errorNotificationService;
        $this->emailService = $emailService;
        
        // Load configuration from config/jobseeker.php
        $config = config('jobseeker.acbar_default_filters', []);
        
        $this->baseUrl = $config['base_url'] ?? 'https://www.acbar.org/jobs';
        $this->locationId = $config['default_location_id'] ?? 14;
        $this->timeout = $config['timeout'] ?? 60;
        $this->maxRetries = $config['max_retries'] ?? 5;
        $this->baseDelay = $config['base_delay'] ?? 1000000;
    }

    /**
     * Get provider categories from schedule rule for filtering
     * This is the new correct approach using provider categories
     *
     * @param int|null $scheduleRuleId
     * @return array Array of provider_identifier values for API calls
     */
    protected function getProviderCategoriesFromScheduleRule(?int $scheduleRuleId): array
    {
        if (!$scheduleRuleId) {
            // No schedule rule - return all ACBAR categories
            return \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'acbar')
                ->pluck('provider_identifier')
                ->toArray();
        }

        try {
            $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::find($scheduleRuleId);
            if (!$rule || empty($rule->provider_category_ids)) {
                Log::info('AcbarJobService: No category filter in schedule rule', [
                    'schedule_rule_id' => $scheduleRuleId
                ]);
                // Return all ACBAR categories
                return \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'acbar')
                    ->pluck('provider_identifier')
                    ->toArray();
            }

            $providerCategories = \Modules\JobSeeker\Entities\ProviderJobCategory::whereIn('id', $rule->provider_category_ids)
                ->where('provider_name', 'acbar')
                ->pluck('provider_identifier')
                ->toArray();

            Log::info('AcbarJobService: Retrieved provider categories from schedule rule', [
                'schedule_rule_id' => $scheduleRuleId,
                'provider_category_ids' => $rule->provider_category_ids,
                'provider_identifiers' => $providerCategories
            ]);

            return $providerCategories;

        } catch (\Exception $e) {
            Log::error('AcbarJobService: Error getting provider categories from schedule rule', [
                'schedule_rule_id' => $scheduleRuleId,
                'error' => $e->getMessage()
            ]);
            // Fallback to all categories
            return \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'acbar')
                ->pluck('provider_identifier')
                ->toArray();
        }
    }

    /**
     * Get provider locations from schedule rule for filtering
     *
     * @param int|null $scheduleRuleId
     * @return array Array of provider_identifier values for API calls
     */
    protected function getProviderLocationsFromScheduleRule(?int $scheduleRuleId): array
    {
        if (!$scheduleRuleId) {
            return [];
        }

        try {
            $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::with('filter')->find($scheduleRuleId);
            if (!$rule || !$rule->filter || empty($rule->filter->locations)) {
                return [];
            }

            return \Modules\JobSeeker\Entities\ProviderJobLocation::whereIn('id', $rule->filter->locations)
                ->where('provider_name', 'acbar')
                ->where('is_active', true)
                ->pluck('provider_identifier')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('AcbarJobService: Error getting provider locations from schedule rule', [
                'schedule_rule_id' => $scheduleRuleId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Make a rate-limited HTTP request with retries and exponential backoff
     *
     * @param string $url
     * @return \Illuminate\Http\Client\Response
     * @throws \Exception
     */
    protected function makeRateLimitedRequest(string $url)
    {
        $rateLimitKey = 'acbar_api_request';
        $maxAttempts = $this->maxRetries;
        $baseDelay = $this->baseDelay; // 1 second in microseconds

        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            try {
                if (RateLimiter::tooManyAttempts($rateLimitKey, 10)) {
                    $retryAfter = RateLimiter::availableIn($rateLimitKey);
                    Log::warning("Rate limited - waiting {$retryAfter} seconds", [
                        'attempt' => $attempt,
                        'url' => $url
                    ]);
                    sleep($retryAfter);
                }

                RateLimiter::hit($rateLimitKey, 60);

                $response = Http::timeout($this->timeout)
                    ->withUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
                    ->get($url);

                if ($response->successful()) {
                    Log::debug("HTTP request successful", [
                        'url' => $url,
                        'attempt' => $attempt,
                        'status' => $response->status()
                    ]);
                    return $response;
                }

                Log::warning("HTTP request failed", [
                    'url' => $url,
                    'attempt' => $attempt,
                    'status' => $response->status(),
                    'body' => substr($response->body(), 0, 200)
                ]);

                if ($attempt < $maxAttempts) {
                    $delayMs = $baseDelay * (2 ** ($attempt - 1));
                    usleep($delayMs);
                }

            } catch (\Exception $e) {
                Log::error("Exception during HTTP request", [
                    'url' => $url,
                    'attempt' => $attempt,
                    'error' => $e->getMessage()
                ]);

                if ($attempt < $maxAttempts) {
                    $delayMs = $baseDelay * (2 ** ($attempt - 1));
                    usleep($delayMs);
                } else {
                    throw $e;
                }
            }
        }

        throw new \Exception("Failed to make HTTP request after {$maxAttempts} attempts");
    }

    /**
     * Make a standard HTTP request
     *
     * @param string $url
     * @return \Illuminate\Http\Client\Response
     */
    protected function makeRequest(string $url)
    {
        return $this->makeRateLimitedRequest($url);
    }

    

    /**
     * Sync jobs for a specific ACBAR category and location
     *
     * @param string $categoryId The ACBAR provider identifier to sync
     * @param string|null $locationId The ACBAR location identifier (defaults to configured default)
     * @return array Statistics about the sync process for this category
     */
    public function syncAcbarCategory(string $categoryId, ?string $locationId = null): array
    {
        $stats = [
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped_no_category_map' => 0,
            'category_processed' => false
        ];

        try {
            // Check if this provider identifier exists in our mappings
            $providerCategory = ProviderJobCategory::where('provider_name', 'acbar')
                ->where('provider_identifier', $categoryId)
                ->first();
                
            if (!$providerCategory) {
                Log::info("Skipping ACBAR category - no provider mapping found", [
                    'provider_identifier' => $categoryId
                ]);
                $stats['skipped_no_category_map']++;
                return $stats;
            }
            
            // FIXED: Use dynamic location ID or fall back to configured default
            $effectiveLocationId = $locationId ?? $this->locationId;
            $url = "{$this->baseUrl}?location={$effectiveLocationId}&category={$categoryId}";
            Log::info("Fetching jobs from ACBAR.org", [
                'url' => $url,
                'provider_identifier' => $categoryId,
                'provider_category_name' => $providerCategory->name,
                'location_id' => $effectiveLocationId,
                'location_source' => $locationId ? 'dynamic' : 'default_config'
            ]);

            // Use rate-limited request
            $response = $this->makeRequest($url);
            Log::debug("ACBAR response received", [
                'status' => $response->status(),
                'body_length' => strlen($response->body()),
                'url' => $url,
                'has_content' => !empty($response->body())
            ]);

            // Parse HTML
            $document = new DOMDocument();
            @$document->loadHTML($response->body());
            $xpath = new DOMXPath($document);
            
            // Find the job table
            $jobTable = $xpath->query('//table[@class="table table-bordered"]');
            Log::info("Job table search result", [
                'table_found' => $jobTable->length > 0,
                'provider_identifier' => $categoryId,
                'table_count' => $jobTable->length
            ]);
            
            if ($jobTable->length === 0) {
                Log::warning("No job table found for category", [
                    'provider_identifier' => $categoryId,
                    'html_snippet' => substr($response->body(), 0, 500)
                ]);
                return $stats;
            }

            // Get the category name from the HTML or use provider category name
            $categoryName = $providerCategory->name;
            $select = $xpath->query('//select[@id="data_industry"]');
            
            if ($select->length > 0) {
                $selectedOption = $xpath->query('.//option[@selected]', $select->item(0));
                
                if (!$selectedOption || $selectedOption->length === 0) {
                    $selectedOption = $xpath->query(".//option[@value='{$categoryId}']", $select->item(0));
                }
                
                if ($selectedOption && $selectedOption->length > 0) {
                    $categoryName = trim($selectedOption->item(0)->nodeValue);
                }
            }
            
            Log::info("Category name determined", [
                'found_name' => $categoryName,
                'provider_identifier' => $categoryId,
                'provider_category_name' => $providerCategory->name
            ]);
            
            // Save/retrieve the ACBAR category
            $acbarCategory = $this->saveAcbarCategory($categoryId, $categoryName);
            if (!$acbarCategory) {
                $stats['skipped_no_category_map']++;
                Log::warning("Failed to save/retrieve ACBAR category", [
                    'provider_identifier' => $categoryId,
                    'category_name' => $categoryName
                ]);
                return $stats;
            }
            
            Log::info("ACBAR category retrieved/saved", [
                'acbar_category_id' => $acbarCategory->id,
                'acbar_category_name' => $acbarCategory->name,
                'parent_id' => $acbarCategory->parent_id,
                'source' => $acbarCategory->source,
                'source_id' => $acbarCategory->source_id
            ]);
            
            // Get the canonical category through the parent relationship
            $canonicalCategory = $acbarCategory->parent;
            if (!$canonicalCategory) {
                Log::error("ACBAR category has no parent (canonical) category", [
                    'acbar_category_id' => $acbarCategory->id,
                    'acbar_category_name' => $acbarCategory->name,
                    'provider_identifier' => $categoryId
                ]);
                $stats['errors']++;
                return $stats;
            }
            
            Log::info("Canonical category found", [
                'canonical_id' => $canonicalCategory->id,
                'canonical_name' => $canonicalCategory->name,
                'is_canonical' => $canonicalCategory->is_canonical,
                'is_active' => $canonicalCategory->is_active
            ]);
            
            $stats['category_processed'] = true;

            // Process job listings - get all rows except header
            $rows = $xpath->query('//table[@class="table table-bordered"]/tr[position()>1]');
            Log::info("Found job rows", [
                'count' => $rows->length,
                'provider_identifier' => $categoryId,
                'has_rows' => $rows->length > 0
            ]);

            foreach ($rows as $row) {
                try {
                    $cells = $xpath->query('td', $row);
                    if ($cells->length < 5) {
                        Log::warning("Invalid row structure", [
                            'provider_identifier' => $categoryId,
                            'cells_count' => $cells->length,
                            'expected_cells' => 5,
                            'row_html' => $document->saveHTML($row)
                        ]);
                        continue;
                    }

                    // Extract job data
                    $jobTitle = trim($cells->item(1)->nodeValue);
                    $jobUrl = trim($cells->item(1)->getElementsByTagName('a')->item(0)->getAttribute('href'));
                    if (Str::startsWith($jobUrl, '/')) {
                        $jobUrl = 'https://www.acbar.org' . $jobUrl;
                    }

                    $organizationName = trim($cells->item(2)->nodeValue);
                    
                    // Extract location
                    $locationLinks = $xpath->query('a', $cells->item(3));
                    $locations = [];
                    foreach ($locationLinks as $link) {
                        if (Str::contains($link->getAttribute('href'), '/job/location/')) {
                            $locations[] = trim($link->nodeValue);
                        }
                    }
                    $locationString = implode(', ', $locations);

                    // Parse close date
                    $closeDateString = trim($cells->item(4)->nodeValue);
                    $expireDate = Carbon::parse($closeDateString)->format('Y-m-d');

                    Log::info("Extracted job data", [
                        'title' => $jobTitle,
                        'organization' => $organizationName,
                        'location' => $locationString,
                        'expire_date' => $expireDate,
                        'url' => $jobUrl,
                        'provider_identifier' => $categoryId
                    ]);

                    // Prepare job data
                    $jobData = [
                        'position' => $jobTitle,
                        'company_name' => $organizationName,
                        'slug' => Str::slug($organizationName . '-' . $jobTitle . '-' . md5($jobUrl)),
                        'description' => 'Job details available at: ' . $jobUrl,
                        'publish_date' => Carbon::now()->format('Y-m-d'),
                        'expire_date' => $expireDate,
                        'locations' => $locationString,
                        'source' => 'ACBAR',
                        'url' => $jobUrl,
                        'is_active' => true,
                        'organizationName' => $organizationName,
                        'jobTitle' => $jobTitle,
                        'location' => $locationString,
                        'jobUrl' => $jobUrl,
                        // Add normalized fields for deduplication
                        'normalized_company_name' => $organizationName,
                        'normalized_job_title' => $jobTitle,
                        'normalized_location' => $locationString,
                        'job_fingerprint' => md5(strtolower($organizationName . $jobTitle . $locationString))
                    ];

                    DB::beginTransaction();
                    try {
                        // Create or update job
                        $job = $this->jobRepository->createOrUpdate($jobData);
                        $jobWasCreated = $job->wasRecentlyCreated;

                        // NEW APPROACH: Attach provider categories
                        $job->providerCategories()->sync([$providerCategory->id]);
                        Log::info('AcbarJobService: Synced job with provider categories', [
                            'job_id' => $job->id,
                            'provider_category_id' => $providerCategory->id,
                            'provider_category_name' => $providerCategory->name
                        ]);

                        // DEPRECATED: Old canonical category sync (keeping temporarily for compatibility)
                        $job->categories()->sync([$canonicalCategory->id, $acbarCategory->id]);
                        
                        DB::commit();

                        // Bypassing queue and sending email directly for synchronous processing
                        // This replaces the asynchronous JobProcessedEvent dispatch
                        $this->sendSynchronousEmailNotification($job);
                        
                        if ($jobWasCreated) {
                            $stats['created']++;
                            Log::info("Created new job and dispatched JobProcessedEvent", [
                                'job_id' => $job->id,
                                'title' => $jobTitle,
                                'organization' => $organizationName,
                                'provider_identifier' => $categoryId
                            ]);
                        } else {
                            $stats['updated']++;
                            Log::info("Updated existing job and dispatched JobProcessedEvent", [
                                'job_id' => $job->id,
                                'title' => $jobTitle,
                                'organization' => $organizationName,
                                'provider_identifier' => $categoryId
                            ]);
                        }
                    } catch (\Exception $e) {
                        DB::rollBack();
                        throw $e;
                    }
                } catch (\Exception $e) {
                    $stats['errors']++;
                    Log::error("Error processing job row", [
                        'provider_identifier' => $categoryId,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
        } catch (\Exception $e) {
            $stats['errors']++;
            Log::error("Error processing ACBAR category", [
                'provider_identifier' => $categoryId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $stats;
    }

    /**
     * Sync jobs from ACBAR.org using provider identifiers directly
     * This method now accepts ACBAR provider identifiers directly from provider_job_categories table
     *
     * @param array|null $providerIdentifiers Array of ACBAR provider identifiers (e.g., ["16", "70"])
     * @param int|null $scheduleRuleId Optional schedule rule ID for translated filters
     * @return array Statistics about the sync process
     */
    public function syncAcbarJobs($providerIdentifiers = null, ?int $scheduleRuleId = null): array
    {
        // Reset category cache
        $this->categoryCache = [];

        $startTime = microtime(true);
        $totalStats = [
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped_no_category_map' => 0,
            'categories_processed' => 0,
            'filtered_categories' => [],
            // Health dashboard fields
            'jobs_fetched' => 0,
            'jobs_by_category' => [],
            'error_types' => [],
            'api_response_time' => 0
        ];

        // Sanitize input: support string, array, or null
        if (is_string($providerIdentifiers)) {
            $providerIdentifiers = [$providerIdentifiers];
        } elseif (!is_array($providerIdentifiers)) {
            $providerIdentifiers = [];
        }

        Log::info('Starting ACBAR.org job synchronization', [
            'provider_identifiers' => !empty($providerIdentifiers) ? $providerIdentifiers : 'all/filtered',
            'schedule_rule_id' => $scheduleRuleId
        ]);

        try {
            // Priority 1: Manual provider identifier filtering takes precedence
            if (!empty($providerIdentifiers)) {
                // Convert to strings and validate they exist in provider_job_categories
                $validIdentifiers = [];
                foreach ($providerIdentifiers as $identifier) {
                    $identifier = (string)$identifier;
                    $exists = ProviderJobCategory::where('provider_name', 'acbar')
                        ->where('provider_identifier', $identifier)
                        ->exists();
                    
                    if ($exists) {
                        $validIdentifiers[] = $identifier;
                    } else {
                        Log::warning("Invalid ACBAR provider identifier", [
                            'provider_identifier' => $identifier
                        ]);
                    }
                }
                
                $totalStats['filtered_categories'] = $validIdentifiers;
                
                Log::info('Using manual provider identifier filtering for ACBAR sync', [
                    'requested_identifiers' => $providerIdentifiers,
                    'valid_identifiers' => $validIdentifiers,
                    'identifier_count' => count($validIdentifiers)
                ]);
                
                // Early sanity check: if list is empty after validation, return immediately
                if (empty($validIdentifiers)) {
                    Log::warning('ACBAR sync aborted: no valid provider identifiers found', [
                        'requested_identifiers' => $providerIdentifiers,
                        'message' => 'The provided provider identifiers do not exist in provider_job_categories table'
                    ]);
                    $totalStats['api_response_time'] = microtime(true) - $startTime;
                    $totalStats['skipped_no_category_map'] = count($providerIdentifiers);
                    return $this->formatHealthTrackingReturn($totalStats, true, 'No valid ACBAR provider identifiers found');
                }
                
                // Process manual provider identifiers sequentially
                $totalCategories = count($validIdentifiers);
                foreach ($validIdentifiers as $index => $identifier) {
                    $categoryStartTime = microtime(true);
                    $currentCategory = $index + 1;
                    
                    Log::info("ACBAR Manual Processing: Starting provider identifier {$currentCategory}/{$totalCategories}", [
                        'provider_identifier' => $identifier,
                        'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                    ]);
                    
                    // Process single category with full backend completion
                    $stats = $this->processAcbarCategoryCompletely($identifier, $scheduleRuleId);
                    $this->aggregateHealthStats($stats, $totalStats);
                    
                    $categoryDuration = microtime(true) - $categoryStartTime;
                    Log::info("ACBAR Manual Processing: Provider identifier {$currentCategory}/{$totalCategories} completed", [
                        'provider_identifier' => $identifier,
                        'jobs_processed' => $stats['jobs_fetched'],
                        'duration_seconds' => round($categoryDuration, 2),
                        'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                    ]);
                    
                    // Enhanced delay based on jobs processed
                    if ($currentCategory < $totalCategories) {
                        $this->smartDelayBetweenCategories($stats, $categoryDuration);
                    }
                }
                
                // Calculate total API response time
                $totalStats['api_response_time'] = microtime(true) - $startTime;
                
                return $this->formatHealthTrackingReturn($totalStats, true);
            }
            
            // Priority 2: If a schedule rule ID is provided, use NEW provider category approach
            if ($scheduleRuleId !== null) {
                try {
                    // NEW APPROACH: Get provider categories directly from schedule rule
                    $providerIdentifiers = $this->getProviderCategoriesFromScheduleRule($scheduleRuleId);

                    Log::info('Using NEW provider category approach for ACBAR sync', [
                        'schedule_rule_id' => $scheduleRuleId,
                        'provider_identifiers' => $providerIdentifiers,
                        'identifier_count' => count($providerIdentifiers)
                    ]);

                    // DEPRECATED: Old filter repository approach (keeping as fallback temporarily)
                    if (empty($providerIdentifiers)) {
                        Log::warning('NEW approach returned no categories, falling back to old FilterRepository approach');
                        $translatedFilters = $this->filterRepository->getAcbarTranslatedFilters($scheduleRuleId);
                        $providerIdentifiers = $translatedFilters['category_ids'] ?? [];

                        // Update configuration from filters
                        if (isset($translatedFilters['max_retries'])) {
                            $this->maxRetries = $translatedFilters['max_retries'];
                        }
                        if (isset($translatedFilters['timeout'])) {
                            $this->timeout = $translatedFilters['timeout'];
                        }
                        if (isset($translatedFilters['base_delay'])) {
                            $this->baseDelay = $translatedFilters['base_delay'];
                        }
                    }
                    
                    Log::info('Using translated ACBAR filters from schedule rule', [
                        'schedule_rule_id' => $scheduleRuleId,
                        'provider_identifiers' => $providerIdentifiers,
                        'location_ids' => $translatedFilters['location_ids'] ?? [],
                        'max_retries' => $this->maxRetries,
                        'timeout' => $this->timeout,
                        'base_delay' => $this->baseDelay
                    ]);

                    if (empty($providerIdentifiers)) {
                        // No identifier filter = sync all available ACBAR categories
                        Log::info('No identifier filter specified, syncing all ACBAR categories');
                        $providerIdentifiers = ProviderJobCategory::where('provider_name', 'acbar')
                            ->pluck('provider_identifier')
                            ->toArray();
                    }

                    $totalStats['filtered_categories'] = $providerIdentifiers;

                    // Process filtered provider identifiers sequentially
                    $totalCategories = count($providerIdentifiers);
                    foreach ($providerIdentifiers as $index => $identifier) {
                        $categoryStartTime = microtime(true);
                        $currentCategory = $index + 1;
                        
                        Log::info("ACBAR Sequential Processing: Starting provider identifier {$currentCategory}/{$totalCategories}", [
                            'provider_identifier' => $identifier,
                            'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                        ]);
                        
                        // Process single category with full backend completion
                        $stats = $this->processAcbarCategoryCompletely($identifier, $scheduleRuleId);
                        $this->aggregateHealthStats($stats, $totalStats);
                        
                        $categoryDuration = microtime(true) - $categoryStartTime;
                        Log::info("ACBAR Sequential Processing: Provider identifier {$currentCategory}/{$totalCategories} completed", [
                            'provider_identifier' => $identifier,
                            'jobs_processed' => $stats['jobs_fetched'],
                            'duration_seconds' => round($categoryDuration, 2),
                            'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                        ]);
                        
                        // Enhanced delay based on jobs processed
                        if ($currentCategory < $totalCategories) {
                            $this->smartDelayBetweenCategories($stats, $categoryDuration);
                        }
                    }

                    // Calculate total API response time
                    $totalStats['api_response_time'] = microtime(true) - $startTime;

                    return $this->formatHealthTrackingReturn($totalStats, true);

                } catch (\Exception $e) {
                    Log::error('Failed to get translated filters for ACBAR', [
                        'schedule_rule_id' => $scheduleRuleId,
                        'error' => $e->getMessage()
                    ]);
                    // Fall through to default behavior
                }
            }
            
            // Priority 3: Default behavior - sync all available ACBAR categories
            Log::info('Using default behavior: syncing all ACBAR categories');
            
            $allProviderIdentifiers = ProviderJobCategory::where('provider_name', 'acbar')
                ->pluck('provider_identifier')
                ->toArray();
            
            $totalStats['filtered_categories'] = $allProviderIdentifiers;
            $totalCategories = count($allProviderIdentifiers);
            $currentCategory = 0;

            foreach ($allProviderIdentifiers as $identifier) {
                $categoryStartTime = microtime(true);
                $currentCategory++;
                
                Log::info("ACBAR Default Processing: Starting provider identifier {$currentCategory}/{$totalCategories}", [
                    'provider_identifier' => $identifier,
                    'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                ]);
                
                // Process single category with full backend completion
                $stats = $this->processAcbarCategoryCompletely($identifier, $scheduleRuleId);
                $this->aggregateHealthStats($stats, $totalStats);
                
                $categoryDuration = microtime(true) - $categoryStartTime;
                Log::info("ACBAR Default Processing: Provider identifier {$currentCategory}/{$totalCategories} completed", [
                    'provider_identifier' => $identifier,
                    'jobs_processed' => $stats['jobs_fetched'],
                    'duration_seconds' => round($categoryDuration, 2),
                    'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                ]);
                
                // Enhanced delay based on jobs processed
                if ($currentCategory < $totalCategories) {
                    $this->smartDelayBetweenCategories($stats, $categoryDuration);
                }
            }

            // Calculate total API response time
            $totalStats['api_response_time'] = microtime(true) - $startTime;

            return $this->formatHealthTrackingReturn($totalStats, true);

        } catch (\Exception $e) {
            $totalStats['errors']++;
            $totalStats['api_response_time'] = microtime(true) - $startTime;

            Log::error('Critical error in ACBAR job synchronization', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Report critical error to founder
            $this->errorNotificationService->reportJobFetchFailure(
                'ACBAR',
                'Critical error in ACBAR job synchronization: ' . $e->getMessage(),
                [
                    'total_stats' => $totalStats,
                    'api_response_time' => $totalStats['api_response_time'],
                    'schedule_rule_id' => $scheduleRuleId,
                    'category_ids' => $categoryIds,
                ],
                $e
            );

            return $this->formatHealthTrackingReturn($totalStats, false, $e->getMessage());
        }
    }

    /**
     * Aggregate health statistics from individual category sync operations
     * 
     * @param array $stats
     * @param array $totalStats
     * @return void
     */
    private function aggregateHealthStats(array $stats, array &$totalStats): void
    {
        $totalStats['created'] += $stats['created'];
        $totalStats['updated'] += $stats['updated'];
        $totalStats['errors'] += $stats['errors'];
        $totalStats['skipped_no_category_map'] += $stats['skipped_no_category_map'];
        if ($stats['category_processed']) {
            $totalStats['categories_processed']++;
        }
        
        // Aggregate health dashboard fields
        $totalStats['jobs_fetched'] += $stats['jobs_fetched'] ?? 0;
        
        // Merge jobs by category
        if (!empty($stats['jobs_by_category'])) {
            foreach ($stats['jobs_by_category'] as $categoryName => $count) {
                if (!isset($totalStats['jobs_by_category'][$categoryName])) {
                    $totalStats['jobs_by_category'][$categoryName] = 0;
                }
                $totalStats['jobs_by_category'][$categoryName] += $count;
            }
        }
        
        // Merge error types
        if (!empty($stats['error_types'])) {
            foreach ($stats['error_types'] as $errorType => $count) {
                if (!isset($totalStats['error_types'][$errorType])) {
                    $totalStats['error_types'][$errorType] = 0;
                }
                $totalStats['error_types'][$errorType] += $count;
            }
        }
    }

    /**
     * Format the return array with health tracking fields
     * 
     * @param array $stats
     * @param bool $success
     * @param string|null $errorMessage
     * @return array
     */
    private function formatHealthTrackingReturn(array $stats, bool $success, ?string $errorMessage = null): array
    {
        $result = [
            'success' => $success,
            'created' => $stats['created'],
            'updated' => $stats['updated'],
            'errors' => $stats['errors'],
            'skipped_no_category_map' => $stats['skipped_no_category_map'],
            'categories_processed' => $stats['categories_processed'],
            'filtered_categories' => $stats['filtered_categories'],
            // Health dashboard fields
            'jobs_fetched' => $stats['jobs_fetched'],
            'jobs_by_category' => $stats['jobs_by_category'],
            'error_types' => $stats['error_types'],
            'api_response_time' => $stats['api_response_time']
        ];

        if (!$success && $errorMessage) {
            $result['error_message'] = $errorMessage;
        }

        return $result;
    }

    /**
     * Classify error type for health dashboard tracking
     * 
     * @param \Exception $exception
     * @return string
     */
    private function classifyError(\Exception $exception): string
    {
        $message = strtolower($exception->getMessage());
        $exceptionClass = get_class($exception);
        
        // Network-related errors
        if (str_contains($message, 'connection') || 
            str_contains($message, 'timeout') || 
            str_contains($message, 'network') ||
            str_contains($message, 'curl') ||
            str_contains($exceptionClass, 'Connection') ||
            str_contains($exceptionClass, 'Timeout')) {
            return 'NETWORK';
        }
        
        // API-related errors  
        if (str_contains($message, 'api') ||
            str_contains($message, 'json') ||
            str_contains($message, 'response') ||
            str_contains($message, 'http') ||
            str_contains($exceptionClass, 'Request') ||
            str_contains($exceptionClass, 'Response')) {
            return 'API';
        }
        
        // Rate limiting errors
        if (str_contains($message, 'rate limit') ||
            str_contains($message, 'too many requests') ||
            str_contains($message, '429')) {
            return 'RATE_LIMIT';
        }
        
        // Database-related errors
        if (str_contains($message, 'database') ||
            str_contains($message, 'sql') ||
            str_contains($message, 'duplicate') ||
            str_contains($message, 'constraint') ||
            str_contains($exceptionClass, 'Query') ||
            str_contains($exceptionClass, 'Database')) {
            return 'DATA';
        }
        
        // Timeout errors
        if (str_contains($message, 'timeout') ||
            str_contains($message, 'time out') ||
            str_contains($exceptionClass, 'Timeout')) {
            return 'TIMEOUT';
        }
        
        // Default category
        return 'UNKNOWN';
    }



    /**
     * Save/retrieve ACBAR category in the job_categories table
     *
     * @param string $categoryId ACBAR provider identifier
     * @param string $categoryName ACBAR category name
     * @return JobCategory|null
     */
    private function saveAcbarCategory(string $categoryId, string $categoryName): ?JobCategory
    {
        try {
            // First, try to find existing category by source and source_id
            $category = JobCategory::findBySource('acbar', $categoryId);

            if ($category) {
                Log::debug('AcbarJobService: Found existing ACBAR category', [
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'source_id' => $categoryId
                ]);
                return $category;
            }

            // Get canonical category mapping from config
            $categoryMapping = config('jobseeker.acbar_default_filters.category_mapping', []);
            $canonicalCategoryId = $categoryMapping[$categoryId] ?? null;

            // Create new category
            $category = new JobCategory();
            $category->name = $categoryName;
            $category->slug = Str::slug($categoryName . '-acbar-' . $categoryId); // Generate unique slug
            $category->source = 'acbar';
            $category->source_id = $categoryId;
            $category->is_active = true;
            $category->is_canonical = false; // ACBAR categories are not canonical
            $category->is_archived = false;

            // Set parent_id if we have a canonical mapping
            if ($canonicalCategoryId) {
                $canonicalCategory = JobCategory::find($canonicalCategoryId);
                if ($canonicalCategory) {
                    $category->parent_id = $canonicalCategory->id;
                    Log::debug('AcbarJobService: Mapped ACBAR category to canonical', [
                        'acbar_category_id' => $categoryId,
                        'canonical_category_id' => $canonicalCategoryId,
                        'canonical_category_name' => $canonicalCategory->name
                    ]);
                }
            }

            $category->save();

            Log::info('AcbarJobService: Created new ACBAR category', [
                'category_id' => $category->id,
                'category_name' => $category->name,
                'source_id' => $categoryId,
                'parent_id' => $category->parent_id
            ]);

            return $category;

        } catch (\Exception $e) {
            Log::error('AcbarJobService: Error saving ACBAR category', [
                'category_id' => $categoryId,
                'category_name' => $categoryName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get ACBAR category name by provider identifier
     *
     * @param string $providerIdentifier
     * @return string|null
     */
    private function getAcbarCategoryName(string $providerIdentifier): ?string
    {
        try {
            // Get provider category mapping
            $providerCategory = ProviderJobCategory::where('provider_name', 'acbar')
                ->where('provider_identifier', $providerIdentifier)
                ->first();

            if ($providerCategory) {
                return $providerCategory->name;
            }

            // Fallback: Use generic ACBAR category naming
            return "ACBAR Category {$providerIdentifier}";

        } catch (\Exception $e) {
            Log::warning('AcbarJobService: Error getting category name', [
                'provider_identifier' => $providerIdentifier,
                'error' => $e->getMessage()
            ]);
            return "Unknown Category {$providerIdentifier}";
        }
    }

    /**
     * Format execution stats for command schedule health tracking
     * 
     * @param array $stats
     * @return array
     */
    public function formatExecutionStats(array $stats): array
    {
        try {
            return [
                'jobs_fetched' => (int) ($stats['jobs_fetched'] ?? $stats['created'] + $stats['updated'] ?? 0),
                'jobs_by_category' => $stats['jobs_by_category'] ?? [],
                'error_type' => $this->determineMainErrorType($stats['error_types'] ?? []),
                'error_details' => [
                    'total_errors' => (int) ($stats['errors'] ?? 0),
                    'error_breakdown' => $stats['error_types'] ?? [],
                    'api_response_time' => (float) ($stats['api_response_time'] ?? 0),
                    'categories_processed' => (int) ($stats['categories_processed'] ?? 0),
                    'skipped_no_category_map' => (int) ($stats['skipped_no_category_map'] ?? 0)
                ]
            ];
        } catch (\Exception $e) {
            Log::error('AcbarJobService: Error formatting execution stats', [
                'error' => $e->getMessage(),
                'stats' => $stats
            ]);
            
            return [
                'jobs_fetched' => 0,
                'jobs_by_category' => [],
                'error_type' => 'UNKNOWN',
                'error_details' => ['total_errors' => 1, 'format_error' => $e->getMessage()]
            ];
        }
    }

    /**
     * Determine the main error type from error statistics
     * 
     * @param array $errorTypes
     * @return string
     */
    private function determineMainErrorType(array $errorTypes): string
    {
        if (empty($errorTypes)) {
            return 'NONE';
        }
        
        // Return the error type with the highest count
        arsort($errorTypes);
        return array_key_first($errorTypes);
    }

    /**
     * Process a single ACBAR category completely with full backend completion
     * This ensures all database operations, events, and background jobs are finished
     * before proceeding to the next category.
     *
     * @param string $providerIdentifier ACBAR provider identifier
     * @param int|null $scheduleRuleId Optional schedule rule ID for dynamic location filtering
     * @return array
     */
    private function processAcbarCategoryCompletely(string $providerIdentifier, ?int $scheduleRuleId = null): array
    {
        $startTime = microtime(true);
        $stats = [
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped_no_category_map' => 0,
            'category_processed' => false,
            'jobs_fetched' => 0,
            'jobs_by_category' => [],
            'error_types' => [],
            'category_response_time' => 0
        ];

        try {
            Log::info("ACBAR Category Processing: Starting complete processing", [
                'provider_identifier' => $providerIdentifier,
                'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);

            // Step 1: Get dynamic locations if schedule rule ID is provided
            $locationIds = [];
            if ($scheduleRuleId) {
                try {
                    $translatedFilters = $this->filterRepository->getAcbarTranslatedFilters($scheduleRuleId);
                    $locationIds = $translatedFilters['location_ids'] ?? [];

                    Log::debug("ACBAR Category Processing: Got dynamic locations from schedule rule", [
                        'schedule_rule_id' => $scheduleRuleId,
                        'location_ids' => $locationIds,
                        'location_count' => count($locationIds)
                    ]);
                } catch (\Exception $e) {
                    Log::warning("ACBAR Category Processing: Failed to get dynamic locations, using default", [
                        'schedule_rule_id' => $scheduleRuleId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // If no dynamic locations, use default location
            if (empty($locationIds)) {
                $locationIds = [(string)$this->locationId];
                Log::debug("ACBAR Category Processing: Using default location", [
                    'default_location_id' => $this->locationId
                ]);
            }

            // Step 2: Process the category for each location
            $originalStats = [
                'created' => 0,
                'updated' => 0,
                'errors' => 0,
                'skipped_no_category_map' => 0,
                'category_processed' => false
            ];

            foreach ($locationIds as $locationId) {
                Log::debug("ACBAR Category Processing: Processing category-location combination", [
                    'provider_identifier' => $providerIdentifier,
                    'location_id' => $locationId
                ]);

                $locationStats = $this->syncAcbarCategory($providerIdentifier, (string)$locationId);

                // Aggregate stats from this location
                $originalStats['created'] += $locationStats['created'];
                $originalStats['updated'] += $locationStats['updated'];
                $originalStats['errors'] += $locationStats['errors'];
                $originalStats['skipped_no_category_map'] += $locationStats['skipped_no_category_map'];
                $originalStats['category_processed'] = $originalStats['category_processed'] || $locationStats['category_processed'];
            }
            
            // Step 3: Ensure all database transactions are committed
            \DB::commit(); // Ensure any pending transactions are committed
            
            // Step 4: Wait for any background job processing to complete
            // (This is important if job processing triggers events or queue jobs)
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch(); // Handle any pending signals
            }
            
            // Step 4: Clear memory and optimize for next category
            $this->clearCategoryCache();
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles(); // Force garbage collection
            }
            
            // Merge basic stats
            $stats['created'] = $originalStats['created'];
            $stats['updated'] = $originalStats['updated'];
            $stats['errors'] = $originalStats['errors'];
            $stats['skipped_no_category_map'] = $originalStats['skipped_no_category_map'];
            $stats['category_processed'] = $originalStats['category_processed'];
            
            // Calculate health dashboard metrics
            $stats['jobs_fetched'] = $stats['created'] + $stats['updated'];
            
            // Get category name for tracking
            $categoryName = $this->getAcbarCategoryName($providerIdentifier);
            if ($categoryName && $stats['jobs_fetched'] > 0) {
                $stats['jobs_by_category'][$categoryName] = $stats['jobs_fetched'];
            }
            
            $stats['category_response_time'] = microtime(true) - $startTime;
            
            Log::info("ACBAR Category Processing: Complete processing finished", [
                'provider_identifier' => $providerIdentifier,
                'category_name' => $categoryName,
                'jobs_fetched' => $stats['jobs_fetched'],
                'jobs_created' => $stats['created'],
                'jobs_updated' => $stats['updated'],
                'response_time' => round($stats['category_response_time'], 2),
                'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);
            
        } catch (\Exception $e) {
            // Classify and track the error
            $errorType = $this->classifyError($e);
            $stats['error_types'][$errorType] = 1;
            $stats['errors']++;
            $stats['category_response_time'] = microtime(true) - $startTime;
            
            Log::error("ACBAR Category Processing: Error in complete processing", [
                'provider_identifier' => $providerIdentifier,
                'error_type' => $errorType,
                'error' => $e->getMessage(),
                'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);
        }

        return $stats;
    }

    /**
     * Implement smart delay between categories based on processing results
     * This prevents rate limiting and ensures proper system recovery time
     * 
     * @param array $stats Processing statistics from the category
     * @param float $categoryDuration Time taken to process the category
     * @return void
     */
    private function smartDelayBetweenCategories(array $stats, float $categoryDuration): void
    {
        // Base delay (minimum wait time)
        $baseDelay = 3; // 3 seconds minimum
        
        // Calculate additional delay based on jobs processed
        $jobsProcessed = $stats['jobs_fetched'] ?? 0;
        $jobBasedDelay = min($jobsProcessed * 0.1, 5); // Max 5 seconds for jobs
        
        // Calculate additional delay based on processing time
        $timeBasedDelay = min($categoryDuration * 0.2, 3); // Max 3 seconds for time
        
        // Additional delay if there were errors
        $errorDelay = 0;
        if (!empty($stats['error_types'])) {
            $errorDelay = 2; // Extra 2 seconds if there were errors
        }
        
        // Calculate total delay
        $totalDelay = $baseDelay + $jobBasedDelay + $timeBasedDelay + $errorDelay;
        $totalDelay = min($totalDelay, 15); // Cap at 15 seconds maximum
        
        Log::info("ACBAR Sequential Processing: Smart delay between categories", [
            'base_delay' => $baseDelay,
            'job_based_delay' => round($jobBasedDelay, 2),
            'time_based_delay' => round($timeBasedDelay, 2),
            'error_delay' => $errorDelay,
            'total_delay' => round($totalDelay, 2),
            'jobs_processed' => $jobsProcessed,
            'category_duration' => round($categoryDuration, 2),
            'has_errors' => !empty($stats['error_types'])
        ]);
        
        // Execute the delay
        sleep((int) $totalDelay);
        
        // Add micro-delay for fractional seconds
        $microSeconds = ($totalDelay - floor($totalDelay)) * 1000000;
        if ($microSeconds > 0) {
            usleep((int) $microSeconds);
        }
    }

    /**
     * Clear category-specific cache and optimize memory usage
     * 
     * @return void
     */
    private function clearCategoryCache(): void
    {
        // Clear the category cache
        $this->categoryCache = [];
        
        // Clear any static caches
        static::$canonicalCategoriesCache = [];
        
        Log::debug("ACBAR Category Processing: Cache cleared for next category");
    }

    /**
     * Send synchronous email notifications for a processed job
     * This method is adapted from JobsAfService::sendJobNotificationEmail()
     * to provide immediate email delivery for ACBAR jobs
     *
     * @param \Modules\JobSeeker\Entities\Job $job The processed job
     * @return bool Success status
     */
    protected function sendSynchronousEmailNotification(\Modules\JobSeeker\Entities\Job $job): bool
    {
        try {
            Log::info('AcbarJobService: Preparing to send synchronous job notification', [
                'job_id' => $job->id,
                'job_title' => $job->position,
                'company' => $job->company_name
            ]);

            // Ensure job has categories loaded
            if (!$job->relationLoaded('categories')) {
                $job->load('categories');
            }

            $jobCategories = $job->categories->pluck('id')->toArray();
            if (empty($jobCategories)) {
                Log::warning('AcbarJobService: Job processed without categories, skipping notification', [
                    'job_id' => $job->id
                ]);
                return false;
            }

            // Format job data for email (single job array)
            $jobData = [
                'id' => $job->id,
                'position' => $job->position,
                'company_name' => $job->company_name,
                'locations' => $job->locations,
                'expire_date' => $job->expire_date,
                'publish_date' => $job->publish_date,
                'categories' => $jobCategories,
                'source' => $job->source
            ];

            $allJobs = [$jobData];

            // Get all active notification setups that match job categories
            // Note: We ignore provider_name to allow ACBAR jobs to trigger Jobs.af setups
            $setups = JobNotificationSetup::with(['categories', 'recipients', 'jobSeeker'])
                ->where('is_active', true)
                ->whereHas('categories', function ($query) use ($jobCategories) {
                    $query->whereIn('job_categories.id', $jobCategories);
                })
                ->get();

            if ($setups->isEmpty()) {
                Log::info("AcbarJobService: No active notification setups found for job categories", [
                    'job_id' => $job->id,
                    'categories' => $jobCategories
                ]);
                return false;
            }

            Log::info("AcbarJobService: Found matching notification setups", [
                'job_id' => $job->id,
                'setup_count' => $setups->count(),
                'setup_names' => $setups->pluck('name')->toArray()
            ]);

            // Pre-fetch existing notifications to avoid duplicates
            $setupIds = $setups->pluck('id')->all();
            $allRecipientEmails = $setups->flatMap(function ($setup) {
                $emails = $setup->recipients->pluck('email');
                if ($setup->jobSeeker) {
                    $emails->push($setup->jobSeeker->email);
                }
                return $emails;
            })->unique()->filter()->all();

            $existingNotifications = JobNotificationSentJob::where('setup_id', $setupIds)
                ->where('job_id', $job->id)
                ->whereIn('recipient_email', $allRecipientEmails)
                ->get();

            $sentMap = [];
            foreach ($existingNotifications as $sent) {
                $key = $sent->setup_id . '-' . $sent->job_id . '-' . $sent->recipient_email;
                $sentMap[$key] = true;
            }

            $sentCount = 0;
            $notificationsToCreate = [];

            foreach ($setups as $setup) {
                $setupCategories = $setup->categories->pluck('id')->toArray();
                if (empty($setupCategories)) {
                    continue;
                }

                // Check if job categories match setup categories
                $relevantJobs = array_filter($allJobs, function ($jobItem) use ($setupCategories) {
                    if (!isset($jobItem['categories']) || !is_array($jobItem['categories'])) {
                        return false;
                    }
                    return !empty(array_intersect($jobItem['categories'], $setupCategories));
                });

                if (empty($relevantJobs)) {
                    continue;
                }

                // Collect recipients
                $recipients = [];
                if ($setup->jobSeeker && $setup->jobSeeker->email) {
                    $recipients[] = [
                        'email' => $setup->jobSeeker->email,
                        'name' => $setup->jobSeeker->name ?? 'Job Seeker'
                    ];
                }
                foreach ($setup->recipients as $recipient) {
                    if ($recipient->email) {
                        $recipients[] = [
                            'email' => $recipient->email,
                            'name' => $recipient->name ?? 'Recipient'
                        ];
                    }
                }

                // Remove duplicate recipients
                $uniqueRecipients = [];
                $emailsSeen = [];
                foreach ($recipients as $recipient) {
                    if (!isset($emailsSeen[$recipient['email']])) {
                        $uniqueRecipients[] = $recipient;
                        $emailsSeen[$recipient['email']] = true;
                    }
                }
                $recipients = $uniqueRecipients;

                foreach ($recipients as $recipient) {
                    foreach ($relevantJobs as $jobItem) {
                        $sentKey = $setup->id . '-' . $jobItem['id'] . '-' . $recipient['email'];
                        if (isset($sentMap[$sentKey])) {
                            continue; // Already sent, skip
                        }

                        try {
                            // Prepare email subject with proper validation
                            $jobTitle = trim($jobItem['position'] ?? '') ?: 'Job Position';
                            $companyName = trim($jobItem['company_name'] ?? '') ?: 'Company';
                            $emailSubject = "Job Opportunity: {$jobTitle} at {$companyName}";
                            $emailView = 'jobseeker::emails.jobs.jobseeker_notification';

                            // Format jobs for email template
                            $emailData = [
                                'jobs' => $this->formatJobsForEmail([$jobItem]),
                                'jobSeeker' => (object)$recipient,
                                'currentDate' => Carbon::now()->format('F j, Y'),
                                'setupName' => $setup->name
                            ];

                            // Send email using EmailService with correct parameter order
                            $emailSuccess = $this->emailService->send(
                                $recipient['email'],    // $to
                                $emailSubject,          // $subject
                                '',                     // $body (empty - will be rendered from view)
                                $emailData,             // $viewData
                                $emailView,             // $view
                                [],                     // $attachments
                                [],                     // $cc
                                null,                   // $fromEmail
                                null                    // $fromName
                            );

                            if ($emailSuccess) {
                                // Collect data for batch insert
                                $now = now();
                                $notificationsToCreate[] = [
                                    'setup_id' => $setup->id,
                                    'job_id' => $jobItem['id'],
                                    'recipient_email' => $recipient['email'],
                                    'sent_at' => $now,
                                    'created_at' => $now,
                                    'updated_at' => $now,
                                ];
                                $sentMap[$sentKey] = true; // Mark as sent for this run
                                $sentCount++;

                                Log::info('AcbarJobService: Email notification sent synchronously', [
                                    'job_id' => $jobItem['id'],
                                    'setup_id' => $setup->id,
                                    'recipient' => $recipient['email'],
                                    'job_title' => $jobTitle
                                ]);
                            } else {
                                Log::error('AcbarJobService: Failed to send email notification', [
                                    'job_id' => $jobItem['id'],
                                    'setup_id' => $setup->id,
                                    'recipient' => $recipient['email'],
                                ]);
                            }
                        } catch (\Exception $e) {
                            Log::error('AcbarJobService: Exception sending email notification', [
                                'job_id' => $jobItem['id'] ?? 'unknown',
                                'setup_id' => $setup->id,
                                'recipient' => $recipient['email'],
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString()
                            ]);
                        }
                    }
                }
            }

            // Perform batch insert of notification tracking records
            if (!empty($notificationsToCreate)) {
                DB::table('job_notification_sent_jobs')->insertOrIgnore($notificationsToCreate);
            }

            Log::info("AcbarJobService: Synchronous job notifications completed", [
                'job_id' => $job->id,
                'notifications_sent' => $sentCount
            ]);

            return $sentCount > 0;

        } catch (\Exception $e) {
            Log::error('AcbarJobService: Critical error in synchronous email notification', [
                'job_id' => $job->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Format jobs for email template
     * Adapted from JobsAfService to ensure consistent email formatting
     *
     * @param array $jobs
     * @return array
     */
    protected function formatJobsForEmail(array $jobs): array
    {
        return array_map(function ($job) {
            return [
                'id' => $job['id'] ?? null,
                'position' => $job['position'] ?? 'Unknown Position',
                'company_name' => $job['company_name'] ?? 'Unknown Company',
                'locations' => $job['locations'] ?? 'Not specified',
                'expire_date' => $job['expire_date'] ?? null,
                'publish_date' => $job['publish_date'] ?? null,
                'source' => $job['source'] ?? 'ACBAR',
                // Add any other fields needed by the email template
            ];
        }, $jobs);
    }
}