---
title: Frontend Debugging Rules
---

- **Zero Tolerance Quality Standard**
  - Every debugging session MUST result in a completely resolved issue
  - No partial fixes or workarounds allowed
  - All root causes MUST be identified and permanently addressed
  - Comprehensive testing MUST verify the complete resolution

- **Systematic Approach**
  - Read the exact error message with complete attention to detail
  - Document every step of the debugging process
  - Identify the precise file and line number
  - Look for **common patterns** with zero tolerance for assumptions
  - Maintain a detailed debugging log for future reference

- **Common Issue Patterns**
  - **CORS errors** → Implement bulletproof CORS configuration
    - Document all headers and configurations
    - Test across all environments
    - Verify security implications
  - **Hydration mismatches** → Achieve perfect server/client synchronization
    - Comprehensive component testing
    - Full state management verification
    - Cross-browser validation
  - **Routing problems** → Implement foolproof routing logic
    - 100% test coverage for routes
    - Complete navigation testing
    - Perfect error handling
  - **State Management** → Zero tolerance for state inconsistencies
    - Complete state flow documentation
    - Comprehensive state testing
    - Perfect error recovery

- **Debugging Hierarchy**
  1. **Console errors** – Must be completely eliminated
  2. **Network failures** – Zero tolerance for unhandled requests
  3. **Logic errors** – Perfect behavioral consistency required
  4. **UI/UX issues** – Flawless visual implementation
  5. **Performance optimizations** – Meet all performance metrics

- **Tools & Techniques**
  - Implement **comprehensive logging strategy**
  - Use **all available debugging tools**
  - Maintain **complete test coverage**
  - Perform **exhaustive cross-browser testing**
  - Document **all debugging findings**

- **Progressive Problem Solving**
  - Start with **thorough problem analysis**
  - Create **complete reproduction steps**
  - Implement **permanent solutions only**
  - Verify fix with **comprehensive testing**
  - Document **resolution and prevention**

- **Quality Assurance**
  - **100% resolution verification**
  - **Cross-browser compatibility**
  - **Performance impact analysis**
  - **Security implications review**
  - **Accessibility compliance check**

- **Documentation Requirements**
  - **Complete issue description**
  - **Detailed resolution steps**
  - **Prevention measures**
  - **Testing procedures**
  - **Future recommendations**

- **Post-Resolution Protocol**
  - **Verify in all environments**
  - **Update related documentation**
  - **Share knowledge with team**
  - **Implement preventive measures**
  - **Monitor for recurrence**


  - **Don't over-engineer** solutions.
  - Break complex problems into **smaller parts**.
  - **Always verify** the fix actually works before moving on.

