-- 
-- Populate provider_job_categories table with existing config mappings
-- This enables the dynamic provider category mapping system
-- 

-- First, let's clear any existing data to avoid duplicates
DELETE FROM provider_job_categories WHERE provider_name IN ('jobs.af', 'acbar');

-- 
-- Jobs.af Category Mappings
-- These categories come from config('jobseeker.jobs_af_default_filters.searchFilters.categories')
-- For Jobs.af, the provider_identifier is the exact category name sent to their API
-- 

-- IT/Technology categories
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('jobs.af', 'IT - Software', 'IT - Software', 1),
('jobs.af', 'Information Technology', 'Information Technology', 1),
('jobs.af', 'Software engineering', 'Software engineering', 1),
('jobs.af', 'software development', 'software development', 1),
('jobs.af', 'software development ', 'software development ', 1),
('jobs.af', 'Computer Science', 'Computer Science', 1);

-- Management categories  
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('jobs.af', 'Management', 'Management', 2),
('jobs.af', 'Leadership', 'Leadership', 3);

-- Marketing category
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('jobs.af', 'Sales/Marketing', 'Sales/Marketing', 4);

-- Human Resources category
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('jobs.af', 'Human Resources', 'Human Resources', 8);

-- Transportation category
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('jobs.af', 'Transportation', 'Transportation', 9);

-- Research category
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('jobs.af', 'Research/Survey', 'Research/Survey', 11);

-- Administration category
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('jobs.af', 'Administrative', 'Administrative', 12);

-- Other Jobs.af categories
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('jobs.af', 'Consulting', 'Consulting', 2),        -- Mapped to Management
('jobs.af', 'Security/Safety', 'Security/Safety', 17),
('jobs.af', 'Travel/Tourism', 'Travel/Tourism', 12), -- Mapped to Administration
('jobs.af', 'Internships', 'Internships', 12),      -- Mapped to Administration  
('jobs.af', 'Translation', 'Translation', 6),
('jobs.af', 'Industrial', 'Industrial', 14),        -- Mapped to Engineering
('jobs.af', 'Nursing', 'Nursing', 15),              -- Mapped to Health
('jobs.af', 'Customer Service', 'Customer Service', 8), -- Mapped to Human Resources
('jobs.af', 'Business Administration', 'Business Administration', 2), -- Mapped to Management
('jobs.af', 'Social Science', 'Social Science', 12), -- Mapped to Administration
('jobs.af', 'Human Rights', 'Human Rights', 18),     -- Mapped to Legal
('jobs.af', 'Graphic Designer', 'Graphic Designer', 1); -- Mapped to Information Technology

-- 
-- ACBAR Category Mappings  
-- These come from config('jobseeker.acbar_default_filters.category_mapping')
-- For ACBAR, the provider_identifier is the numeric category ID sent to their API
-- The mapping is: ACBAR_ID => CANONICAL_ID
-- 

-- Information Technology (canonical_category_id = 1)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Design', '14', 1),
('acbar', 'Information Technology', '31', 1),
('acbar', 'Telecommunications', '58', 1),
('acbar', 'Telecom', '64', 1),
('acbar', 'IT', '70', 1);

-- Management (canonical_category_id = 2)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Business Development', '10', 2),
('acbar', 'General Business', '24', 2),
('acbar', 'Management', '36', 2),
('acbar', 'Strategy-Planning', '56', 2),
('acbar', 'Consultant', '12', 2),
('acbar', 'Professional Services', '44', 2),
('acbar', 'Real Estate', '48', 2),
('acbar', 'Franchise', '22', 2);

-- Leadership (canonical_category_id = 3)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Executive', '19', 3);

-- Marketing (canonical_category_id = 4)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Marketing', '37', 4),
('acbar', 'Media-Journalism', '38', 4),
('acbar', 'Sales', '52', 4),
('acbar', 'Communication', '68', 4);

-- Education (canonical_category_id = 5)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Education', '16', 5),
('acbar', 'Training', '59', 5),
('acbar', 'Capacity Building', '71', 5);

-- Translation (canonical_category_id = 6)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Translator', '60', 6);

-- Programme (canonical_category_id = 7)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Agriculture', '6', 7),
('acbar', 'Government', '26', 7),
('acbar', 'Monitoring and Evaluation', '39', 7),
('acbar', 'Nonprofit-Social Services', '40', 7),
('acbar', 'Program', '45', 7),
('acbar', 'Natural Resources Management', '65', 7),
('acbar', 'Coordination', '72', 7);

-- Human Resources (canonical_category_id = 8)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Customer Service', '13', 8),
('acbar', 'Human Resources', '30', 8);

-- Transportation (canonical_category_id = 9)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Automotive', '7', 9),
('acbar', 'Transportation', '61', 9);

-- Research/Survey (canonical_category_id = 11)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Biotech', '9', 11),
('acbar', 'Research', '49', 11),
('acbar', 'Science', '53', 11);

-- Administration (canonical_category_id = 12)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Admin-Clerical', '5', 12),
('acbar', 'Facilities', '20', 12),
('acbar', 'General', '23', 12),
('acbar', 'General Labor', '25', 12),
('acbar', 'Other', '42', 12),
('acbar', 'Support', '67', 12),
('acbar', 'Entry Level', '18', 12),
('acbar', 'Grocery', '27', 12),
('acbar', 'Hospitality-Hotel', '29', 12),
('acbar', 'Restaurant-Food Service', '50', 12),
('acbar', 'Retail', '51', 12);

-- Finance (canonical_category_id = 13)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Banking', '8', 13),
('acbar', 'Finance', '21', 13),
('acbar', 'Insurance', '33', 13),
('acbar', 'Accounting', '69', 13);

-- Engineering (canonical_category_id = 14)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Construction', '11', 14),
('acbar', 'Engineering', '17', 14),
('acbar', 'Installation-Maint-Repair', '32', 14),
('acbar', 'QA-Quality Control', '47', 14),
('acbar', 'Skilled Labor', '55', 14);

-- Health (canonical_category_id = 15)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Health Care', '28', 15),
('acbar', 'Nurse', '41', 15),
('acbar', 'Pharmaceutical', '43', 15),
('acbar', 'Veterinary Services', '62', 15);

-- Logistics (canonical_category_id = 16)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Distribution-Shipping', '15', 16),
('acbar', 'Inventory', '34', 16),
('acbar', 'Purchasing-Procurement', '46', 16),
('acbar', 'Supply Chain', '57', 16),
('acbar', 'Warehouse', '63', 16);

-- Security (canonical_category_id = 17)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Security', '54', 17);

-- Legal (canonical_category_id = 18)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id) VALUES
('acbar', 'Legal', '35', 18);

-- Verify the data was inserted correctly
SELECT 
    provider_name,
    COUNT(*) as category_count,
    GROUP_CONCAT(DISTINCT canonical_category_id ORDER BY canonical_category_id) as mapped_canonical_ids
FROM provider_job_categories 
WHERE provider_name IN ('jobs.af', 'acbar')
GROUP BY provider_name;

-- Show sample mappings for verification
SELECT 
    pjc.provider_name,
    pjc.name as provider_category_name,
    pjc.provider_identifier,
    jc.name as canonical_category_name,
    jc.id as canonical_category_id
FROM provider_job_categories pjc
JOIN job_categories jc ON pjc.canonical_category_id = jc.id
WHERE jc.is_canonical = true
ORDER BY pjc.provider_name, jc.name; 