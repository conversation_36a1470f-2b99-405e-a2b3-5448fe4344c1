# ClassCalendarDataController Implementation

## Overview
The `ClassCalendarDataController` has been enhanced to handle different program types and their corresponding report queries. The controller now dynamically determines which queries to execute based on the programs associated with a given class.

## Program Types and Report Tables

### 1. Nouranya Program
- **Detection**: Program titles containing "Nouranya", "Qaedah", or "Arabic Language"
- **Report Table**: `student_nouranya_report`
- **Completion Criteria**:
  - Level 1: `from_lesson` AND `to_lesson` not null
  - Level 2: `from_lesson`, `to_lesson`, `from_lesson_line_number`, `to_lesson_line_number` not null
  - Level 3: `talaqqi_from_lesson`, `talaqqi_to_lesson`, `talqeen_from_lesson`, `talqeen_to_lesson` not null

### 2. Memorization and Revision Program
- **Detection**: Program titles containing "Memorization", "Revision", or "Hefz"
- **Report Tables**:
  - `student_hefz_report` (Memorization)
  - `student_revision_report` (Revision)
- **Completion Criteria**:
  - **Hefz**: `hefz_from_surat`, `hefz_from_ayat`, `hefz_to_surat`, `hefz_to_ayat` not null
  - **Revision**: `revision_from_surat`, `revision_from_ayat`, `revision_to_surat`, `revision_to_ayat` not null

### 3. Ijazah and Sanad Program
- **Detection**: Program titles containing "Ijaza", "Sanad", or "Ijazah"
- **Report Tables**:
  - `student_ijazasanad_memorization_report`
  - `student_ijazasanad_revision_report`
- **Completion Criteria**:
  - **Memorization**: Multiple levels of completion supported:
    - Basic: `hefz_from_surat` AND `hefz_to_surat` not null
    - Advanced: `hefz_from_surat`, `hefz_from_ayat`, `hefz_to_surat`, `hefz_to_ayat` not null
    - Lesson-based: Various lesson types (talqeen, revision, jazariyah, seminars)
  - **Revision**: `revision_from_surat` AND `revision_to_surat` not null

## Implementation Details

### Controller Logic
1. **Class and Program Retrieval**: Gets the class with its associated programs and translations using Eloquent relationships
2. **Date Range Processing**: Extracts `start` and `end` parameters from the request for date filtering
3. **Title-Only Program Detection**: Uses helper methods to check ONLY program titles with flexible matching:
   - `hasNouranyaProgram()`: Checks titles for Nouranya-related keywords (Nouranya, Qaedah, Arabic Language)
   - `hasMemorizationRevisionProgram()`: Checks titles for memorization keywords (Memorization, Revision, Hefz)
   - `hasIjazahSanadProgram()`: Checks titles for Ijazah keywords (Ijaza, Sanad, Ijazah)
4. **Translation Trait Integration**: Uses `astrotomic/laravel-translatable` package to access program titles
5. **Query Execution with Date Filtering**: Executes appropriate queries based on program types detected, applying date range filters to `created_at` column
6. **Result Merging**: Combines results from multiple report types into a single collection
7. **Debug Logging**: Logs program titles and date filters for troubleshooting (can be removed in production)

### Query Structure
Each query follows the pattern:
```sql
SELECT CONCAT("[Program Type]: ", COUNT(*), " st reported") as title,
       date(created_at) as start
FROM [report_table]
WHERE class_id = [classId]
  AND [completion_criteria]
  AND created_at >= '[startDate] 00:00:00'  -- if startDate provided
  AND created_at <= '[endDate] 23:59:59'    -- if endDate provided
GROUP BY date(created_at)
```

### Date Filtering
- **Date Column**: All queries use `created_at` column for date filtering
- **Frontend Parameters**: Accepts `start` and `end` query parameters (e.g., `?start=2025-06-29&end=2025-08-10`)
- **Time Handling**: Start date includes 00:00:00, end date includes 23:59:59 for full day coverage
- **Model Integration**: Follows the same pattern as model `scopeAfter` and `scopeBefore` methods

### Calendar Event Titles
- Nouranya: "Nouranya: X st reported"
- Memorization: "Memorization: X st reported"
- Revision: "Revision: X st reported"
- Ijaza Memorization: "Ijaza Memorization: X st reported"
- Ijaza Revision: "Ijaza Revision: X st reported"

## Database Relationships
- Classes can have multiple programs (many-to-many via `class_programs` table)
- Each program type has its own dedicated report tables
- Reports are linked to classes via `class_id` foreign key

## Usage
The controller is invoked as a single action controller:
```php
Route::get('/class-calendar-data/{classId}', ClassCalendarDataController::class);
```

### Example Requests
```
GET /class-calendar-data/260
GET /class-calendar-data/260?start=2025-06-29&end=2025-08-10
```

The controller automatically detects the program types for the given class and returns appropriate calendar data for all applicable programs, filtered by the provided date range if specified.
