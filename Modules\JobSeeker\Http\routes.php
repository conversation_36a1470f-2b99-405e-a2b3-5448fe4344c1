<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\JobSeeker\Http\Controllers\JobsController;
use <PERSON><PERSON><PERSON>\JobSeeker\Http\Controllers\Admin\JobsAfScheduleController;
use Mo<PERSON>les\JobSeeker\Http\Controllers\Admin\EmailControlBoardController;

// Route::group(['middleware' => ['web', 'auth:job_seeker', 'jobseeker.throttle'], 'prefix' => 'jobseeker'], function () {
    Route::group(['middleware' => ['web', 'auth:job_seeker'], 'prefix' => 'jobseeker'], function () {

    // Dashboard route




    Route::get('/dashboard', [JobsController::class, 'dashboard'])->name('jobseeker.dashboard');

    // Temporary test route for debugging time extraction
    Route::get('/test-time-extraction', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'testTimeExtraction']);
    
    // Jobs routes with search throttling for data endpoints
    Route::get('/jobs', 'JobsController@index')->name('jobseeker.index');
    Route::get('/jobs/widget/{days?}', 'JobsController@dashboardWidget')->name('jobseeker.widget');

    // Public jobs routes with search throttling
    Route::get('/public-jobs', 'JobsController@publicIndex')->name('jobseeker.public')->middleware('jobseeker.search.throttle');
    Route::get('/jobs/data', 'JobsController@getJobsData')->name('jobseeker.data')->middleware('jobseeker.search.throttle');

    // Company Watchlist Routes
    Route::group(['prefix' => 'jobs/company-watchlist', 'as' => 'company_watchlist.'], function () {
        Route::get('/', 'JobCompanyWatchlistController@index')->name('index');
        Route::get('/create', 'JobCompanyWatchlistController@create')->name('create');
        Route::post('/', 'JobCompanyWatchlistController@store')->name('store');
        Route::get('/{id}', 'JobCompanyWatchlistController@show')->name('show');
        Route::get('/{id}/edit', 'JobCompanyWatchlistController@edit')->name('edit');
        Route::put('/{id}', 'JobCompanyWatchlistController@update')->name('update');
        Route::delete('/{id}', 'JobCompanyWatchlistController@destroy')->name('destroy');
        // API Routes with search throttling for search endpoints
        Route::post('/quick-add', 'JobCompanyWatchlistController@quickAdd')->name('quick_add');
        Route::get('/companies/search', 'JobCompanyWatchlistController@getCompanies')->name('companies.search')->middleware('jobseeker.search.throttle');
        Route::get('/{id}/jobs', 'JobCompanyWatchlistController@getJobs')->name('jobs');
    });

    // Job notification management
    Route::get('/jobs/notifications', 'JobsController@notifications')->name('jobseeker.notifications');
    Route::post('/jobs/subscribe', 'JobsController@subscribe')->name('jobseeker.subscribe');
    Route::post('/jobs/unsubscribe', 'JobsController@unsubscribe')->name('jobseeker.unsubscribe');
    Route::get('/jobs/verify-subscription', 'JobsController@verifySubscription')->name('jobseeker.verify-subscription');
    
    // Notification setup data routes
    Route::get('/jobs/notification-setup/data', 'JobsController@getNotificationSetupData')->name('jobseeker.notification-setup.data');
    Route::get('/jobs/notification-setup/check-push-status', 'JobsController@checkPushNotificationStatus')->name('jobseeker.notification-setup.check-push-status');
    
    // Notification setup CRUD routes
    Route::post('/jobs/notification-setup', 'JobsController@storeNotificationSetup')->name('jobseeker.notification-setup.store');
    Route::post('/jobs/notification-setup/bulk-delete', 'JobsController@bulkDeleteNotificationSetup')->name('jobseeker.notification-setup.bulk-delete');
    Route::delete('/jobs/notification-setup/{id}', 'JobsController@deleteNotificationSetup')->name('jobseeker.notification-setup.delete');
    Route::put('/jobs/notification-setup/{id}', 'JobsController@updateNotificationSetup')->name('jobseeker.notification-setup.update');

   
    // API endpoint to fetch jobs by category for notification modal with search throttling
    Route::get('/jobs/notifications/category/{categoryId}/jobs', 'JobsController@getJobsByCategory')->name('jobseeker.category.jobs')->middleware('jobseeker.search.throttle');

    // Job Notification Management Routes
    Route::get('jobs/notification-setup/{id}', 'JobsController@getNotificationSetup')->name('jobseeker.notification-setup.get');
    Route::post('jobs/notification-setup/{id}', 'JobsController@updateNotificationSetup')->name('jobseeker.notification-setup.update');
    
    // Job Notification Recipients Management Routes (Legacy - for backward compatibility)
    Route::get('recipients', 'JobsController@recipients')->name('jobseeker.recipients');
    Route::get('recipients/data', 'JobsController@getRecipients')->name('jobseeker.recipients.data')->middleware('jobseeker.search.throttle');
    Route::get('recipients/search', 'JobsController@searchRecipients')->name('jobseeker.recipients.search')->middleware('jobseeker.search.throttle');
    Route::get('recipients/template', 'JobsController@downloadRecipientsTemplate')->name('jobseeker.recipients.template');
    Route::get('recipients/get/{id}', 'JobsController@getRecipient')->name('jobseeker.recipients.get');
    Route::post('recipients/store', 'JobsController@storeRecipient')->name('jobseeker.recipients.store');
    Route::put('recipients/update/{id}', 'JobsController@updateRecipient')->name('jobseeker.recipients.update');
    Route::delete('recipients/delete/{id}', 'JobsController@deleteRecipient')->name('jobseeker.recipients.delete');
    Route::post('recipients/import', 'JobsController@importRecipients')->name('jobseeker.recipients.import');

    // Personal Contacts Hub Routes (New enhanced functionality)
    Route::get('personal-contacts/search', 'JobsController@searchPersonalContacts')->name('jobseeker.personal-contacts.search')->middleware('jobseeker.search.throttle');
    Route::get('personal-contacts/get/{id}', 'JobsController@getPersonalContact')->name('jobseeker.personal-contacts.get');
    Route::post('personal-contacts/store', 'JobsController@storePersonalContact')->name('jobseeker.personal-contacts.store');
    Route::put('personal-contacts/update/{id}', 'JobsController@updatePersonalContact')->name('jobseeker.personal-contacts.update');
    Route::delete('personal-contacts/delete/{id}', 'JobsController@destroyPersonalContact')->name('jobseeker.personal-contacts.delete');

 
    // Monitoring Routes
    Route::get('monitoring', 'JobNotificationMonitoringController@index')->name('jobseeker.monitoring');
    Route::post('monitoring/retry', 'JobNotificationMonitoringController@retryFailedJob')->name('jobseeker.monitoring.retry');
    Route::get('monitoring/clear-failed', 'JobNotificationMonitoringController@clearFailedJobs')->name('jobseeker.monitoring.clear-failed');
    Route::post('monitoring/retry-notification', 'JobNotificationMonitoringController@retryNotificationFailure')->name('jobseeker.monitoring.retry-notification');
    Route::post('monitoring/clear-rate-limit', 'JobNotificationMonitoringController@clearRateLimit')->name('jobseeker.monitoring.clear-rate-limit');
    
    // Job Operations Routes
    Route::get('operations', [JobsController::class, 'showJobOperationsPage'])->name('jobseeker.jobs.operations');
    Route::post('execute-sync', [JobsController::class, 'executeSyncJobsCommand'])->name('jobseeker.jobs.executeSync');
    Route::post('execute-acbar-sync', [JobsController::class, 'executeAcbarSyncCommand'])->name('jobseeker.jobs.executeAcbarSync');
    
    // User Profile and Account Management Routes
    Route::get('/profile', 'UserProfileController@profile')->name('jobseeker.profile');
    Route::put('/profile', 'UserProfileController@updateProfile')->name('jobseeker.profile.update');
    Route::get('/account/settings', 'UserProfileController@accountSettings')->name('jobseeker.account.settings');
    Route::put('/account/settings', 'UserProfileController@updateAccountSettings')->name('jobseeker.account.settings.update');
    Route::get('/account/password', 'UserProfileController@changePassword')->name('jobseeker.account.password');
    Route::put('/account/password', 'UserProfileController@updatePassword')->name('jobseeker.password.update');
    Route::get('/account/security', 'UserProfileController@securitySettings')->name('jobseeker.account.security');
    Route::put('/account/security', 'UserProfileController@updateSecuritySettings')->name('jobseeker.account.security.update');
});

// Authentication routes with auth throttling middleware
Route::group(['middleware' => ['web', 'jobseeker.auth.throttle']], function () {
    // JobSeeker authentication routes
    Route::get('/jlogin', 'Auth\LoginController@showLoginForm')->name('jobseeker.login.form');
    Route::get('/jregister', 'Auth\LoginController@showRegistrationForm')->name('jobseeker.register.form');
    Route::post('/jlogin', 'Auth\LoginController@login')->name('jobseeker.login');
    Route::post('/jregister', 'Auth\LoginController@register')->name('jobseeker.register');
    Route::post('/jlogout', 'Auth\LoginController@logout')->name('jobseeker.logout');
    Route::post('/jpr', 'Auth\LoginController@resetPassword')->name('jobseeker.password.request');
    
    // Google OAuth routes
    Route::get('/jauth/google', 'Auth\LoginController@redirectToProvider')->name('jobseeker.auth.google');
    Route::get('/jauth/google/callback', 'Auth\LoginController@handleProviderCallback')->name('jobseeker.auth.google.callback');
    
    // Email verification routes (JobSeeker-specific)
    Route::get('/jobseeker/email/verify', 'EmailVerificationController@notice')->name('jobseeker.verification.notice');
    Route::get('/jobseeker/email/verify/{id}/{hash}', 'EmailVerificationController@verify')->name('jobseeker.verification.verify')->middleware('signed');
    Route::post('/jobseeker/email/verification-notification', 'EmailVerificationController@send')->name('jobseeker.verification.send')->middleware('auth:job_seeker');
    
    // Static pages routes
    Route::get('/terms', function () {
        return view('jobseeker::terms');
    })->name('jobseeker.terms');
    
    Route::get('/privacy', function () {
        return view('jobseeker::privacy');
    })->name('jobseeker.privacy');
});

// Password management routes (requires authentication)
Route::group(['middleware' => ['web', 'auth:job_seeker', 'jobseeker.throttle']], function () {
    Route::post('/password/change', 'Auth\LoginController@changePassword')->name('jobseeker.password.change');
    // Security routes commented out - controller doesn't exist
    // Route::get('/account/security', 'Auth\SecurityController@index')->name('jobseeker.account.security');
    // Route::post('/account/unlock-request', 'Auth\SecurityController@requestUnlock')->name('jobseeker.account.unlock.request');
});

// Admin routes for Jobs.af schedule management (legacy)
Route::middleware(['jobseeker.admin'])->prefix('admin/jobseeker')->name('admin.jobseeker.')->group(function () {
    // Route::prefix('jobsaf-schedule')->name('jobsaf_schedule.')->group(function () {
    //     Route::get('/', [JobsAfScheduleController::class, 'index'])->name('index');
    //     Route::get('/data', [JobsAfScheduleController::class, 'getData'])->name('data');
    //     Route::get('/command-stats/{command}', [JobsAfScheduleController::class, 'getCommandStats'])->name('command_stats');
    //     Route::post('/', [JobsAfScheduleController::class, 'store'])->name('store');
    //     Route::get('/{id}', [JobsAfScheduleController::class, 'show'])->name('show');
    //     Route::put('/{id}', [JobsAfScheduleController::class, 'update'])->name('update');
    //     Route::delete('/{id}', [JobsAfScheduleController::class, 'destroy'])->name('destroy');
    //     Route::post('/{id}/toggle', [JobsAfScheduleController::class, 'toggle'])->name('toggle');
    //     Route::post('/bulk-toggle', [JobsAfScheduleController::class, 'bulkToggle'])->name('bulk_toggle');
    //     Route::delete('/bulk-delete', [JobsAfScheduleController::class, 'bulkDelete'])->name('bulk_delete');
    // });

    // New Command Schedule management routes
    Route::prefix('command-schedule')->name('command_schedule.')->group(function () {
        Route::get('/', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'index'])->name('index');
        Route::get('/data', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getData'])->name('data');
        Route::get('/command-stats/{command}', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getCommandStats'])->name('command_stats');
        Route::get('/{id}/execution-history', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getExecutionHistory'])->name('execution_history');
        Route::get('/health-data', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getHealthDashboardData'])->name('health_data');
        Route::get('/filters/defaults', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getFilterDefaults'])->name('filters.defaults');
        Route::get('/filters/acbar-defaults', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getAcbarFilterDefaults'])->name('filters.acbar_defaults');

        // Bulk editing interface routes - MUST be before parameterized routes
        Route::get('/bulk-edit', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'bulkEditView'])->name('bulk_edit');

        // API endpoints for SPA
        Route::prefix('api')->name('api.')->group(function () {
            Route::get('/bulk-data', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getBulkData'])->name('bulk_data');
            Route::get('/bulk-data-optimized', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getBulkDataOptimized'])->name('bulk_data_optimized');
            Route::post('/update-field', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'updateField'])->name('update_field');
            Route::post('/clone-rules', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'cloneRules'])->name('clone_rules');
            Route::post('/copy-filters', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'copyFilters'])->name('copy_filters');
        });

        Route::post('/', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'store'])->name('store');
        Route::post('/bulk-toggle', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'bulkToggle'])->name('bulk_toggle');
        Route::delete('/bulk-delete', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'bulkDelete'])->name('bulk_delete');
        Route::get('/provider-categories/{provider}', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getProviderCategories'])->name('provider_categories');
        Route::get('/provider-categories-sweetalert/{provider}', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getProviderCategoriesForSweetAlert'])->name('provider_categories_sweetalert');
        Route::get('/provider-locations-sweetalert/{provider}', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'getProviderLocationsForSweetAlert'])->name('provider_locations_sweetalert');


        // Parameterized routes - MUST be after specific routes
        Route::get('/{id}', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'show'])->name('show');
        Route::put('/{id}', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'update'])->name('update');
        Route::delete('/{id}', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'destroy'])->name('destroy');
        Route::post('/{id}/toggle', [\Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController::class, 'toggle'])->name('toggle');
    });
    
    // Email Control Board routes
    Route::prefix('email-control-board')->name('email_control_board.')->group(function () {
        Route::get('/', [EmailControlBoardController::class, 'index'])->name('index');
        Route::post('/update-configuration', [EmailControlBoardController::class, 'updateConfiguration'])->name('update_configuration');
        Route::post('/send-test-email', [EmailControlBoardController::class, 'sendTestEmail'])->name('send_test_email');
        Route::get('/fetch-logs', [EmailControlBoardController::class, 'fetchLogs'])->name('fetch_logs');
        
        // Circuit Breaker Management Routes (Epic 6)
        Route::get('/circuit-breaker/states', [EmailControlBoardController::class, 'fetchCircuitBreakerStates'])->name('fetch_circuit_breaker_states');
        Route::post('/circuit-breaker/reset', [EmailControlBoardController::class, 'resetCircuitBreaker'])->name('reset_circuit_breaker');
        Route::post('/circuit-breaker/config', [EmailControlBoardController::class, 'updateCircuitBreakerConfig'])->name('update_circuit_breaker_config');
        
        // Emergency Controls Routes (Epic 7)
        Route::get('/emergency-pause/status', [EmailControlBoardController::class, 'getEmergencyPauseStatus'])->name('get_emergency_pause_status');
        Route::post('/emergency-pause/toggle', [EmailControlBoardController::class, 'toggleEmergencyPause'])->name('toggle_emergency_pause');
        
        // Recovery Operations Routes (Epic 8)
        Route::get('/recovery/stats', [EmailControlBoardController::class, 'getRecoveryStats'])->name('get_recovery_stats');
        Route::post('/recovery/analyze', [EmailControlBoardController::class, 'analyzeFailedEmails'])->name('analyze_failed_emails');
        Route::post('/recovery/retry', [EmailControlBoardController::class, 'retryFailedEmails'])->name('retry_failed_emails');
        Route::post('/recovery/export', [EmailControlBoardController::class, 'exportEmails'])->name('export_emails');
        Route::post('/recovery/reset-attempts', [EmailControlBoardController::class, 'resetEmailAttempts'])->name('reset_email_attempts');
        
        // Outbox Management Routes (Epic 8)
        Route::get('/outbox/browse', [EmailControlBoardController::class, 'browseOutbox'])->name('browse_outbox');
        Route::get('/outbox/data', [EmailControlBoardController::class, 'getOutboxData'])->name('get_outbox_data');
        Route::get('/outbox/statistics', [EmailControlBoardController::class, 'getOutboxStatistics'])->name('get_outbox_statistics');
    });

    // Test route for notification system
    Route::get('/test-notifications/{setupId}', 'JobsController@testNotificationSystem')->name('test-notifications');
});





// Device Token Management Routes (requires authentication)
Route::group(['middleware' => ['web', 'auth:job_seeker']], function () {
    Route::post('/device-tokens', 'UserDeviceTokenController@store')->name('jobseeker.device-tokens.store');
    Route::delete('/device-tokens/{token}', 'UserDeviceTokenController@destroy')->name('jobseeker.device-tokens.destroy');
    Route::get('/device-tokens/cleanup', 'UserDeviceTokenController@cleanup')->name('jobseeker.device-tokens.cleanup');
});



