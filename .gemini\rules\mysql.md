---
description: "MySQL Database Interaction and Connection Rules"
globs: ["**/*.php", "**/*.sql"]
alwaysApply: true
---

# MySQL Database Protocol

This document outlines the rules and connection details for interacting with the MySQL database in this project.

## Connection Details

-   **Database:** `itqan`
-   **Username:** `root`
-   **Password:** (none)
-   **Command:** `mysql -u root itqan`

## Key Rules

-   **Use Eloquent ORM:** All application-level data manipulation (CRUD) must be done through Lara<PERSON>'s Eloquent ORM.
-   **No Raw SQL for CRUD:** Avoid using raw SQL queries for create, read, update, and delete operations. Use Eloquent's methods and relationships.
-   **Raw SQL for Schema/Seeding:** Schema changes and initial data seeding should be performed directly via MySQL queries, not Laravel migrations or seeders.
-   **Eloquent Relationships:** Always define and use proper Eloquent relationships (`hasMany`, `belongsTo`, etc.) instead of manual joins in queries.
-   **Query Optimization:** Use eager loading (`with()`) to prevent N+1 query problems.
-   **No `RefreshDatabase` Trait:** The `Illuminate\Foundation\Testing\RefreshDatabase` trait is strictly forbidden in all tests.
-   **Protect `.env`:** Never modify the `.env` file directly in the application code.
