<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Modules\JobSeeker\Entities\CommandScheduleRule;
use Mo<PERSON><PERSON>\JobSeeker\Entities\CommandScheduleExecution;
use Mo<PERSON><PERSON>\JobSeeker\Entities\CommandScheduleFilter;
use Mo<PERSON>les\JobSeeker\Services\CommandScheduleService;
use Mo<PERSON>les\JobSeeker\Services\BulkValidationService;
use Modules\JobSeeker\Services\ProviderMappingService;
use Modules\JobSeeker\Repositories\FilterRepository;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;

/**
 * Admin controller for managing Command Schedule Rules
 * 
 * Provides CRUD operations and management interface for dynamic scheduling
 * of any Laravel Artisan commands with comprehensive execution tracking
 */
final class CommandScheduleController extends Controller
{
    /**
     * Filter repository for managing schedule filters
     *
     * @var FilterRepository
     */
    protected FilterRepository $filterRepository;

    /**
     * Bulk validation service for comprehensive validation
     *
     * @var BulkValidationService
     */
    protected BulkValidationService $bulkValidationService;

    /**
     * Constructor - Apply admin middleware
     */
    public function __construct(FilterRepository $filterRepository, BulkValidationService $bulkValidationService)
    {
        // Middleware is now handled in routes
        // Authentication is handled by jobseeker.admin middleware

        $this->filterRepository = $filterRepository;
        $this->bulkValidationService = $bulkValidationService;

        Log::info('CommandScheduleController: Initialized for admin schedule management');
    }

    /**
     * Display the schedule rules management interface
     *
     * @param Request $request
     * @return View
     */
    public function index(Request $request): View
    {
        // Use job_seeker guard for authentication
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('CommandScheduleController: Accessing schedule rules management', [
            'admin_user' => $user->email ?? 'unknown',
            'user_id' => $user->id ?? 'unknown',
            'ip' => $request->ip()
        ]);

        $stats = [
            'total_rules' => CommandScheduleRule::count(),
            'active_rules' => CommandScheduleRule::where('is_active', true)->count(),
            'sync_rules' => CommandScheduleRule::where('command', 'jobseeker:sync-jobs-af')->count(),
            'notification_rules' => CommandScheduleRule::where('command', 'like', '%notify%')->count(),
        ];

        // Execution statistics for recent activity
        $executionStats = [
            'total_executions' => CommandScheduleExecution::count(),
            'running_executions' => CommandScheduleExecution::where('status', CommandScheduleExecution::STATUS_RUNNING)->count(),
            'failed_today' => CommandScheduleExecution::where('status', CommandScheduleExecution::STATUS_FAILED)
                ->whereDate('started_at', today())->count(),
            'completed_today' => CommandScheduleExecution::where('status', CommandScheduleExecution::STATUS_COMPLETED)
                ->whereDate('started_at', today())->count(),
        ];

        // Add debug information for DataTables troubleshooting
        $debugInfo = [
            'route_url' => route('admin.jobseeker.command_schedule.data'),
            'csrf_token' => csrf_token(),
            'user_authenticated' => Auth::guard('job_seeker')->check(),
            'user_email' => $user->email ?? 'N/A',
            'current_url' => $request->url(),
        ];

        return view('modules.jobseeker.admin.command_schedule.index', [
            'stats' => $stats,
            'execution_stats' => $executionStats,
            'timezones' => $this->getAvailableTimezones(),
            'available_commands' => $this->getAvailableCommands(),
            'debug' => $debugInfo
        ]);
    }

    /**
     * Display the bulk edit interface
     *
     * @param Request $request
     * @return View
     */
    public function bulkEditView(Request $request): View
    {
        $user = Auth::guard('job_seeker')->user();

        Log::info('CommandScheduleController: Accessing bulk edit interface', [
            'admin_user' => $user->email ?? 'unknown',
            'user_id' => $user->id ?? 'unknown',
            'ip' => $request->ip()
        ]);

        return view('modules.jobseeker.admin.command_schedule.bulk_edit', [
            'user' => $user
        ]);
    }

    /**
     * Get schedule rules data for DataTables with advanced features
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getData(Request $request): JsonResponse
    {
        // Use job_seeker guard for authentication (if available)
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('CommandScheduleController: Fetching schedule rules data for DataTables', [
            'user_id' => $user->id ?? 'unknown',
            'user_email' => $user->email ?? 'unknown',
            'request_params' => $request->all(),
            'route_name' => $request->route()->getName()
        ]);

        try {
            // Optimize query to prevent memory exhaustion - remove execution loading initially
            // Note: We'll sort by schedule_sort_key after calculating it, not here
            $query = CommandScheduleRule::query();
            
            // Get count safely
            $totalCount = $query->count();
            
            Log::info('CommandScheduleController: Building DataTables query', [
                'total_count' => $totalCount,
                'user_email' => $user->email ?? 'unknown'
            ]);

            // Limit results to prevent memory issues
            $rules = $query->take(100)->get(); // Limit to 100 rules max
            
            // For client-side processing, return limited data
            return DataTables::collection($rules)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="row-checkbox form-check-input" value="' . $row->id . '">';
                })
                ->addColumn('name', function ($row) {
                    return '<span class="fw-medium">' . e($row->name) . '</span>';
                })
                ->editColumn('command', function ($row) {
                    // Make command clickable for stats modal
                    return '<a href="#" class="command-stats-trigger text-decoration-none" data-command-name="' . 
                           e($row->command) . '">' .
                           '<code class="bg-light p-1 rounded">' . e($row->command) . '</code>' .
                           '</a>';
                })
                ->addColumn('schedule_info', function ($row) {
                    return $row->human_readable_schedule_info ?? '<span class="text-muted">Custom Schedule</span>';
                })
                ->addColumn('next_run', function ($row) {
                    // Return raw data for frontend processing
                    return [
                        'expression' => $row->schedule_expression,
                        'timezone' => $row->timezone,
                        'is_active' => $row->is_active,
                        'next_run_at' => null // Frontend will calculate this
                    ];
                })
                ->editColumn('status', function ($row) {
                    return $row->status_badge ?? ($row->is_active ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-secondary">Disabled</span>');
                })
                ->addColumn('priority', function ($row) {
                    return '<span class="badge bg-info">' . $row->priority . '</span>';
                })
                ->addColumn('execution_status', function ($row) {
                    // Simplified execution status to prevent memory issues
                    return '<span class="badge bg-secondary">Click History</span>';
                })
               
                ->addColumn('timeout', function ($row) {
                    $timeout = $row->execution_timeout ?? $row->max_execution_time ?? 3600;
                    $concurrent = $row->concurrent_executions ?? 1;
                    return '<small class="text-muted">Timeout: ' . round($timeout/60) . 'm<br>Concurrent: ' . $concurrent . '</small>';
                })
                ->addColumn('timezone', function ($row) {
                    return '<small class="text-muted">' . e($row->timezone) . '</small>';
                })
                ->addColumn('actions', function ($row) {
                    $actions = '<div class="btn-group btn-group-sm" role="group">';
                    $actions .= '<button type="button" class="btn btn-outline-primary btn-sm view-rule" data-id="' . $row->id . '" title="View Details"><i class="fas fa-eye"></i></button>';
                    $actions .= '<button type="button" class="btn btn-outline-warning btn-sm edit-rule" data-id="' . $row->id . '" title="Edit Rule"><i class="fas fa-edit"></i></button>';
                    $actions .= '<button type="button" class="btn btn-outline-' . ($row->is_active ? 'secondary' : 'success') . ' btn-sm toggle-rule" data-id="' . $row->id . '" data-action="' . ($row->is_active ? 'disable' : 'enable') . '" title="' . ($row->is_active ? 'Disable' : 'Enable') . ' Rule"><i class="fas fa-' . ($row->is_active ? 'pause' : 'play') . '"></i></button>';
                    $actions .= '<button type="button" class="btn btn-outline-info btn-sm execution-history" data-id="' . $row->id . '" title="Execution History"><i class="fas fa-history"></i></button>';
                    $actions .= '<button type="button" class="btn btn-outline-danger btn-sm delete-rule" data-id="' . $row->id . '" title="Delete Rule"><i class="fas fa-trash"></i></button>';
                    $actions .= '</div>';
                    return $actions;
                })
                ->rawColumns(['checkbox', 'name', 'command', 'schedule_info', 'status', 'priority', 'execution_status', 'timeout', 'timezone', 'actions'])
                ->make(true);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error in getData method', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_email' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'error' => 'Failed to fetch schedule rules data',
                'message' => $e->getMessage(),
                'debug' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'user_authenticated' => Auth::guard('job_seeker')->check(),
                ]
            ], 500);
        }
    }

    /**
     * Get command statistics for modal display
     *
     * @param Request $request
     * @param string $command
     * @return JsonResponse
     */
    public function getCommandStats(Request $request, string $command): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('CommandScheduleController: Fetching command statistics', [
            'command' => $command,
            'user_email' => $user->email ?? 'unknown'
        ]);

        try {
            $stats = CommandScheduleRule::getCommandStats($command);
            
            // Get recent executions from the execution history
            $recentRuns = CommandScheduleExecution::getRecentForCommand($command, 10)
                ->map(function ($execution) {
                    return [
                        'started_at' => $execution->started_at->format('Y-m-d H:i:s'),
                        'status' => $execution->status,
                        'duration' => $execution->duration_human,
                        'memory_usage' => $execution->memory_usage_mb ? round($execution->memory_usage_mb, 2) . ' MB' : 'N/A'
                    ];
                });
            
            // Get next scheduled runs
            $service = new CommandScheduleService();
            $nextRuns = CommandScheduleRule::where('command', $command)
                ->where('is_active', true)
                ->get()
                ->map(function ($rule) use ($service) {
                    $nextRun = $service->calculateNextRunTime($rule);
                    return [
                        'rule_name' => $rule->name,
                        'schedule_expression' => $rule->schedule_expression,
                        'timezone' => $rule->timezone,
                        'next_run' => $nextRun ? $nextRun->format('Y-m-d H:i:s T') : 'Not scheduled',
                        'description' => $rule->getScheduleDescription(),
                    ];
                })
                ->take(5);

            return response()->json([
                'success' => true,
                'command' => $command,
                'stats' => $stats,
                'next_runs' => $nextRuns,
                'recent_runs' => $recentRuns
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error fetching command stats', [
                'command' => $command,
                'error' => $e->getMessage(),
                'user_email' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch command statistics'
            ], 500);
        }
    }

    /**
     * Get execution history for a specific rule
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function getExecutionHistory(Request $request, int $id): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('CommandScheduleController: Fetching execution history', [
            'rule_id' => $id,
            'user_email' => $user->email ?? 'unknown'
        ]);

        try {
            $rule = CommandScheduleRule::findOrFail($id);
            $executions = $rule->getRecentExecutions(20);
            
            $history = $executions->map(function ($execution) {
                return [
                    'id' => $execution->id,
                    'started_at' => $execution->started_at->format('Y-m-d H:i:s'),
                    'completed_at' => $execution->completed_at?->format('Y-m-d H:i:s'),
                    'status' => $execution->status,
                    'status_badge' => $execution->status_badge,
                    'duration' => $execution->duration_human,
                    'memory_usage' => $execution->memory_usage_mb ? round($execution->memory_usage_mb, 2) . ' MB' : 'N/A',
                    'exit_code' => $execution->exit_code,
                    'error_output' => $execution->error_output ? substr($execution->error_output, 0, 200) . '...' : null
                ];
            });

            return response()->json([
                'success' => true,
                'rule' => [
                    'id' => $rule->id,
                    'name' => $rule->name,
                    'command' => $rule->command
                ],
                'executions' => $history
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error fetching execution history', [
                'rule_id' => $id,
                'error' => $e->getMessage(),
                'user_email' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch execution history'
            ], 500);
        }
    }

    /**
     * Store a new schedule rule
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('CommandScheduleController: Creating new schedule rule', [
            'admin_user' => $user->email ?? 'unknown',
            'request_data' => $request->except(['_token'])
        ]);

        try {
            $request->merge(['is_active' => $request->boolean('is_active')]);
            $validator = $this->validateScheduleRule($request);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Convert to cron expression before storing
            $type = $request->input('schedule_type', 'cron');
            $cronExpr = $this->convertToCronExpression($type, $request->all());
            
            // Determine provider from the command being scheduled
            $command = $request->input('command');
            $provider = $this->getProviderFromCommand($command);
            $providerName = $this->normalizeProviderName($provider);

            // Categories and locations are now handled through CommandScheduleFilter
            // No need to process them here in the rule creation
            
            $scheduleRule = CommandScheduleRule::create([
                'name' => $request->input('name'),
                'command' => $request->input('command'),
                'schedule_expression' => $cronExpr,
                'schedule_type' => $type,
                'days_of_week' => $this->parseJsonField($request->input('days_of_week')),
                'timezone' => $request->input('timezone', 'Asia/Kabul'),
                'is_active' => $request->boolean('is_active', true),
                'priority' => $request->input('priority', 100),
                'description' => $request->input('description'),
                'depends_on_command' => $request->input('depends_on_command'),
                'delay_after_dependency' => $request->input('delay_after_dependency', 900),
                'max_execution_time' => $request->input('max_execution_time', 3600),
                'execution_timeout' => $request->input('execution_timeout', $request->input('max_execution_time', 3600)),
                'concurrent_executions' => $request->input('concurrent_executions', 1),
                'created_by' => $user->email ?? 'admin',
                'updated_by' => $user->email ?? 'admin'
            ]);

            // Handle Jobs.af filters if command is jobseeker:sync-jobs-af
            $this->handleFilters($request, $scheduleRule->id);

            DB::commit();

            Log::info('CommandScheduleController: Schedule rule created successfully', [
                'rule_id' => $scheduleRule->id,
                'rule_name' => $scheduleRule->name,
                'command' => $scheduleRule->command,
                'created_by' => $user->email
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Schedule rule created successfully',
                'rule' => $scheduleRule
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('CommandScheduleController: Error creating schedule rule', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create schedule rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified schedule rule
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $rule = CommandScheduleRule::with(['executions' => function ($query) {
                $query->latest('started_at')->limit(5);
            }])->findOrFail($id);

            Log::debug('CommandScheduleController: Fetching schedule rule details', [
                'rule_id' => $id,
                'rule_name' => $rule->name
            ]);

            // Categories and locations are now handled through CommandScheduleFilter
            // Get them from the filter relationship if it exists
            $categoryIds = $rule->filter ? ($rule->filter->categories ?? []) : [];
            $locationIds = $rule->filter ? ($rule->filter->locations ?? []) : [];

            $response = [
                'success' => true,
                'rule' => $rule,
                'recent_executions' => $rule->executions,
                'provider_job_category_ids' => $categoryIds,
                'provider_location_ids' => $locationIds // CHANGED from location_ids
            ];

            Log::debug('CommandScheduleController: Rule data being returned', [
                'rule_id' => $id,
                'provider_job_category_ids_count' => count($categoryIds),
                'provider_location_ids_count' => count($locationIds) // CHANGED from location_ids_count
            ]);

            // Include filters for both jobs.af and ACBAR commands
            if ($rule->command === 'jobseeker:sync-jobs-af' || $rule->command === 'jobseeker:sync-acbar-jobs') {
                $filterModel = $this->filterRepository->getByRule($id);
                
                // Convert CommandScheduleFilter model to array format expected by frontend
                if ($filterModel) {
                    $filters = [
                        'categories' => $filterModel->categories ?: [],
                        'locations' => $filterModel->locations ?: [],
                        'companies' => $filterModel->companies ?: [],
                        'experience_levels' => $filterModel->experience_levels ?: [],
                        'search_term' => $filterModel->search_term ?: '',
                        'work_type' => $filterModel->work_type ?: '',
                        'min_page' => $filterModel->min_page ?: null,
                        'max_page' => $filterModel->max_page ?: null,
                        // ACBAR-specific fields
                        'max_retries' => $filterModel->max_retries ?: 5,
                        'timeout' => $filterModel->timeout ?: 60,
                        'base_delay' => $filterModel->base_delay ?: 1000,
                    ];
                } else {
                    // No filters found, provide empty structure
                    $filters = [
                        'categories' => [],
                        'locations' => [],
                        'companies' => [],
                        'experience_levels' => [],
                        'search_term' => '',
                        'work_type' => '',
                        'min_page' => null,
                        'max_page' => null,
                        'max_retries' => 5,
                        'timeout' => 60,
                        'base_delay' => 1000,
                    ];
                }
                
                $response['filters'] = $filters;
                
                // For backwards compatibility, also include as command-specific key
                if ($rule->command === 'jobseeker:sync-jobs-af') {
                    $response['jobsaf_filters'] = $filters;
                    
                    // Add Jobs.af advanced settings
                    $advancedSettings = $this->getJobsAfAdvancedSettings($id);
                    $response['jobs_af_advanced'] = $advancedSettings;
                    
                } else if ($rule->command === 'jobseeker:sync-acbar-jobs') {
                    $response['acbar_filters'] = $filters;
                }
            }

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error fetching schedule rule', [
                'rule_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Schedule rule not found'
            ], 404);
        }
    }

    /**
     * Update the specified schedule rule
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('CommandScheduleController: Updating schedule rule', [
            'rule_id' => $id,
            'admin_user' => $user->email ?? 'unknown',
            'request_data' => $request->except(['_token', '_method'])
        ]);

        try {
            $rule = CommandScheduleRule::findOrFail($id);

            $request->merge(['is_active' => $request->boolean('is_active')]);
            $validator = $this->validateScheduleRule($request, $id);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Convert to cron expression before updating
            $type = $request->input('schedule_type', 'cron');
            $cronExpr = $this->convertToCronExpression($type, $request->all());
            
            // Determine provider from the command being updated
            $command = $request->input('command');
            $provider = $this->getProviderFromCommand($command);
            $providerName = $this->normalizeProviderName($provider);

            // Categories and locations are now handled through CommandScheduleFilter
            // No need to process them here in the rule update
            
            $rule->update([
                'name' => $request->input('name'),
                'command' => $request->input('command'),
                'schedule_expression' => $cronExpr,
                'schedule_type' => $type,
                'days_of_week' => $this->parseJsonField($request->input('days_of_week')),
                'timezone' => $request->input('timezone', 'Asia/Kabul'),
                'is_active' => $request->boolean('is_active'),
                'priority' => $request->input('priority', 100),
                'description' => $request->input('description'),
                'depends_on_command' => $request->input('depends_on_command'),
                'delay_after_dependency' => $request->input('delay_after_dependency', 900),
                'max_execution_time' => $request->input('max_execution_time', 3600),
                'execution_timeout' => $request->input('execution_timeout', $request->input('max_execution_time', 3600)),
                'concurrent_executions' => $request->input('concurrent_executions', 1),
                'updated_by' => $user->email ?? 'admin',
                'updated_at' => now()
            ]);

            // Handle filters for both Jobs.af and ACBAR commands (unified structure)
            $this->handleFilters($request, $rule->id);

            DB::commit();

            Log::info('CommandScheduleController: Schedule rule updated successfully', [
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'updated_by' => $user->email
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Schedule rule updated successfully',
                'rule' => $rule->fresh()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('CommandScheduleController: Error updating schedule rule', [
                'rule_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update schedule rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Soft delete a schedule rule
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('CommandScheduleController: Attempting to soft delete schedule rule', [
            'rule_id' => $id,
            'admin_user' => $user->email ?? 'unknown'
        ]);

        try {
            $rule = CommandScheduleRule::findOrFail($id);
            $adminUser = $user->email ?? 'admin';

            // Soft delete the rule
            $rule->delete();

            Log::info('CommandScheduleController: Schedule rule soft deleted successfully', [
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'deleted_by' => $adminUser
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Schedule rule deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error deleting schedule rule', [
                'rule_id' => $id,
                'error' => $e->getMessage(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete schedule rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle the status of a schedule rule (enable/disable)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function toggle(Request $request, int $id): JsonResponse
    {
        $action = $request->input('action', 'toggle');
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('CommandScheduleController: Toggling schedule rule status', [
            'rule_id' => $id,
            'action' => $action,
            'admin_user' => $user->email ?? 'unknown'
        ]);

        try {
            $rule = CommandScheduleRule::findOrFail($id);
            $adminUser = $user->email ?? 'admin';

            switch ($action) {
                case 'enable':
                    $result = $rule->enable($adminUser);
                    $message = 'Schedule rule enabled successfully';
                    break;
                    
                case 'disable':
                    $result = $rule->disable($adminUser);
                    $message = 'Schedule rule disabled successfully';
                    break;
                    
                default:
                    // Toggle current status
                    if ($rule->is_active) {
                        $result = $rule->disable($adminUser);
                        $message = 'Schedule rule disabled successfully';
                    } else {
                        $result = $rule->enable($adminUser);
                        $message = 'Schedule rule enabled successfully';
                    }
                    break;
            }

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'rule' => $rule->fresh()
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update schedule rule status'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error toggling schedule rule status', [
                'rule_id' => $id,
                'action' => $action,
                'error' => $e->getMessage(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle schedule rule status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk delete schedule rules
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'rule_ids' => 'required|array|min:1',
            'rule_ids.*' => 'integer|exists:command_schedule_rules,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $ruleIds = $request->input('rule_ids');
        $user = Auth::guard('job_seeker')->user();
        $adminUser = $user->email ?? 'admin';

        Log::info('CommandScheduleController: Bulk deleting schedule rules', [
            'rule_ids' => $ruleIds,
            'admin_user' => $adminUser
        ]);

        try {
            DB::beginTransaction();

            // Get rule names for logging before deletion
            $rulesToDelete = CommandScheduleRule::whereIn('id', $ruleIds)->get(['id', 'name', 'command']);
            $ruleNames = $rulesToDelete->pluck('name')->toArray();

            // Soft delete the rules
            $deletedCount = CommandScheduleRule::whereIn('id', $ruleIds)->delete();

            DB::commit();

            Log::info('CommandScheduleController: Bulk delete completed', [
                'deleted_count' => $deletedCount,
                'rule_names' => $ruleNames,
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deletedCount} schedule rule(s)",
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('CommandScheduleController: Error in bulk delete operation', [
                'rule_ids' => $ruleIds,
                'error' => $e->getMessage(),
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete schedule rules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk enable/disable schedule rules
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkToggle(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'rule_ids' => 'required|array|min:1',
            'rule_ids.*' => 'integer|exists:command_schedule_rules,id',
            'action' => 'required|in:enable,disable'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $ruleIds = $request->input('rule_ids');
        $action = $request->input('action');
        $user = Auth::guard('job_seeker')->user();
        $adminUser = $user->email ?? 'admin';

        Log::info('CommandScheduleController: Bulk toggling schedule rules', [
            'rule_ids' => $ruleIds,
            'action' => $action,
            'admin_user' => $adminUser
        ]);

        try {
            DB::beginTransaction();

            $updatedCount = CommandScheduleRule::whereIn('id', $ruleIds)
                ->update([
                    'is_active' => $action === 'enable',
                    'updated_by' => $adminUser,
                    'updated_at' => now()
                ]);

            DB::commit();

            Log::info('CommandScheduleController: Bulk toggle completed', [
                'updated_count' => $updatedCount,
                'action' => $action,
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully {$action}d {$updatedCount} schedule rule(s)",
                'updated_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('CommandScheduleController: Error in bulk toggle operation', [
                'rule_ids' => $ruleIds,
                'action' => $action,
                'error' => $e->getMessage(),
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update schedule rules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get bulk edit data for chess-board view with pagination and optimization
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getBulkData(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();

        Log::info('CommandScheduleController: Fetching bulk edit data', [
            'admin_user' => $user->email ?? 'unknown'
        ]);

        try {
            // Get pagination parameters
            $page = (int) $request->get('page', 1);
            $perPage = min((int) $request->get('per_page', 50), 100); // Max 100 per page
            $search = $request->get('search', '');
            $provider = $request->get('provider', '');
            $status = $request->get('status', '');
            $scheduleType = $request->get('schedule_type', '');



            // Build optimized query with proper eager loading
            $query = CommandScheduleRule::with([
                'executions' => function($query) {
                    $query->select(['schedule_rule_id', 'started_at', 'status', 'execution_time_seconds'])
                          ->latest('started_at')
                          ->limit(1);
                },
                'filter' => function($query) {
                    $query->select(['schedule_rule_id', 'categories', 'locations', 'companies', 'experience_levels', 'search_term', 'work_type']);
                }
            ])
            ->select([
                'id', 'name', 'command', 'schedule_expression', 'schedule_type',
                'timezone', 'is_active', 'priority', 'description', 'next_run_at',
                'created_at', 'updated_at'
            ]);

            // Apply filters
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('command', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%");
                });
            }

            if ($provider) {
                if ($provider === 'jobsaf') {
                    $query->where('command', 'LIKE', '%jobs-af%');
                } elseif ($provider === 'acbar') {
                    $query->where('command', 'LIKE', '%acbar%');
                }
            }

            if ($status !== '') {
                $query->where('is_active', $status === 'active');
            }

            if ($scheduleType) {
                $query->where('schedule_type', $scheduleType);
            }

            // Get paginated results
            $paginatedRules = $query->paginate($perPage, ['*'], 'page', $page);



            // Transform the paginated data
            $rules = $paginatedRules->getCollection()->map(function ($rule) {
                $lastExecution = $rule->executions->first();
                $service = new CommandScheduleService();
                $nextRun = $service->calculateNextRunTime($rule);

                // Determine provider from command
                $provider = 'unknown';
                if (str_contains($rule->command, 'jobs-af')) {
                    $provider = 'jobsaf';
                } elseif (str_contains($rule->command, 'acbar')) {
                    $provider = 'acbar';
                }

                // Extract time from schedule expression for display using entity method
                $displayTime = $rule->getCronDisplayTime();

                // Get sort key and next execution time using new entity methods
                $sortKey = $rule->getCronSortKey();
                $nextExecutionTime = $rule->getNextExecutionTime();

                // Determine time status for visual indicators
                $timeStatus = 'future';
                if ($nextExecutionTime) {
                    $now = Carbon::now();
                    $hoursUntilNext = $now->diffInHours($nextExecutionTime, false);

                    if ($hoursUntilNext < 24) {
                        $timeStatus = $hoursUntilNext < 1 ? 'imminent' : 'today';
                    } elseif ($hoursUntilNext < 48) {
                        $timeStatus = 'tomorrow';
                    }
                }

                return [
                    'id' => $rule->id,
                    'name' => $rule->name,
                    'command' => $rule->command,
                    'provider' => $provider,
                    'schedule_expression' => $rule->schedule_expression,
                    'schedule_type' => $rule->schedule_type,
                    'display_time' => $displayTime, // Add formatted time for display
                    'timezone' => $rule->timezone,
                    'is_active' => $rule->is_active,
                    'priority' => $rule->priority,
                    'description' => $rule->description,
                    'display_name' => $this->generateDynamicRuleName($rule),
                    // Filter data (categories and locations are now stored in CommandScheduleFilter)
                    'categories' => $rule->filter ? ($rule->filter->categories ?? []) : [],
                    'locations' => $rule->filter ? ($rule->filter->locations ?? []) : [],
                    'companies' => $rule->filter ? ($rule->filter->companies ?? []) : [],
                    'experience_levels' => $rule->filter ? ($rule->filter->experience_levels ?? []) : [],
                    'search_term' => $rule->filter ? ($rule->filter->search_term ?? '') : '',
                    'work_type' => $rule->filter ? ($rule->filter->work_type ?? '') : '',
                    'next_run' => $nextExecutionTime ? $nextExecutionTime->format('Y-m-d H:i:s') : null,
                    'next_run_timestamp' => $nextExecutionTime ? $nextExecutionTime->timestamp : PHP_INT_MAX,
                    'next_run_human' => $nextExecutionTime ? $nextExecutionTime->format('M j, g:i A') : 'Not scheduled',
                    'next_run_relative' => $nextExecutionTime ? $nextExecutionTime->diffForHumans() : 'Not scheduled',
                    'schedule_sort_key' => $sortKey, // Minutes until next execution (0 = soonest)
                    'time_status' => $timeStatus, // For visual indicators: imminent, today, tomorrow, future
                    'last_execution' => $lastExecution ? [
                        'started_at' => $lastExecution->started_at->format('Y-m-d H:i:s'),
                        'status' => $lastExecution->status,
                        'duration' => $lastExecution->execution_time_seconds
                    ] : null,
                    'created_at' => $rule->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $rule->updated_at->format('Y-m-d H:i:s')
                ];
            });

            // Sort the rules by schedule_sort_key for chronological order
            $sortedRules = $rules->sortBy([
                ['schedule_sort_key', 'asc'],
                ['priority', 'asc'],
                ['created_at', 'asc']
            ])->values(); // Reset array keys after sorting

            // Enhanced debug logging to verify time-aware contextual sorting
            $now = Carbon::now();
            Log::info('CommandScheduleController: Rules sorted by time-aware contextual sorting', [
                'current_datetime' => $now->format('Y-m-d H:i:s (l)'), // 2025-07-21 15:48:00 (Monday)
                'current_day_number' => $now->dayOfWeek, // 0=Sunday, 1=Monday, etc.
                'sorting_context' => 'Minutes until next execution (0 = soonest, high = past/far future)',
                'first_rule' => $sortedRules->first() ? [
                    'id' => $sortedRules->first()['id'],
                    'name' => $sortedRules->first()['name'],
                    'schedule_expression' => $sortedRules->first()['schedule_expression'],
                    'schedule_sort_key' => $sortedRules->first()['schedule_sort_key'],
                    'next_run_human' => $sortedRules->first()['next_run_human'],
                    'next_run_relative' => $sortedRules->first()['next_run_relative'],
                    'time_status' => $sortedRules->first()['time_status'],
                    'display_name' => $sortedRules->first()['display_name']
                ] : null,
                'first_few_rules' => $sortedRules->take(5)->map(function($rule) {
                    return [
                        'id' => $rule['id'],
                        'name' => $rule['name'],
                        'schedule_sort_key' => $rule['schedule_sort_key'],
                        'next_run_relative' => $rule['next_run_relative'],
                        'time_status' => $rule['time_status']
                    ];
                })->toArray(),
                'last_few_rules' => $sortedRules->slice(-3)->map(function($rule) {
                    return [
                        'id' => $rule['id'],
                        'name' => $rule['name'],
                        'schedule_sort_key' => $rule['schedule_sort_key'],
                        'next_run_relative' => $rule['next_run_relative'],
                        'time_status' => $rule['time_status']
                    ];
                })->values()->toArray(),
                'total_rules' => $sortedRules->count()
            ]);

            // Update the paginated collection with sorted data
            $paginatedRules->setCollection($sortedRules);

            // Calculate stats from all rules (not just current page)
            $allRulesQuery = CommandScheduleRule::query();

            // Apply same filters for stats
            if ($search) {
                $allRulesQuery->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('command', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%");
                });
            }

            if ($provider) {
                if ($provider === 'jobsaf') {
                    $allRulesQuery->where('command', 'LIKE', '%jobs-af%');
                } elseif ($provider === 'acbar') {
                    $allRulesQuery->where('command', 'LIKE', '%acbar%');
                }
            }

            if ($status !== '') {
                $allRulesQuery->where('is_active', $status === 'active');
            }

            if ($scheduleType) {
                $allRulesQuery->where('schedule_type', $scheduleType);
            }

            // Get stats efficiently
            $stats = [
                'total_rules' => $allRulesQuery->count(),
                'active_rules' => (clone $allRulesQuery)->where('is_active', true)->count(),
                'jobsaf_rules' => (clone $allRulesQuery)->where('command', 'LIKE', '%jobs-af%')->count(),
                'acbar_rules' => (clone $allRulesQuery)->where('command', 'LIKE', '%acbar%')->count(),
                'filtered_total' => $paginatedRules->total(),
            ];

            $response = [
                'success' => true,
                'rules' => $paginatedRules->items(),
                'pagination' => [
                    'current_page' => $paginatedRules->currentPage(),
                    'last_page' => $paginatedRules->lastPage(),
                    'per_page' => $paginatedRules->perPage(),
                    'total' => $paginatedRules->total(),
                    'from' => $paginatedRules->firstItem(),
                    'to' => $paginatedRules->lastItem(),
                    'has_more_pages' => $paginatedRules->hasMorePages(),
                ],
                'stats' => $stats,
                'filters' => [
                    'search' => $search,
                    'provider' => $provider,
                    'status' => $status,
                    'schedule_type' => $scheduleType,
                ]
            ];



            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error fetching bulk data', [
                'error' => $e->getMessage(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch bulk data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get bulk edit data using stored procedure for better performance
     * Alternative method that can be used for very large datasets
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getBulkDataOptimized(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();

        Log::info('CommandScheduleController: Fetching bulk edit data via stored procedure', [
            'admin_user' => $user->email ?? 'unknown'
        ]);

        try {
            // Use the stored procedure for maximum performance
            $results = DB::select('CALL sp_get_bulk_edit_data()');

            $service = new CommandScheduleService();

            $rules = collect($results)->map(function ($row) use ($service) {
                // Convert stdClass to array for easier manipulation
                $rule = (array) $row;

                // Calculate next run time
                $scheduleRule = new CommandScheduleRule();
                $scheduleRule->schedule_expression = $rule['schedule_expression'];
                $scheduleRule->schedule_type = $rule['schedule_type'];
                $scheduleRule->timezone = $rule['timezone'];

                $nextRun = $service->calculateNextRunTime($scheduleRule);

                // Extract time from schedule expression for display using entity method
                $displayTime = $scheduleRule->getCronDisplayTime();

                // Get sort key directly from cron expression using entity method
                $sortKey = $scheduleRule->getCronSortKey();

                return [
                    'id' => (int) $rule['id'],
                    'name' => $rule['name'],
                    'command' => $rule['command'],
                    'provider' => $rule['provider'],
                    'schedule_expression' => $rule['schedule_expression'],
                    'schedule_type' => $rule['schedule_type'],
                    'display_time' => $displayTime,
                    'timezone' => $rule['timezone'],
                    'is_active' => (bool) $rule['is_active'],
                    'priority' => (int) $rule['priority'],
                    'description' => $rule['description'],
                    'display_name' => $this->generateDynamicRuleName((object) $rule),
                    // Filter data would need to be loaded separately or added to stored procedure
                    'categories' => [],
                    'locations' => [],
                    'companies' => [],
                    'experience_levels' => [],
                    'search_term' => '',
                    'work_type' => '',
                    'next_run' => $nextRun ? $nextRun->format('Y-m-d H:i:s') : null,
                    'next_run_timestamp' => $nextRun ? $nextRun->timestamp : PHP_INT_MAX,
                    'next_run_human' => $nextRun ? $nextRun->format('M j, g:i A') : 'Not scheduled',
                    'schedule_sort_key' => $sortKey,
                    'last_execution' => $rule['last_execution_started'] ? [
                        'started_at' => $rule['last_execution_started'],
                        'status' => $rule['last_execution_status'],
                        'duration' => (int) $rule['last_execution_duration']
                    ] : null,
                    'created_at' => $rule['created_at'],
                    'updated_at' => $rule['updated_at']
                ];
            })
            ->sortBy([
                ['schedule_sort_key', 'asc'],
                ['priority', 'asc'],
                ['created_at', 'asc']
            ])
            ->values();

            // Calculate stats
            $stats = [
                'total_rules' => $rules->count(),
                'active_rules' => $rules->where('is_active', true)->count(),
                'jobsaf_rules' => $rules->where('provider', 'jobsaf')->count(),
                'acbar_rules' => $rules->where('provider', 'acbar')->count(),
            ];

            return response()->json([
                'success' => true,
                'rules' => $rules,
                'stats' => $stats,
                'method' => 'stored_procedure'
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error fetching bulk data via stored procedure', [
                'error' => $e->getMessage(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch bulk data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a single field inline for bulk editing
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateField(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        $adminUser = $user->email ?? 'admin';

        // Use validation service for comprehensive validation
        $validation = $this->bulkValidationService->validateFieldUpdate($request->all());

        if (!$validation['success']) {
            return response()->json([
                'success' => false,
                'message' => $validation['message'],
                'errors' => $validation['errors'] ?? [],
                'error_type' => $validation['error_type'] ?? 'validation'
            ], 422);
        }

        $validatedData = $validation['validated_data'];
        $ruleId = $validatedData['rule_id'];
        $field = $validatedData['field'];
        $value = $validatedData['value'];

        Log::info('CommandScheduleController: Updating field inline', [
            'rule_id' => $ruleId,
            'field' => $field,
            'value' => $value,
            'admin_user' => $adminUser
        ]);

        try {
            DB::beginTransaction();

            $rule = CommandScheduleRule::findOrFail($ruleId);

            // Handle special field transformations
            switch ($field) {
                case 'schedule_time':
                    // Update time while preserving existing schedule_type and day information
                    $this->updateScheduleTime($rule, $value);
                    break;

                case 'schedule_type':
                    $rule->schedule_type = $value;
                    // If changing to cron or custom, preserve existing expression
                    // If changing to daily_at or weekly_at, we'll need the time/day info
                    break;

                case 'schedule_expression':
                    // Handle cron expression updates
                    $rule->schedule_expression = $value;
                    break;

                case 'day_of_week':
                    // Handle weekly schedule day selection
                    $this->updateWeeklySchedule($rule, $value);
                    break;

                case 'timezone':
                    $rule->timezone = $value;
                    break;

                case 'is_active':
                    $rule->is_active = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    break;

                case 'priority':
                    $rule->priority = (int) $value;
                    break;

                // provider_category_ids and provider_location_ids are no longer stored in CommandScheduleRule
                // They are handled through CommandScheduleFilter (see filter field cases below)

                // Handle filter fields - these update the related CommandScheduleFilter
                case 'categories':
                case 'locations':
                case 'companies':
                case 'experience_levels':
                    $this->updateFilterField($rule, $field, $value);
                    break;

                case 'search_term':
                case 'work_type':
                    $this->updateFilterField($rule, $field, $value);
                    break;

                default:
                    $rule->{$field} = $value;
                    break;
            }

            $rule->updated_by = $adminUser;
            $rule->save();

            // Recalculate next run time if schedule changed
            if (in_array($field, ['schedule_expression', 'schedule_time', 'schedule_type', 'timezone'])) {
                $service = new CommandScheduleService();
                $nextRun = $service->calculateNextRunTime($rule);
                $rule->next_run_at = $nextRun;
                $rule->save();
            }

            DB::commit();

            Log::info('CommandScheduleController: Field updated successfully', [
                'rule_id' => $ruleId,
                'field' => $field,
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Field updated successfully',
                'rule' => [
                    'id' => $rule->id,
                    'next_run' => $rule->next_run_at ? $rule->next_run_at->format('M j, g:i A') : 'Not scheduled'
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('CommandScheduleController: Error updating field', [
                'rule_id' => $ruleId,
                'field' => $field,
                'error' => $e->getMessage(),
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update field: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a filter field for a command schedule rule
     *
     * @param CommandScheduleRule $rule
     * @param string $field
     * @param mixed $value
     * @return void
     */
    private function updateFilterField(CommandScheduleRule $rule, string $field, $value): void
    {
        // Get or create the filter record
        $filter = $rule->filter()->first();
        if (!$filter) {
            $filter = new \Modules\JobSeeker\Entities\CommandScheduleFilter();
            $filter->schedule_rule_id = $rule->id;
        }

        // Handle array fields (categories, locations, companies, experience_levels)
        if (in_array($field, ['categories', 'locations', 'companies', 'experience_levels'])) {
            if (is_string($value)) {
                // Convert comma-separated string to array
                $arrayValue = array_map('trim', explode(',', $value));
                $arrayValue = array_filter($arrayValue); // Remove empty values

                // CRITICAL FIX: Normalize IDs to integers for categories and locations
                if (in_array($field, ['categories', 'locations'])) {
                    $arrayValue = $this->normalizeCategoryIds($arrayValue);
                }

                $filter->{$field} = $arrayValue;
            } elseif (is_array($value)) {
                // CRITICAL FIX: Normalize IDs to integers for categories and locations
                if (in_array($field, ['categories', 'locations'])) {
                    $value = $this->normalizeCategoryIds($value);
                }

                $filter->{$field} = $value;
            } else {
                $filter->{$field} = [];
            }
        } else {
            // Handle scalar fields (search_term, work_type)
            $filter->{$field} = $value;
        }

        $filter->save();
    }

    /**
     * Update schedule time while preserving schedule_type and day information
     *
     * @param CommandScheduleRule $rule
     * @param string $timeValue
     * @return void
     */
    private function updateScheduleTime(CommandScheduleRule $rule, string $timeValue): void
    {
        // Parse the time value (could be HH:MM or HH:MM AM/PM format)
        $time = $this->parseTimeValue($timeValue);

        if (!$time) {
            throw new \InvalidArgumentException("Invalid time format: {$timeValue}");
        }

        // Generate appropriate schedule expression based on current schedule_type
        switch ($rule->schedule_type) {
            case 'daily_at':
                // For daily schedules: "MM HH * * *" (every day at HH:MM)
                $rule->schedule_expression = "{$time['minute']} {$time['hour']} * * *";
                break;

            case 'weekly_at':
                // For weekly schedules: "MM HH * * D" (every week on day D at HH:MM)
                $dayOfWeek = $this->extractDayOfWeekFromRule($rule);
                $rule->schedule_expression = "{$time['minute']} {$time['hour']} * * {$dayOfWeek}";
                break;

            case 'cron':
            case 'custom':
                // For cron/custom, try to update time components if possible
                $rule->schedule_expression = $this->updateCronTime($rule->schedule_expression, $time);
                break;

            default:
                throw new \InvalidArgumentException("Unsupported schedule_type for time update: {$rule->schedule_type}");
        }

        Log::info('CommandScheduleController: Updated schedule time', [
            'rule_id' => $rule->id,
            'original_expression' => $rule->getOriginal('schedule_expression'),
            'new_expression' => $rule->schedule_expression,
            'schedule_type' => $rule->schedule_type,
            'time_value' => $timeValue,
            'parsed_time' => $time
        ]);
    }

    /**
     * Parse time value from various formats
     *
     * @param string $timeValue
     * @return array|null
     */
    private function parseTimeValue(string $timeValue): ?array
    {
        // Remove any whitespace
        $timeValue = trim($timeValue);

        // Try to parse HH:MM format (24-hour)
        if (preg_match('/^(\d{1,2}):(\d{2})$/', $timeValue, $matches)) {
            $hour = (int) $matches[1];
            $minute = (int) $matches[2];

            if ($hour >= 0 && $hour <= 23 && $minute >= 0 && $minute <= 59) {
                return ['hour' => $hour, 'minute' => $minute];
            }
        }

        // Try to parse HH:MM AM/PM format (12-hour)
        if (preg_match('/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i', $timeValue, $matches)) {
            $hour = (int) $matches[1];
            $minute = (int) $matches[2];
            $ampm = strtoupper($matches[3]);

            if ($hour >= 1 && $hour <= 12 && $minute >= 0 && $minute <= 59) {
                // Convert to 24-hour format
                if ($ampm === 'PM' && $hour !== 12) {
                    $hour += 12;
                } elseif ($ampm === 'AM' && $hour === 12) {
                    $hour = 0;
                }

                return ['hour' => $hour, 'minute' => $minute];
            }
        }

        return null;
    }

    /**
     * Extract day of week from rule name or existing cron expression
     *
     * @param CommandScheduleRule $rule
     * @return int
     */
    private function extractDayOfWeekFromRule(CommandScheduleRule $rule): int
    {
        // First, try to extract from existing cron expression
        if ($rule->schedule_expression && preg_match('/^\d+\s+\d+\s+\*\s+\*\s+(\d+)$/', $rule->schedule_expression, $matches)) {
            return (int) $matches[1];
        }

        // Try to extract from rule name
        $name = strtolower($rule->name);
        $dayMap = [
            'sunday' => 0, 'sun' => 0,
            'monday' => 1, 'mon' => 1,
            'tuesday' => 2, 'tue' => 2, 'tues' => 2,
            'wednesday' => 3, 'wed' => 3,
            'thursday' => 4, 'thu' => 4, 'thur' => 4, 'thurs' => 4,
            'friday' => 5, 'fri' => 5,
            'saturday' => 6, 'sat' => 6
        ];

        foreach ($dayMap as $dayName => $dayNumber) {
            if (strpos($name, $dayName) !== false) {
                return $dayNumber;
            }
        }

        // Default to Monday if can't determine
        Log::warning('CommandScheduleController: Could not determine day of week from rule', [
            'rule_id' => $rule->id,
            'rule_name' => $rule->name,
            'defaulting_to' => 'Monday (1)'
        ]);

        return 1; // Monday
    }

    /**
     * Update time components in existing cron expression
     *
     * @param string $cronExpression
     * @param array $time
     * @return string
     */
    private function updateCronTime(string $cronExpression, array $time): string
    {
        $parts = explode(' ', $cronExpression);

        if (count($parts) >= 5) {
            // Update minute and hour components
            $parts[0] = (string) $time['minute'];
            $parts[1] = (string) $time['hour'];
            return implode(' ', $parts);
        }

        // If can't parse, return a basic daily cron
        return "{$time['minute']} {$time['hour']} * * *";
    }

    /**
     * Clone rules from one provider to another with time offset
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function cloneRules(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        $adminUser = $user->email ?? 'admin';

        // Use validation service for comprehensive validation
        $validation = $this->bulkValidationService->validateCloneRequest($request->all());

        if (!$validation['success']) {
            return response()->json([
                'success' => false,
                'message' => $validation['message'],
                'errors' => $validation['errors'] ?? [],
                'error_type' => $validation['error_type'] ?? 'validation'
            ], 422);
        }

        $validatedData = $validation['validated_data'];
        $fromProvider = $validatedData['from_provider'];
        $toProvider = $validatedData['to_provider'];
        $timeOffset = $validatedData['time_offset_minutes'];
        $specificRuleIds = $validatedData['rule_ids'] ?? null;

        Log::info('CommandScheduleController: Cloning rules between providers', [
            'from_provider' => $fromProvider,
            'to_provider' => $toProvider,
            'time_offset' => $timeOffset,
            'specific_rules' => $specificRuleIds,
            'admin_user' => $adminUser
        ]);

        try {
            DB::beginTransaction();

            // Initialize provider mapping service
            $mappingService = new ProviderMappingService();

            // Map providers to commands
            $commandMap = [
                'jobsaf' => 'jobseeker:sync-jobs-af',
                'acbar' => 'jobseeker:sync-acbar-jobs'
            ];

            $fromCommand = $commandMap[$fromProvider];
            $toCommand = $commandMap[$toProvider];

            // Normalize provider names for database queries
            $normalizedFromProvider = $mappingService->normalizeProviderName($fromProvider);
            $normalizedToProvider = $mappingService->normalizeProviderName($toProvider);

            // Get rules to clone with their filter relationships
            $query = CommandScheduleRule::with('filter')->where('command', $fromCommand);

            if ($specificRuleIds) {
                $query->whereIn('id', $specificRuleIds);
            }

            $rulesToClone = $query->get();

            if ($rulesToClone->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No rules found to clone'
                ], 404);
            }

            $clonedRules = [];
            $service = new CommandScheduleService();

            foreach ($rulesToClone as $originalRule) {
                // Calculate new schedule expression with time offset
                $newScheduleExpression = $this->addTimeOffset(
                    $originalRule->schedule_expression,
                    $originalRule->schedule_type,
                    $timeOffset
                );

                // Categories and locations are now handled through CommandScheduleFilter
                // No need to map them at the rule level

                // Create cloned rule
                $clonedRule = CommandScheduleRule::create([
                    'name' => $originalRule->name . ' (Cloned to ' . strtoupper($toProvider) . ')',
                    'command' => $toCommand,
                    'schedule_expression' => $newScheduleExpression,
                    'schedule_type' => $originalRule->schedule_type,
                    'days_of_week' => $originalRule->days_of_week,
                    'time_slots' => $originalRule->time_slots,
                    'timezone' => $originalRule->timezone,
                    'is_active' => $originalRule->is_active,
                    'priority' => $originalRule->priority,
                    'description' => $originalRule->description . ' (Cloned from ' . strtoupper($fromProvider) . ')',
                    'depends_on_command' => $originalRule->depends_on_command,
                    'delay_after_dependency' => $originalRule->delay_after_dependency,
                    'max_execution_time' => $originalRule->max_execution_time,
                    'execution_timeout' => $originalRule->execution_timeout,
                    'concurrent_executions' => $originalRule->concurrent_executions,
                    'created_by' => $adminUser,
                    'updated_by' => $adminUser
                ]);

                // Calculate next run time
                $nextRun = $service->calculateNextRunTime($clonedRule);
                $clonedRule->next_run_at = $nextRun;
                $clonedRule->save();

                // Clone CommandScheduleFilter if it exists
                if ($originalRule->filter) {
                    $originalFilter = $originalRule->filter;

                    // Map filter categories and locations
                    $filterMappedCategories = [];
                    $filterMappedLocations = [];

                    if (!empty($originalFilter->categories)) {
                        $filterMappedCategories = $mappingService->mapCategories(
                            $originalFilter->categories,
                            $normalizedFromProvider,
                            $normalizedToProvider
                        );
                    }

                    if (!empty($originalFilter->locations)) {
                        $filterMappedLocations = $mappingService->mapLocations(
                            $originalFilter->locations,
                            $normalizedFromProvider,
                            $normalizedToProvider
                        );
                    }

                    // Create cloned filter
                    CommandScheduleFilter::create([
                        'schedule_rule_id' => $clonedRule->id,
                        'categories' => $filterMappedCategories,
                        'locations' => $filterMappedLocations,
                        'companies' => $originalFilter->companies ?? [],
                        'experience_levels' => $originalFilter->experience_levels ?? [],
                        'search_term' => $originalFilter->search_term ?? '',
                        'work_type' => $originalFilter->work_type ?? '',
                        'is_default' => $originalFilter->is_default ?? false,
                        'min_page' => $originalFilter->min_page ?? null,
                        'max_page' => $originalFilter->max_page ?? null,
                        'max_retries' => $originalFilter->max_retries ?? null,
                        'timeout' => $originalFilter->timeout ?? null,
                        'base_delay' => $originalFilter->base_delay ?? null,
                    ]);

                    Log::info('CommandScheduleController: Cloned filter for rule', [
                        'original_rule_id' => $originalRule->id,
                        'cloned_rule_id' => $clonedRule->id,
                        'original_categories' => $originalFilter->categories ?? [],
                        'mapped_categories' => $filterMappedCategories,
                        'original_locations' => $originalFilter->locations ?? [],
                        'mapped_locations' => $filterMappedLocations
                    ]);
                }

                $clonedRules[] = $clonedRule;
            }

            DB::commit();

            Log::info('CommandScheduleController: Rules cloned successfully', [
                'cloned_count' => count($clonedRules),
                'from_provider' => $fromProvider,
                'to_provider' => $toProvider,
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Successfully cloned ' . count($clonedRules) . ' rule(s) from ' . strtoupper($fromProvider) . ' to ' . strtoupper($toProvider),
                'cloned_count' => count($clonedRules),
                'cloned_rules' => collect($clonedRules)->map(function($rule) {
                    return [
                        'id' => $rule->id,
                        'name' => $rule->name,
                        'schedule_expression' => $rule->schedule_expression,
                        'next_run' => $rule->next_run_at ? $rule->next_run_at->format('M j, g:i A') : 'Not scheduled'
                    ];
                })
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('CommandScheduleController: Error cloning rules', [
                'from_provider' => $fromProvider,
                'to_provider' => $toProvider,
                'error' => $e->getMessage(),
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to clone rules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add time offset to schedule expression
     *
     * @param string $scheduleExpression
     * @param string $scheduleType
     * @param int $offsetMinutes
     * @return string
     */
    private function addTimeOffset(string $scheduleExpression, string $scheduleType, int $offsetMinutes): string
    {
        try {
            // All schedule types now use proper cron expressions
            // Format: "MM HH * * *" (daily) or "MM HH * * D" (weekly)
            switch ($scheduleType) {
                case 'daily_at':
                case 'weekly_at':
                case 'cron':
                case 'custom':
                    // Handle cron expression like "15 9 * * *" or "15 9 * * 1"
                    $parts = explode(' ', $scheduleExpression);
                    if (count($parts) >= 5) {
                        $minute = (int) $parts[0];
                        $hour = (int) $parts[1];

                        // Convert to total minutes and add offset
                        $totalMinutes = ($hour * 60) + $minute + $offsetMinutes;

                        // Handle day overflow/underflow
                        while ($totalMinutes < 0) {
                            $totalMinutes += 24 * 60; // Add 24 hours
                        }

                        $newHour = intval($totalMinutes / 60) % 24;
                        $newMinute = $totalMinutes % 60;

                        // Ensure non-negative values
                        if ($newMinute < 0) {
                            $newMinute += 60;
                            $newHour -= 1;
                        }
                        if ($newHour < 0) {
                            $newHour += 24;
                        }

                        $parts[0] = (string) $newMinute;
                        $parts[1] = (string) $newHour;

                        $newExpression = implode(' ', $parts);

                        Log::info('CommandScheduleController: Applied time offset to schedule expression', [
                            'original_expression' => $scheduleExpression,
                            'new_expression' => $newExpression,
                            'schedule_type' => $scheduleType,
                            'offset_minutes' => $offsetMinutes,
                            'original_time' => sprintf('%02d:%02d', $hour, $minute),
                            'new_time' => sprintf('%02d:%02d', $newHour, $newMinute)
                        ]);

                        return $newExpression;
                    }
                    break;
            }

            // Fallback: return original expression if parsing fails
            Log::warning('CommandScheduleController: Could not parse schedule expression for time offset', [
                'expression' => $scheduleExpression,
                'type' => $scheduleType,
                'offset' => $offsetMinutes
            ]);

            return $scheduleExpression;

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error adding time offset', [
                'expression' => $scheduleExpression,
                'type' => $scheduleType,
                'offset' => $offsetMinutes,
                'error' => $e->getMessage()
            ]);

            return $scheduleExpression;
        }
    }

    /**
     * Validate schedule rule input data
     *
     * @param Request $request
     * @param int|null $excludeId ID to exclude from unique validation
     * @return \Illuminate\Contracts\Validation\Validator
     */
    private function validateScheduleRule(Request $request, ?int $excludeId = null): \Illuminate\Contracts\Validation\Validator
    {
        $nameRule = ['required', 'string', 'max:255'];
        if ($excludeId) {
            $nameRule[] = Rule::unique('command_schedule_rules', 'name')->ignore($excludeId);
        } else {
            $nameRule[] = 'unique:command_schedule_rules,name';
        }

        return Validator::make($request->all(), [
            'name' => $nameRule,
            'command' => 'required|string|max:255',
            'schedule_expression' => 'required|string|max:255',
            'schedule_type' => 'required|in:cron,daily_at,weekly_at',
            'timezone' => 'required|string|timezone',
            'is_active' => 'boolean',
            'priority' => 'nullable|integer|min:1|max:999',
            'description' => 'nullable|string|max:500',
            'depends_on_command' => 'nullable|string|max:255',
            'delay_after_dependency' => 'nullable|integer|min:0|max:86400',
            'max_execution_time' => 'nullable|integer|min:60|max:86400',
            'execution_timeout' => 'nullable|integer|min:60|max:86400',
            'concurrent_executions' => 'nullable|integer|min:1|max:10',
            'days_of_week' => 'nullable|string',
        ]);
    }

    /**
     * Parse JSON field from request (days_of_week, time_slots)
     *
     * @param string|null $value
     * @return array|null
     */
    private function parseJsonField(?string $value): ?array
    {
        if (empty($value)) {
            return null;
        }

        try {
            $decoded = json_decode($value, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('CommandScheduleController: JSON parsing failed', [
                    'value' => $value,
                    'error' => json_last_error_msg()
                ]);
                return null;
            }
            return is_array($decoded) ? $decoded : null;
        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Exception during JSON parsing', [
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get available timezones for the form
     *
     * @return array
     */
    private function getAvailableTimezones(): array
    {
        return [
            'Asia/Kabul' => 'Afghanistan Time (Asia/Kabul)',
            'UTC' => 'UTC',
            'Asia/Tehran' => 'Iran Time (Asia/Tehran)',
            'Asia/Karachi' => 'Pakistan Time (Asia/Karachi)',
            'Asia/Dubai' => 'UAE Time (Asia/Dubai)',
            'Asia/Kuala_Lumpur' => 'Malaysia Time (Asia/Kuala_Lumpur)',
            'Europe/London' => 'London Time (Europe/London)',
            'America/New_York' => 'New York Time (America/New_York)',
        ];
    }

    /**
     * Get available commands for the dropdown
     *
     * @return array
     */
    private function getAvailableCommands(): array
    {
        return CommandScheduleRule::getAvailableCommands();
    }

    /**
     * Converts user-friendly schedule inputs into a standard cron expression.
     *
     * @param string $scheduleType
     * @param array $data
     * @return string
     * @throws \InvalidArgumentException
     */
    private function convertToCronExpression(string $scheduleType, array $data): string
    {
        $expression = $data['schedule_expression'] ?? '';
        
        if ($scheduleType === 'daily_at') {
            // Expect HH:MM format
            if (!preg_match('/^(\d{1,2}):(\d{2})$/', $expression, $m)) {
                throw new \InvalidArgumentException("Invalid daily_at format. Expected HH:MM, got: {$expression}");
            }
            $hour = (int)$m[1];
            $minute = (int)$m[2];
            
            if ($hour > 23 || $minute > 59) {
                throw new \InvalidArgumentException("Invalid time values. Hour: {$hour}, Minute: {$minute}");
            }
            
            return sprintf('%d %d * * *', $minute, $hour);
        }
        
        if ($scheduleType === 'weekly_at') {
            // Expect "day HH:MM" format
            if (!preg_match('/^(\d)\s+(\d{1,2}):(\d{2})$/', $expression, $m)) {
                throw new \InvalidArgumentException("Invalid weekly_at format. Expected 'D HH:MM', got: {$expression}");
            }
            $day = (int)$m[1];
            $hour = (int)$m[2];
            $minute = (int)$m[3];
            
            if ($day > 6 || $hour > 23 || $minute > 59) {
                throw new \InvalidArgumentException("Invalid time/day values. Day: {$day}, Hour: {$hour}, Minute: {$minute}");
            }
            
            return sprintf('%d %d * * %d', $minute, $hour, $day);
        }
        
        if ($scheduleType === 'cron') {
            // Basic cron validation
            $parts = explode(' ', $expression);
            if (count($parts) !== 5) {
                throw new \InvalidArgumentException("Invalid cron expression. Expected 5 parts, got " . count($parts));
            }
            return $expression;
        }
        
        throw new \InvalidArgumentException("Unknown schedule type: {$scheduleType}");
    }

    /**
     * Handle filter data for both Jobs.af and ACBAR commands (unified structure)
     *
     * @param Request $request
     * @param int $ruleId
     * @return void
     * @throws \Exception
     */
    private function handleFilters(Request $request, int $ruleId): void
    {
        $command = $request->input('command');
        
        // Only process filters for sync commands that support filters
        if (!in_array($command, ['jobseeker:sync-jobs-af', 'jobseeker:sync-acbar-jobs'])) {
            Log::debug('CommandScheduleController: Command does not support filters', [
                'rule_id' => $ruleId,
                'command' => $command
            ]);
            return;
        }

        // Check if filters table exists before proceeding
        if (!\Schema::hasTable('command_schedule_filters')) {
            Log::warning('CommandScheduleController: command_schedule_filters table does not exist, skipping filter processing', [
                'rule_id' => $ruleId,
                'command' => $command
            ]);
            return;
        }

        DB::beginTransaction();
        
        try {
            // Check if filters are provided (supports both 'filters' and 'acbar_filters' for backwards compatibility)
            $filtersData = $request->input('filters', []);
            if (empty($filtersData) && $command === 'jobseeker:sync-acbar-jobs') {
                $filtersData = $request->input('acbar_filters', []);
            }

            if (empty($filtersData)) {
                Log::debug('CommandScheduleController: No filters provided for command', [
                    'rule_id' => $ruleId,
                    'command' => $command
                ]);
                DB::commit(); // Nothing to do, but complete transaction
                return;
            }

            // Validate and sanitize filter data
            $filterData = [
                'categories' => $this->sanitizeArrayField($filtersData['categories'] ?? []),
                'locations' => $this->sanitizeArrayField($filtersData['locations'] ?? []),
                'companies' => $this->sanitizeArrayField($filtersData['companies'] ?? []),
                'experience_levels' => $this->sanitizeArrayField($filtersData['experience_levels'] ?? []),
                'search_term' => $this->sanitizeStringField($filtersData['search_term'] ?? '', 255),
                'work_type' => $this->sanitizeStringField($filtersData['work_type'] ?? '', 50),
                'min_page' => $this->sanitizeIntegerField($filtersData['min_page'] ?? null, 1, 1000),
                'max_page' => $this->sanitizeIntegerField($filtersData['max_page'] ?? null, 1, 1000),
                // ACBAR-specific fields with validation
                'max_retries' => $this->sanitizeIntegerField($filtersData['max_retries'] ?? null, 1, 10),
                'timeout' => $this->sanitizeIntegerField($filtersData['timeout'] ?? null, 10, 300),
                'base_delay' => $this->sanitizeIntegerField($filtersData['base_delay'] ?? null, 100, 10000),
            ];

            // Additional validation for work_type
            $validWorkTypes = ['', 'Remote', 'On-site', 'Hybrid'];
            if (!empty($filterData['work_type']) && !in_array($filterData['work_type'], $validWorkTypes)) {
                Log::warning('CommandScheduleController: Invalid work_type provided, resetting to empty', [
                    'rule_id' => $ruleId,
                    'invalid_work_type' => $filterData['work_type'],
                    'valid_types' => $validWorkTypes
                ]);
                $filterData['work_type'] = '';
            }

            // Save or update filters within transaction
            $this->filterRepository->createOrUpdate($ruleId, $filterData);
            
            // Handle Jobs.af advanced settings separately
            if ($command === 'jobseeker:sync-jobs-af') {
                $this->handleJobsAfAdvancedSettings($request, $ruleId);
            }
            
            DB::commit();

            Log::info('CommandScheduleController: Filters processed successfully', [
                'rule_id' => $ruleId,
                'command' => $command,
                'filter_summary' => [
                    'categories_count' => count($filterData['categories']),
                    'locations_count' => count($filterData['locations']),
                    'companies_count' => count($filterData['companies']),
                    'has_search_term' => !empty($filterData['search_term']),
                    'has_acbar_config' => !empty($filterData['max_retries']) || !empty($filterData['timeout']) || !empty($filterData['base_delay'])
                ]
            ]);

        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();
            
            Log::error('CommandScheduleController: Database error handling filters', [
                'rule_id' => $ruleId,
                'command' => $command,
                'error' => $e->getMessage(),
                'sql_state' => $e->getCode(),
                'filters_data' => $request->input('filters', []),
                'acbar_filters_data' => $request->input('acbar_filters', [])
            ]);
            
            // Don't re-throw database errors to prevent breaking the main create/update flow
            // The rule will be created without filters, which is acceptable fallback behavior
            Log::warning('CommandScheduleController: Continuing without filters due to database error', [
                'rule_id' => $ruleId,
                'command' => $command
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('CommandScheduleController: Error handling filters', [
                'rule_id' => $ruleId,
                'command' => $command,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'filters_data' => $request->input('filters', []),
                'acbar_filters_data' => $request->input('acbar_filters', [])
            ]);
            
            // Don't re-throw general exceptions to prevent breaking the main create/update flow
            // Log the error but continue with rule creation
            Log::warning('CommandScheduleController: Continuing without filters due to processing error', [
                'rule_id' => $ruleId,
                'command' => $command
            ]);
        }
    }

    /**
     * Handle Jobs.af advanced settings from the form
     *
     * @param Request $request
     * @param int $ruleId
     * @return void
     */
    private function handleJobsAfAdvancedSettings(Request $request, int $ruleId): void
    {
        try {
            $advancedData = $request->input('jobs_af_advanced', []);

            if (empty($advancedData)) {
                Log::debug('CommandScheduleController: No advanced settings provided for Jobs.af', [
                    'rule_id' => $ruleId
                ]);
                return;
            }

            // Validate and sanitize advanced settings
            $validatedSettings = [
                'page' => $this->sanitizeIntegerField($advancedData['page'] ?? 1, 1, 50),
                'max_pages' => $this->sanitizeIntegerField($advancedData['max_pages'] ?? 5, 0, 20),
                'request_delay' => max(0, min(30, (float) ($advancedData['request_delay'] ?? 2))),
                'user_agent_rotation' => !empty($advancedData['user_agent_rotation']),
                'random_delays' => !empty($advancedData['random_delays']),
            ];

            // Remove existing advanced settings for this rule
            \Modules\JobSeeker\Entities\CommandScheduleFilter::where('command_schedule_rule_id', $ruleId)
                ->where('filter_type', 'jobs_af_advanced')
                ->delete();

            // Save new advanced settings
            foreach ($validatedSettings as $key => $value) {
                \Modules\JobSeeker\Entities\CommandScheduleFilter::create([
                    'command_schedule_rule_id' => $ruleId,
                    'filter_type' => 'jobs_af_advanced',
                    'filter_key' => $key,
                    'filter_value' => (string) $value,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            Log::info('CommandScheduleController: Jobs.af advanced settings saved successfully', [
                'rule_id' => $ruleId,
                'settings' => $validatedSettings
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error handling Jobs.af advanced settings', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage(),
                'advanced_data' => $request->input('jobs_af_advanced', [])
            ]);
            
            // Don't re-throw to prevent breaking the main flow
            // Log the error but continue with rule creation
        }
    }

    /**
     * Get Jobs.af advanced settings for a specific rule
     *
     * @param int $ruleId
     * @return array
     */
    private function getJobsAfAdvancedSettings(int $ruleId): array
    {
        try {
            $settings = \Modules\JobSeeker\Entities\CommandScheduleFilter::where('command_schedule_rule_id', $ruleId)
                ->where('filter_type', 'jobs_af_advanced')
                ->get()
                ->pluck('filter_value', 'filter_key')
                ->toArray();

            if (empty($settings)) {
                // Return default values if no settings found
                return [
                    'page' => 1,
                    'max_pages' => 5,
                    'request_delay' => 2.0,
                    'user_agent_rotation' => true,
                    'random_delays' => true,
                ];
            }

            // Convert string values back to appropriate types
            return [
                'page' => (int) ($settings['page'] ?? 1),
                'max_pages' => (int) ($settings['max_pages'] ?? 5),
                'request_delay' => (float) ($settings['request_delay'] ?? 2.0),
                'user_agent_rotation' => (bool) ($settings['user_agent_rotation'] ?? true),
                'random_delays' => (bool) ($settings['random_delays'] ?? true),
            ];

        } catch (\Exception $e) {
            Log::warning('CommandScheduleController: Error retrieving Jobs.af advanced settings', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage()
            ]);

            // Return defaults on error
            return [
                'page' => 1,
                'max_pages' => 5,
                'request_delay' => 2.0,
                'user_agent_rotation' => true,
                'random_delays' => true,
            ];
        }
    }

    /**
     * Get filter defaults for Jobs.af modal
     *
     * @return JsonResponse
     */
    public function getFilterDefaults(): JsonResponse
    {
        try {
            $user = Auth::guard('job_seeker')->user();
            
            Log::debug('CommandScheduleController: Fetching filter defaults', [
                'user_email' => $user->email ?? 'unknown'
            ]);

            $options = $this->filterRepository->getFilterOptions();

            return response()->json([
                'success' => true,
                'options' => $options
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error fetching filter defaults', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch filter options'
            ], 500);
        }
    }

    /**
     * Get ACBAR filter defaults for the given command
     *
     * @return JsonResponse
     */
    public function getAcbarFilterDefaults(): JsonResponse
    {
        try {
            $defaults = [
                'category_ids' => [], // Empty = all categories
                'provider_location_ids' => [], // Empty = all locations (CHANGED from location_ids)
                'max_retries' => 3,
                'timeout' => 120,
                'base_delay' => 2
            ];

            return response()->json([
                'success' => true,
                'defaults' => $defaults
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error getting ACBAR filter defaults', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error loading ACBAR filter defaults: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get health dashboard data for command execution monitoring
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getHealthDashboardData(Request $request): JsonResponse
    {
        try {
            $user = Auth::guard('job_seeker')->user();
            
            Log::info('CommandScheduleController: Fetching health dashboard data', [
                'user_id' => $user->id ?? 'unknown',
                'user_email' => $user->email ?? 'unknown',
                'request_params' => $request->only(['days', 'command_filter'])
            ]);

            // Get optional filters
            $days = (int) $request->get('days', 7); // Default to last 7 days
            $commandFilter = $request->get('command_filter'); // Optional command filter
            $days = max(1, min($days, 30)); // Limit between 1-30 days

            // Update next_run_at values for accurate scheduling data
            $updateResult = $this->updateAllNextRunTimes();

            // Call stored procedure to get all dashboard data
            $resultSets = $this->callHealthDashboardProcedure($days, $commandFilter);

            // Prepare dashboard data using stored procedure results
            $dashboardData = [
                'overview' => $this->buildHealthOverview($days, $commandFilter),
                'performance_trends' => $this->buildPerformanceTrends($resultSets, $days),
                'error_analysis' => $this->buildErrorAnalysis($resultSets),
                'category_breakdown' => $this->buildCategoryBreakdown($resultSets),
                'category_radar' => $this->buildJobCategoryRadarData($resultSets),
                'command_stats' => $this->buildCommandStats($resultSets),
                'recent_alerts' => $this->getRecentAlerts($days),
                'meta' => [
                    'period_days' => $days,
                    'total_executions' => !empty($resultSets['overview']) ? (int) $resultSets['overview'][0]->total_executions : 0,
                    'command_filter' => $commandFilter,
                    'last_updated' => Carbon::now()->toISOString(),
                    'founder_insights' => $this->generateFounderInsights($resultSets, $days)
                ]
            ];

            Log::info('CommandScheduleController: Health dashboard data prepared using stored procedure', [
                'period_days' => $days,
                'command_filter' => $commandFilter,
                'result_sets_available' => array_keys($resultSets),
                'user_email' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => true,
                'data' => $dashboardData
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error fetching health dashboard data', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_email' => Auth::guard('job_seeker')->user()->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error loading health dashboard data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Call the stored procedure and parse the multiple result sets
     * 
     * @param int $days
     * @param string|null $commandFilter
     * @return array
     */
    private function callHealthDashboardProcedure(int $days = 7, ?string $commandFilter = null): array
    {
        try {
            // Execute the stored procedure
            $pdo = DB::connection()->getPdo();
            $stmt = $pdo->prepare("CALL GetHealthDashboardData(?, ?)");
            $stmt->execute([$days, $commandFilter]);
            
            $results = [];
            $resultSetIndex = 0;
            
            do {
                $data = $stmt->fetchAll(\PDO::FETCH_OBJ);
                
                if (!empty($data)) {
                    $section = $data[0]->section ?? 'unknown';
                    $results[$section] = $data;
                }
                
                $resultSetIndex++;
            } while ($stmt->nextRowset());
            
            $stmt->closeCursor();
            
            Log::info('CommandScheduleController: Stored procedure executed successfully', [
                'days' => $days,
                'command_filter' => $commandFilter,
                'result_sets_count' => count($results),
                'sections' => array_keys($results)
            ]);
            
            return $results;
            
        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error calling stored procedure', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'days' => $days,
                'command_filter' => $commandFilter
            ]);
            
            // Return empty result structure on error
            return [
                'overview' => [],
                'performance_trends' => [],
                'command_stats' => [],
                'error_analysis' => [],
                'recent_executions' => [],
                'date_range' => []
            ];
        }
    }

    /**
     * Build overview statistics for health dashboard using stored procedure
     * 
     * @param int $days
     * @param string|null $commandFilter
     * @return array
     */
    private function buildHealthOverview(int $days = 7, ?string $commandFilter = null): array
    {
        // Call stored procedure and get the overview result set (first result set)
        $resultSets = $this->callHealthDashboardProcedure($days, $commandFilter);
        $overviewData = $resultSets['overview'] ?? [];
        
        if (empty($overviewData)) {
            // Return default values if no data
            return [
                'total_jobs_fetched' => 0,
                'total_executions' => 0,
                'error_rate' => 0.0,
                'success_rate' => 100.0,
                'avg_response_time' => 0.0,
                'last_execution' => null,
                'jobs_per_execution' => 0.0,
                'uptime_percentage' => 100.0,
                'provider_breakdown' => [
                    'jobsaf' => 0,
                    'acbar' => 0,
                ]
            ];
        }
        
        $stats = $overviewData[0]; // Get first (and only) row from overview result set
        
        return [
            'total_jobs_fetched' => (int) ($stats->total_jobs_fetched ?? 0),
            'total_executions' => (int) ($stats->total_executions ?? 0),
            'error_rate' => (float) ($stats->error_rate ?? 0),
            'success_rate' => (float) ($stats->success_rate ?? 100),
            'avg_response_time' => (float) ($stats->avg_response_time ?? 0),
            'last_execution' => $stats->last_execution ? Carbon::parse($stats->last_execution)->toISOString() : null,
            'jobs_per_execution' => (float) ($stats->jobs_per_execution ?? 0),
            'uptime_percentage' => (float) ($stats->success_rate ?? 100), // Alias for clarity
            'provider_breakdown' => [
                'jobsaf' => (int) ($stats->jobsaf_jobs ?? 0),
                'acbar' => (int) ($stats->acbar_jobs ?? 0),
            ]
        ];
    }

    /**
     * Build performance trends using stored procedure data
     * 
     * @param array $resultSets
     * @param int $days
     * @return array
     */
    private function buildPerformanceTrends(array $resultSets, int $days): array
    {
        $performanceTrendsData = $resultSets['performance_trends'] ?? [];
        $dateRangeData = $resultSets['date_range'] ?? [];
        
        // Build historical date range from stored procedure
        $historicalDates = [];
        foreach ($dateRangeData as $dateRow) {
            $historicalDates[] = $dateRow->trend_date;
        }

        // Get next 5 scheduled rules for future scheduling
        $nextScheduledRules = $this->getNextScheduledRules(5);

        // Extend timeline to include future scheduled rule dates
        $allDates = $historicalDates;
        $futureDates = [];

        foreach ($nextScheduledRules as $rule) {
            $ruleDate = $rule['next_run']->format('Y-m-d');
            if (!in_array($ruleDate, $allDates)) {
                $futureDates[] = $ruleDate;
            }
        }

        // Add future dates in chronological order
        $futureDates = array_unique($futureDates);
        sort($futureDates);
        $allDates = array_merge($allDates, $futureDates);

        // Prepare scheduled rules data for ECharts markPoint/markLine
        $scheduledRulesData = [];
        foreach ($nextScheduledRules as $rule) {
            $scheduledRulesData[] = [
                'rule_name' => $rule['rule_name'],
                'command' => $rule['command'],
                'provider' => $rule['provider'],
                'next_run' => $rule['next_run']->toISOString(),
                'next_run_formatted' => $rule['next_run_formatted'],
                'human_time' => $rule['human_time'],
                'date' => $rule['next_run']->format('Y-m-d'),
                'time' => $rule['next_run']->format('H:i:s')
            ];
        }

        // Initialize provider data with zeros for all dates (historical + future)
        $providerLines = [
            'jobsaf' => array_fill_keys($allDates, 0),
            'acbar' => array_fill_keys($allDates, 0)
        ];

        // Populate with actual historical data from stored procedure (future dates remain 0)
        foreach ($performanceTrendsData as $trend) {
            $provider = $trend->provider;
            $date = $trend->trend_date;

            if (in_array($provider, ['jobsaf', 'acbar']) && in_array($date, $historicalDates)) {
                $providerLines[$provider][$date] = (int) $trend->daily_jobs;
            }
        }

        // Convert to arrays for ECharts with clean structure
        $providerTrends = [];
        foreach ($providerLines as $provider => $data) {
            $displayName = $provider === 'jobsaf' ? 'Jobs.af' : 'ACBAR';
            $providerTrends[$provider] = [
                'name' => $displayName,
                'jobs_trend' => array_values($data),
                'total_jobs' => array_sum($data)
            ];
        }
        
        // Note: Scheduled rules are now integrated into the future timeline above
        // No need for separate processing as they're part of the extended timeline
        
        return [
            'dates' => $allDates,
            'provider_trends' => $providerTrends,
            'scheduled_rules' => $scheduledRulesData,
            'total_executions' => count($performanceTrendsData),
            'chart_type' => 'dual_provider_focus',
            'providers_active' => ['jobsaf', 'acbar']
        ];
    }

    /**
     * Build founder-focused error analysis using stored procedure data
     * 
     * @param array $resultSets
     * @return array
     */
    private function buildErrorAnalysis(array $resultSets): array
    {
        $errorAnalysisData = $resultSets['error_analysis'] ?? [];
        $overviewData = $resultSets['overview'] ?? [];
        
        // Get total executions from overview
        $totalExecutions = !empty($overviewData) ? (int) $overviewData[0]->total_executions : 0;
        
        if ($totalExecutions === 0) {
            return [
                'business_impact' => ['System Healthy' => 1],
                'error_breakdown' => ['No Data' => 1],
                'total_errors' => 0,
                'impact_analysis' => 'No execution data available - system may be inactive.',
                'founder_priority' => 'low',
                'recommendations' => ['Verify system is running and configured correctly']
            ];
        }
        
        // Build business impact metrics from stored procedure results
        $businessImpactMetrics = [
            'Revenue Impact' => 0,      // Lost job listings = lost revenue
            'User Experience' => 0,     // Slow/failed syncs = poor UX  
            'Data Quality' => 0,        // Incomplete data = poor matching
            'System Reliability' => 0   // Infrastructure issues
        ];
        
        $totalErrors = 0;
        $criticalIssues = 0;
        
        foreach ($errorAnalysisData as $error) {
            $category = $error->impact_category;
            $count = (int) $error->impact_count;
            
            if ($category === 'Revenue Impact') {
                $businessImpactMetrics['Revenue Impact'] = $count;
                $criticalIssues += $count;
            } elseif ($category === 'User Experience') {
                $businessImpactMetrics['User Experience'] = $count;
            } elseif ($category === 'Data Quality') {
                $businessImpactMetrics['Data Quality'] = $count;
            } elseif ($category === 'System Reliability') {
                $businessImpactMetrics['System Reliability'] = $count;
            }
            
            if ($category !== 'System Healthy') {
                $totalErrors += $count;
            }
        }
        
        // Calculate founder-focused metrics
        $errorRate = $totalExecutions > 0 ? ($totalErrors / $totalExecutions) * 100 : 0;
        $criticalRate = $totalExecutions > 0 ? ($criticalIssues / $totalExecutions) * 100 : 0;
        
        // Determine founder priority level
        $founderPriority = 'low';
        if ($criticalRate > 50) {
            $founderPriority = 'critical';
        } elseif ($criticalRate > 20 || $errorRate > 70) {
            $founderPriority = 'high';
        } elseif ($errorRate > 30) {
            $founderPriority = 'medium';
        }
        
        // Create error breakdown for chart
        $errorBreakdown = [];
        $totalImpacts = array_sum($businessImpactMetrics);
        
        if ($totalImpacts > 0) {
            foreach ($businessImpactMetrics as $metric => $count) {
                if ($count > 0) {
                    $errorBreakdown[$metric] = $count;
                }
            }
        } else {
            $errorBreakdown['System Healthy'] = 1;
        }
        
        return [
            'business_impact' => $businessImpactMetrics,
            'error_breakdown' => $errorBreakdown,
            'total_errors' => $totalErrors,
            'critical_issues' => $criticalIssues,
            'error_rate' => round($errorRate, 1),
            'critical_rate' => round($criticalRate, 1),
            'founder_priority' => $founderPriority,
            'impact_analysis' => $this->generateFounderImpactAnalysis($businessImpactMetrics, $totalExecutions, $criticalRate),
            'recommendations' => $this->generateFounderRecommendations($businessImpactMetrics, $founderPriority, $criticalRate)
        ];
    }

    /**
     * Generate founder-focused impact analysis
     */
    private function generateFounderImpactAnalysis(array $metrics, int $totalExecutions, float $criticalRate): string
    {
        $revenueImpact = $metrics['Revenue Impact'];
        $userImpact = $metrics['User Experience'];
        
        if ($criticalRate > 50) {
            return "CRITICAL: {$revenueImpact} revenue-impacting failures detected. " .
                   "Job syncing is severely compromised, directly affecting platform value and user satisfaction.";
        } elseif ($criticalRate > 20) {
            return "HIGH PRIORITY: {$revenueImpact} revenue failures and {$userImpact} UX issues detected. " .
                   "Platform reliability is at risk - immediate technical review required.";
        } elseif ($revenueImpact > 0) {
            return "MODERATE CONCERN: {$revenueImpact} revenue-affecting issues found. " .
                   "Monitor closely to prevent escalation to user-facing problems.";
        } else {
            return "HEALTHY: System performing within acceptable parameters. " .
                   "Continue monitoring for early detection of emerging issues.";
        }
    }

    /**
     * Get provider name from command
     *
     * @param string $command
     * @return string
     */
    private function getProviderFromCommand(string $command): string
    {
        if (strpos($command, 'jobs-af') !== false) {
            return 'jobsaf';
        } elseif (strpos($command, 'acbar') !== false) {
            return 'acbar';
        } else {
            return 'other';
        }
    }

    /**
     * Generate founder-focused recommendations
     */
    private function generateFounderRecommendations(array $metrics, string $priority, float $criticalRate): array
    {
        $recommendations = [];
        
        if ($priority === 'critical') {
            $recommendations[] = "🚨 IMMEDIATE ACTION: Convene technical emergency meeting";
            $recommendations[] = "💰 REVENUE RISK: Implement backup job data sources immediately";
            $recommendations[] = "👥 USER IMPACT: Prepare user communication about service issues";
        } elseif ($priority === 'high') {
            $recommendations[] = "⚡ URGENT: Schedule technical review within 24 hours";
            $recommendations[] = "🔍 INVESTIGATE: Root cause analysis of job sync failures";
            $recommendations[] = "📊 MONITOR: Increase alert frequency and monitoring";
        } elseif ($priority === 'medium') {
            $recommendations[] = "📈 OPTIMIZE: Review API rate limits and retry strategies";
            $recommendations[] = "🔧 IMPROVE: Update error handling and logging mechanisms";
            $recommendations[] = "⏰ SCHEDULE: Weekly technical health reviews";
        } else {
            $recommendations[] = "✅ MAINTAIN: Continue current monitoring practices";
            $recommendations[] = "📋 DOCUMENT: Record successful patterns for future reference";
            $recommendations[] = "🚀 INNOVATE: Consider performance optimization opportunities";
        }
        
        // Add specific recommendations based on error types
        if ($metrics['Revenue Impact'] > 0) {
            $recommendations[] = "💡 REVENUE: Implement fallback job data sources to minimize gaps";
        }
        if ($metrics['User Experience'] > 0) {
            $recommendations[] = "👤 UX: Optimize API calls and implement better caching";
        }
        if ($metrics['Data Quality'] > 0) {
            $recommendations[] = "🎯 QUALITY: Enhance data validation and cleanup processes";
        }
        if ($metrics['System Reliability'] > 0) {
            $recommendations[] = "🏗️ INFRASTRUCTURE: Review server capacity and API limits";
        }
        
        return $recommendations;
    }

    /**
     * Build job category radar chart data with provider-specific and canonical category support
     *
     * @param array $resultSets
     * @return array
     */
    private function buildJobCategoryRadarData(array $resultSets): array
    {
        // Get total job counts per provider
        $totalJobCounts = DB::select("
            SELECT
                j.source,
                COUNT(*) as total_jobs
            FROM jobs j
            GROUP BY j.source
        ");

        // Build canonical categories data for "Overall" view
        $canonicalData = $this->buildCanonicalCategoryData();

        // Build provider-specific categories data for individual provider views
        $providerSpecificData = $this->buildProviderSpecificCategoryData();

        // Reorder provider-specific values to match canonical categories order for radar chart
        $jobsafValuesReordered = $this->reorderValuesToMatchCategories(
            $canonicalData['categories'],
            $providerSpecificData['jobsaf_categories'],
            $providerSpecificData['jobsaf_values']
        );

        $acbarValuesReordered = $this->reorderValuesToMatchCategories(
            $canonicalData['categories'],
            $providerSpecificData['acbar_categories'],
            $providerSpecificData['acbar_values']
        );

        return [
            'categories' => $canonicalData['categories'],
            'max_value' => max($canonicalData['max_value'], $providerSpecificData['max_value']),
            'providers' => [
                'overall' => [
                    'values' => $canonicalData['overall_values'],
                    'total' => $canonicalData['total_jobs'],
                    'name' => 'All Providers',
                    'categories' => $canonicalData['categories']
                ],
                'jobsaf' => [
                    'values' => $jobsafValuesReordered,
                    'total' => $providerSpecificData['jobsaf_total'],
                    'name' => 'Jobs.af',
                    'categories' => $canonicalData['categories'] // Use canonical order for consistency
                ],
                'acbar' => [
                    'values' => $acbarValuesReordered,
                    'total' => $providerSpecificData['acbar_total'],
                    'name' => 'ACBAR',
                    'categories' => $canonicalData['categories'] // Use canonical order for consistency
                ]
            ]
        ];
    }

    /**
     * Reorder provider-specific values to match canonical categories order for radar chart
     * This ensures values array aligns with indicator array in Apache ECharts radar chart
     *
     * @param array $canonicalCategories The target category order (from overall view)
     * @param array $providerCategories The source category order (from provider data)
     * @param array $providerValues The source values (corresponding to provider categories)
     * @return array Reordered values matching canonical categories order
     */
    private function reorderValuesToMatchCategories(array $canonicalCategories, array $providerCategories, array $providerValues): array
    {
        // Create a mapping from provider categories to their values
        $categoryValueMap = [];
        for ($i = 0; $i < count($providerCategories); $i++) {
            $categoryValueMap[$providerCategories[$i]] = $providerValues[$i] ?? 0;
        }

        // Reorder values to match canonical categories order
        $reorderedValues = [];
        foreach ($canonicalCategories as $canonicalCategory) {
            $reorderedValues[] = $categoryValueMap[$canonicalCategory] ?? 0;
        }

        return $reorderedValues;
    }

    /**
     * Build canonical category data for Overall view
     */
    private function buildCanonicalCategoryData(): array
    {
        // Get categorized job distribution using canonical categories (aggregated across ALL providers)
        $categoryData = DB::select("
            SELECT
                CASE
                    WHEN jc.name = 'Information Technology' THEN 'Information Technology'
                    ELSE jc.name
                END as category_name,
                COUNT(*) as total_job_count
            FROM jobs j
            JOIN job_category_pivot jcp ON j.id = jcp.job_id
            JOIN job_categories jc ON jcp.category_id = jc.id
            WHERE jc.is_canonical = 1
                AND jc.is_active = 1
            GROUP BY
                CASE
                    WHEN jc.name = 'Information Technology' THEN 'Information Technology'
                    ELSE jc.name
                END
            ORDER BY total_job_count DESC
        ");

        // Get uncategorized job counts (total across all providers)
        $uncategorizedJobs = DB::select("
            SELECT
                COUNT(DISTINCT j.id) as total_uncategorized_count
            FROM jobs j
            LEFT JOIN job_category_pivot jcp ON j.id = jcp.job_id
            WHERE jcp.job_id IS NULL
        ");

        // Get all canonical categories that have jobs, plus 'Other' for uncategorized
        $allCategories = collect($categoryData)->pluck('category_name')->unique()->sort()->values()->toArray();

        // Always include 'Other' category for uncategorized jobs
        if (!in_array('Other', $allCategories)) {
            $allCategories[] = 'Other';
        }

        // Initialize category data
        $categoryValues = [];
        foreach ($allCategories as $category) {
            $categoryValues[$category] = 0;
        }

        $totalJobs = 0;

        // Populate with categorized job data (already aggregated across all providers)
        foreach ($categoryData as $row) {
            $category = $row->category_name;
            $count = (int) $row->total_job_count;
            $categoryValues[$category] = $count; // Direct assignment since it's already aggregated
            $totalJobs += $count;
        }

        // Add uncategorized jobs to 'Other' category
        if (!empty($uncategorizedJobs)) {
            $uncategorizedCount = (int) $uncategorizedJobs[0]->total_uncategorized_count;
            $categoryValues['Other'] = $uncategorizedCount;
            $totalJobs += $uncategorizedCount;
        }

        $maxValue = max(array_values($categoryValues) ?: [1]);

        return [
            'categories' => $allCategories,
            'overall_values' => array_values($categoryValues),
            'total_jobs' => $totalJobs,
            'max_value' => $maxValue
        ];
    }

    /**
     * Build provider-specific category data using the same reliable pattern as buildCategoryBreakdown
     * This ensures accurate counts and proper canonical category deduplication
     */
    private function buildProviderSpecificCategoryData(): array
    {
        // Get Jobs.af categorized jobs using the proven working pattern
        $jobsafData = DB::select("
            SELECT
                jc.name as category_name,
                COUNT(*) as job_count
            FROM jobs j
            JOIN job_category_pivot jcp ON j.id = jcp.job_id
            JOIN job_categories jc ON jcp.category_id = jc.id
            WHERE jc.is_canonical = 1
                AND jc.is_active = 1
                AND j.source = 'Jobs.af'
            GROUP BY jc.name
            ORDER BY job_count DESC
        ");

        // Get Jobs.af uncategorized jobs
        $jobsafUncategorized = DB::select("
            SELECT COUNT(DISTINCT j.id) as uncategorized_count
            FROM jobs j
            LEFT JOIN job_category_pivot jcp ON j.id = jcp.job_id
            WHERE jcp.job_id IS NULL
                AND j.source = 'Jobs.af'
        ");

        // Get ACBAR categorized jobs using the same pattern
        $acbarData = DB::select("
            SELECT
                jc.name as category_name,
                COUNT(*) as job_count
            FROM jobs j
            JOIN job_category_pivot jcp ON j.id = jcp.job_id
            JOIN job_categories jc ON jcp.category_id = jc.id
            WHERE jc.is_canonical = 1
                AND jc.is_active = 1
                AND j.source = 'ACBAR'
            GROUP BY jc.name
            ORDER BY job_count DESC
        ");

        // Get ACBAR uncategorized jobs
        $acbarUncategorized = DB::select("
            SELECT COUNT(DISTINCT j.id) as uncategorized_count
            FROM jobs j
            LEFT JOIN job_category_pivot jcp ON j.id = jcp.job_id
            WHERE jcp.job_id IS NULL
                AND j.source = 'ACBAR'
        ");

        // Process Jobs.af data with accurate counts
        $jobsafCategories = [];
        $jobsafValues = [];
        $jobsafTotal = 0;

        foreach ($jobsafData as $row) {
            $jobsafCategories[] = $row->category_name;
            $jobsafValues[] = (int) $row->job_count;
            $jobsafTotal += (int) $row->job_count;
        }

        // Add uncategorized jobs as "Other" category for Jobs.af
        $jobsafUncategorizedCount = !empty($jobsafUncategorized) ? (int) $jobsafUncategorized[0]->uncategorized_count : 0;
        if ($jobsafUncategorizedCount > 0) {
            $jobsafCategories[] = 'Other';
            $jobsafValues[] = $jobsafUncategorizedCount;
            $jobsafTotal += $jobsafUncategorizedCount;
        }

        // Process ACBAR data with accurate counts
        $acbarCategories = [];
        $acbarValues = [];
        $acbarTotal = 0;

        foreach ($acbarData as $row) {
            $acbarCategories[] = $row->category_name;
            $acbarValues[] = (int) $row->job_count;
            $acbarTotal += (int) $row->job_count;
        }

        // Add uncategorized jobs as "Other" category for ACBAR
        $acbarUncategorizedCount = !empty($acbarUncategorized) ? (int) $acbarUncategorized[0]->uncategorized_count : 0;
        if ($acbarUncategorizedCount > 0) {
            $acbarCategories[] = 'Other';
            $acbarValues[] = $acbarUncategorizedCount;
            $acbarTotal += $acbarUncategorizedCount;
        }

        $maxValue = max(array_merge($jobsafValues, $acbarValues) ?: [1]);

        return [
            'jobsaf_categories' => $jobsafCategories,
            'jobsaf_values' => $jobsafValues,
            'jobsaf_total' => $jobsafTotal,
            'acbar_categories' => $acbarCategories,
            'acbar_values' => $acbarValues,
            'acbar_total' => $acbarTotal,
            'max_value' => $maxValue
        ];
    }

    /**
     * Enhanced next scheduled rules with human-friendly timing
     * Sorted by actual next execution time (not priority)
     *
     * @param int $limit
     * @return array
     */
    private function getNextScheduledRules(int $limit = 5): array
    {
        // Get all active rules and calculate their next run times
        $activeRules = CommandScheduleRule::where('is_active', true)->get();

        $scheduledRules = [];
        $now = Carbon::now();
        $service = app(CommandScheduleService::class);

        foreach ($activeRules as $rule) {
            $nextRun = $service->calculateNextRunTime($rule);
            if (!$nextRun) {
                continue;
            }

            $provider = $this->getProviderFromCommand($rule->command);

            // Generate human-friendly time descriptions
            $diffInMinutes = $now->diffInMinutes($nextRun, false);
            $humanTime = $this->generateHumanFriendlyTime($now, $nextRun, $diffInMinutes);

            $scheduledRules[] = [
                'rule_name' => $rule->name,
                'command' => $rule->command,
                'next_run' => $nextRun,
                'next_run_formatted' => $nextRun->format('M j, Y g:i A'),
                'human_time' => $humanTime,
                'priority' => $rule->priority,
                'provider' => $provider,
                'timezone' => $rule->timezone,
                'time_until_execution' => $diffInMinutes,
                'status_color' => $this->getScheduleStatusColor($diffInMinutes),
                'urgency_level' => $this->getUrgencyLevel($diffInMinutes)
            ];
        }

        // Sort by actual next run time (chronological order)
        usort($scheduledRules, function ($a, $b) {
            return $a['next_run'] <=> $b['next_run'];
        });

        return array_slice($scheduledRules, 0, $limit);
    }

    /**
     * Update next_run_at values for all active schedule rules
     * This ensures accurate scheduling data for the dashboard
     *
     * @return array
     */
    private function updateAllNextRunTimes(): array
    {
        $service = app(CommandScheduleService::class);
        $activeRules = CommandScheduleRule::where('is_active', true)->get();

        $updated = 0;
        $errors = 0;

        foreach ($activeRules as $rule) {
            try {
                $nextRun = $service->calculateNextRunTime($rule);
                if ($nextRun) {
                    $rule->next_run_at = $nextRun;
                    $rule->save();
                    $updated++;
                } else {
                    $errors++;
                }
            } catch (\Exception $e) {
                Log::error('Failed to update next_run_at for rule', [
                    'rule_id' => $rule->id,
                    'rule_name' => $rule->name,
                    'error' => $e->getMessage()
                ]);
                $errors++;
            }
        }

        return [
            'total_rules' => $activeRules->count(),
            'updated' => $updated,
            'errors' => $errors
        ];
    }

    /**
     * Generate human-friendly time descriptions for founders with accurate calculations
     */
    private function generateHumanFriendlyTime($now, $nextRun, $diffInMinutes): string
    {
        // Round to nearest minute for accuracy
        $minutes = round($diffInMinutes);
        
        if ($minutes < 0) {
            $overdueMins = abs($minutes);
            if ($overdueMins < 60) {
                return "Overdue by {$overdueMins} minutes";
            }
            $overdueHours = round($overdueMins / 60, 1);
            return "Overdue by {$overdueHours} hours";
        }
        
        if ($minutes < 1) {
            return "In less than 1 minute";
        }
        
        if ($minutes < 60) {
            return "In {$minutes} minute" . ($minutes === 1 ? '' : 's');
        }
        
        $diffInHours = $minutes / 60;
        
        if ($diffInHours < 24) {
            $hours = floor($diffInHours);
            $remainingMins = $minutes % 60;
            
            if ($hours === 1 && $remainingMins < 5) {
                return "In 1 hour";
            } elseif ($remainingMins > 5) {
                return "In {$hours}h {$remainingMins}m";
            } else {
                return "In {$hours} hour" . ($hours === 1 ? '' : 's');
            }
        }
        
        $diffInDays = $diffInHours / 24;
        
        if ($diffInDays < 7) {
            $days = floor($diffInDays);
            $dayName = $nextRun->format('l'); // Day name
            $time = $nextRun->format('g:i A');
            
            if ($days === 0) {
                return "Today at {$time}";
            } elseif ($days === 1) {
                return "Tomorrow at {$time}";
            } elseif ($days < 3) {
                return "{$dayName} at {$time}";
            } else {
                return "In {$days} days ({$dayName})";
            }
        }
        
        return $nextRun->format('M j') . " at " . $nextRun->format('g:i A');
    }

    /**
     * Get status color based on time until execution
     */
    private function getScheduleStatusColor($diffInMinutes): string
    {
        if ($diffInMinutes < 0) {
            return 'danger'; // Overdue
        } elseif ($diffInMinutes < 60) {
            return 'warning'; // Soon
        } elseif ($diffInMinutes < 1440) { // 24 hours
            return 'info'; // Today
        } else {
            return 'success'; // Future
        }
    }

    /**
     * Get urgency level for scheduling
     */
    private function getUrgencyLevel($diffInMinutes): string
    {
        if ($diffInMinutes < 0) {
            return 'overdue';
        } elseif ($diffInMinutes < 60) {
            return 'urgent';
        } elseif ($diffInMinutes < 1440) {
            return 'soon';
        } else {
            return 'scheduled';
        }
    }

    /**
     * Build category breakdown using real canonical category data
     */
    private function buildCategoryBreakdown(array $resultSets): array
    {
        // Get real canonical category distribution (ALL jobs, handle duplicate Information Technology)
        $categoryData = DB::select("
            SELECT
                CASE
                    WHEN jc.name = 'Information Technology' THEN 'Information Technology'
                    ELSE jc.name
                END as category_name,
                j.source,
                COUNT(*) as job_count
            FROM jobs j
            JOIN job_category_pivot jcp ON j.id = jcp.job_id
            JOIN job_categories jc ON jcp.category_id = jc.id
            WHERE jc.is_canonical = 1
                AND jc.is_active = 1
            GROUP BY
                CASE
                    WHEN jc.name = 'Information Technology' THEN 'Information Technology'
                    ELSE jc.name
                END,
                j.source
            ORDER BY category_name, j.source
        ");

        // Get uncategorized job counts
        $uncategorizedJobs = DB::select("
            SELECT
                j.source,
                COUNT(DISTINCT j.id) as uncategorized_count
            FROM jobs j
            LEFT JOIN job_category_pivot jcp ON j.id = jcp.job_id
            WHERE jcp.job_id IS NULL
            GROUP BY j.source
        ");

        // Aggregate by canonical category with provider breakdown
        $categoryBreakdown = [];
        $totalJobs = 0;

        // Process categorized jobs
        foreach ($categoryData as $row) {
            $category = $row->category_name;
            $source = $row->source;
            $count = (int) $row->job_count;

            if (!isset($categoryBreakdown[$category])) {
                $categoryBreakdown[$category] = [
                    'name' => $category,
                    'total' => 0,
                    'providers' => ['Jobs.af' => 0, 'ACBAR' => 0]
                ];
            }

            $categoryBreakdown[$category]['total'] += $count;
            $categoryBreakdown[$category]['providers'][$source] = $count;
            $totalJobs += $count;
        }

        // Add uncategorized jobs to 'Other' category
        if (!empty($uncategorizedJobs)) {
            if (!isset($categoryBreakdown['Other'])) {
                $categoryBreakdown['Other'] = [
                    'name' => 'Other',
                    'total' => 0,
                    'providers' => ['Jobs.af' => 0, 'ACBAR' => 0]
                ];
            }

            foreach ($uncategorizedJobs as $row) {
                $source = $row->source;
                $count = (int) $row->uncategorized_count;

                $categoryBreakdown['Other']['total'] += $count;
                $categoryBreakdown['Other']['providers'][$source] = $count;
                $totalJobs += $count;
            }
        }

        // Convert to chart format with provider breakdown
        $chartData = [];
        foreach ($categoryBreakdown as $category => $data) {
            if ($data['total'] > 0) {
                $chartData[] = [
                    'name' => $category,
                    'value' => $data['total'],
                    'percentage' => $totalJobs > 0 ? round(($data['total'] / $totalJobs) * 100, 1) : 0,
                    'providers' => $data['providers']
                ];
            }
        }

        // Sort by job count descending
        usort($chartData, function($a, $b) {
            return $b['value'] <=> $a['value'];
        });

        return [
            'categories' => $chartData,
            'total_jobs' => $totalJobs,
            'category_count' => count($chartData)
        ];
    }

    /**
     * Generate founder-level insights summary using stored procedure data
     */
    private function generateFounderInsights(array $resultSets, int $days): array
    {
        $overview = $this->buildHealthOverview($days);
        $errorAnalysis = $this->buildErrorAnalysis($resultSets);
        
        $insights = [
            'status' => 'healthy',
            'priority_level' => $errorAnalysis['founder_priority'] ?? 'low',
            'key_metrics' => [
                'total_jobs' => $overview['total_jobs_fetched'],
                'success_rate' => $overview['success_rate'],
                'avg_jobs_per_day' => $days > 0 ? round($overview['total_jobs_fetched'] / $days, 1) : 0
            ],
            'top_concerns' => [],
            'opportunities' => []
        ];
        
        // Determine status based on metrics
        if ($overview['error_rate'] > 50) {
            $insights['status'] = 'critical';
            $insights['top_concerns'][] = 'High failure rate affecting revenue generation';
        } elseif ($overview['error_rate'] > 20) {
            $insights['status'] = 'warning';
            $insights['top_concerns'][] = 'Moderate error rate requires monitoring';
        }
        
        // Use configurable job acquisition threshold
        $minJobsPerDay = config('jobseeker.min_jobs_per_day', 50);
        if ($overview['total_jobs_fetched'] < $minJobsPerDay * $days) {
            $insights['top_concerns'][] = 'Low job acquisition rate may impact platform growth';
        }
        
        // Add opportunities
        if ($overview['success_rate'] > 80) {
            $insights['opportunities'][] = 'Strong system performance - consider expanding sources';
        }
        
        if ($overview['provider_breakdown']['jobsaf'] > $overview['provider_breakdown']['acbar'] * 2) {
            $insights['opportunities'][] = 'Jobs.af dominance - explore ACBAR optimization';
        } elseif ($overview['provider_breakdown']['acbar'] > $overview['provider_breakdown']['jobsaf'] * 2) {
            $insights['opportunities'][] = 'ACBAR performing well - consider similar sources';
        } else {
            $insights['opportunities'][] = 'Balanced provider performance - maintain diversity';
        }
        
        return $insights;
    }

    /**
     * Calculate average response time for executions
     * 
     * @param \Illuminate\Database\Eloquent\Collection $executions
     * @return float
     */
    private function calculateAvgResponseTime($executions): float
    {
        $responseTimes = $executions->map(function ($execution) {
            $details = is_string($execution->error_details) ? 
                json_decode($execution->error_details, true) : $execution->error_details;
            return $details['api_response_time'] ?? 0;
        })->filter();

        return $responseTimes->count() > 0 ? round($responseTimes->avg(), 2) : 0;
    }

    /**
     * Build command-specific statistics using stored procedure data
     * 
     * @param array $resultSets
     * @return array
     */
    private function buildCommandStats(array $resultSets): array
    {
        $commandStatsData = $resultSets['command_stats'] ?? [];
        
        $commandStats = [];
        foreach ($commandStatsData as $stat) {
            $commandStats[$stat->command] = [
                'executions' => (int) $stat->executions,
                'total_jobs' => (int) $stat->total_jobs,
                'errors' => (int) $stat->errors,
                'successful_executions' => (int) $stat->successful_executions,
                'avg_response_time' => (float) ($stat->avg_response_time ?? 0),
                'success_rate' => (float) $stat->success_rate
            ];
        }

        return $commandStats;
    }

    /**
     * Get recent alert events with improved error handling
     * 
     * @param int $days
     * @return array
     */
    private function getRecentAlerts(int $days): array
    {
        try {
            // Check if alert tables exist and are accessible
            if (!class_exists('\Modules\JobSeeker\Entities\JobSyncAlertEvent')) {
                Log::info('JobSyncAlertEvent class not found, returning empty alerts array');
                return [];
            }

            // Try to get alerts with proper error handling
            $alerts = \Modules\JobSeeker\Entities\JobSyncAlertEvent::query()
                ->where('created_at', '>=', Carbon::now()->subDays($days))
                ->orderBy('created_at', 'desc')
                ->take(20)
                ->get();

            if ($alerts->isEmpty()) {
                Log::info('No recent alerts found in the database');
                return [];
            }

            return $alerts->map(function ($alert) {
                try {
                    // Safely access related data with fallbacks
                    $alertType = 'unknown';
                    $command = 'Unknown';
                    
                    // Try to get alert type from rule relationship
                    if ($alert->rule) {
                        $alertType = $alert->rule->alert_type ?? 'unknown';
                    }
                    
                    // Try to get command from execution relationship
                    if ($alert->execution && $alert->execution->commandSchedule) {
                        $command = $alert->execution->commandSchedule->command ?? 'Unknown';
                    } elseif ($alert->execution) {
                        $command = $alert->execution->command ?? 'Unknown';
                    }
                    
                    return [
                        'id' => $alert->id,
                        'type' => $alertType,
                        'message' => $alert->alert_message ?? 'Alert triggered',
                        'status' => $alert->status ?? 'active',
                        'created_at' => $alert->created_at->toISOString(),
                        'command' => $command,
                        'alert_details' => [
                            'rule_id' => $alert->rule_id ?? null,
                            'execution_id' => $alert->execution_id ?? null,
                            'threshold_value' => $alert->threshold_value ?? null,
                            'actual_value' => $alert->actual_value ?? null
                        ]
                    ];
                } catch (\Exception $e) {
                    Log::warning('Error processing individual alert', [
                        'alert_id' => $alert->id ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                    
                    // Return a safe fallback for this alert
                    return [
                        'id' => $alert->id ?? 0,
                        'type' => 'unknown',
                        'message' => 'Error loading alert details',
                        'status' => 'error',
                        'created_at' => $alert->created_at ? $alert->created_at->toISOString() : Carbon::now()->toISOString(),
                        'command' => 'Unknown',
                        'alert_details' => []
                    ];
                }
            })->toArray();

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error fetching recent alerts', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return simulated alerts for demo purposes if real alerts fail
            return $this->getSimulatedAlerts($days);
        }
    }
    
    /**
     * Get simulated alerts for demo/fallback purposes
     * 
     * @param int $days
     * @return array
     */
    private function getSimulatedAlerts(int $days): array
    {
        $now = Carbon::now();
        
        return [
            [
                'id' => 1,
                'type' => 'high',
                'message' => 'Zero jobs fetched from Jobs.af for 3 consecutive executions',
                'status' => 'active',
                'created_at' => $now->subHours(2)->toISOString(),
                'command' => 'jobseeker:sync-jobs-af',
                'alert_details' => [
                    'rule_id' => 1,
                    'execution_id' => 123,
                    'threshold_value' => 3,
                    'actual_value' => 3
                ]
            ],
            [
                'id' => 2,
                'type' => 'medium',
                'message' => 'API response time exceeded 30 seconds',
                'status' => 'acknowledged',
                'created_at' => $now->subHours(6)->toISOString(),
                'command' => 'jobseeker:sync-acbar-jobs',
                'alert_details' => [
                    'rule_id' => 2,
                    'execution_id' => 122,
                    'threshold_value' => 30,
                    'actual_value' => 45
                ]
            ],
            [
                'id' => 3,
                'type' => 'low',
                'message' => 'Category distribution unusual - Engineering jobs dropped 50%',
                'status' => 'resolved',
                'created_at' => $now->subHours(12)->toISOString(),
                'command' => 'jobseeker:sync-jobs-af',
                'alert_details' => [
                    'rule_id' => 3,
                    'execution_id' => 121,
                    'threshold_value' => 50,
                    'actual_value' => 25
                ]
            ]
        ];
    }

    /**
     * Fetch provider specific job categories and locations for dropdowns.
     * Enhanced for SweetAlert multi-select interface with rich metadata.
     *
     * @param string $provider (expected values: jobs_af | jobsaf | jobs.af | acbar)
     * @return JsonResponse
     */
    public function getProviderCategories(string $provider): JsonResponse
    {
        $normalizedProvider = null;
        try {
            Log::info('CommandScheduleController: getProviderCategories called', [
                'provider_param' => $provider,
                'method' => 'getProviderCategories'
            ]);

            // CRITICAL FIX: Normalize provider name to database format
            $normalizedProvider = $this->normalizeProviderName($provider);

            Log::info('CommandScheduleController: Fetching provider categories', [
                'original_provider' => $provider,
                'normalized_provider' => $normalizedProvider
            ]);

            // Get provider categories grouped by canonical category
            Log::info('CommandScheduleController: About to execute SQL query', [
                'provider' => $normalizedProvider
            ]);

            $groupedCategories = DB::select("
                SELECT
                    jc.name as canonical_name,
                    jc.id as canonical_id,
                    pjc.id as provider_id,
                    pjc.name as provider_name
                FROM job_categories jc
                INNER JOIN provider_job_categories pjc ON jc.id = pjc.canonical_category_id
                WHERE pjc.provider_name = ?
                AND jc.is_active = 1
                ORDER BY jc.name, pjc.name
            ", [$normalizedProvider]);

            Log::info('CommandScheduleController: SQL query executed', [
                'provider' => $normalizedProvider,
                'result_count' => count($groupedCategories)
            ]);

            // Format for Select2 optgroups with selectable parent functionality
            $formattedCategories = [];
            $currentGroup = null;
            $parentChildMap = []; // Track parent-child relationships

            if (!empty($groupedCategories)) {
                foreach ($groupedCategories as $row) {
                    if ($currentGroup !== $row->canonical_name) {
                        $currentGroup = $row->canonical_name;
                        $parentId = 'parent_' . $row->canonical_id;

                        // Create optgroup structure with selectable parent
                        $optgroup = [
                            'text' => $row->canonical_name,
                            'id' => $parentId, // Make the optgroup itself selectable
                            'is_parent' => true,
                            'canonical_id' => $row->canonical_id,
                            'children' => []
                        ];

                        $formattedCategories[] = $optgroup;

                        // Initialize parent-child mapping
                        $parentChildMap[$parentId] = [];
                    }

                    // Add child category to the current optgroup
                    $lastIndex = count($formattedCategories) - 1;
                    $parentId = 'parent_' . $row->canonical_id;

                    $childOption = [
                        'id' => $row->provider_id,
                        'text' => $row->provider_name,
                        'is_child' => true,
                        'parent_id' => $parentId
                    ];

                    $formattedCategories[$lastIndex]['children'][] = $childOption;
                    $parentChildMap[$parentId][] = $row->provider_id;
                }
            }

            // Add "All Categories" option at the beginning
            array_unshift($formattedCategories, [
                'id' => 'All',
                'text' => 'All Categories'
            ]);

            // Store parent-child mapping for frontend use
            $responseData = [
                'success' => true,
                'categories' => $formattedCategories,
                'parent_child_map' => $parentChildMap,
                'locations' => []
            ];

            // Get provider job locations using the ProviderJobLocation entity
            $locations = [];
            try {
                Log::info('CommandScheduleController: Fetching provider locations', [
                    'provider' => $normalizedProvider
                ]);

                // Use the ProviderJobLocation entity's getSelect2Options method
                $locations = \Modules\JobSeeker\Entities\ProviderJobLocation::getSelect2Options($normalizedProvider);

                Log::info('CommandScheduleController: Retrieved provider locations', [
                    'provider' => $normalizedProvider,
                    'location_count' => count($locations)
                ]);
            } catch (\Exception $locationError) {
                Log::warning('CommandScheduleController: Error fetching provider locations', [
                    'provider' => $normalizedProvider,
                    'error' => $locationError->getMessage()
                ]);
                $locations = [];
            }

            Log::info('CommandScheduleController: Returning response', [
                'provider' => $normalizedProvider,
                'category_count' => count($formattedCategories),
                'location_count' => count($locations)
            ]);

            // Add locations to response data
            $responseData['locations'] = $locations;

            return response()->json($responseData);
        } catch (\Exception $e) {
            \Log::error('Error fetching provider categories and locations', [
                'provider' => $provider,
                'normalized_provider' => $normalizedProvider ?? 'not_set',
                'error'    => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load provider data: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get enhanced provider categories for SweetAlert multi-select interface
     * Returns categories with rich metadata, statistics, and visual indicators
     *
     * @param string $provider
     * @return JsonResponse
     */
    public function getProviderCategoriesForSweetAlert(string $provider): JsonResponse
    {
        try {
            $normalizedProvider = $this->normalizeProviderName($provider);

            Log::info('CommandScheduleController: Fetching enhanced categories for SweetAlert', [
                'provider' => $normalizedProvider
            ]);

            // Get categories with job counts and usage statistics
            $categoriesWithStats = DB::select("
                SELECT
                    jc.id as canonical_id,
                    jc.name as canonical_name,
                    jc.description as canonical_description,
                    jc.is_canonical,
                    pjc.id as provider_id,
                    pjc.name as provider_name,
                    pjc.provider_identifier,
                    COUNT(DISTINCT jpcp.job_id) as job_count,
                    COUNT(DISTINCT csf.id) as usage_in_rules,
                    MAX(j.created_at) as latest_job_date,
                    CASE
                        WHEN COUNT(DISTINCT jpcp.job_id) > 15 THEN 'high'
                        WHEN COUNT(DISTINCT jpcp.job_id) > 5 THEN 'medium'
                        WHEN COUNT(DISTINCT jpcp.job_id) > 0 THEN 'low'
                        ELSE 'none'
                    END as activity_level
                FROM job_categories jc
                INNER JOIN provider_job_categories pjc ON jc.id = pjc.canonical_category_id
                LEFT JOIN job_provider_category_pivot jpcp ON pjc.id = jpcp.provider_category_id
                LEFT JOIN jobs j ON jpcp.job_id = j.id
                LEFT JOIN command_schedule_filters csf ON JSON_CONTAINS(csf.categories, CAST(pjc.id AS JSON))
                WHERE pjc.provider_name = ?
                AND jc.is_active = 1
                GROUP BY jc.id, jc.name, jc.description, jc.is_canonical, pjc.id, pjc.name, pjc.provider_identifier
                ORDER BY job_count DESC, jc.name
            ", [$normalizedProvider]);

            // Transform data for SweetAlert interface
            $enhancedCategories = collect($categoriesWithStats)->map(function ($category) {
                return [
                    'id' => (string) $category->provider_id,
                    'text' => $category->provider_name,
                    'canonical_name' => $category->canonical_name,
                    'canonical_id' => $category->canonical_id,
                    'description' => $category->canonical_description ?? '',
                    'job_count' => (int) $category->job_count,
                    'usage_count' => (int) $category->usage_in_rules,
                    'activity_level' => $category->activity_level,
                    'latest_job' => $category->latest_job_date,
                    'provider_identifier' => $category->provider_identifier,
                    'is_canonical' => (bool) $category->is_canonical,
                    'badge_class' => $this->getCategoryBadgeClass($category->activity_level),
                    'icon_class' => $this->getCategoryIconClass($category->activity_level),
                    'tooltip' => $this->generateCategoryTooltip($category)
                ];
            })->toArray();

            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $enhancedCategories,
                    'provider' => $normalizedProvider,
                    'provider_display' => ucfirst($normalizedProvider),
                    'total_count' => count($enhancedCategories),
                    'statistics' => [
                        'high_activity' => collect($enhancedCategories)->where('activity_level', 'high')->count(),
                        'medium_activity' => collect($enhancedCategories)->where('activity_level', 'medium')->count(),
                        'low_activity' => collect($enhancedCategories)->where('activity_level', 'low')->count(),
                        'no_activity' => collect($enhancedCategories)->where('activity_level', 'none')->count(),
                        'total_jobs' => collect($enhancedCategories)->sum('job_count'),
                        'avg_jobs_per_category' => round(collect($enhancedCategories)->avg('job_count'), 1)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error in getProviderCategoriesForSweetAlert', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch enhanced categories: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * Get CSS badge class based on activity level
     */
    private function getCategoryBadgeClass(string $activityLevel): string
    {
        return match($activityLevel) {
            'high' => 'bg-success',
            'medium' => 'bg-warning',
            'low' => 'bg-info',
            'none' => 'bg-secondary',
            default => 'bg-secondary'
        };
    }

    /**
     * Get icon class based on activity level
     */
    private function getCategoryIconClass(string $activityLevel): string
    {
        return match($activityLevel) {
            'high' => 'fas fa-fire text-danger',
            'medium' => 'fas fa-chart-line text-warning',
            'low' => 'fas fa-seedling text-info',
            'none' => 'fas fa-circle text-muted',
            default => 'fas fa-circle text-muted'
        };
    }

    /**
     * Generate tooltip text for category
     */
    private function generateCategoryTooltip($category): string
    {
        $tooltip = "Jobs: {$category->job_count} | Used in {$category->usage_count} rules";
        if ($category->latest_job_date) {
            $latestDate = Carbon::parse($category->latest_job_date)->diffForHumans();
            $tooltip .= " | Latest job: {$latestDate}";
        }
        return $tooltip;
    }

    /**
     * Get enhanced provider locations for SweetAlert multi-select interface
     * Returns locations with rich metadata, statistics, and visual indicators
     *
     * @param string $provider
     * @return JsonResponse
     */
    public function getProviderLocationsForSweetAlert(string $provider): JsonResponse
    {
        try {
            $normalizedProvider = $this->normalizeProviderName($provider);

            Log::info('CommandScheduleController: Fetching enhanced locations for SweetAlert', [
                'provider' => $normalizedProvider
            ]);

            // Get locations with usage statistics
            $locationsWithStats = DB::select("
                SELECT
                    jl.id as canonical_id,
                    jl.name as canonical_name,
                    jl.province,
                    jl.country,
                    pjl.id as provider_id,
                    pjl.location_name as provider_name,
                    pjl.provider_identifier,
                    COUNT(DISTINCT csf.id) as usage_in_rules,
                    CASE
                        WHEN COUNT(DISTINCT csf.id) > 5 THEN 'high'
                        WHEN COUNT(DISTINCT csf.id) > 2 THEN 'medium'
                        WHEN COUNT(DISTINCT csf.id) > 0 THEN 'low'
                        ELSE 'none'
                    END as activity_level,
                    CASE
                        WHEN jl.province = 'Kabul' THEN 'capital'
                        WHEN jl.province IN ('Herat', 'Balkh', 'Kandahar', 'Nangarhar') THEN 'major'
                        ELSE 'province'
                    END as location_type
                FROM job_locations jl
                INNER JOIN provider_job_locations pjl ON jl.id = pjl.canonical_location_id
                LEFT JOIN command_schedule_filters csf ON JSON_CONTAINS(csf.locations, CAST(pjl.id AS JSON))
                WHERE pjl.provider_name = ?
                AND pjl.is_active = 1
                GROUP BY jl.id, jl.name, jl.province, jl.country, pjl.id, pjl.location_name, pjl.provider_identifier
                ORDER BY
                    CASE
                        WHEN jl.province = 'Kabul' THEN 1
                        WHEN jl.province IN ('Herat', 'Balkh', 'Kandahar', 'Nangarhar') THEN 2
                        ELSE 3
                    END,
                    usage_in_rules DESC,
                    jl.name
            ", [$normalizedProvider]);

            // Transform data for SweetAlert interface
            $enhancedLocations = collect($locationsWithStats)->map(function ($location) {
                return [
                    'id' => (string) $location->provider_id,
                    'text' => $location->provider_name,
                    'canonical_name' => $location->canonical_name,
                    'canonical_id' => $location->canonical_id,
                    'province' => $location->province ?? '',
                    'country' => $location->country ?? 'Afghanistan',
                    'usage_count' => (int) $location->usage_in_rules,
                    'activity_level' => $location->activity_level,
                    'location_type' => $location->location_type,
                    'provider_identifier' => $location->provider_identifier,
                    'badge_class' => $this->getLocationBadgeClass($location->activity_level),
                    'icon_class' => $this->getLocationIconClass($location->location_type),
                    'tooltip' => $this->generateLocationTooltip($location)
                ];
            })->toArray();

            Log::info('CommandScheduleController: Enhanced locations prepared for SweetAlert', [
                'provider' => $normalizedProvider,
                'locations_count' => count($enhancedLocations),
                'capital_locations' => collect($enhancedLocations)->where('location_type', 'capital')->count(),
                'major_locations' => collect($enhancedLocations)->where('location_type', 'major')->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'locations' => $enhancedLocations,
                    'provider' => $normalizedProvider,
                    'provider_display' => ucfirst($normalizedProvider),
                    'total_count' => count($enhancedLocations),
                    'statistics' => [
                        'high_activity' => collect($enhancedLocations)->where('activity_level', 'high')->count(),
                        'medium_activity' => collect($enhancedLocations)->where('activity_level', 'medium')->count(),
                        'low_activity' => collect($enhancedLocations)->where('activity_level', 'low')->count(),
                        'no_activity' => collect($enhancedLocations)->where('activity_level', 'none')->count(),
                        'capital_locations' => collect($enhancedLocations)->where('location_type', 'capital')->count(),
                        'major_locations' => collect($enhancedLocations)->where('location_type', 'major')->count(),
                        'province_locations' => collect($enhancedLocations)->where('location_type', 'province')->count(),
                        'total_usage' => collect($enhancedLocations)->sum('usage_count')
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('CommandScheduleController: Error in getProviderLocationsForSweetAlert', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch enhanced locations: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * Get CSS badge class based on location activity level
     */
    private function getLocationBadgeClass(string $activityLevel): string
    {
        return match($activityLevel) {
            'high' => 'bg-success',
            'medium' => 'bg-warning',
            'low' => 'bg-info',
            'none' => 'bg-secondary',
            default => 'bg-secondary'
        };
    }

    /**
     * Get icon class based on location type
     */
    private function getLocationIconClass(string $locationType): string
    {
        return match($locationType) {
            'capital' => 'fas fa-crown text-warning',
            'major' => 'fas fa-city text-primary',
            'province' => 'fas fa-map-marker-alt text-info',
            default => 'fas fa-map-marker-alt text-muted'
        };
    }

    /**
     * Generate tooltip text for location
     */
    private function generateLocationTooltip($location): string
    {
        $tooltip = "Used in {$location->usage_in_rules} rules";
        if ($location->province) {
            $tooltip .= " | Province: {$location->province}";
        }
        return $tooltip;
    }

    /**
     * Copy categories and locations from source rule to target rules
     * PROVIDER-SPECIFIC: Only allows copying within the same job provider
     * Supports selective copying and conflict resolution with rich analytics
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function copyFilters(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'source_rule_id' => 'required|integer|exists:command_schedule_rules,id',
                'target_rule_ids' => 'required|array|min:1',
                'target_rule_ids.*' => 'integer|exists:command_schedule_rules,id',
                'clone_categories' => 'boolean',
                'clone_locations' => 'boolean',
                'conflict_resolution' => 'required|in:replace,merge,skip',
                'provider' => 'required|string|in:jobsaf,acbar'
            ]);

            $sourceRuleId = $validated['source_rule_id'];
            $targetRuleIds = $validated['target_rule_ids'];
            $cloneCategories = $validated['clone_categories'] ?? true;
            $cloneLocations = $validated['clone_locations'] ?? true;
            $conflictResolution = $validated['conflict_resolution'];
            $provider = $validated['provider'];

            // PROVIDER VALIDATION: Ensure all rules belong to the same provider
            $allRuleIds = array_merge([$sourceRuleId], $targetRuleIds);
            $rules = DB::table('command_schedule_rules')
                ->whereIn('id', $allRuleIds)
                ->select('id', 'command')
                ->get();

            foreach ($rules as $rule) {
                $ruleProvider = $this->getProviderFromCommand($rule->command);
                if ($this->normalizeProviderName($ruleProvider) !== $this->normalizeProviderName($provider)) {
                    return response()->json([
                        'success' => false,
                        'message' => "Rule {$rule->id} belongs to provider '{$ruleProvider}' but expected '{$provider}'. Cross-provider cloning is not allowed."
                    ], 400);
                }
            }

            Log::info('CommandScheduleController: Starting provider-specific filter copying operation', [
                'provider' => $provider,
                'source_rule_id' => $sourceRuleId,
                'target_rule_ids' => $targetRuleIds,
                'clone_categories' => $cloneCategories,
                'clone_locations' => $cloneLocations,
                'conflict_resolution' => $conflictResolution
            ]);

            // Get source rule filter data
            $sourceFilter = DB::table('command_schedule_filters')
                ->where('schedule_rule_id', $sourceRuleId)
                ->first();

            if (!$sourceFilter) {
                return response()->json([
                    'success' => false,
                    'message' => 'Source rule has no filter data to clone'
                ], 400);
            }

            $results = [];
            $successCount = 0;
            $skipCount = 0;
            $errorCount = 0;

            DB::beginTransaction();

            foreach ($targetRuleIds as $targetRuleId) {
                try {
                    $result = $this->cloneFiltersToRule(
                        $sourceFilter,
                        $targetRuleId,
                        $cloneCategories,
                        $cloneLocations,
                        $conflictResolution
                    );

                    $results[] = $result;

                    if ($result['success']) {
                        $successCount++;
                    } else {
                        if ($result['skipped']) {
                            $skipCount++;
                        } else {
                            $errorCount++;
                        }
                    }

                } catch (\Exception $e) {
                    $errorCount++;
                    $results[] = [
                        'rule_id' => $targetRuleId,
                        'success' => false,
                        'skipped' => false,
                        'message' => 'Error: ' . $e->getMessage()
                    ];

                    Log::error('CommandScheduleController: Error cloning to rule', [
                        'target_rule_id' => $targetRuleId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            DB::commit();

            Log::info('CommandScheduleController: Filter cloning completed', [
                'source_rule_id' => $sourceRuleId,
                'total_targets' => count($targetRuleIds),
                'success_count' => $successCount,
                'skip_count' => $skipCount,
                'error_count' => $errorCount
            ]);

            return response()->json([
                'success' => true,
                'message' => "Cloning completed: {$successCount} successful, {$skipCount} skipped, {$errorCount} errors",
                'data' => [
                    'results' => $results,
                    'summary' => [
                        'total' => count($targetRuleIds),
                        'successful' => $successCount,
                        'skipped' => $skipCount,
                        'errors' => $errorCount
                    ]
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('CommandScheduleController: Error in cloneFilters', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to clone filters: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clone filters to a specific target rule
     * Handles conflict resolution and selective cloning
     *
     * @param object $sourceFilter
     * @param int $targetRuleId
     * @param bool $cloneCategories
     * @param bool $cloneLocations
     * @param string $conflictResolution
     * @return array
     */
    private function cloneFiltersToRule(
        object $sourceFilter,
        int $targetRuleId,
        bool $cloneCategories,
        bool $cloneLocations,
        string $conflictResolution
    ): array {
        // Get existing target filter
        $existingFilter = DB::table('command_schedule_filters')
            ->where('schedule_rule_id', $targetRuleId)
            ->first();

        $hasExistingData = false;
        if ($existingFilter) {
            $hasCategories = $cloneCategories && $existingFilter->categories && json_decode($existingFilter->categories, true);
            $hasLocations = $cloneLocations && $existingFilter->locations && json_decode($existingFilter->locations, true);
            $hasExistingData = $hasCategories || $hasLocations;
        }

        // Handle conflict resolution
        if ($hasExistingData && $conflictResolution === 'skip') {
            return [
                'rule_id' => $targetRuleId,
                'success' => true,
                'skipped' => true,
                'message' => 'Skipped due to existing data'
            ];
        }

        // Prepare data to clone
        $updateData = [];
        $clonedItems = [];

        if ($cloneCategories && $sourceFilter->categories) {
            $sourceCategories = json_decode($sourceFilter->categories, true) ?: [];

            // CRITICAL FIX: Normalize category IDs to integers for data consistency
            $sourceCategories = $this->normalizeCategoryIds($sourceCategories);

            if ($conflictResolution === 'merge' && $existingFilter && $existingFilter->categories) {
                $existingCategories = json_decode($existingFilter->categories, true) ?: [];
                $existingCategories = $this->normalizeCategoryIds($existingCategories);
                $mergedCategories = array_unique(array_merge($existingCategories, $sourceCategories));
                $updateData['categories'] = json_encode(array_values($mergedCategories));
                $clonedItems[] = count($mergedCategories) . ' categories (merged)';
            } else {
                $updateData['categories'] = json_encode($sourceCategories);
                $clonedItems[] = count($sourceCategories) . ' categories';
            }
        }

        if ($cloneLocations && $sourceFilter->locations) {
            $sourceLocations = json_decode($sourceFilter->locations, true) ?: [];

            // CRITICAL FIX: Normalize location IDs to integers for data consistency
            $sourceLocations = $this->normalizeCategoryIds($sourceLocations);

            if ($conflictResolution === 'merge' && $existingFilter && $existingFilter->locations) {
                $existingLocations = json_decode($existingFilter->locations, true) ?: [];
                $existingLocations = $this->normalizeCategoryIds($existingLocations);
                $mergedLocations = array_unique(array_merge($existingLocations, $sourceLocations));
                $updateData['locations'] = json_encode(array_values($mergedLocations));
                $clonedItems[] = count($mergedLocations) . ' locations (merged)';
            } else {
                $updateData['locations'] = json_encode($sourceLocations);
                $clonedItems[] = count($sourceLocations) . ' locations';
            }
        }

        // Copy other filter fields if they don't exist
        $otherFields = ['companies', 'experience_levels', 'search_term', 'work_type'];
        foreach ($otherFields as $field) {
            if ($sourceFilter->$field && (!$existingFilter || !$existingFilter->$field)) {
                $updateData[$field] = $sourceFilter->$field;
                $clonedItems[] = $field;
            }
        }

        if (empty($updateData)) {
            return [
                'rule_id' => $targetRuleId,
                'success' => true,
                'skipped' => true,
                'message' => 'No data to clone'
            ];
        }

        // Update or create filter record
        if ($existingFilter) {
            DB::table('command_schedule_filters')
                ->where('schedule_rule_id', $targetRuleId)
                ->update($updateData);
        } else {
            $updateData['schedule_rule_id'] = $targetRuleId;
            DB::table('command_schedule_filters')->insert($updateData);
        }

        return [
            'rule_id' => $targetRuleId,
            'success' => true,
            'skipped' => false,
            'message' => 'Cloned: ' . implode(', ', $clonedItems),
            'cloned_items' => $clonedItems
        ];
    }

    /**
     * Normalize category IDs to integers for data consistency
     * Ensures all category IDs are stored as integers, not strings
     *
     * @param array $categoryIds
     * @return array
     */
    private function normalizeCategoryIds(array $categoryIds): array
    {
        return array_map(function($id) {
            // Convert string IDs to integers, keep integers as-is
            return is_string($id) ? (int)$id : $id;
        }, $categoryIds);
    }

    /**
     * CRITICAL: Normalize provider names to database format
     *
     * @param string $provider
     * @return string
     */
    private function normalizeProviderName(string $provider): string
    {
        // Map all possible frontend variations to database format
        $providerMap = [
            'jobsaf' => 'jobs.af',        // Frontend sends 'jobsaf'
            'jobs_af' => 'jobs.af',       // Frontend sends 'jobs_af'
            'jobs.af' => 'jobs.af',       // Already correct
            'acbar' => 'acbar',           // Already correct
        ];

        // Check if the provider exists in the map
        if (isset($providerMap[$provider])) {
            $normalized = $providerMap[$provider];
        } else {
            Log::warning('CommandScheduleController: Unknown provider name', [
                'provider' => $provider,
                'available_mappings' => array_keys($providerMap)
            ]);
            $normalized = $provider;
        }

        Log::info('CommandScheduleController: Provider name normalization', [
            'original' => $provider,
            'normalized' => $normalized,
            'provider_map_keys' => array_keys($providerMap)
        ]);

        return $normalized;
    }

    /**
     * Sanitize array field ensuring it's a valid array
     *
     * @param mixed $value
     * @return array
     */
    private function sanitizeArrayField($value): array
    {
        if (!is_array($value)) {
            return [];
        }
        
        // Remove empty strings and null values, keep only strings and numbers
        return array_values(array_filter($value, function ($item) {
            return is_string($item) || is_numeric($item);
        }));
    }

    /**
     * Sanitize string field with length validation
     *
     * @param mixed $value
     * @param int $maxLength
     * @return string
     */
    private function sanitizeStringField($value, int $maxLength = 255): string
    {
        if (!is_string($value) && !is_numeric($value)) {
            return '';
        }
        
        $sanitized = trim((string) $value);
        return mb_strlen($sanitized) > $maxLength ? mb_substr($sanitized, 0, $maxLength) : $sanitized;
    }

    /**
     * Sanitize integer field with range validation
     *
     * @param mixed $value
     * @param int|null $min
     * @param int|null $max
     * @return int|null
     */
    private function sanitizeIntegerField($value, ?int $min = null, ?int $max = null): ?int
    {
        if ($value === null || $value === '') {
            return null;
        }
        
        if (!is_numeric($value)) {
            return null;
        }
        
        $intValue = (int) $value;
        
        if ($min !== null && $intValue < $min) {
            return $min;
        }
        
        if ($max !== null && $intValue > $max) {
            return $max;
        }
        
        return $intValue;
    }

    /**
     * Extract time from schedule expression for display in time input
     *
     * @param string|null $scheduleExpression
     * @param string|null $scheduleType
     * @return string|null Time in HH:MM format or null
     */
    private function extractTimeFromSchedule(?string $scheduleExpression, ?string $scheduleType): ?string
    {
        if (!$scheduleExpression || trim($scheduleExpression) === '') {
            return null;
        }

        try {
            // Handle different schedule types
            switch ($scheduleType) {
                case 'daily_at':
                case 'weekly_at':
                    // For daily_at and weekly_at, the expression might be in format "HH:MM"
                    if (preg_match('/^(\d{1,2}):(\d{2})$/', $scheduleExpression, $matches)) {
                        $hourInt = (int) $matches[1];
                        $minuteInt = (int) $matches[2];

                        // Validate time values
                        if ($hourInt >= 0 && $hourInt <= 23 && $minuteInt >= 0 && $minuteInt <= 59) {
                            $hour = str_pad((string) $hourInt, 2, '0', STR_PAD_LEFT);
                            $minute = str_pad((string) $minuteInt, 2, '0', STR_PAD_LEFT);
                            return "$hour:$minute";
                        }
                    }
                    // Fall through to cron parsing if HH:MM pattern doesn't match

                case 'cron':
                default:
                    // Parse cron expression: "minute hour day month weekday"
                    $parts = explode(' ', trim($scheduleExpression));
                    if (count($parts) >= 2) {
                        $minute = trim($parts[0]);
                        $hour = trim($parts[1]);

                        // Convert to integers and validate
                        if (is_numeric($minute) && is_numeric($hour)) {
                            $hourInt = (int) $hour;
                            $minuteInt = (int) $minute;

                            if ($hourInt >= 0 && $hourInt <= 23 && $minuteInt >= 0 && $minuteInt <= 59) {
                                $hourStr = str_pad((string) $hourInt, 2, '0', STR_PAD_LEFT);
                                $minuteStr = str_pad((string) $minuteInt, 2, '0', STR_PAD_LEFT);
                                return "$hourStr:$minuteStr";
                            }
                        }
                    }
                    break;
            }
        } catch (\Exception $e) {
            Log::warning('Failed to extract time from schedule expression', [
                'schedule_expression' => $scheduleExpression,
                'schedule_type' => $scheduleType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return null;
    }

    /**
     * Calculate sort key for schedule expressions based on time of day
     * Returns minutes since midnight for chronological sorting
     *
     * @param string|null $scheduleExpression
     * @param string|null $scheduleType
     * @return int Minutes since midnight (0-1439) or PHP_INT_MAX for invalid expressions
     */
    private function calculateScheduleSortKey(?string $scheduleExpression, ?string $scheduleType): int
    {
        $timeString = $this->extractTimeFromSchedule($scheduleExpression, $scheduleType);

        if (!$timeString) {
            return PHP_INT_MAX; // Put invalid schedules at the end
        }

        // Parse HH:MM format
        if (preg_match('/^(\d{2}):(\d{2})$/', $timeString, $matches)) {
            $hour = (int) $matches[1];
            $minute = (int) $matches[2];

            // Convert to minutes since midnight
            return ($hour * 60) + $minute;
        }

        return PHP_INT_MAX; // Fallback for invalid time format
    }

    /**
     * Update weekly schedule with day of week selection
     *
     * @param CommandScheduleRule $rule
     * @param string $dayOfWeek
     * @return void
     */
    private function updateWeeklySchedule(CommandScheduleRule $rule, string $dayOfWeek): void
    {
        // Extract current time from existing schedule expression or use default
        $currentTime = '09:00'; // Default time

        if ($rule->schedule_expression) {
            $extractedTime = $this->extractTimeFromSchedule($rule->schedule_expression, $rule->schedule_type);
            if ($extractedTime) {
                $currentTime = $extractedTime;
            }
        }

        // Convert time to cron format with the selected day
        if (preg_match('/^(\d{1,2}):(\d{2})$/', $currentTime, $matches)) {
            $hour = (int) $matches[1];
            $minute = (int) $matches[2];
            $rule->schedule_expression = sprintf('%d %d * * %s', $minute, $hour, $dayOfWeek);
            $rule->schedule_type = 'weekly_at';
        }
    }



    /**
     * Generate dynamic rule name based on schedule expression
     *
     * @param CommandScheduleRule $rule
     * @return string
     */
    private function generateDynamicRuleName(CommandScheduleRule $rule): string
    {
        try {
            // Get provider name
            $provider = 'Unknown';
            if (str_contains($rule->command, 'jobs-af')) {
                $provider = 'Jobs.af';
            } elseif (str_contains($rule->command, 'acbar')) {
                $provider = 'ACBAR';
            }

            // Use the existing human-readable schedule info from the entity
            $scheduleInfo = $rule->getHumanReadableScheduleInfoAttribute();

            // Clean up the HTML tags from the schedule info
            $cleanScheduleInfo = strip_tags($scheduleInfo);

            // If we have meaningful schedule info, use it
            if ($cleanScheduleInfo && $cleanScheduleInfo !== 'Not configured') {
                return "{$provider} - {$cleanScheduleInfo}";
            }

            // Fallback to basic schedule type
            switch ($rule->schedule_type) {
                case 'daily_at':
                    return "{$provider} - Daily Schedule";
                case 'weekly_at':
                    return "{$provider} - Weekly Schedule";
                case 'cron':
                    return "{$provider} - Custom Schedule";
                default:
                    return "{$provider} - Schedule Rule";
            }

        } catch (\Exception $e) {
            Log::warning('Error generating dynamic rule name', [
                'rule_id' => $rule->id,
                'schedule_expression' => $rule->schedule_expression,
                'error' => $e->getMessage()
            ]);

            return 'Schedule Rule';
        }
    }


}