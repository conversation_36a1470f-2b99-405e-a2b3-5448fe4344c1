/*
 * Description: Create system_health_checks table for continuous health monitoring
 * Module: <PERSON>Seeker
 * Author: Automated Health Monitoring System
 * Date: 2025-07-28
 * 
 * IMPORTANT: This file must be reviewed and executed by authorized database administrators
 */

-- Create system_health_checks table
CREATE TABLE IF NOT EXISTS `system_health_checks` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `check_name` varchar(100) NOT NULL COMMENT 'Name of the health check',
    `check_type` enum('critical','important','informational') NOT NULL DEFAULT 'important' COMMENT 'Importance level of the check',
    `status` enum('healthy','warning','critical','unknown') NOT NULL DEFAULT 'unknown' COMMENT 'Current health status',
    `message` text DEFAULT NULL COMMENT 'Detailed status message',
    `metrics` json DEFAULT NULL COMMENT 'Health metrics as JSON',
    `execution_time_ms` int(11) DEFAULT NULL COMMENT 'Time taken to execute the check in milliseconds',
    `last_healthy_at` timestamp NULL DEFAULT NULL COMMENT 'When this check was last healthy',
    `consecutive_failures` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of consecutive failures',
    `recovery_attempted` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether automated recovery was attempted',
    `recovery_successful` tinyint(1) DEFAULT NULL COMMENT 'Whether recovery was successful (NULL if not attempted)',
    `next_check_at` timestamp NULL DEFAULT NULL COMMENT 'When to run this check next',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_check_name` (`check_name`),
    KEY `idx_status` (`status`),
    KEY `idx_check_type` (`check_type`),
    KEY `idx_next_check` (`next_check_at`),
    KEY `idx_consecutive_failures` (`consecutive_failures`),
    KEY `idx_last_healthy` (`last_healthy_at`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='System health check results and metrics';

-- Create system_health_history table for trending analysis
CREATE TABLE IF NOT EXISTS `system_health_history` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `check_name` varchar(100) NOT NULL COMMENT 'Name of the health check',
    `status` enum('healthy','warning','critical','unknown') NOT NULL COMMENT 'Status at time of check',
    `message` text DEFAULT NULL COMMENT 'Status message',
    `metrics` json DEFAULT NULL COMMENT 'Metrics captured',
    `execution_time_ms` int(11) DEFAULT NULL COMMENT 'Execution time in milliseconds',
    `checked_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When the check was performed',
    PRIMARY KEY (`id`),
    KEY `idx_check_name` (`check_name`),
    KEY `idx_status` (`status`),
    KEY `idx_checked_at` (`checked_at`),
    KEY `idx_check_name_time` (`check_name`, `checked_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Historical health check data for trending';

-- Insert initial health checks
INSERT IGNORE INTO `system_health_checks` (`check_name`, `check_type`, `status`, `message`, `next_check_at`, `created_at`, `updated_at`) VALUES
('email_notifications', 'critical', 'unknown', 'Email notification system health check', NOW(), NOW(), NOW()),
('job_fetching_acbar', 'critical', 'unknown', 'ACBAR job fetching health check', NOW(), NOW(), NOW()),
('job_fetching_jobsaf', 'critical', 'unknown', 'Jobs.af job fetching health check', NOW(), NOW(), NOW()),
('database_connectivity', 'critical', 'unknown', 'Database connectivity health check', NOW(), NOW(), NOW()),
('queue_processing', 'important', 'unknown', 'Queue processing health check', NOW(), NOW(), NOW()),
('api_response_times', 'important', 'unknown', 'API response time monitoring', NOW(), NOW(), NOW()),
('disk_space', 'important', 'unknown', 'Disk space monitoring', NOW(), NOW(), NOW()),
('memory_usage', 'informational', 'unknown', 'Memory usage monitoring', NOW(), NOW(), NOW()),
('configuration_validation', 'critical', 'unknown', 'Critical configuration validation', NOW(), NOW(), NOW()),
('recent_job_activity', 'important', 'unknown', 'Recent job activity monitoring', NOW(), NOW(), NOW());

-- Create indexes for performance optimization
CREATE INDEX `idx_health_history_recent` ON `system_health_history` (`checked_at` DESC, `check_name`);
CREATE INDEX `idx_health_history_status_time` ON `system_health_history` (`status`, `checked_at`);

-- Create cleanup procedure for old health history (keep last 30 days)
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `CleanupHealthHistory`()
BEGIN
    DELETE FROM `system_health_history` 
    WHERE `checked_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    SELECT ROW_COUNT() as deleted_records;
END //
DELIMITER ;

-- Rollback procedure (commented out for safety)
-- DROP TABLE IF EXISTS `system_health_history`;
-- DROP TABLE IF EXISTS `system_health_checks`;
-- DROP PROCEDURE IF EXISTS `CleanupHealthHistory`;
