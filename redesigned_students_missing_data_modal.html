<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students With Missing Data - Redesigned</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #e9ecef;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .modal-dialog-xl {
            max-width: 95%;
            margin: 1.75rem auto;
        }
        .modal-content {
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
            border: none;
        }
        .modal-header {
            border-bottom: 1px solid #dee2e6;
            padding: 1.5rem 2rem;
            background-color: #fff;
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
        }
        .modal-title {
            font-weight: 600;
            font-size: 1.5rem;
            color: #212529;
        }
        .modal-body {
            padding: 2rem;
        }
        .card {
            border-radius: 0.75rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid #dee2e6;
        }
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        .form-select, .form-control {
            border-radius: 0.5rem;
            background-color: #fff;
        }
        .btn {
            border-radius: 0.5rem;
            padding: 0.6rem 1.2rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }
        .btn-primary {
            background-color: #007aff;
            border-color: #007aff;
        }
        .btn-primary:hover {
            background-color: #005ecb;
            border-color: #005ecb;
        }
        .table-hover tbody tr:hover {
            background-color: #f1f1f1;
            cursor: pointer;
        }
        .table th {
            font-weight: 600;
            color: #495057;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }
        .table td, .table th {
            vertical-align: middle;
        }
        .bulk-assignment-card {
            background-color: #f8f9fa;
        }
        .selection-counter {
            font-weight: 500;
            color: #6c757d;
            background-color: #e9ecef;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
        }
        .card-title i {
            color: #007aff;
        }
    </style>
</head>
<body>

    <!-- This div simulates the modal being open on the page -->
    <div class="modal fade show" style="display: block;" tabindex="-1">
        <div class="modal-dialog modal-dialog-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Manage Student Data</h5>
                    <button type="button" class="btn-close" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Filters Card -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6 class="card-title mb-3"><i class="fas fa-filter me-2"></i>Filter Students</h6>
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="userStatus" class="form-label">Status</label>
                                    <select class="form-select" id="userStatus"><option selected>All Students</option></select>
                                </div>
                                <div class="col-md-3">
                                    <label for="program" class="form-label">Program</label>
                                    <select class="form-select" id="program"><option selected>All Programs</option></select>
                                </div>
                                <div class="col-md-3">
                                    <label for="center" class="form-label">Center</label>
                                    <select class="form-select" id="center"><option selected>All Centers</option></select>
                                </div>
                                <div class="col-md-3">
                                    <label for="class" class="form-label">Class</label>
                                    <select class="form-select" id="class"><option selected>All Classes</option></select>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end mt-3">
                                <button class="btn btn-light me-2"><i class="fas fa-sync-alt me-2"></i>Refresh</button>
                                <button class="btn btn-secondary me-2"><i class="fas fa-times me-2"></i>Clear</button>
                                <button class="btn btn-primary"><i class="fas fa-check me-2"></i>Apply Filters</button>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Assignment Card -->
                    <div class="card mb-4 bulk-assignment-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="card-title mb-0"><i class="fas fa-users me-2"></i>Bulk Assign to Class</h6>
                                <span class="selection-counter">0 students selected</span>
                            </div>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="bulkProgram" class="form-label">Program <span class="text-danger">*</span></label>
                                    <select class="form-select" id="bulkProgram"><option selected>Choose Program</option></select>
                                </div>
                                <div class="col-md-4">
                                    <label for="bulkCenter" class="form-label">Center <span class="text-danger">*</span></label>
                                    <select class="form-select" id="bulkCenter"><option selected>Choose Center</option></select>
                                </div>
                                <div class="col-md-4">
                                    <label for="bulkClass" class="form-label">Class <span class="text-danger">*</span></label>
                                    <select class="form-select" id="bulkClass"><option selected>Choose Class</option></select>
                                </div>
                            </div>
                             <div class="d-flex justify-content-end mt-3">
                                <button class="btn btn-success"><i class="fas fa-check-circle me-2"></i>Apply Assignment</button>
                            </div>
                        </div>
                    </div>

                    <!-- Student Data Table -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th><input class="form-check-input" type="checkbox"></th>
                                    <th>Full Name</th>
                                    <th>Email</th>
                                    <th>Gender</th>
                                    <th>Date of Birth</th>
                                    <th>Program</th>
                                    <th>Center</th>
                                    <th>Class</th>
                                    <th class="text-center">Save</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input class="form-check-input" type="checkbox"></td>
                                    <td>Nausheen Fatima</td>
                                    <td><EMAIL></td>
                                    <td><select class="form-select form-select-sm"><option>Choose</option><option selected>Female</option></select></td>
                                    <td><input type="date" class="form-control form-control-sm" value="1998-05-21"></td>
                                    <td><select class="form-select form-select-sm"><option selected>Select Program</option></select></td>
                                    <td><select class="form-select form-select-sm"><option selected>Select Center</option></select></td>
                                    <td><select class="form-select form-select-sm"><option selected>Select Class</option></select></td>
                                    <td class="text-center"><button class="btn btn-sm btn-outline-primary"><i class="fas fa-save"></i></button></td>
                                </tr>
                                <tr>
                                    <td><input class="form-check-input" type="checkbox"></td>
                                    <td>Nahid Soubhy</td>
                                    <td><EMAIL></td>
                                    <td><select class="form-select form-select-sm"><option selected>Choose</option></select></td>
                                    <td><input type="date" class="form-control form-control-sm"></td>
                                    <td><select class="form-select form-select-sm"><option selected>Select Program</option></select></td>
                                    <td><select class="form-select form-select-sm"><option selected>Select Center</option></select></td>
                                    <td><select class="form-select form-select-sm"><option selected>Select Class</option></select></td>
                                    <td class="text-center"><button class="btn btn-sm btn-outline-primary"><i class="fas fa-save"></i></button></td>
                                </tr>
                                <!-- ... more student rows ... -->
                            </tbody>
                        </table>
                    </div>
                     <nav class="d-flex justify-content-between align-items-center mt-3">
                        <div>Showing 1 to 10 of 514 entries</div>
                        <ul class="pagination mb-0">
                            <li class="page-item disabled"><a class="page-link" href="#">Previous</a></li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item"><a class="page-link" href="#">Next</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>