-- Create Health Dashboard Data Calculation Procedure
-- This procedure replaces all complex PHP calculations with optimized SQL queries
-- for accurate and consistent health dashboard data

DELIMITER $$

DROP PROCEDURE IF EXISTS GetHealthDashboardData$$

CREATE PROCEDURE GetHealthDashboardData(
    IN p_days INT,
    IN p_command_filter VARCHAR(255)
)
BEGIN
    DECLARE v_start_date DATETIME;
    DECLARE v_end_date DATETIME;
    
    -- Handle default values
    IF p_days IS NULL OR p_days <= 0 THEN
        SET p_days = 7;
    END IF;
    
    -- Set date range
    SET v_end_date = NOW();
    SET v_start_date = DATE_SUB(v_end_date, INTERVAL p_days DAY);
    
    -- RESULT SET 1: Main overview statistics
    SELECT 
        'overview' as section,
        COALESCE(SUM(CASE WHEN jobs_fetched IS NOT NULL THEN jobs_fetched ELSE 0 END), 0) as total_jobs_fetched,
        COUNT(CASE WHEN jobs_fetched IS NOT NULL THEN 1 END) as total_executions,
        COUNT(CASE WHEN status IN ('failed', 'timeout') OR (error_type NOT IN ('none', '') AND error_type IS NOT NULL) THEN 1 END) as total_errors,
        ROUND(AVG(CASE WHEN execution_time_seconds IS NOT NULL AND execution_time_seconds > 0 THEN execution_time_seconds END), 2) as avg_response_time,
        MAX(started_at) as last_execution,
        -- Calculate success rate
        ROUND(
            CASE 
                WHEN COUNT(CASE WHEN jobs_fetched IS NOT NULL THEN 1 END) > 0 THEN
                    (COUNT(CASE WHEN jobs_fetched IS NOT NULL THEN 1 END) - COUNT(CASE WHEN status IN ('failed', 'timeout') OR (error_type NOT IN ('none', '') AND error_type IS NOT NULL) THEN 1 END)) * 100.0 / COUNT(CASE WHEN jobs_fetched IS NOT NULL THEN 1 END)
                ELSE 100.0 
            END, 1
        ) as success_rate,
        -- Calculate error rate
        ROUND(
            CASE 
                WHEN COUNT(CASE WHEN jobs_fetched IS NOT NULL THEN 1 END) > 0 THEN
                    COUNT(CASE WHEN status IN ('failed', 'timeout') OR (error_type NOT IN ('none', '') AND error_type IS NOT NULL) THEN 1 END) * 100.0 / COUNT(CASE WHEN jobs_fetched IS NOT NULL THEN 1 END)
                ELSE 0.0 
            END, 1
        ) as error_rate,
        -- Jobs per execution
        ROUND(
            CASE 
                WHEN COUNT(CASE WHEN jobs_fetched IS NOT NULL THEN 1 END) > 0 THEN
                    SUM(CASE WHEN jobs_fetched IS NOT NULL THEN jobs_fetched ELSE 0 END) / COUNT(CASE WHEN jobs_fetched IS NOT NULL THEN 1 END)
                ELSE 0.0 
            END, 1
        ) as jobs_per_execution,
        -- Provider breakdown
        COALESCE(SUM(CASE WHEN command LIKE BINARY '%sync-jobs-af%' AND jobs_fetched IS NOT NULL THEN jobs_fetched ELSE 0 END), 0) as jobsaf_jobs,
        COALESCE(SUM(CASE WHEN command LIKE BINARY '%sync-acbar%' AND jobs_fetched IS NOT NULL THEN jobs_fetched ELSE 0 END), 0) as acbar_jobs
    FROM command_schedule_executions 
    WHERE started_at >= v_start_date 
      AND started_at <= v_end_date
      AND (p_command_filter IS NULL OR command LIKE BINARY CONCAT('%', p_command_filter, '%'))
      AND jobs_fetched IS NOT NULL;

    -- RESULT SET 2: Provider-specific trends data for the specified period
    SELECT 
        'performance_trends' as section,
        DATE(started_at) as trend_date,
                        CASE 
                    WHEN command LIKE BINARY '%sync-jobs-af%' THEN 'jobsaf'
                    WHEN command LIKE BINARY '%sync-acbar%' THEN 'acbar'
                    ELSE 'other'
                END as provider,
        COALESCE(SUM(jobs_fetched), 0) as daily_jobs,
        COUNT(*) as daily_executions,
        ROUND(AVG(execution_time_seconds), 2) as avg_execution_time
    FROM command_schedule_executions 
    WHERE started_at >= v_start_date 
      AND started_at <= v_end_date
      AND (p_command_filter IS NULL OR command LIKE BINARY CONCAT('%', p_command_filter, '%'))
      AND jobs_fetched IS NOT NULL
    GROUP BY DATE(started_at), provider
    ORDER BY trend_date ASC, provider;

    -- RESULT SET 3: Command-specific statistics
    SELECT 
        'command_stats' as section,
        command,
        COUNT(*) as executions,
        COALESCE(SUM(jobs_fetched), 0) as total_jobs,
        COUNT(CASE WHEN status IN ('failed', 'timeout') OR (error_type NOT IN ('none', '') AND error_type IS NOT NULL) THEN 1 END) as errors,
        COUNT(CASE WHEN status = 'completed' AND (error_type = 'none' OR error_type IS NULL OR error_type = '') THEN 1 END) as successful_executions,
        ROUND(AVG(CASE WHEN execution_time_seconds IS NOT NULL AND execution_time_seconds > 0 THEN execution_time_seconds END), 2) as avg_response_time,
        -- Success rate calculation
        ROUND(
            CASE 
                WHEN COUNT(*) > 0 THEN
                    COUNT(CASE WHEN status = 'completed' AND (error_type = 'none' OR error_type IS NULL OR error_type = '') THEN 1 END) * 100.0 / COUNT(*)
                ELSE 0.0 
            END, 2
        ) as success_rate
    FROM command_schedule_executions 
    WHERE started_at >= v_start_date 
      AND started_at <= v_end_date
      AND (p_command_filter IS NULL OR command LIKE BINARY CONCAT('%', p_command_filter, '%'))
      AND jobs_fetched IS NOT NULL
    GROUP BY command
    ORDER BY total_jobs DESC;

    -- RESULT SET 4: Error analysis breakdown
    SELECT 
        'error_analysis' as section,
        CASE 
            WHEN command LIKE BINARY '%sync-jobs%' AND (status IN ('failed', 'timeout') OR (error_type NOT IN ('none', '') AND error_type IS NOT NULL)) THEN 'Revenue Impact'
            WHEN error_type IN ('timeout', 'network') THEN 'User Experience'
            WHEN error_type IN ('api', 'network', 'unknown') THEN 'System Reliability'
            WHEN error_type IN ('data', 'validation') THEN 'Data Quality'
            ELSE 'System Healthy'
        END as impact_category,
        COUNT(*) as impact_count
    FROM command_schedule_executions 
    WHERE started_at >= v_start_date 
      AND started_at <= v_end_date
      AND (p_command_filter IS NULL OR command LIKE BINARY CONCAT('%', p_command_filter, '%'))
      AND jobs_fetched IS NOT NULL
    GROUP BY impact_category
    ORDER BY impact_count DESC;

    -- RESULT SET 5: Recent executions for timeline
    SELECT 
        'recent_executions' as section,
        id,
        command,
        status,
        started_at,
        completed_at,
        COALESCE(jobs_fetched, 0) as jobs_fetched,
        error_type,
        execution_time_seconds,
        memory_usage_mb,
        CASE 
            WHEN command LIKE BINARY '%sync-jobs-af%' THEN 'jobsaf'
            WHEN command LIKE BINARY '%sync-acbar%' THEN 'acbar'
            ELSE 'other'
        END as provider
    FROM command_schedule_executions 
    WHERE started_at >= v_start_date 
      AND started_at <= v_end_date
      AND (p_command_filter IS NULL OR command LIKE BINARY CONCAT('%', p_command_filter, '%'))
      AND jobs_fetched IS NOT NULL
    ORDER BY started_at DESC 
    LIMIT 20;

    -- RESULT SET 6: Date range for trends (to ensure we have all dates even with zero data)
    SELECT 
        'date_range' as section,
        DATE(DATE_SUB(v_end_date, INTERVAL (seq.n - 1) DAY)) as trend_date
    FROM (
        SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7
        UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14
        UNION SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20
        UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION SELECT 26
        UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION SELECT 31
    ) seq
    WHERE seq.n <= p_days
    ORDER BY trend_date ASC;

END$$

DELIMITER ; 