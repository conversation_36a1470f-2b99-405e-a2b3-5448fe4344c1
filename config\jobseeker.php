<?php

return [
    'admin_notification_email' => '<EMAIL>',
    'default_location' => 'Kabul',
    'name' => 'JobSeeker',

    /*
    |--------------------------------------------------------------------------
    | Jobs.af Category Priorities Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the priority of job categories for sorting
    | purposes when fetching from Jobs.af. Lower numbers indicate higher priority. 
    | This affects how jobs are ordered in notifications and listings.
    |
    */
    'jobs_af_category_priorities' => [
        'IT - Software' => 1,
        'Software engineering' => 1,
        'software development' => 1, 
        'software development ' => 1,
        'Information Technology' => 1,
        'Computer Science' => 1,
        'Leadership' => 2,
        'Management' => 3,
        'Administrative' => 4,
        'Human Resources' => 4,
        'Sales/Marketing' => 5,
        'Business Administration' => 5,
        'Consulting' => 6,
        'Graphic Designer' => 6,
        'Customer Service' => 7,
        'Security/Safety' => 7,
        'Research/Survey' => 8,
        'Translation' => 8,
        'Nursing' => 9,
        'Travel/Tourism' => 10,
        'Transportation' => 10,
        'Industrial' => 10,
        'Internships' => 11,
        'Social Science' => 12,
        'Human Rights' => 12
    ],

    /*
    |--------------------------------------------------------------------------
    | Jobs.af API Default Filters Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the default filters used when fetching jobs
    | from the jobs.af API. These filters specify which job categories,
    | locations, and other criteria to include in the API requests.
    |
    */
    'jobs_af_default_filters' => [
        'page' => 1,
        "searchFilters" => [
            "searchTerm" => "",
            "categories" => [
                "IT - Software",
                "Management",
                "Leadership",
                "Software engineering",
                "software development ",
                "software development",
                "Information Technology",
                "Administrative",
                "Sales/Marketing",
                "Consulting",
                "Security/Safety",
                "Travel/Tourism",
                "Transportation",
                "Human Resources",
                "Internships",
                "Research/Survey",
                "Translation",
                "Industrial",
                "Nursing",
                "Customer Service",
                "Business Administration",
                "Social Science",
                "Human Rights",
                "Computer Science",
                "Graphic Designer"
            ],
            "workType" => "",
            "companies" => [],
            "experienceLevels" => [],
            "locations" => ["Kabul"] // Only include jobs in Kabul
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | ACBAR Jobs Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the settings for fetching jobs from ACBAR.org.
    | These include API settings, location preferences, and category mappings
    | from ACBAR categories to our canonical job categories.
    |
    */
    'acbar_default_filters' => [
        'base_url' => 'https://www.acbar.org/jobs',
        'default_location_id' => 14, // Kabul
        'timeout' => 60, // HTTP request timeout in seconds
        'max_retries' => 5, // Maximum number of retries for rate-limited requests
        'base_delay' => 1000000, // Base delay in microseconds (1 second)
        
        // ACBAR category names mapping (ID => Name)
        'category_names' => [
            5 => 'Admin-Clerical',
            6 => 'Agriculture', 
            7 => 'Automotive',
            8 => 'Banking',
            9 => 'Biotech',
            10 => 'Business Development',
            11 => 'Construction',
            12 => 'Consultant',
            13 => 'Customer Service',
            14 => 'Design',
            15 => 'Distribution-Shipping',
            16 => 'Education',
            17 => 'Engineering',
            18 => 'Entry Level',
            19 => 'Executive',
            20 => 'Facilities',
            21 => 'Finance',
            22 => 'Franchise',
            23 => 'General',
            24 => 'General Business',
            25 => 'General Labor',
            26 => 'Government',
            27 => 'Grocery',
            28 => 'Health Care',
            29 => 'Hospitality-Hotel',
            30 => 'Human Resources',
            31 => 'Information Technology',
            32 => 'Installation-Maint-Repair',
            33 => 'Insurance',
            34 => 'Inventory',
            35 => 'Legal',
            36 => 'Management',
            37 => 'Marketing',
            38 => 'Media-Journalism',
            39 => 'Monitoring and Evaluation',
            40 => 'Nonprofit-Social Services',
            41 => 'Nurse',
            42 => 'Other',
            43 => 'Pharmaceutical',
            44 => 'Professional Services',
            45 => 'Program',
            46 => 'Purchasing-Procurement',
            47 => 'QA-Quality Control',
            48 => 'Real Estate',
            49 => 'Research',
            50 => 'Restaurant-Food Service',
            51 => 'Retail',
            52 => 'Sales',
            53 => 'Science',
            54 => 'Security',
            55 => 'Skilled Labor',
            56 => 'Strategy-Planning',
            57 => 'Supply Chain',
            58 => 'Telecommunications',
            59 => 'Training',
            60 => 'Translator',
            61 => 'Transportation',
            62 => 'Veterinary Services',
            63 => 'Warehouse',
            64 => 'Telecom',
            65 => 'Natural Resources Management',
            67 => 'Support',
            68 => 'Communication',
            69 => 'Accounting',
            70 => 'IT',
            71 => 'Capacity Building',
            72 => 'Coordination',
        ],
        
        // Maps ACBAR category IDs to our canonical category IDs
        'category_mapping' => [
            // Administration related
            5 => 12,   // Admin-Clerical -> Administration
            20 => 12,  // Facilities -> Administration
            23 => 12,  // General -> Administration
            25 => 12,  // General Labor -> Administration
            42 => 12,  // Other -> Administration
            67 => 12,  // Support -> Administration
            18 => 12,  // Entry Level -> Administration
            
            // Agriculture/Programme related
            6 => 7,    // Agriculture -> Programme
            26 => 7,   // Government -> Programme
            39 => 7,   // Monitoring and Evaluation -> Programme
            40 => 7,   // Nonprofit-Social Services -> Programme
            45 => 7,   // Program -> Programme
            65 => 7,   // Natural Resources Management -> Programme
            72 => 7,   // Coordination -> Programme
            
            // Transportation/Automotive related
            7 => 9,    // Automotive -> Transportation
            61 => 9,   // Transportation -> Transportation
            
            // Finance related
            8 => 13,   // Banking -> Finance
            21 => 13,  // Finance -> Finance
            33 => 13,  // Insurance -> Finance
            69 => 13,  // Accounting -> Finance
            
            // Science/Research related
            9 => 11,   // Biotech -> Research/Survey
            49 => 11,  // Research -> Research/Survey
            53 => 11,  // Science -> Research/Survey
            
            // Management related
            10 => 2,   // Business Development -> Management
            24 => 2,   // General Business -> Management
            36 => 2,   // Management -> Management
            56 => 2,   // Strategy-Planning -> Management
            12 => 2,   // Consultant -> Management
            44 => 2,   // Professional Services -> Management
            48 => 2,   // Real Estate -> Management
            22 => 2,   // Franchise -> Management
            
            // Engineering/Construction related
            11 => 14,  // Construction -> Engineering
            17 => 14,  // Engineering -> Engineering
            32 => 14,  // Installation-Maint-Repair -> Engineering
            47 => 14,  // QA-Quality Control -> Engineering
            55 => 14,  // Skilled Labor -> Engineering
            
            // Human Resources related
            13 => 8,   // Customer Service -> Human Resources
            30 => 8,   // Human Resources -> Human Resources
            
            // Information Technology related
            14 => 1,   // Design -> Information Technology
            31 => 1,   // Information Technology -> Information Technology
            58 => 1,   // Telecommunications -> Information Technology
            64 => 1,   // Telecom -> Information Technology
            70 => 1,   // IT -> Information Technology
            
            // Logistics related
            15 => 16,  // Distribution-Shipping -> Logistics
            34 => 16,  // Inventory -> Logistics
            46 => 16,  // Purchasing-Procurement -> Logistics
            57 => 16,  // Supply Chain -> Logistics
            63 => 16,  // Warehouse -> Logistics
            
            // Education related
            16 => 5,   // Education -> Education
            59 => 5,   // Training -> Education
            71 => 5,   // Capacity Building -> Education
            
            // Leadership related
            19 => 3,   // Executive -> Leadership
            
            // Health related
            28 => 15,  // Health Care -> Health
            41 => 15,  // Nurse -> Health
            43 => 15,  // Pharmaceutical -> Health
            62 => 15,  // Veterinary Services -> Health
            
            // Legal related
            35 => 18,  // Legal -> Legal
            
            // Marketing/Communication related
            37 => 4,   // Marketing -> Marketing
            38 => 4,   // Media-Journalism -> Marketing
            52 => 4,   // Sales -> Marketing
            68 => 4,   // Communication -> Marketing
            
            // Service industries (mapped to Administration)
            27 => 12,  // Grocery -> Administration
            29 => 12,  // Hospitality-Hotel -> Administration
            50 => 12,  // Restaurant-Food Service -> Administration
            51 => 12,  // Retail -> Administration
            
            // Security related
            54 => 17,  // Security -> Security
            
            // Translation related
            60 => 6,   // Translator -> Translation
        ]
    ],

    "units" => [
        "dashboards" => [
            "icon" => "building-o",
            "actions" => [
                "have teacher dashboard",
                "have supervisor dashboard",
                "have finance dashboard",
                "have human_resource dashboard",
                "view approval-awaiting new applications"
            ]
        ],
        "backups" => [
            "icon" => "building-o",
            "actions" => [
                "have students backup",
                "download students backup"
            ]
        ],
        "status" => [
            "icon" => "building-o",
            "actions" => [
                "show system Logs"
            ]
        ],
        "commands" => [
            "icon" => "building-o",
            "actions" => [
                "show commands interface for admins"
            ]
        ],
//        "departments" => [
//            "icon" => "building-o",
//            "actions" => [
//                "access departments",
//                "add department",
//                "update department",
//                "delete department",
//                "view department",
//            ],
//        ],
        "roles" => [
            "icon" => "key",
            "actions" => [
                "access roles",
                "add role",
                "show role",
                "show role create form",
                "show role edit form",
                "update role",
                "remove role"
            ],
        ],
        "strategies" => [
            "icon" => "bullseye",
            "actions" => [
                "access strategies",
                "add strategy",
                "update strategy",
                "delete strategy",
                "view strategy",
            ]
        ],
        "form_builder" => [
            "icon" => "paper",
            "actions" => [
                "access form_builder",
                "add builder_form",
                "update builder_form",
                "delete builder_form",
                "view builder_form",
            ]
        ],
        "user_verifier" => [
            "icon" => "user",
            "actions" => [
                "access user_verifier",
                "add user_verifier",
                "update user_verifier",
                "delete user_verifier",
                "view user_verifier",
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Notifications Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration settings for the job notification system
    | including rate limits and security measures to prevent abuse and email
    | service blacklisting.
    |
    */
    
    'job_notifications' => [
        // System-wide rate limits
        'hourly_limit' => env('JOB_NOTIFICATION_HOURLY_LIMIT', 100),
        'daily_limit' => env('JOB_NOTIFICATION_DAILY_LIMIT', 500),
        
        // Per-subscriber rate limits
        'subscriber_hourly_limit' => env('JOB_NOTIFICATION_SUBSCRIBER_HOURLY_LIMIT', 2),
        'subscriber_daily_limit' => env('JOB_NOTIFICATION_SUBSCRIBER_DAILY_LIMIT', 5),
        
        // Timing controls (in milliseconds)
        'jitter_ms' => env('JOB_NOTIFICATION_JITTER_MS', 2000),  // Random delay up to 2 seconds
        'email_spacing_ms' => env('JOB_NOTIFICATION_EMAIL_SPACING_MS', 500),  // 500ms between each email
        
        // Security measures
        'max_recipients_per_setup' => env('JOB_NOTIFICATION_MAX_RECIPIENTS', 10),
        'max_setups_per_subscriber' => env('JOB_NOTIFICATION_MAX_SETUPS', 5),
        'min_notification_interval' => env('JOB_NOTIFICATION_MIN_INTERVAL', 1), // Minimum 1 hour between notifications
        
        // Throttling settings
        'throttle_attempts' => env('JOB_NOTIFICATION_THROTTLE_ATTEMPTS', 5),
        'throttle_minutes' => env('JOB_NOTIFICATION_THROTTLE_MINUTES', 30),
        
        // Additional protection
        'require_verification' => env('JOB_NOTIFICATION_REQUIRE_VERIFICATION', true),
        'verification_expiry_minutes' => env('JOB_NOTIFICATION_VERIFICATION_EXPIRY', 60),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Command Schedule Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the new command scheduling system that replaces
    | event-driven notifications with schedule-only execution.
    |
    */
    'disable_event_driven_notifications' => env('JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS', false),
    
    'disable_ssl_verify' => env('JOBSEEKER_DISABLE_SSL_VERIFY', false),

    /*
    |--------------------------------------------------------------------------
    | JobSeeker Module Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for the JobSeeker module including job fetching,
    | health monitoring, and alert thresholds.
    |
    */

    // Job acquisition thresholds
    'min_jobs_per_day' => env('JOBSEEKER_MIN_JOBS_PER_DAY', 50),
    
    // Health dashboard performance thresholds
    'health_thresholds' => [
        'success_rate' => [
            'good' => env('JOBSEEKER_SUCCESS_RATE_GOOD', 80),     // >= 80% = green
            'warning' => env('JOBSEEKER_SUCCESS_RATE_WARNING', 60) // >= 60% = yellow, < 60% = red
        ],
        'error_rate' => [
            'good' => env('JOBSEEKER_ERROR_RATE_GOOD', 10),       // <= 10% = green
            'warning' => env('JOBSEEKER_ERROR_RATE_WARNING', 30)   // <= 30% = yellow, > 30% = red
        ],
        'response_time' => [
            'good' => env('JOBSEEKER_RESPONSE_TIME_GOOD', 5),     // <= 5s = green
            'warning' => env('JOBSEEKER_RESPONSE_TIME_WARNING', 15) // <= 15s = yellow, > 15s = red
        ]
    ],

    // Alert system configuration
    'alerts' => [
        'enabled' => env('JOBSEEKER_ALERTS_ENABLED', true),
        'email_recipients' => env('JOBSEEKER_ALERT_EMAILS', '<EMAIL>'),
        'error_rate_threshold' => env('JOBSEEKER_ERROR_RATE_THRESHOLD', 70), // Trigger alert at 70% error rate
        'consecutive_failures_threshold' => env('JOBSEEKER_CONSECUTIVE_FAILURES', 3),
        'zero_jobs_threshold' => env('JOBSEEKER_ZERO_JOBS_THRESHOLD', 3),
    ],

    // Admin notification settings
    'admin_notification_email' => env('JOBSEEKER_ADMIN_EMAIL', '<EMAIL>'),
    
    // Job sync settings
    'sync' => [
        'default_timeout' => env('JOBSEEKER_SYNC_TIMEOUT', 300), // 5 minutes
        'max_retries' => env('JOBSEEKER_SYNC_MAX_RETRIES', 3),
        'rate_limit_delay' => env('JOBSEEKER_RATE_LIMIT_DELAY', 2000), // milliseconds
    ],

    // Data quality settings
    'data_quality' => [
        'min_job_fields_required' => ['position', 'company', 'location'],
        'max_description_length' => env('JOBSEEKER_MAX_DESCRIPTION_LENGTH', 10000),
        'min_description_length' => env('JOBSEEKER_MIN_DESCRIPTION_LENGTH', 50),
    ],

    // Health dashboard settings
    'dashboard' => [
        'default_period_days' => env('JOBSEEKER_DASHBOARD_PERIOD', 7),
        'max_period_days' => env('JOBSEEKER_DASHBOARD_MAX_PERIOD', 30),
        'refresh_interval_seconds' => env('JOBSEEKER_DASHBOARD_REFRESH', 300), // 5 minutes
    ],

    // Provider-specific settings
    'providers' => [
        'jobs_af' => [
            'enabled' => env('JOBSEEKER_JOBS_AF_ENABLED', true),
            'api_url' => env('JOBSEEKER_JOBS_AF_API_URL', 'https://jobs.af/api/jobs'),
            'timeout' => env('JOBSEEKER_JOBS_AF_TIMEOUT', 60),
            'rate_limit_delay' => env('JOBSEEKER_JOBS_AF_DELAY', 2000),
        ],
        'acbar' => [
            'enabled' => env('JOBSEEKER_ACBAR_ENABLED', true),
            'base_url' => env('JOBSEEKER_ACBAR_BASE_URL', 'https://www.acbar.org'),
            'timeout' => env('JOBSEEKER_ACBAR_TIMEOUT', 120),
            'max_retries' => env('JOBSEEKER_ACBAR_MAX_RETRIES', 3),
            'base_delay' => env('JOBSEEKER_ACBAR_BASE_DELAY', 2),
        ],
    ],
];
