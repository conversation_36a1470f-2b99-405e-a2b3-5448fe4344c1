<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Student;
use App\StudentRevisionReport;
use App\StudentRevisionPlan;
use App\AttendanceOption;
use App\Classes;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

final class DailyReportsRevisionSheet implements WithTitle, WithStyles, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get revision reports - includes ALL students with active plans for every day of the month
     */
    private function getRevisionReports(): Collection
    {
        $classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $studentId = $this->filters['studentId'] ?? null;

        // Get all days in the month
        $startDate = Carbon::create($year, $month, 1);
        $endDate = $startDate->copy()->endOfMonth();
        $daysInMonth = [];
        
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $daysInMonth[] = $date->format('Y-m-d');
        }

        // Get all students with active revision plans in the selected classes for this month
        $planYearMonth = sprintf('%d-%02d', $year, $month);
        
        $studentsWithPlans = Student::with([
            'revisionPlans' => function($query) use ($classIds, $planYearMonth) {
                $query->whereIn('class_id', $classIds)
                      ->where('plan_year_and_month', $planYearMonth)
                      ->where('status', 'active')
                      ->with(['class.center', 'class.programs', 'class.teachers']);
            }
        ])
        ->whereHas('revisionPlans', function($query) use ($classIds, $planYearMonth) {
            $query->whereIn('class_id', $classIds)
                  ->where('plan_year_and_month', $planYearMonth)
                  ->where('status', 'active');
        })
        ->when($studentId, function($query, $studentId) {
            return $query->where('id', $studentId);
        })
        ->get();

        // Get all existing revision reports for these students and classes
        $existingReports = StudentRevisionReport::with([
            'classes.center',
            'classes.programs', 
            'classes.teachers',
            'fromSurat',
            'toSurat',
            'result',
            'attendanceOptions',
            'revisionPlan'
        ])
        ->whereIn('class_id', $classIds)
        ->whereYear('created_at', $year)
        ->whereMonth('created_at', $month)
        ->when($studentId, function($query, $studentId) {
            return $query->where('student_id', $studentId);
        })
        ->get()
        ->groupBy(function($report) {
            return $report->student_id . '_' . $report->created_at->format('Y-m-d') . '_' . $report->class_id;
        });

        $results = collect();

        // Generate rows for ALL students for ALL days
        foreach ($studentsWithPlans as $student) {
            foreach ($student->revisionPlans as $plan) {
                if (in_array($plan->class_id, $classIds)) {
                    $classProgram = $plan->class->programs->first()->title ?? 'N/A';
                    $teacherNames = $plan->class->teachers->pluck('full_name')->join(', ');
                    
                    // Calculate attendance summary for this student in this class
                    $attendanceSummary = $this->calculateAttendanceSummary($student->id, $plan->class_id, $year, $month);
                    
                    foreach ($daysInMonth as $date) {
                        $reportKey = $student->id . '_' . $date . '_' . $plan->class_id;
                        $existingReport = $existingReports->get($reportKey)?->first();
                        
                        // Check if day is in timetable
                        $isInTimetable = $this->checkIfDayInTimetable($plan->class_id, $date);
                        
                        if ($existingReport) {
                            // Student has a report for this day
                            $numberOfPages = $this->calculateRevisionPages($existingReport);
                            $attendanceStatus = $existingReport->attendanceOptions->title ?? 'N/A';
                            $isAbsentOrExcused = in_array(strtolower($attendanceStatus), ['absent', 'excused']);
                            
                            $results->push([
                                'centre_id' => $plan->class->center->id ?? 'N/A',
                                'centre_name' => $plan->class->center->name ?? 'N/A',
                                'class_id' => $plan->class_id,
                                'class_program' => $classProgram,
                                'teacher_name' => $teacherNames ?: 'N/A',
                                'date' => $date,
                                'is_day_in_timetable' => $isInTimetable ? 'Yes' : 'No',
                                'student_id' => $student->id,
                                'student_name' => $student->full_name ?? 'N/A',
                                'attendance' => $attendanceStatus,
                                'attendance_summary' => $attendanceSummary,
                                'from_surah' => $existingReport->fromSurat->name ?? '',
                                'from_verse' => $existingReport->revision_from_ayat ?? '',
                                'to_surah' => $existingReport->toSurat->name ?? '',
                                'to_verse' => $existingReport->revision_to_ayat ?? '',
                                'no_of_pages' => $numberOfPages,
                                'evaluation' => $existingReport->result->title ?? '',
                                'evaluation_note' => $existingReport->revision_evaluation_note ?? '',
                                'is_absent_or_excused' => $isAbsentOrExcused,
                                'has_report' => true
                            ]);
                        } else {
                            // Student has no report for this day - create empty row
                            $results->push([
                                'centre_id' => $plan->class->center->id ?? 'N/A',
                                'centre_name' => $plan->class->center->name ?? 'N/A',
                                'class_id' => $plan->class_id,
                                'class_program' => $classProgram,
                                'teacher_name' => $teacherNames ?: 'N/A',
                                'date' => $date,
                                'is_day_in_timetable' => $isInTimetable ? 'Yes' : 'No',
                                'student_id' => $student->id,
                                'student_name' => $student->full_name ?? 'N/A',
                                'attendance' => $isInTimetable ? '❌ Absent' : 'No Class',
                                'attendance_summary' => $attendanceSummary,
                                'from_surah' => '',
                                'from_verse' => '',
                                'to_surah' => '',
                                'to_verse' => '',
                                'no_of_pages' => '',
                                'evaluation' => '',
                                'evaluation_note' => '',
                                'is_absent_or_excused' => $isInTimetable,
                                'has_report' => false
                            ]);
                        }
                    }
                }
            }
        }

        return $results->sortBy(['student_name', 'date']);
    }

    /**
     * Calculate attendance summary for a student in a class for the month
     */
    private function calculateAttendanceSummary(int $studentId, int $classId, int $year, int $month): string
    {
        // Get total timetable days for this class in the month
        $totalTimetableDays = $this->getTimetableDaysCount($classId, $year, $month);
        
        if ($totalTimetableDays === 0) {
            return 'No scheduled classes';
        }

        // Get attendance counts from revision reports
        $attendanceCounts = StudentRevisionReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->join('attendance_options', 'student_revision_report.attendance_id', '=', 'attendance_options.id')
            ->selectRaw('attendance_options.title, COUNT(*) as count')
            ->groupBy('attendance_options.title')
            ->pluck('count', 'title');

        $presentCount = ($attendanceCounts['On Time'] ?? 0) + ($attendanceCounts['Late'] ?? 0);
        $absentCount = $attendanceCounts['Absent'] ?? 0;
        $excusedCount = $attendanceCounts['Excused'] ?? 0;
        $noReportCount = $totalTimetableDays - $presentCount - $absentCount - $excusedCount;

        $attendanceRate = $totalTimetableDays > 0 ? round(($presentCount / $totalTimetableDays) * 100, 1) : 0;

        return "{$attendanceRate}% ({$presentCount}P/{$absentCount}A/{$excusedCount}E/{$noReportCount}NR)";
    }

    /**
     * Get count of timetable days for a class in a month
     */
    private function getTimetableDaysCount(int $classId, int $year, int $month): int
    {
        try {
            // Get the class timetable
            $timetable = DB::table('class_timetable')
                ->where('class_id', $classId)
                ->whereNull('deleted_at')
                ->first();

            if (!$timetable) {
                return 0;
            }

            $startDate = Carbon::create($year, $month, 1);
            $endDate = $startDate->copy()->endOfMonth();
            $count = 0;

            for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
                $dayOfWeek = strtolower($date->format('D')); // sat, sun, mon, etc.
                if (!is_null($timetable->$dayOfWeek)) {
                    $count++;
                }
            }

            return $count;
        } catch (\Exception $e) {
            \Log::error('Error counting timetable days: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Calculate revision pages using stored procedure
     */
    private function calculateRevisionPages(StudentRevisionReport $report): int
    {
        try {
            if ($report->revisionPlan && $report->revisionPlan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $report->revision_from_surat,
                    $report->revision_from_ayat,
                    $report->revision_to_surat,
                    $report->revision_to_ayat
                ]);
                
                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $report->revision_from_surat,
                    $report->revision_from_ayat,
                    $report->revision_to_surat,
                    $report->revision_to_ayat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating revision pages: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Check if the day is in timetable
     */
    private function checkIfDayInTimetable(int $classId, string $date): bool
    {
        try {
            // Get the day of week from the date
            $dayOfWeek = strtolower(Carbon::parse($date)->format('D')); // sat, sun, mon, etc.

            // Get the class timetable
            $timetable = DB::table('class_timetable')
                ->where('class_id', $classId)
                ->whereNull('deleted_at')
                ->first();

            if (!$timetable) {
                return false;
            }

            // Check if the day has a time set (not null)
            return !is_null($timetable->$dayOfWeek);

        } catch (\Exception $e) {
            \Log::error('Error checking timetable: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        return [
            'Centre ID',
            'Centre Name',
            'Class ID',
            'Class Program',
            'Teacher Name',
            'Date',
            'Is Day in Timetable?',
            'Student ID',
            'Student Name',
            'Attendance',
            'Attendance Summary',
            'From Surah',
            'From Verse',
            'To Surah',
            'To Verse',
            'No. of Pages',
            'Evaluation',
            'Evaluation Note',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Revision Reports';
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [];
    }

    /**
     * Register events for creating the table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createTable($event->sheet);
            },
        ];
    }

    /**
     * Create table on the worksheet with visual distinctions
     */
    private function createTable($sheet)
    {
        $worksheet = $sheet->getDelegate();

        // Get data
        $revisionData = $this->getRevisionReports();
        $headings = $this->getTableHeadings();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'] ?? [])->pluck('name')->join(', ');

        $worksheet->setCellValue('A1', "REVISION DAILY REPORTS - {$classNames} - {$monthName} {$year}");
        $worksheet->mergeCells('A1:R1');

        // Style main title
        $worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E8F4F8']]
        ]);

        $currentRow = 3;

        // Create Table
        $this->createDataTable($worksheet, $currentRow, 'REVISION REPORTS', $headings, $revisionData);

        // Auto-size columns
        foreach (range('A', 'R') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    /**
     * Create a single table with title, headers, and data with visual distinctions
     */
    private function createDataTable($worksheet, $startRow, $title, $headings, $data): int
    {
        // Set table title
        $worksheet->setCellValue("A{$startRow}", $title);
        $worksheet->mergeCells("A{$startRow}:R{$startRow}");

        // Style table title
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);

        $headerRow = $startRow + 1;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $headerRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$headerRow}:R{$headerRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '4CAF50']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $headerRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows with visual distinctions
        foreach ($data as $row) {
            $col = 'A';
            foreach ($row as $key => $value) {
                if ($key !== 'is_absent_or_excused' && $key !== 'has_report') {
                    $worksheet->setCellValue($col . $currentDataRow, $value);
                    $col++;
                }
            }
            
            // Apply visual distinctions based on attendance
            if ($row['is_absent_or_excused'] && $row['has_report']) {
                // Red background for absent, yellow for excused
                $bgColor = strtolower($row['attendance']) === 'absent' ? 'FFCDD2' : 'FFF9C4';
                $worksheet->getStyle("A{$currentDataRow}:R{$currentDataRow}")->applyFromArray([
                    'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => $bgColor]]
                ]);
            } elseif (!$row['has_report'] && $row['is_absent_or_excused']) {
                // Light red for no report on timetable day
                $worksheet->getStyle("A{$currentDataRow}:R{$currentDataRow}")->applyFromArray([
                    'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FFEBEE']]
                ]);
            }
            
            $currentDataRow++;
        }

        // Style data rows
        if ($data->count() > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:R{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            return $dataEndRow;
        }

        // If no data, add "No data available" message
        $worksheet->setCellValue("A{$dataStartRow}", 'No data available');
        $worksheet->mergeCells("A{$dataStartRow}:R{$dataStartRow}");
        $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'font' => ['italic' => true],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        return $dataStartRow;
    }
}