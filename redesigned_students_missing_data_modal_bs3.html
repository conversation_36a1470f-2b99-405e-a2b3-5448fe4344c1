<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students With Missing Data - Bootstrap 3</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
        .modal-dialog-wide {
            width: 95%;
            margin: 30px auto;
        }
        .modal-content {
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,.5);
        }
        .modal-header {
            border-bottom: 1px solid #e5e5e5;
            padding: 20px 25px;
        }
        .modal-title {
            font-weight: 600;
            font-size: 22px;
        }
        .modal-body {
            padding: 25px;
        }
        .panel {
             border-radius: 8px;
        }
        .panel-heading {
            font-weight: bold;
        }
        .panel-heading .fa {
            margin-right: 8px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .control-label {
            font-weight: 600;
        }
        .btn {
            border-radius: 6px;
            padding: 8px 15px;
            font-weight: 500;
        }
        .bulk-assignment-panel {
            background-color: #f9f9f9;
        }
        .selection-counter {
            font-weight: 500;
            color: #333;
            background-color: #eee;
            padding: 8px 12px;
            border-radius: 6px;
            display: inline-block;
            margin-top: 5px;
        }
        .table-actions {
            text-align: center;
        }
        .filter-buttons, .bulk-buttons {
            text-align: right;
            margin-top: 15px;
        }
        .pagination {
            margin-top: 0;
        }
        .table-responsive {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>

    <div class="modal fade in" style="display: block;" tabindex="-1">
        <div class="modal-dialog modal-dialog-wide">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Manage Student Data</h4>
                </div>
                <div class="modal-body">
                    <!-- Filters Panel -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title"><i class="fa fa-filter"></i> Filter Students</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="userStatus" class="control-label">Status</label>
                                        <select class="form-control" id="userStatus"><option selected>All Students</option></select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="program" class="control-label">Program</label>
                                        <select class="form-control" id="program"><option selected>All Programs</option></select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="center" class="control-label">Center</label>
                                        <select class="form-control" id="center"><option selected>All Centers</option></select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="class" class="control-label">Class</label>
                                        <select class="form-control" id="class"><option selected>All Classes</option></select>
                                    </div>
                                </div>
                            </div>
                            <div class="filter-buttons">
                                <button class="btn btn-default"><i class="fa fa-refresh"></i> Refresh</button>
                                <button class="btn btn-warning"><i class="fa fa-times"></i> Clear</button>
                                <button class="btn btn-primary"><i class="fa fa-check"></i> Apply Filters</button>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Assignment Panel -->
                    <div class="panel panel-default bulk-assignment-panel">
                        <div class="panel-heading">
                            <h3 class="panel-title"><i class="fa fa-users"></i> Bulk Assign to Class</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="bulkProgram" class="control-label">Program <span class="text-danger">*</span></label>
                                                <select class="form-control" id="bulkProgram"><option selected>Choose Program</option></select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="bulkCenter" class="control-label">Center <span class="text-danger">*</span></label>
                                                <select class="form-control" id="bulkCenter"><option selected>Choose Center</option></select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="bulkClass" class="control-label">Class <span class="text-danger">*</span></label>
                                                <select class="form-control" id="bulkClass"><option selected>Choose Class</option></select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                     <label class="control-label">&nbsp;</label>
                                     <div>
                                        <span class="selection-counter">0 students selected</span>
                                     </div>
                                </div>
                            </div>
                            <div class="bulk-buttons">
                                <button class="btn btn-success"><i class="fa fa-check-circle"></i> Apply Assignment</button>
                            </div>
                        </div>
                    </div>

                    <!-- Student Data Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th><input type="checkbox"></th>
                                    <th>Full Name</th>
                                    <th>Email</th>
                                    <th>Gender</th>
                                    <th>Date of Birth</th>
                                    <th>Program</th>
                                    <th>Center</th>
                                    <th>Class</th>
                                    <th class="table-actions">Save</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>Nausheen Fatima</td>
                                    <td><EMAIL></td>
                                    <td><select class="form-control input-sm"><option>Choose</option><option selected>Female</option></select></td>
                                    <td><input type="date" class="form-control input-sm"></td>
                                    <td><select class="form-control input-sm"><option selected>Select Program</option></select></td>
                                    <td><select class="form-control input-sm"><option selected>Select Center</option></select></td>
                                    <td><select class="form-control input-sm"><option selected>Select Class</option></select></td>
                                    <td class="table-actions"><button class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>Nahid Soubhy</td>
                                    <td><EMAIL></td>
                                    <td><select class="form-control input-sm"><option selected>Choose</option></select></td>
                                    <td><input type="date" class="form-control input-sm"></td>
                                    <td><select class="form-control input-sm"><option selected>Select Program</option></select></td>
                                    <td><select class="form-control input-sm"><option selected>Select Center</option></select></td>
                                    <td><select class="form-control input-sm"><option selected>Select Class</option></select></td>
                                    <td class="table-actions"><button class="btn btn-sm btn-primary"><i class="fa fa-save"></i></button></td>
                                </tr>
                                <!-- ... more student rows ... -->
                            </tbody>
                        </table>
                    </div>
                     <div class="row">
                        <div class="col-md-6">
                            Showing 1 to 10 of 514 entries
                        </div>
                        <div class="col-md-6 text-right">
                            <ul class="pagination">
                                <li class="disabled"><a href="#">&laquo;</a></li>
                                <li class="active"><a href="#">1</a></li>
                                <li><a href="#">2</a></li>
                                <li><a href="#">3</a></li>
                                <li><a href="#">&raquo;</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
</body>
</html>
