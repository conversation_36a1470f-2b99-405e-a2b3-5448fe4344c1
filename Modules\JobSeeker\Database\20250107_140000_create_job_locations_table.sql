--
-- Create job_locations table for canonical location management
-- This table stores canonical locations used by job seekers
--

CREATE TABLE job_locations (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL COMMENT 'Human-readable location name (e.g., Kabul Province)',
    country varchar(100) NOT NULL COMMENT 'Country name (e.g., Afghanistan)',
    province varchar(255) NOT NULL COMMENT 'Province/state name (e.g., Kabul)',
    is_active tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether this location is active',
    created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY job_locations_country_province_unique (country, province),
    KEY job_locations_country_index (country),
    KEY job_locations_province_index (province),
    KEY job_locations_is_active_index (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better query performance
CREATE INDEX job_locations_name_index ON job_locations (name);
CREATE INDEX job_locations_compound_country_active ON job_locations (country, is_active);

-- Insert table comments
ALTER TABLE job_locations COMMENT = 'Canonical job locations for job seeker interface - country and province focused';

-- Verify table creation
DESCRIBE job_locations; 