    <!--
        AVAILABLE HEADER CLASSES

        Default nav height: 96px
        .header-md      = 70px nav height
        .header-sm      = 60px nav height

        .noborder       = remove bottom border (only with transparent use)
        .transparent    = transparent header
        .translucent    = translucent header
        .sticky         = sticky header
        .static         = static header
        .dark           = dark header
        .bottom         = header on bottom

        shadow-before-1 = shadow 1 header top
        shadow-after-1  = shadow 1 header bottom
        shadow-before-2 = shadow 2 header top
        shadow-after-2  = shadow 2 header bottom
        shadow-before-3 = shadow 3 header top
        shadow-after-3  = shadow 3 header bottom

        .clearfix       = required for mobile menu, do not remove!

        Example Usage:  class="clearfix sticky header-sm transparent noborder"
    -->


    <div id="header" class="sticky clearfix">

        <!-- TOP NAV -->
        <header id="topNav">
            <div class="clearfix hidden-sm hidden-xs">
                <div class="col-md-12 pre-header navbar-primary">
                    <div class="col-md-3">
                        @if(config('settings.links_facebook'))
                        <a target="_blank" href="{{config('settings.links_facebook')}}" class="top_social small-social-icon social-icon-round  social-facebook" data-toggle="tooltip" data-placement="top" title="" data-original-title="Facebook">
                            <i style="font-size: " class="top_social_icon icon-facebook"></i>
                            <i style="font-size: " class="top_social_icon icon-facebook"></i>
                        </a>
                        @endif
                        @if(config('settings.links_youtube'))

                        <a target="_blank" href="{{ config('settings.links_youtube') }}" class="top_social small-social-icon social-icon-round  social-youtube" data-toggle="tooltip" data-placement="top" title="" data-original-title="Youtube">
                            <i style="font-size: " class="top_social_icon icon-youtube"></i>
                            <i style="font-size: " class="top_social_icon icon-youtube"></i>
                        </a>
                        @endif
                        @if(config('settings.links_twitter'))

                        <a target="_blank" href="{{ config('settings.links_twitter') }}" class="top_social small-social-icon social-icon-round  social-twitter" data-toggle="tooltip" data-placement="top" title="" data-original-title="Youtube">
                            <i style="font-size: " class="top_social_icon icon-twitter"></i>
                            <i style="font-size: " class="top_social_icon icon-twitter"></i>
                        </a>
                        @endif
                        @if(config('settings.links_linkedin'))

                        <a target="_blank" href="{{ config('settings.links_linkedin') }}" class="top_social small-social-icon social-icon-round  social-linkedin" data-toggle="tooltip" data-placement="top" title="" data-original-title="Youtube">
                            <i style="font-size: " class="top_social_icon icon-linkedin"></i>
                            <i style="font-size: " class="top_social_icon icon-linkedin"></i>
                        </a>
                        @endif
                    </div>
                    <div class="col-md-6 text-center">
                        {{ config('settings.website_title_'.config('app.locale')) }}
                    </div>
                    <div class="col-md-3">
                    @if (count(config('app.locales')) > 1)
                        <a href="{{ url('workplace') }}" class="pull-right"><i class="fa fa-users"></i> Workplace</a>
                        <ul id="" class="nav navbar-nav languages-nav pull-right" style="margin:0 20px">
                          <li class="dropdown">
                            <a class="dropdown-toggle" data-toggle="dropdown" href="#">{{ trans('common.language') }} [{{config('app.locale')}}]
                            <span class="caret"></span></a>
                            <ul class="dropdown-menu">
                                @foreach (config('app.locales') as $lang)
                                    <li>
                                        <a href="{{ change_language($lang) }}">{{ get_language_name($lang) }}</a>
                                    </li>
                                @endforeach
                            </ul>
                          </li>
                        </ul>
                    @endif
                    </div>
                </div>
            </div>

            <div class="container">

                <!-- Mobile Menu Button -->
                <button class="btn btn-mobile" data-toggle="collapse" data-target=".nav-main-collapse">
                    <i class="fa fa-bars"></i>
                </button>

                <!-- BUTTONS -->
{{--                <ul class="pull-right nav nav-pills nav-second-main">--}}

                    <!-- SEARCH -->
{{--                    <li class="search">--}}
{{--                        <a href="javascript:;">--}}
{{--                            <i class="fa fa-search"></i>--}}
{{--                        </a>--}}
{{--                        <div class="search-box">--}}
{{--                            <form action="#?" method="get">--}}
{{--                                <div class="input-group">--}}
{{--                                    <input type="text" name="src" placeholder="{{trans('search')}}" class="form-control" />--}}
{{--                                    <span class="input-group-btn">--}}
{{--                                        <button class="btn btn-primary" type="submit">{{trans('search')}}</button>--}}
{{--                                    </span>--}}
{{--                                </div>--}}
{{--                            </form>--}}
{{--                        </div>--}}
{{--                    </li>--}}
                    <!-- /SEARCH -->

{{--                </ul>--}}
                <!-- /BUTTONS -->


                <!-- Logo -->
                <a class="logo pull-left" href="/{{config('app.locale')}}">
                    <img src="{!! url(config('logo', '/images/no-image.png')) !!}" alt="" />
                </a>

                <!--
                    Top Nav

                    AVAILABLE CLASSES:
                    submenu-dark = dark sub menu
                -->
                <div class="navbar-collapse pull-right nav-main-collapse collapse submenu-dark">
                    <nav class="nav-main">

                        <!--
                            NOTE

                            For a regular link, remove "dropdown" class from LI tag and "dropdown-toggle" class from the href.
                            Direct Link Example:

                            <li>
                                <a href="#">HOME</a>
                            </li>
                        -->
                        <ul id="topMain" class="nav nav-pills nav-main">
                            <li><a href="/{{ config('app.locale') }}"
                            >{{ trans('common.home') }}</a></li>
                        @foreach ($menu_elements as $element)
                            <li>
                                <a href="
                                @if (isset($element['children']) && count($element['children']))
                                #
                                @else
                                    @if($element['type'] == 2)
                                    {!! $element['slug'] !!}
                                    @else
                                    {{ $element['slug'] ? url(config('app.locale').'/'.$element['slug'])  : '#'}}
                                    @endif
                                @endif
                                "
                                @if (isset($element['children']) && count($element['children']))
                                 class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false"
                                @endif
                                >
                                    {{ $element['title'] ?? ""  }}
                                </a>
                                @if (isset($element['children']) && count($element['children']))
                                    <ul class="dropdown-menu" role="menu">
                                    @foreach ($element['children'] as $child)
                                        <li>
                                            @if($child['type'] == 2)
                                            <a href="{!! $child['slug'] !!}">
                                            @else
                                            <a href="{{ $child['slug'] ? url(config('app.locale').'/'.$child['slug']) : '#' }}">
                                            @endif
                                                {{ $child['title'] ?? ""}}
                                            </a>
                                        </li>
                                    @endforeach

                                    </ul>
                                @endif
                            </li>
                        @endforeach

                            <li> <a href="{{ url(config('app.locale').'/donate-now') }}" style="background:#eea236;margin-top: 20px;margin-bottom: 20px; height: 45px;line-height: 25px;color: #FFF" class=" ">{{ trans('common.donate_now') }}</a></li>
                            @if(empty(get_guard()))


                                <li>
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                                    {{ trans('auth.login') }}
                                    </a>
                                    <ul class="dropdown-menu" role="menu">
                                    <li><a href="{{ url(config('app.locale').'/student/login') }}">{{ trans('common.student') }}</a></li>
                                    @if(config('settings.show_student_registeration_link'))
                                    <li><a href="{{ url(config('app.locale').'/guardian/login') }}">{{ trans('common.guardian') }}</a></li>
                                    @endif
                                </ul>
                                </li>
                                <li><a href="{{ url(config('app.locale').'/registeration') }}">{{ trans('auth.register') }}</a></li>

                            @else
                                <li><a href="{{ url(config('app.locale').'/'.get_guard().'/logout') }}" onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">{{ trans('auth.logout') }}</a>
                                </li>
                                <li> <a href="{{ url(config('app.locale').'/'.get_guard().'/home') }}" class=" ">Profile</a></li>



                                <form id="logout-form" action="{{ url(config('app.locale').'/'.get_guard().'/logout') }}" method="POST" style="display: none;">
                                    {{ csrf_field() }}
                                </form>
                            @endif


                            @if(config('settings.show_contact_page_link'))
                            <li><a href="{{ url(config('app.locale').'/contact-us') }}">{{ trans('common.contact_us') }}</a></li>
                            @endif
                            <li class="hidden-lg hidden-md">
                                <a href="{{ url('workplace') }}"><i class="fa fa-users"></i> Workplace</a>
                            </li>
                        </ul>

                    </nav>
                </div>

            </div>
        </header>
        <!-- /Top Nav -->

    </div>

