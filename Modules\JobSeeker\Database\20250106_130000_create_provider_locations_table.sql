--
-- Create provider_locations table for dynamic location mapping
-- This enables flexible provider-specific location handling without hardcoded configuration
-- Focus: Afghanistan locations only, purely database-driven approach
--

CREATE TABLE provider_locations (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    provider_name varchar(50) NOT NULL COMMENT 'Provider identifier (e.g., jobs.af, acbar)',
    location_name varchar(255) NOT NULL COMMENT 'Human-readable location name',
    provider_identifier varchar(255) NOT NULL COMMENT 'The actual value sent to provider API',
    is_active tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether this location mapping is active',
    created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY provider_locations_provider_identifier_unique (provider_name, provider_identifier),
    KEY provider_locations_provider_name_index (provider_name),
    KEY provider_locations_is_active_index (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better query performance
CREATE INDEX provider_locations_location_name_index ON provider_locations (location_name);
CREATE INDEX provider_locations_compound_provider_active ON provider_locations (provider_name, is_active);

-- Insert comments explaining the table structure
ALTER TABLE provider_locations COMMENT = 'Maps provider-specific location identifiers for Afghanistan locations - purely database-driven approach';

-- Verify table creation
DESCRIBE provider_locations; 