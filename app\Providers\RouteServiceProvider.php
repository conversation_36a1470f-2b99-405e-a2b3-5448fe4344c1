<?php

namespace App\Providers;

use App\AdmissionInterview;
use App\Employee;
use App\Student;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{

    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //



        parent::boot();

        /** https://laravel.com/docs/8.x/routing#explicit-binding for 'accept.interview' route */
        Route::bind('admissionInterviewAdmissionId', function ($value) {
            return AdmissionInterview::where('admission_id', $value)->firstOrFail();
        });



        // used for employees.show.archived route
        Route::bind('archived_employee', function ($id) {
            return Employee::onlyTrashed()->find($id);
        });

        Route::bind('student', function ($student) {
            return Student::withTrashed()->where('id', $student)->firstOrFail();
        });

        // Custom route model binding
//        Route::model('studentId', Student::class);


    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();

        $this->mapSponsorRoutes();
        $this->mapLeaveRoutes();
        $this->mapEducationalReportsRoutes();
        $this->mapExaminationCertificationRoutes();

        $this->mapSuperiorRoutes();
        $this->mapSiteRoutes();

        $this->mapEmployeeRoutes();
        $this->mapAccountRoutes();
        $this->mapPayrollRoutes();
        $this->mapCommunicationRoutes();

        $this->mapStudentRoutes();

        $this->mapGuardianRoutes();

//        $this->mapOrganizationRoutes();
        $this->mapAdminRoutes();
//        $this->mapJobsRoutes();

        $this->mapRegistrationRoutes();
        $this->mapRolePermissionsRoutes();
        
        // Map menu tracking routes
        $this->mapMenuTrackingRoutes();


        // used for user impersonation plugin to enable impersonation for the following guards
        Route::middleware(['web', 'employee'])->group(function (Router $router) {
//            $router->impersonate();
            $router->multiAuthImpersonate('multiAuthImpersonate');
        });


        //
    }

    /**
     * Define the "student" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
//    protected function mapStudentRoutes()
//    {
//        Route::middleware('web')
//            ->namespace($this->namespace)
//            ->group(base_path('routes/student.php'));
//        require base_path('routes/student.php');
//    }
    protected function mapStudentRoutes()
    {
        Route::middleware('web')
            ->namespace($this->namespace)
            ->group(base_path('routes/student.php'));
    }

    /**
     * this is used for the student or parent registration
     */
//    protected function mapRegistrationRoutes()
//    {
//        Route::group([
//            "middleware" =>'web',
//             'domain' => config('app.platform_domain'),
////            'prefix' => 'studentapplication',
//            'as' => 'studentapplication.',
//            'namespace' => $this->namespace],
//        function($router){
//            require base_path('routes/studentapplication.php');
//        });
//
//    }


    protected function mapSiteRoutes()
    {


        Route::group([
//            'middleware' => ['web'],
//            'domain' => config('app.platform_domain'),
            'domain' => config('domain'),
//            'as' => 'organization.',
//            'namespace' => $this->namespace,
        ], function ($router) {


            require base_path('routes/site.php');

        });


    }


    protected function mapRegistrationRoutes()
    {


        Route::group([
            'middleware' => ['web', 'auth'],
//            'domain' => config('app.platform_domain'),
//            'as' => 'organization.',
//            'namespace' => $this->namespace,
        ], function ($router) {


            require base_path('routes/applicationcenter.php');

        });


//        Route::group([
//            "middleware" =>['web','auth'],
//            'domain' => config('app.platform_domain'),
////            'prefix' => 'studentapplication',
////            'as' => 'studentapplication.',
//            'namespace' => $this->namespace
//        ],
//            function($router){
////                require base_path('routes/studentapplication.php');
//                require module_path('ApplicationCenter', '/Http/routes.php');
//            });

    }

    /**
     * Define the "organization" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapOrganizationRoutes()
    {
        Route::group([
            'middleware' => ['web', 'organization', 'auth:organization'],
            'domain' => config('app.platform_domain'),
            'as' => 'organization.',
            'namespace' => $this->namespace,
        ], function ($router) {


            require base_path('routes/organization.php');

        });

    }

    protected function mapAdminRoutes()
    {

        Route::middleware('web')
            ->namespace($this->namespace)
            ->group(base_path('routes/admin.php'));


//        Route::group([
//            'middleware' => ['web'],
//            'as' => 'admin',
//            'domain' => config('app.platform_domain'),
//            'namespace' => $this->namespace
//        ], function () {
//
//            return base_path('routes/admin.php');
//        });
    }


    /**
     * Define the "guardian" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapGuardianRoutes()
    {

        Route::middleware('web')
            ->namespace($this->namespace)
            ->group(base_path('routes/parent.php'));

//        Route::group([
//            'middleware' => 'web',
//            'namespace' => $this->namespace,
//        ], function ($router) {
//            require base_path('routes/parent.php');
//        });
    }


    /**
     * Define the "employee" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */


    /**
     * Define the "jobs" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapJobsRoutes()
    {

        Route::group([
//            'middleware' => ['web', 'jobs', 'auth:jobs'],
            'middleware' => ['web'],
//            'prefix' => 'job',
//            'as' => 'job.',
            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/jobs.php');
        });
    }


    /**
     * Define the "employee" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapEmployeeRoutes()
    {
        Route::group([
            'middleware' => ['web', 'employee', 'auth:employee', 'viewer.restrictions'],
            'prefix' => 'workplace',
            'as' => 'employee.',
            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/employee.php');
        });
    }


    /**
     * Define the "employee" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapPayrollRoutes()
    {
        Route::group([
            'middleware' => ['web', 'auth:employee', 'viewer.restrictions'],
            // 'domain' => 'sponsor.' . env('APP_DOMAIN'),
            'as' => 'payroll.',
            'prefix' => 'workplace',
//            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/payroll.php');
        });

    }
    /**
     * Define the "account" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapAccountRoutes()
    {



        Route::group([
            'middleware' => ['web', 'auth:employee', 'viewer.restrictions'],
            // 'domain' => 'sponsor.' . env('APP_DOMAIN'),
            'as' => 'account.',
            'prefix' => 'workplace',
//            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/account.php');
        });

    }


    /**
     * Define the "superior" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapSuperiorRoutes()
    {

        Route::group([
            'middleware' => ['web', 'superior', 'auth:superior'],
            'doamin' => config('app.platform_domain'),
            'prefix' => 'superior',
            'as' => 'superior.',
            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/superior.php');
        });
    }

    /**
     * Define the "sponsor" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapSponsorRoutes()
    {
        Route::group([
            'middleware' => ['web', 'sponsor', 'auth:sponsor'],
            // 'domain' => 'sponsor.' . env('APP_DOMAIN'),
            'as' => 'sponsor.',
            'prefix' => config('app.locale') . '/' . 'sponsors',
            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/sponsor.php');
        });
    }

    protected function mapLeaveRoutes()
    {
        Route::group([
            'middleware' => ['web', 'auth:employee'],
            // 'domain' => 'sponsor.' . env('APP_DOMAIN'),
            'as' => 'leave.',
            'prefix' => 'workplace',
//            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/leave.php');
        });
    }
    protected function mapEducationalReportsRoutes()
    {
        Route::group([
            'middleware' => ['web', 'auth:employee'],
            // 'domain' => 'sponsor.' . env('APP_DOMAIN'),
            'as' => 'educationalreports.',
            'prefix' => 'workplace',
//            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/educationalreports.php');
        });
    }

  


    protected function mapExaminationCertificationRoutes()
    {
        Route::group([
            'middleware' => ['web', 'auth:employee'],
            // 'domain' => 'sponsor.' . env('APP_DOMAIN'),
            'as' => 'examinationcertification.',
            'prefix' => 'workplace',
//            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/examinationcertification.php');
        });
    }
    protected function mapAttendanceRoutes()
    {
        Route::group([
            'middleware' => ['web', 'auth:employee'],
            // 'domain' => 'sponsor.' . env('APP_DOMAIN'),
            'as' => 'attendance.',
            'prefix' => 'workplace',
//            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/attendance.php');
        });
    }


    protected function mapRolePermissionsRoutes()
    {
        Route::group([
            'middleware' => ['web', 'auth:employee'],
            // 'domain' => 'sponsor.' . env('APP_DOMAIN'),
            'as' => 'rolepermission.',
            'prefix' => 'workplace',
//            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/rolePermission.php');
        });
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware('web')
            ->namespace($this->namespace)
            ->group(base_path('routes/web.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
            ->middleware('api')
            ->namespace($this->namespace)
            ->group(base_path('routes/api.php'));
    }

    protected function mapCommunicationRoutes()
    {

//        Route::group([
//            'middleware' => ['web', 'employee', 'auth:employee'],
//            'prefix' => 'workplace',
//            'as' => 'communicate.',
//            'namespace' => $this->namespace,
//        ], function ($router) {
//            require base_path('routes/communicate.php');
//        });

        Route::group([
            'middleware' => ['web', 'employee', 'auth:employee', 'viewer.restrictions'],
            'prefix' => 'workplace',
//            'as' => 'communicate.',
//            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/communicate.php');
        });

    }

    /**
     * Define the "menu tracking" routes for the application.
     *
     * @return void
     */
    protected function mapMenuTrackingRoutes()
    {
        Route::middleware('web')
            ->namespace($this->namespace)
            ->group(base_path('routes/menu_tracking.php'));
    }
}
