<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * JobSeeker Admin Middleware
 * 
 * Handles authentication and authorization for JobSeeker module admin interfaces
 */
final class JobSeekerAdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string|null $guard
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $guard = null)
    {
        Log::info('JobSeekerAdminMiddleware: Checking admin access', [
            'route' => $request->route()->getName(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        // Check if user is authenticated with job_seeker guard
        if (!Auth::guard('job_seeker')->check()) {
            Log::warning('JobSeekerAdminMiddleware: Unauthenticated access attempt', [
                'route' => $request->route()->getName(),
                'ip' => $request->ip()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required'
                ], 401);
            }

            return redirect()->route('jobseeker.login')
                // ->with('error', 'Please log in to access admin features')
                ->with('intended', $request->fullUrl());
        }

        $user = Auth::guard('job_seeker')->user();
        
        // Check if user has admin privileges with proper role-based access control
        if (!$this->hasAdminPrivileges($user)) {
            Log::warning('JobSeekerAdminMiddleware: Unauthorized admin access attempt', [
                'user_id' => $user->id,
                'email' => $user->email,
                'route' => $request->route()->getName(),
                'ip' => $request->ip(),
                'has_admin_role' => $this->hasAdminRole($user),
                'is_admin_email' => $this->isAdminEmail($user->email),
                'is_active' => $user->is_active ?? false
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Admin privileges required. Contact system administrator if you believe this is an error.'
                ], 403);
            }

            return redirect()->route('jobseeker.dashboard')
                ->with('error', 'You do not have permission to access admin features. Contact system administrator if you believe this is an error.');
        }

        Log::info('JobSeekerAdminMiddleware: Admin access granted', [
            'user_id' => $user->id,
            'email' => $user->email,
            'route' => $request->route()->getName()
        ]);

        return $next($request);
    }

    /**
     * Check if the user has admin privileges using proper role-based access control
     * 
     * @param mixed $user
     * @return bool
     */
    private function hasAdminPrivileges($user): bool
    {
        if (!$user || !($user->is_active ?? false)) {
            return false;
        }

        // Check for admin role (if user has roles system)
        if ($this->hasAdminRole($user)) {
            return true;
        }

        // Check if email is in configured admin emails list
        if ($this->isAdminEmail($user->email)) {
            return true;
        }

        // Check for admin flag in database (if such field exists)
        if (isset($user->is_admin) && $user->is_admin) {
            return true;
        }

        return false;
    }

    /**
     * Check if the user has an admin role using Spatie Permission
     * 
     * @param mixed $user
     * @return bool
     */
    private function hasAdminRole($user): bool
    {
        // Check if user has Spatie Permission HasRoles trait and admin role
        if (method_exists($user, 'hasRole')) {
            return $user->hasRole('admin') || $user->hasRole('administrator');
        }

        return false;
    }

    /**
     * Check if the email is in the configured admin emails list
     * 
     * @param string $email
     * @return bool
     */
    private function isAdminEmail(string $email): bool
    {
        $adminEmails = config('jobseeker.admin_emails', []);
        
        if (empty($adminEmails) || !is_array($adminEmails)) {
            return false;
        }

        return in_array(strtolower($email), array_map('strtolower', $adminEmails), true);
    }
} 