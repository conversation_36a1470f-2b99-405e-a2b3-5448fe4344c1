-- =====================================================
-- CANONICAL CATEGORIES CONSOLIDATION SCRIPT
-- =====================================================
-- This script consolidates duplicate canonical categories and updates all references
-- Execute this script in the production database to fix category issues
-- 
-- Author: System Administrator
-- Date: 2025-01-18
-- Purpose: Fix duplicate canonical categories (Technology, Health, Security, Administration, Marketing)
-- =====================================================

-- Start transaction to ensure data integrity
START TRANSACTION;

-- =====================================================
-- STEP 1: CREATE BACKUP TABLE (OPTIONAL - RECOMMENDED)
-- =====================================================
CREATE TABLE job_categories_backup AS SELECT * FROM job_categories;
CREATE TABLE provider_job_categories_backup AS SELECT * FROM provider_job_categories;

-- =====================================================
-- STEP 2: CONSOLIDATE TECHNOLOGY CATEGORIES
-- =====================================================

-- Keep "Technology" as the main canonical category (we'll use ID 1)
UPDATE job_categories 
SET name = 'Technology', slug = 'technology' 
WHERE id = 1;

-- Update all provider categories pointing to duplicate IT categories to point to Technology (ID: 1)
UPDATE provider_job_categories 
SET canonical_category_id = 1 
WHERE canonical_category_id IN (10, 20, 31); -- IT-Software, IT-Hardware, Information Technology duplicate

-- =====================================================
-- STEP 3: CONSOLIDATE HEALTH CATEGORIES  
-- =====================================================

-- Keep "Health" as the main canonical category (we'll use ID 16 which has more mappings)
UPDATE job_categories 
SET name = 'Health', slug = 'health' 
WHERE id = 16;

-- Update provider categories pointing to duplicate Health category to point to Health (ID: 16)
UPDATE provider_job_categories 
SET canonical_category_id = 16 
WHERE canonical_category_id = 27; -- Health duplicate

-- =====================================================
-- STEP 4: CONSOLIDATE SECURITY CATEGORIES
-- =====================================================

-- Keep "Security" as the main canonical category (we'll use ID 15 which has mappings)
UPDATE job_categories 
SET name = 'Security', slug = 'security' 
WHERE id = 15;

-- Update provider categories pointing to duplicate Security category to point to Security (ID: 15)
UPDATE provider_job_categories 
SET canonical_category_id = 15 
WHERE canonical_category_id = 29; -- Security duplicate

-- =====================================================
-- STEP 5: CONSOLIDATE ADMINISTRATION CATEGORIES
-- =====================================================

-- Keep "Administration" as the main canonical category (we'll use ID 24)
UPDATE job_categories 
SET name = 'Administration', slug = 'administration' 
WHERE id = 24;

-- Update provider categories pointing to Administrative to point to Administration (ID: 24)
UPDATE provider_job_categories 
SET canonical_category_id = 24 
WHERE canonical_category_id = 23; -- Administrative

-- =====================================================
-- STEP 6: CONSOLIDATE MARKETING CATEGORIES
-- =====================================================

-- Keep "Marketing" as the main canonical category (we'll use ID 4)
UPDATE job_categories 
SET name = 'Marketing', slug = 'marketing' 
WHERE id = 4;

-- Update provider categories pointing to Sales/Marketing to point to Marketing (ID: 4)
UPDATE provider_job_categories 
SET canonical_category_id = 4 
WHERE canonical_category_id = 22; -- Sales/Marketing

-- =====================================================
-- STEP 7: UPDATE ANY EXISTING JOB RELATIONSHIPS
-- =====================================================

-- Update job_category_pivot table to point to consolidated categories
UPDATE job_category_pivot 
SET category_id = 1 
WHERE category_id IN (10, 20, 31); -- Technology consolidation

UPDATE job_category_pivot 
SET category_id = 16 
WHERE category_id = 27; -- Health consolidation

UPDATE job_category_pivot 
SET category_id = 15 
WHERE category_id = 29; -- Security consolidation

UPDATE job_category_pivot 
SET category_id = 24 
WHERE category_id = 23; -- Administration consolidation

UPDATE job_category_pivot 
SET category_id = 4 
WHERE category_id = 22; -- Marketing consolidation

-- =====================================================
-- STEP 8: UPDATE NOTIFICATION CATEGORIES
-- =====================================================

-- Update job_notification_category table to point to consolidated categories
UPDATE job_notification_category 
SET category_id = 1 
WHERE category_id IN (10, 20, 31); -- Technology consolidation

UPDATE job_notification_category 
SET category_id = 16 
WHERE category_id = 27; -- Health consolidation

UPDATE job_notification_category 
SET category_id = 15 
WHERE category_id = 29; -- Security consolidation

UPDATE job_notification_category 
SET category_id = 24 
WHERE category_id = 23; -- Administration consolidation

UPDATE job_notification_category 
SET category_id = 4 
WHERE category_id = 22; -- Marketing consolidation

-- =====================================================
-- STEP 9: REMOVE DUPLICATE CANONICAL CATEGORIES
-- =====================================================

-- Delete duplicate categories (this will only work if no other references exist)
DELETE FROM job_categories WHERE id IN (10, 20, 23, 27, 29, 31, 22);

-- =====================================================
-- STEP 10: VERIFICATION QUERIES
-- =====================================================

-- Verify the consolidation worked correctly
SELECT 'VERIFICATION: Consolidated Categories' as verification_step;

SELECT 
    jc.id,
    jc.name as canonical_name,
    COUNT(pjc.id) as provider_categories_count,
    GROUP_CONCAT(DISTINCT pjc.provider_name) as providers
FROM job_categories jc
LEFT JOIN provider_job_categories pjc ON jc.id = pjc.canonical_category_id
WHERE jc.is_canonical = 1 AND jc.name IN ('Technology', 'Health', 'Security', 'Administration', 'Marketing')
GROUP BY jc.id, jc.name
ORDER BY jc.name;

-- Verify no orphaned provider categories
SELECT 'VERIFICATION: Check for orphaned provider categories' as verification_step;

SELECT COUNT(*) as orphaned_count
FROM provider_job_categories pjc
LEFT JOIN job_categories jc ON pjc.canonical_category_id = jc.id
WHERE jc.id IS NULL;

-- Show final canonical categories list
SELECT 'VERIFICATION: Final canonical categories list' as verification_step;

SELECT id, name, slug, is_active, is_canonical 
FROM job_categories 
WHERE is_canonical = 1 
ORDER BY name;

-- =====================================================
-- COMMIT TRANSACTION
-- =====================================================

-- If everything looks good, commit the transaction
COMMIT;

-- =====================================================
-- ROLLBACK INSTRUCTIONS (IF NEEDED)
-- =====================================================
-- If something goes wrong, you can rollback using:
-- ROLLBACK;
-- 
-- To restore from backup (if created):
-- DROP TABLE job_categories;
-- DROP TABLE provider_job_categories;
-- RENAME TABLE job_categories_backup TO job_categories;
-- RENAME TABLE provider_job_categories_backup TO provider_job_categories;
