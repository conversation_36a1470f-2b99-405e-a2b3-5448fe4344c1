<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Listeners;

use <PERSON><PERSON><PERSON>\JobSeeker\Events\JobProcessedEvent;
use <PERSON><PERSON>les\JobSeeker\Jobs\ProcessJobNotificationSetupJob;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobNotificationSetup;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

final class JobNotificationListener
{
    // Removed ShouldQueue to make this listener synchronous
    // This prevents model serialization issues in the queue

    /**
     * The chunk size for processing notification setups
     */
    private const CHUNK_SIZE = 100;

    /**
     * Handle the event.
     *
     * @param JobProcessedEvent $event
     * @return void
     */
    public function handle(JobProcessedEvent $event): void
    {
        // Check if event-driven notifications are disabled in favor of schedule-only execution
        if (config('jobseeker.disable_event_driven_notifications', false)) {
            Log::info('JobNotificationListener: Event-driven notifications disabled, skipping immediate processing', [
                'job_id' => $event->job->id,
                'job_title' => $event->job->position ?? 'Unknown',
                'source' => $event->job->source ?? 'Unknown',
                'config_setting' => 'jobseeker.disable_event_driven_notifications'
            ]);
            return;
        }
        
        $job = $event->job;
        
        // Wrap all operations in a database transaction for safety
        DB::transaction(function () use ($job) {
            // Ensure job has categories loaded
            if ($job->relationLoaded('categories') === false) {
                $job->load('categories');
            }
            
            // Get job canonical category IDs
            $jobCategoryIds = $job->categories->pluck('id')->toArray();
            
            if (empty($jobCategoryIds)) {
                Log::warning('Job processed without canonical categories assigned', [
                    'job_id' => $job->id, 
                    'source' => $job->source
                ]);
                return;
            }
            
            $processedCount = 0;
            
            // Process notification setups in chunks to prevent memory issues
            JobNotificationSetup::query()
                ->where('is_active', true)
                ->whereHas('categories', function ($query) use ($jobCategoryIds) {
                    $query->whereIn('job_categories.id', $jobCategoryIds);
                })
                ->chunk(self::CHUNK_SIZE, function ($matchingSetups) use ($job, &$processedCount) {
                    // Dispatch a job to process each matching notification setup in the current chunk
                    foreach ($matchingSetups as $setup) {
                        // Assign a random delay to each job to prevent thundering herd scenarios
                        // and distribute the load evenly over a short period.
                        $delay = rand(5, 180); // Random delay between 5 seconds and 3 minutes.
                        
                        ProcessJobNotificationSetupJob::dispatch($setup->id)
                            ->onConnection('job_notifications')
                            ->onQueue('setup_processors')
                            ->delay(now()->addSeconds($delay));
                        
                        $processedCount++;
                        
                        Log::info('Dispatched notification job for setup', [
                            'job_id' => $job->id,
                            'setup_id' => $setup->id,
                            'delay_seconds' => $delay
                        ]);
                    }
                });
            
            if ($processedCount === 0) {
                Log::info('No matching notification setups found for job', [
                    'job_id' => $job->id,
                    'job_category_ids' => $jobCategoryIds
                ]);
            } else {
                Log::info('Completed dispatching notification jobs', [
                    'job_id' => $job->id,
                    'total_setups_processed' => $processedCount
                ]);
            }
        });
    }
} 