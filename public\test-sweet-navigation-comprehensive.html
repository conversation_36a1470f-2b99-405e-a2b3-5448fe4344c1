<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sweet Navigation - Comprehensive Testing Suite</title>

    <!-- CSRF Token -->
    <meta name="csrf-token" content="test-token">

    <!-- Bootstrap 3 for compatibility testing -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">

    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

    <!-- Sweet Navigation CSS -->
    <link rel="stylesheet" href="css/components/sweet-navigation.css">

    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .nav-trigger {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #009933;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        
        .nav-trigger:hover {
            background: #007a29;
            color: white;
            text-decoration: none;
        }
        
        .breadcrumb-demo {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #e0e0e0;
        }
        
        .breadcrumb-demo a {
            color: #009933;
            text-decoration: none;
        }
        
        .test-controls {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .test-controls button {
            margin: 5px;
        }
        
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        
        .log-info {
            color: #0c5460;
        }
        
        .log-success {
            color: #155724;
        }
        
        .log-error {
            color: #721c24;
        }
        
        .log-warning {
            color: #856404;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Sweet Navigation - Comprehensive Testing Suite</h1>
        <p class="lead">Complete validation of all Sweet Navigation component functionality</p>

        <div class="test-section">
            <h3>Test Control Panel</h3>
            <div class="test-controls">
                <button class="btn btn-primary" onclick="runAllTests()">
                    <i class="fa fa-play"></i> Run All Tests
                </button>
                <button class="btn btn-success" onclick="runBasicTests()">
                    <i class="fa fa-check"></i> Basic Tests
                </button>
                <button class="btn btn-warning" onclick="runErrorTests()">
                    <i class="fa fa-exclamation-triangle"></i> Error Tests
                </button>
                <button class="btn btn-info" onclick="runPerformanceTests()">
                    <i class="fa fa-tachometer"></i> Performance Tests
                </button>
                <button class="btn btn-default" onclick="clearTestLog()">
                    <i class="fa fa-trash"></i> Clear Log
                </button>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h4>Test Results Summary</h4>
                    <div id="test-summary">
                        <div class="test-result test-pending">Tests not started</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h4>Test Log</h4>
                    <div id="test-log" class="test-log">
                        <div class="log-entry log-info">Test suite initialized. Click 'Run All Tests' to begin.</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Test 1: Hover Functionality</h3>
            <p>Test hover triggers with different delay settings:</p>

            <div class="breadcrumb-demo">
                <span>Dashboard > </span>
                <a href="#" class="sweet-navigation-trigger test-hover-fast" data-ajax-url="/test-navigation-data.json" data-title="Fast Hover Test (100ms)" data-current-id="1">
                    <i class="fa fa-flash"></i> Fast Hover (100ms)
                </a>
                <span> | </span>
                <a href="#" class="sweet-navigation-trigger test-hover-normal" data-ajax-url="/test-navigation-data.json" data-title="Normal Hover Test (200ms)" data-current-id="2">
                    <i class="fa fa-clock-o"></i> Normal Hover (200ms)
                </a>
                <span> | </span>
                <a href="#" class="sweet-navigation-trigger test-hover-slow" data-ajax-url="/test-navigation-data.json" data-title="Slow Hover Test (500ms)" data-current-id="3">
                    <i class="fa fa-hourglass"></i> Slow Hover (500ms)
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3>Test 2: AJAX Data Loading</h3>
            <p>Test various AJAX response scenarios:</p>

            <div class="row">
                <div class="col-md-3">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-ajax-success" data-ajax-url="/test-navigation-data.json" data-title="Success Response">
                        <i class="fa fa-check-circle"></i> Success
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-ajax-empty" data-ajax-url="/test-empty-data.json" data-title="Empty Response">
                        <i class="fa fa-inbox"></i> Empty
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-ajax-error" data-ajax-url="/invalid-endpoint" data-title="Error Response">
                        <i class="fa fa-times-circle"></i> Error
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-ajax-timeout" data-ajax-url="/slow-endpoint" data-title="Slow Response">
                        <i class="fa fa-hourglass-half"></i> Slow
                    </a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Test 3: Search Functionality</h3>
            <p>Test search within popup (trigger any navigation above, then test search):</p>

            <div class="well">
                <h4>Search Test Instructions:</h4>
                <ol>
                    <li>Hover over any navigation trigger above</li>
                    <li>When popup opens, try these search terms:
                        <ul>
                            <li><code>algebra</code> - Should find Algebra classes</li>
                            <li><code>smith</code> - Should find Dr. Smith's classes</li>
                            <li><code>math</code> - Should find Mathematics program</li>
                            <li><code>xyz</code> - Should show no results</li>
                        </ul>
                    </li>
                    <li>Verify search highlighting and filtering works</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>Test 4: Navigation & Action Buttons</h3>
            <p>Test item clicking and action button functionality:</p>

            <a href="#" class="nav-trigger sweet-navigation-trigger test-navigation" data-ajax-url="/test-navigation-data.json" data-title="Navigation Test" data-current-id="2">
                <i class="fa fa-mouse-pointer"></i> Test Navigation & Actions
            </a>

            <div class="well">
                <h4>Navigation Test Instructions:</h4>
                <ol>
                    <li>Click the trigger above</li>
                    <li>Click on any class item (should navigate to class page)</li>
                    <li>Click action buttons (R, V, P) - should open in new tabs</li>
                    <li>Verify current item is highlighted</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>Test 5: Close Handlers</h3>
            <p>Test various ways to close the popup:</p>

            <a href="#" class="nav-trigger sweet-navigation-trigger test-close-handlers" data-ajax-url="/test-navigation-data.json" data-title="Close Handlers Test">
                <i class="fa fa-times"></i> Test Close Handlers
            </a>

            <div class="well">
                <h4>Close Handler Test Instructions:</h4>
                <ol>
                    <li>Click the trigger above</li>
                    <li>Test these close methods:
                        <ul>
                            <li><kbd>ESC</kbd> key - Should close popup</li>
                            <li>Click outside popup - Should close popup</li>
                            <li>Click X button - Should close popup</li>
                            <li>Click Cancel button - Should close popup</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>Test 6: Multiple Instances</h3>
            <p>Test multiple navigation triggers on the same page:</p>

            <div class="row">
                <div class="col-md-2">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-multi-1" data-ajax-url="/test-navigation-data.json" data-title="Instance 1" data-current-id="1">
                        <i class="fa fa-list"></i> Nav 1
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-multi-2" data-ajax-url="/test-navigation-data.json" data-title="Instance 2" data-current-id="2">
                        <i class="fa fa-th"></i> Nav 2
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-multi-3" data-ajax-url="/test-navigation-data.json" data-title="Instance 3" data-current-id="3">
                        <i class="fa fa-grid"></i> Nav 3
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-multi-4" data-ajax-url="/test-navigation-data.json" data-title="Instance 4" data-current-id="4">
                        <i class="fa fa-bars"></i> Nav 4
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-multi-5" data-ajax-url="/test-navigation-data.json" data-title="Instance 5" data-current-id="5">
                        <i class="fa fa-menu"></i> Nav 5
                    </a>
                </div>
                <div class="col-md-2">
                    <a href="#" class="nav-trigger sweet-navigation-trigger test-multi-6" data-ajax-url="/test-navigation-data.json" data-title="Instance 6" data-current-id="6">
                        <i class="fa fa-sitemap"></i> Nav 6
                    </a>
                </div>
            </div>

            <div class="well">
                <h4>Multiple Instance Test Instructions:</h4>
                <ol>
                    <li>Hover over different triggers rapidly</li>
                    <li>Verify only one popup opens at a time</li>
                    <li>Test that each trigger works independently</li>
                    <li>Verify no interference between instances</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>Test 7: Performance & Memory</h3>
            <p>Test performance with rapid interactions:</p>

            <div class="test-controls">
                <button class="btn btn-warning" onclick="performanceStressTest()">
                    <i class="fa fa-bolt"></i> Stress Test (100 rapid hovers)
                </button>
                <button class="btn btn-info" onclick="memoryLeakTest()">
                    <i class="fa fa-memory"></i> Memory Leak Test
                </button>
                <button class="btn btn-success" onclick="performanceReport()">
                    <i class="fa fa-chart-line"></i> Performance Report
                </button>
            </div>

            <div id="performance-results"></div>
        </div>

        <div class="test-section">
            <h3>Test Results Dashboard</h3>
            <div id="test-dashboard">
                <div class="row">
                    <div class="col-md-4">
                        <h4>Passed Tests</h4>
                        <div id="passed-tests" class="test-result test-pass">0</div>
                    </div>
                    <div class="col-md-4">
                        <h4>Failed Tests</h4>
                        <div id="failed-tests" class="test-result test-fail">0</div>
                    </div>
                    <div class="col-md-4">
                        <h4>Total Tests</h4>
                        <div id="total-tests" class="test-result test-pending">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and SweetAlert2 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Sweet Navigation JavaScript -->
    <script src="js/components/sweet-navigation.js"></script>

    <script>
        // Test Suite Variables
        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };

        let testLog = [];

        // Mock AJAX responses for testing
        $(document).ajaxSend(function(event, xhr, settings) {
            logTest('AJAX Request: ' + settings.url, 'info');

            // Mock successful response for test-navigation-data.json
            if (settings.url === '/test-navigation-data.json') {
                setTimeout(function() {
                    const mockData = {
                        success: true,
                        data: {
                            groups: [{
                                name: "Mathematics Program",
                                items: [{
                                    id: "1",
                                    name: "Algebra Fundamentals",
                                    url: "https://example.com/classes/1",
                                    is_current: false,
                                    subtitle: "Teacher: <a href='#' class='sweet-navigation-link'>Dr. Smith</a> | Mon, Wed, Fri 10:00-11:30",
                                    count: "28",
                                    actions: [{
                                        type: "report",
                                        url: "https://example.com/report/1",
                                        title: "Class Report",
                                        label: "R"
                                    }, {
                                        type: "show",
                                        url: "https://example.com/show/1",
                                        title: "View Class",
                                        label: "V"
                                    }, {
                                        type: "reports",
                                        url: "https://example.com/plan/1",
                                        title: "Monthly Plan",
                                        label: "P"
                                    }]
                                }, {
                                    id: "2",
                                    name: "Advanced Calculus",
                                    url: "https://example.com/classes/2",
                                    is_current: true,
                                    subtitle: "Teacher: <a href='#' class='sweet-navigation-link'>Prof. Johnson</a> | Tue, Thu 14:00-16:00",
                                    count: "15",
                                    actions: [{
                                        type: "report",
                                        url: "https://example.com/report/2",
                                        title: "Class Report",
                                        label: "R"
                                    }, {
                                        type: "show",
                                        url: "https://example.com/show/2",
                                        title: "View Class",
                                        label: "V"
                                    }]
                                }]
                            }, {
                                name: "Science Program",
                                items: [{
                                    id: "3",
                                    name: "Physics Laboratory",
                                    url: "https://example.com/classes/3",
                                    is_current: false,
                                    subtitle: "Teacher: <a href='#' class='sweet-navigation-link'>Dr. Wilson</a> | Wed, Fri 16:00-18:00",
                                    count: "20"
                                }]
                            }]
                        }
                    };

                    if (settings.success) {
                        settings.success(mockData);
                        logTest('Mock data loaded successfully', 'success');
                    }
                }, 300);

                xhr.abort();
            }

            // Mock empty response
            if (settings.url === '/test-empty-data.json') {
                setTimeout(function() {
                    if (settings.success) {
                        settings.success({
                            success: true,
                            data: {
                                groups: []
                            },
                            message: "No items found"
                        });
                        logTest('Empty data response', 'info');
                    }
                }, 200);
                xhr.abort();
            }

            // Mock error responses
            if (settings.url === '/invalid-endpoint' || settings.url === '/slow-endpoint') {
                setTimeout(function() {
                    if (settings.error) {
                        settings.error({
                            status: 404,
                            responseJSON: {
                                message: "Endpoint not found"
                            }
                        }, 'error', 'Not Found');
                        logTest('Error response simulated', 'error');
                    }
                }, settings.url === '/slow-endpoint' ? 2000 : 500);
                xhr.abort();
            }
        });

        // Test Functions
        function runAllTests() {
            logTest('=== Starting Comprehensive Test Suite ===', 'info');
            resetTestResults();

            // Run all test categories
            runBasicTests();
            setTimeout(() => runErrorTests(), 1000);
            setTimeout(() => runPerformanceTests(), 2000);
            setTimeout(() => generateTestReport(), 3000);
        }

        function runBasicTests() {
            logTest('--- Running Basic Tests ---', 'info');

            // Test 1: Component Initialization
            testComponentInitialization();

            // Test 2: Hover Configuration
            testHoverConfiguration();

            // Test 3: Data Attribute Parsing
            testDataAttributeParsing();

            // Test 4: Multiple Instance Support
            testMultipleInstances();
        }

        function runErrorTests() {
            logTest('--- Running Error Tests ---', 'warning');

            // Test error handling scenarios
            testErrorHandling();
            testInvalidConfiguration();
            testNetworkErrors();
        }

        function runPerformanceTests() {
            logTest('--- Running Performance Tests ---', 'info');

            // Test performance scenarios
            testRapidHovers();
            testMemoryUsage();
            testEventCleanup();
        }

        function testComponentInitialization() {
            const testName = 'Component Initialization';
            try {
                if (typeof initializeSweetNavigation === 'function') {
                    passTest(testName, 'initializeSweetNavigation function exists');
                } else {
                    failTest(testName, 'initializeSweetNavigation function not found');
                }

                if (window.SweetNavigationConfig) {
                    passTest(testName + ' - Config', 'Global configuration object exists');
                } else {
                    failTest(testName + ' - Config', 'Global configuration object missing');
                }

                const triggerCount = $('.sweet-navigation-trigger').length;
                if (triggerCount > 0) {
                    passTest(testName + ' - Triggers', `Found ${triggerCount} navigation triggers`);
                } else {
                    failTest(testName + ' - Triggers', 'No navigation triggers found');
                }

            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testHoverConfiguration() {
            const testName = 'Hover Configuration';
            try {
                // Test different hover delay configurations
                window.SweetNavigationConfig.hoverDelay = 100;
                $('.test-hover-fast').data('hover-delay', 100);

                window.SweetNavigationConfig.hoverDelay = 500;
                $('.test-hover-slow').data('hover-delay', 500);

                passTest(testName, 'Hover delay configuration successful');
            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testDataAttributeParsing() {
            const testName = 'Data Attribute Parsing';
            try {
                const $trigger = $('.sweet-navigation-trigger').first();
                const ajaxUrl = $trigger.data('ajax-url');
                const title = $trigger.data('title');

                if (ajaxUrl) {
                    passTest(testName + ' - AJAX URL', 'AJAX URL parsed correctly: ' + ajaxUrl);
                } else {
                    failTest(testName + ' - AJAX URL', 'AJAX URL not found');
                }

                if (title) {
                    passTest(testName + ' - Title', 'Title parsed correctly: ' + title);
                } else {
                    failTest(testName + ' - Title', 'Title not found');
                }

            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testMultipleInstances() {
            const testName = 'Multiple Instances';
            try {
                const multiTriggers = $('.sweet-navigation-trigger[class*="test-multi"]').length;
                if (multiTriggers >= 6) {
                    passTest(testName, `Found ${multiTriggers} multiple instance triggers`);
                } else {
                    failTest(testName, `Expected 6+ triggers, found ${multiTriggers}`);
                }
            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testErrorHandling() {
            const testName = 'Error Handling';
            try {
                // Test missing required attributes
                const $invalidTrigger = $('<a href="#" class="sweet-navigation-trigger"></a>');
                $('body').append($invalidTrigger);

                // This should not crash the system
                initializeSweetNavigation();

                $invalidTrigger.remove();
                passTest(testName, 'Invalid configuration handled gracefully');
            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testInvalidConfiguration() {
            const testName = 'Invalid Configuration';
            try {
                // Test with invalid data attributes
                const $trigger = $('.test-ajax-error').first();
                if ($trigger.length) {
                    passTest(testName, 'Invalid endpoint trigger found for testing');
                } else {
                    failTest(testName, 'Invalid endpoint trigger not found');
                }
            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testNetworkErrors() {
            const testName = 'Network Errors';
            try {
                // Network error testing is handled by mock AJAX responses
                passTest(testName, 'Network error simulation configured');
            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testRapidHovers() {
            const testName = 'Rapid Hovers';
            try {
                // Simulate rapid hover events
                let hoverCount = 0;
                const $triggers = $('.sweet-navigation-trigger').slice(0, 3);

                $triggers.each(function() {
                    $(this).trigger('mouseenter');
                    setTimeout(() => $(this).trigger('mouseleave'), 50);
                    hoverCount++;
                });

                passTest(testName, `Simulated ${hoverCount} rapid hovers`);
            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testMemoryUsage() {
            const testName = 'Memory Usage';
            try {
                const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;

                // Simulate memory-intensive operations
                for (let i = 0; i < 10; i++) {
                    initializeSweetNavigation();
                }

                const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                const memoryDiff = finalMemory - initialMemory;

                if (performance.memory) {
                    passTest(testName, `Memory usage: ${(memoryDiff / 1024 / 1024).toFixed(2)} MB`);
                } else {
                    passTest(testName, 'Memory API not available, test skipped');
                }
            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function testEventCleanup() {
            const testName = 'Event Cleanup';
            try {
                // Test that events are properly cleaned up
                const initialEventCount = $._data(document, 'events') ?
                    Object.keys($._data(document, 'events')).length : 0;

                // Add and remove navigation triggers
                const $tempTrigger = $('<a href="#" class="sweet-navigation-trigger" data-ajax-url="/test" data-title="Test"></a>');
                $('body').append($tempTrigger);
                initializeSweetNavigation();
                $tempTrigger.remove();

                passTest(testName, 'Event cleanup test completed');
            } catch (error) {
                failTest(testName, 'Exception: ' + error.message);
            }
        }

        function performanceStressTest() {
            logTest('=== Starting Performance Stress Test ===', 'warning');
            const startTime = performance.now();

            // Simulate 100 rapid hover events
            const $triggers = $('.sweet-navigation-trigger');
            let count = 0;

            const interval = setInterval(() => {
                if (count >= 100) {
                    clearInterval(interval);
                    const endTime = performance.now();
                    const duration = endTime - startTime;

                    logTest(`Stress test completed: 100 hovers in ${duration.toFixed(2)}ms`, 'success');
                    updatePerformanceResults(`Stress Test: ${duration.toFixed(2)}ms for 100 operations`);
                    return;
                }

                const randomTrigger = $triggers.eq(Math.floor(Math.random() * $triggers.length));
                randomTrigger.trigger('mouseenter');
                setTimeout(() => randomTrigger.trigger('mouseleave'), 10);
                count++;
            }, 20);
        }

        function memoryLeakTest() {
            logTest('=== Starting Memory Leak Test ===', 'info');

            if (!performance.memory) {
                logTest('Memory API not available', 'warning');
                return;
            }

            const initialMemory = performance.memory.usedJSHeapSize;

            // Create and destroy many navigation instances
            for (let i = 0; i < 50; i++) {
                const $trigger = $(`<a href="#" class="sweet-navigation-trigger" data-ajax-url="/test-${i}" data-title="Test ${i}"></a>`);
                $('body').append($trigger);
                initializeSweetNavigation();
                $trigger.remove();
            }

            // Force garbage collection if available
            if (window.gc) {
                window.gc();
            }

            setTimeout(() => {
                const finalMemory = performance.memory.usedJSHeapSize;
                const memoryDiff = (finalMemory - initialMemory) / 1024 / 1024;

                logTest(`Memory leak test: ${memoryDiff.toFixed(2)} MB difference`,
                    memoryDiff < 5 ? 'success' : 'warning');
                updatePerformanceResults(`Memory Leak Test: ${memoryDiff.toFixed(2)} MB`);
            }, 1000);
        }

        function performanceReport() {
            const report = {
                triggers: $('.sweet-navigation-trigger').length,
                testsPassed: testResults.passed,
                testsFailed: testResults.failed,
                totalTests: testResults.total,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };

            updatePerformanceResults(`
                <h5>Performance Report</h5>
                <ul>
                    <li>Navigation Triggers: ${report.triggers}</li>
                    <li>Tests Passed: ${report.testsPassed}</li>
                    <li>Tests Failed: ${report.testsFailed}</li>
                    <li>Success Rate: ${((report.testsPassed / report.totalTests) * 100).toFixed(1)}%</li>
                    <li>Browser: ${report.userAgent.split(' ')[0]}</li>
                </ul>
            `);

            logTest('Performance report generated', 'info');
        }

        // Utility Functions
        function logTest(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;

            testLog.push({
                message: logEntry,
                type: type
            });

            const $logContainer = $('#test-log');
            const $entry = $(`<div class="log-entry log-${type}">${logEntry}</div>`);
            $logContainer.append($entry);
            $logContainer.scrollTop($logContainer[0].scrollHeight);
        }

        function passTest(testName, message) {
            testResults.passed++;
            testResults.total++;
            logTest(`✅ PASS: ${testName} - ${message}`, 'success');
            updateTestSummary();
        }

        function failTest(testName, message) {
            testResults.failed++;
            testResults.total++;
            logTest(`❌ FAIL: ${testName} - ${message}`, 'error');
            updateTestSummary();
        }

        function resetTestResults() {
            testResults = {
                passed: 0,
                failed: 0,
                total: 0
            };
            testLog = [];
            $('#test-log').empty();
            updateTestSummary();
        }

        function updateTestSummary() {
            $('#passed-tests').text(testResults.passed);
            $('#failed-tests').text(testResults.failed);
            $('#total-tests').text(testResults.total);

            const successRate = testResults.total > 0 ?
                ((testResults.passed / testResults.total) * 100).toFixed(1) : 0;

            let summaryClass = 'test-pending';
            let summaryText = 'Tests not started';

            if (testResults.total > 0) {
                if (testResults.failed === 0) {
                    summaryClass = 'test-pass';
                    summaryText = `All tests passed! (${testResults.passed}/${testResults.total})`;
                } else if (successRate >= 80) {
                    summaryClass = 'test-pass';
                    summaryText = `Most tests passed (${successRate}% success rate)`;
                } else {
                    summaryClass = 'test-fail';
                    summaryText = `Some tests failed (${successRate}% success rate)`;
                }
            }

            $('#test-summary').html(`<div class="test-result ${summaryClass}">${summaryText}</div>`);
        }

        function updatePerformanceResults(html) {
            $('#performance-results').html(html);
        }

        function clearTestLog() {
            $('#test-log').empty();
            testLog = [];
            logTest('Test log cleared', 'info');
        }

        function generateTestReport() {
            logTest('=== Test Suite Complete ===', 'info');
            logTest(`Final Results: ${testResults.passed} passed, ${testResults.failed} failed, ${testResults.total} total`, 'info');

            const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            logTest(`Success Rate: ${successRate}%`, successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error');
        }

        // Initialize test suite when page loads
        $(document).ready(function() {
            logTest('Sweet Navigation Test Suite initialized', 'info');
            logTest(`Found ${$('.sweet-navigation-trigger').length} navigation triggers`, 'info');

            // Initialize Sweet Navigation
            if (typeof initializeSweetNavigation === 'function') {
                initializeSweetNavigation();
                logTest('Sweet Navigation component initialized', 'success');
            } else {
                logTest('Sweet Navigation component not found', 'error');
            }

            // Set up test configuration
            window.SweetNavigationConfig = window.SweetNavigationConfig || {};
            window.SweetNavigationConfig.debug = true;

            // Auto-run basic tests after a short delay
            setTimeout(() => {
                logTest('Auto-running basic component tests...', 'info');
                testComponentInitialization();
                testDataAttributeParsing();
            }, 1000);
        });

        // Event listeners for manual testing
        $(document).on('mouseenter', '.sweet-navigation-trigger', function() {
            const title = $(this).data('title') || 'Unknown';
            logTest(`Hover detected on: ${title}`, 'info');
        });

        $(document).on('click', '.sweet-navigation-trigger', function(e) {
            e.preventDefault();
            const title = $(this).data('title') || 'Unknown';
            logTest(`Click detected on: ${title}`, 'info');
        });
    </script>
</body>

</html>