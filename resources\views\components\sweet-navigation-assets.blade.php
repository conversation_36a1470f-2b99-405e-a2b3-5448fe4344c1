{{--
Sweet Navigation Assets Component
=================================

This Blade component includes all necessary assets for the Sweet Navigation functionality.
Include this component once per page to enable Sweet Navigation functionality.

Usage:
@include('components.sweet-navigation-assets')

Optional Parameters:
- $includeSweetAlert2: boolean (default: true) - Whether to include SweetAlert2 assets
- $includeJQuery: boolean (default: true) - Whether to include jQuery
- $autoInitialize: boolean (default: true) - Whether to auto-initialize the component

Example with parameters:
@include('components.sweet-navigation-assets', [
    'includeSweetAlert2' => false,  // Don't include if already loaded
    'includeJQuery' => false,       // Don't include if already loaded
    'autoInitialize' => true        // Auto-initialize on DOM ready
])

Dependencies:
- jQuery 3.6.0+
- SweetAlert2 11.0+
- Font Awesome 4.7+ (for icons)

Browser Support:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
--}}

@php
    // Set default values for parameters
    $includeSweetAlert2 = $includeSweetAlert2 ?? true;
    $includeJQuery = $includeJQuery ?? true;
    $autoInitialize = $autoInitialize ?? true;
@endphp

{{-- jQuery (if not already included) --}}
@if($includeJQuery)
    @push('head-scripts')
        <script src="https://code.jquery.com/jquery-3.6.0.min.js" 
                integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" 
                crossorigin="anonymous"></script>
    @endpush
@endif

{{-- SweetAlert2 Assets (if not already included) --}}
@if($includeSweetAlert2)
    @push('head-styles')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    @endpush
    
    @push('head-scripts')
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    @endpush
@endif

{{-- Sweet Navigation CSS --}}
@push('head-styles')
    <link rel="stylesheet" href="{{ asset('css/components/sweet-navigation.css') }}">
@endpush

{{-- Sweet Navigation JavaScript --}}
@push('body-scripts')
    <script src="{{ asset('js/components/sweet-navigation.js') }}"></script>
@endpush

{{-- Auto-initialization Script --}}
@if($autoInitialize)
    @push('body-scripts')
        <script>
            $(document).ready(function() {
                // Initialize Sweet Navigation component
                if (typeof initializeSweetNavigation === 'function') {
                    initializeSweetNavigation();
                    console.log('Sweet Navigation: Auto-initialized successfully');
                } else {
                    console.error('Sweet Navigation: initializeSweetNavigation function not found');
                }
            });
        </script>
    @endpush
@endif

{{-- Configuration Script (Optional Global Config) --}}
@push('body-scripts')
    <script>
        // Global Sweet Navigation Configuration
        // You can override these values in your page-specific scripts
        window.SweetNavigationConfig = window.SweetNavigationConfig || {
            defaultWidth: '800px',
            animationDuration: 300,
            searchPlaceholder: 'Search...',
            hoverDelay: 200,
            debug: {{ config('app.debug') ? 'true' : 'false' }}
        };
        
        @if(config('app.debug'))
            console.log('Sweet Navigation: Configuration loaded', window.SweetNavigationConfig);
        @endif
    </script>
@endpush

{{-- 
Usage Instructions:
==================

1. Basic Usage (include once per page):
   @include('components.sweet-navigation-assets')

2. Add to any element to make it a navigation trigger:
   <a href="#" class="sweet-navigation-trigger"
      data-ajax-url="{{ route('api.navigation.classes') }}"
      data-title="Class Navigation"
      data-current-id="{{ $currentClassId }}">
       <i class="fa fa-list"></i> Navigate Classes
   </a>

3. Required Data Attributes:
   - data-ajax-url: The endpoint that returns navigation data
   - data-title: Title for the popup

4. Optional Data Attributes:
   - data-current-id: ID of current item (for highlighting)
   - data-confirm-button-text: Custom confirm button text
   - data-confirm-button-url: URL for confirm button action
   - data-width: Custom popup width (default: 800px)

5. Expected JSON Response Format:
   {
       "success": true,
       "data": {
           "groups": [
               {
                   "name": "Group Name",
                   "items": [
                       {
                           "id": "1",
                           "name": "Item Name",
                           "url": "/path/to/item",
                           "is_current": false,
                           "subtitle": "Optional subtitle",
                           "count": "25",
                           "actions": [
                               {
                                   "type": "report",
                                   "url": "/report/1",
                                   "title": "Report",
                                   "label": "R"
                               }
                           ]
                       }
                   ]
               }
           ]
       }
   }

6. Manual Re-initialization (if needed):
   initializeSweetNavigation();

7. Custom Configuration:
   window.SweetNavigationConfig.hoverDelay = 500; // Custom hover delay
   initializeSweetNavigation(); // Re-initialize with new config
--}}
