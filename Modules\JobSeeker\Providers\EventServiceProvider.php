<?php

declare(strict_types=1);

namespace Mo<PERSON>les\JobSeeker\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Mo<PERSON>les\JobSeeker\Events\JobProcessedEvent;
use Mo<PERSON><PERSON>\JobSeeker\Listeners\JobNotificationListener;
use <PERSON><PERSON><PERSON>\JobSeeker\Listeners\NotificationFailedListener;
use Illuminate\Notifications\Events\NotificationFailed;

final class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        JobProcessedEvent::class => [
            JobNotificationListener::class,
        ],
        NotificationFailed::class => [
            NotificationFailedListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot(): void
    {
        parent::boot();
    }
} 