<?php

/**
 * Test script to verify the complete Category and Location Fetching flow
 * 
 * This script tests:
 * 1. Schedule Rule ID → CommandScheduleFilter lookup
 * 2. Filter categories/locations → ProviderJobCategory/Location lookup  
 * 3. Provider identifiers → API request parameters
 */

require_once __DIR__ . '/bootstrap/app.php';

use Mo<PERSON>les\JobSeeker\Repositories\FilterRepository;
use Modules\JobSeeker\Entities\CommandScheduleFilter;
use Modules\JobSeeker\Entities\ProviderJobCategory;
use Modules\JobSeeker\Entities\ProviderJobLocation;

echo "=== Testing Category and Location Fetching Flow ===\n\n";

try {
    // Test data: Schedule Rule ID 32
    $scheduleRuleId = 32;
    
    echo "1. Testing Schedule Rule ID: {$scheduleRuleId}\n";
    
    // Step 1: Get CommandScheduleFilter
    $filter = CommandScheduleFilter::where('schedule_rule_id', $scheduleRuleId)->first();
    
    if (!$filter) {
        echo "❌ ERROR: No CommandScheduleFilter found for rule ID {$scheduleRuleId}\n";
        exit(1);
    }
    
    echo "✅ Found CommandScheduleFilter:\n";
    echo "   - Categories: " . json_encode($filter->categories) . "\n";
    echo "   - Locations: " . json_encode($filter->locations) . "\n\n";
    
    // Step 2: Test Jobs.af category translation
    echo "2. Testing Jobs.af Category Translation:\n";
    
    if (!empty($filter->categories)) {
        $jobsAfCategories = ProviderJobCategory::getProviderIdentifiers($filter->categories, 'jobs.af');
        echo "   - Input Category IDs: " . json_encode($filter->categories) . "\n";
        echo "   - Jobs.af Identifiers: " . json_encode($jobsAfCategories) . "\n";
    } else {
        echo "   - No categories to translate\n";
        $jobsAfCategories = [];
    }
    
    // Step 3: Test Jobs.af location translation
    echo "\n3. Testing Jobs.af Location Translation:\n";
    
    if (!empty($filter->locations)) {
        $jobsAfLocations = ProviderJobLocation::getProviderIdentifiers($filter->locations, 'jobs.af');
        echo "   - Input Location IDs: " . json_encode($filter->locations) . "\n";
        echo "   - Jobs.af Identifiers: " . json_encode($jobsAfLocations) . "\n";
    } else {
        echo "   - No locations to translate\n";
        $jobsAfLocations = [];
    }
    
    // Step 4: Test ACBAR category translation
    echo "\n4. Testing ACBAR Category Translation:\n";
    
    if (!empty($filter->categories)) {
        $acbarCategories = ProviderJobCategory::getProviderIdentifiers($filter->categories, 'acbar');
        echo "   - Input Category IDs: " . json_encode($filter->categories) . "\n";
        echo "   - ACBAR Identifiers: " . json_encode($acbarCategories) . "\n";
    } else {
        echo "   - No categories to translate\n";
        $acbarCategories = [];
    }
    
    // Step 5: Test ACBAR location translation
    echo "\n5. Testing ACBAR Location Translation:\n";
    
    if (!empty($filter->locations)) {
        $acbarLocations = ProviderJobLocation::getProviderIdentifiers($filter->locations, 'acbar');
        echo "   - Input Location IDs: " . json_encode($filter->locations) . "\n";
        echo "   - ACBAR Identifiers: " . json_encode($acbarLocations) . "\n";
    } else {
        echo "   - No locations to translate\n";
        $acbarLocations = [];
    }
    
    // Step 6: Test FilterRepository integration
    echo "\n6. Testing FilterRepository Integration:\n";
    
    $filterRepository = app(FilterRepository::class);
    
    // Test Jobs.af filters
    echo "\n   6a. Jobs.af Translated Filters:\n";
    $jobsAfFilters = $filterRepository->getJobsAfTranslatedFilters($scheduleRuleId);
    echo "   - Categories: " . json_encode($jobsAfFilters['searchFilters']['categories'] ?? []) . "\n";
    echo "   - Locations: " . json_encode($jobsAfFilters['searchFilters']['locations'] ?? []) . "\n";
    
    // Test ACBAR filters
    echo "\n   6b. ACBAR Translated Filters:\n";
    $acbarFilters = $filterRepository->getAcbarTranslatedFilters($scheduleRuleId);
    echo "   - Category IDs: " . json_encode($acbarFilters['category_ids'] ?? []) . "\n";
    echo "   - Location IDs: " . json_encode($acbarFilters['location_ids'] ?? []) . "\n";
    
    // Step 7: Validation
    echo "\n7. Validation:\n";
    
    $success = true;
    
    // Validate Jobs.af
    if ($jobsAfFilters['searchFilters']['categories'] !== $jobsAfCategories) {
        echo "❌ Jobs.af categories mismatch!\n";
        $success = false;
    } else {
        echo "✅ Jobs.af categories match\n";
    }
    
    if ($jobsAfFilters['searchFilters']['locations'] !== $jobsAfLocations) {
        echo "❌ Jobs.af locations mismatch!\n";
        $success = false;
    } else {
        echo "✅ Jobs.af locations match\n";
    }
    
    // Validate ACBAR
    $expectedAcbarCategories = array_map('intval', $acbarCategories);
    if ($acbarFilters['category_ids'] !== $expectedAcbarCategories) {
        echo "❌ ACBAR categories mismatch!\n";
        $success = false;
    } else {
        echo "✅ ACBAR categories match\n";
    }
    
    $expectedAcbarLocations = array_map('intval', $acbarLocations);
    if ($acbarFilters['location_ids'] !== $expectedAcbarLocations) {
        echo "❌ ACBAR locations mismatch!\n";
        $success = false;
    } else {
        echo "✅ ACBAR locations match\n";
    }
    
    // Final result
    echo "\n=== FINAL RESULT ===\n";
    if ($success) {
        echo "🎉 SUCCESS: Complete flow working correctly!\n";
        echo "   - No hardcoded values detected\n";
        echo "   - Dynamic category and location fetching operational\n";
        echo "   - Both Jobs.af and ACBAR providers supported\n";
    } else {
        echo "❌ FAILURE: Issues detected in the flow\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
