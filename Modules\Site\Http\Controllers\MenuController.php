<?php

namespace Modules\Site\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Menu;
use Session;

class MenuController extends Controller
{
    public function __construct()
    {
        // $this->middleware('permission:view menu|create menu|edit menu|delete menu', ['only' => ['index','show']]);
        $this->middleware('permission:create menu', ['only' => ['create','store']]);
        $this->middleware('permission:edit menu', ['only' => ['edit','update', 'sort']]);
        $this->middleware('permission:delete menu', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {

        $menu_detail = Menu::paginate(25);
        $menu_elements = [];
        
        $menu = Menu::orderBy('order' , 'ASC')
                                ->get()
                                ->toArray();  

        foreach ($menu as $key => $element) {

            if($element['parent'] == 0){
                $menu_elements[$element['id']] = $element;
            }
        }

        foreach ($menu as $key => $element) {
            if($element['parent'] > 0 && isset($menu_elements[$element['parent']])){
                $menu_elements[$element['parent']]['children'][] = $element;
            }else{
                $menu_elements[$element['id']] = $element;                
            }
        }

        if(isset($edit_mode)){
            return view(theme_path('menu.index'));

        }else{
            return view('site::menu.index', compact('menu_detail' , 'menu_elements'));
        }
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        $menus = Menu::all()->pluck('title', 'id');

        $languages = config('app.locales');


        if(isset($edit_mode)){
            return view(theme_path('menu_page'));
        }

        return view('site::menu.create' , compact('languages' ,'menus'));
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
     public function store(Request $request)
     {
        if($request->type == 3){
            $request->slug= "";
        }
        if(!$request->parent){
            $request->parent = 0;
        }

        $request->slug = \Illuminate\Support\Str::slug($request->slug);
        
        $this->validateMenu($request);
         $requestData = $request->all();
         
         $menu =  new Menu;
 
         $menu->parent = $request->parent;
         $menu->status = $request->status;
         $menu->slug = $request->slug;
         $menu->type = $request->type;
         $menu->order = Menu::all()->count();
         $menu->organization_id  = config('organization_id');
 
 
         foreach ($request->translate as $code => $translate) {
             $menu->translateOrNew($code)->title = $translate['title'];
             $menu->translateOrNew($code)->content = $translate['content'];
         }
 
         $menu->save();
 
        flash('Menu added!');
 
         return redirect(route('menu.index'));
 
     }

     
    /**
     * Save the nesty order of the menu elements .
     * @param  Request $request
     * @return Response
     */
    public function sort(Request $request)
    {
        $requestData = $request->all();


        $i = 0;
        
        foreach($request->elements as $element){
            $i++;
            $menu = Menu::findOrFail($element['id']);

            $menu_id = $menu->id;

            $menu->parent = 0;

            $menu->order = $i;

            $menu->save();

            if(isset($element['children'])){
                foreach($element['children'] as $child_menu){
                    $i++;
                    $child_menu = Menu::findOrFail($child_menu['id']);

                    $child_menu->parent = $menu_id;
                    $child_menu->order = $i;
                    $child_menu->save();
                }
            }
        }

        return ["result" => "sorted"];

    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show($slug, Menu $menu)
    {


        $menu = Menu::where('slug' , '=' , \Illuminate\Support\Str::slug($slug))->first();

        if($menu){
            return view(theme_path('menu_page') , compact('menu'));
        }

        return 'Opps!! No Page';
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $menus = Menu::all()->reject(function ($value, $key) use($id) {
            return $value->id == $id;
        })->pluck('title', 'id');

        $languages = config('app.locales');

        $menu_detail = Menu::findOrFail($id);


        if(isset($edit_mode)){
            return view(theme_path('menu_page'));
        }

        return view('site::menu.edit' , compact('languages' ,'menus' ,'menu_detail'));
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request , $id)
    {
        $request->id = $id;

        if($request->type == 3){
            $request->slug= "";
        }
        if(!$request->parent){
            $request->parent = 0;
        }
        if($request->type != 2){
            $request->slug = \Illuminate\Support\Str::slug($request->slug);
        }

        $this->validateMenu($request);

        $requestData = $request->all();

        
        $menu = Menu::findOrFail($id);

        $menu->parent = $request->parent;
        $menu->type = $request->type;
        $menu->slug = $request->slug;
        $menu->status = $request->status;

        foreach ($request->translate as $code => $translate) {

            $menu->translateOrNew($code)->title = $translate['title'];
            $menu->translateOrNew($code)->content = $translate['content'];
        }

        $menu->save();
        
        flash('Menu updated!');

        return redirect(route('menu.index'));

    }

    public function updateInplace(Request $request , $id)
    {
        $request->id = $id;


        $request->slug = \Illuminate\Support\Str::slug($request->slug);

        $this->validate($request , [
            "title" => 'required|min:3',
            "content" => 'required|min:20',
            "language" => 'required'
        ]);
        
        $menu = Menu::findOrFail($id);

        $menu->translateOrNew($request->language)->title = $request->title;
        $menu->translateOrNew($request->language)->content = $request->content;

        $menu->save();

       flash('Menu updated!');

        return response()->json([
            "status" => "success"
        ]);

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
        Menu::destroy(request()->menu);

       flash('Menu deleted!');

        return redirect(route('menu.index'));

    }

    private function validateMenu($request){


        $rules = [];
        if($request->type != 3){
            $ignore = '';

            if(isset($request->id)){
                $ignore = ','.$request->id;
            }
            if($request->type == 2){
                $rules['slug'] = 'required';                
            }else{
                $rules['slug'] = 'required|alpha_dash|unique:menus,slug'.$ignore;                
            }

        }


        // we ignore checking title when user wants to disable a page
       if($request->status == "1"){

           $rules['translate.*.title'] = 'required|min:3';
       }

        // dd($request->all() , $rules);
        
        $this->validate($request , $rules);
    }
}
