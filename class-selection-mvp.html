<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Class Selection MVP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .demo-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .demo-header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .demo-content {
            padding: 40px;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .filter-card {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
        }
        
        .filter-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #3b82f6;
        }
        
        .filter-card.active {
            border-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.2);
        }
        
        .filter-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .filter-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .filter-label {
            flex: 1;
            margin-left: 16px;
        }
        
        .filter-label label {
            font-weight: 600;
            color: #1f2937;
            font-size: 16px;
        }
        
        .filter-status {
            color: #10b981;
            font-size: 20px;
        }
        
        .filter-card-body {
            padding: 20px;
        }
        
        .filter-value {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 48px;
        }
        
        .filter-value:hover {
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }
        
        .filter-card.active .filter-value {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .count-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 24px;
            height: 24px;
            background: #e5e7eb;
            color: #6b7280;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
            transition: all 0.3s ease;
        }
        
        .count-badge.has-selection {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }
        
        .filter-arrow {
            color: #9ca3af;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .filter-card:hover .filter-arrow {
            color: #3b82f6;
            transform: translateX(2px);
        }
        
        .filter-card.active .filter-arrow {
            color: #10b981;
        }
        
        .demo-actions {
            text-align: center;
            padding: 20px 0;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        /* Class Selection Panel */
        
        .class-selection-panel {
            position: fixed;
            top: 0;
            right: -500px;
            width: 500px;
            height: 100vh;
            background: white;
            box-shadow: -8px 0 32px rgba(0, 0, 0, 0.15);
            z-index: 10001;
            transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
            border-left: 1px solid #e5e7eb;
        }
        
        .class-selection-panel.open {
            right: 0;
        }
        
        .class-panel-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            backdrop-filter: blur(4px);
        }
        
        .class-panel-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .class-panel-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        
        .class-panel-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .class-panel-title h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .class-panel-close {
            background: none;
            border: none;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .class-panel-close:hover {
            background: #f3f4f6;
            color: #374151;
            transform: scale(1.1);
        }
        
        .class-panel-subtitle {
            color: #6b7280;
            font-size: 14px;
            margin: 0;
        }
        
        .class-panel-progress {
            background: #e5e7eb;
            height: 4px;
            border-radius: 2px;
            margin-top: 12px;
            overflow: hidden;
        }
        
        .class-panel-progress-bar {
            background: linear-gradient(90deg, #10b981, #059669);
            height: 100%;
            width: 66%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .class-panel-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .class-search-box {
            position: relative;
            margin-bottom: 20px;
        }
        
        .class-search-input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f9fafb;
        }
        
        .class-search-input:focus {
            outline: none;
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .class-search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 14px;
        }
        
        .class-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 12px 16px;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .class-stats-text {
            color: #1e40af;
            font-size: 14px;
            font-weight: 500;
        }
        
        .class-quick-filters {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .class-filter-btn {
            padding: 6px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 20px;
            background: white;
            color: #6b7280;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .class-filter-btn:hover,
        .class-filter-btn.active {
            border-color: #3b82f6;
            color: #3b82f6;
            background: #f0f9ff;
        }
        /* Center Sections */
        
        .center-section {
            margin-bottom: 24px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            background: white;
        }
        
        .center-section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .center-section-header:hover {
            background: #f1f5f9;
        }
        
        .center-section-header.expanded {
            background: #eff6ff;
            border-bottom-color: #3b82f6;
        }
        
        .center-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .center-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .center-details h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .center-details span {
            font-size: 13px;
            color: #6b7280;
        }
        
        .center-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .select-all-btn {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #6b7280;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .select-all-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .expand-icon {
            color: #9ca3af;
            transition: transform 0.3s ease;
        }
        
        .center-section-header.expanded .expand-icon {
            transform: rotate(90deg);
        }
        
        .center-classes {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .center-classes.expanded {
            max-height: 1000px;
            padding: 16px;
        }
        
        .class-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            position: relative;
            overflow: hidden;
        }
        
        .class-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .class-item:hover::before {
            left: 100%;
        }
        
        .class-item:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .class-item.selected {
            border-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.15);
        }
        
        .class-item.selected::after {
            content: '✓';
            position: absolute;
            top: 12px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: #10b981;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .class-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .class-item.selected .class-checkbox {
            background: #10b981;
            border-color: #10b981;
            color: white;
        }
        
        .class-info {
            flex: 1;
        }
        
        .class-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            font-size: 15px;
        }
        
        .class-item.selected .class-name {
            color: #059669;
        }
        
        .class-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #6b7280;
            font-size: 13px;
        }
        
        .class-meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .class-panel-summary {
            padding: 20px 24px;
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        
        .class-summary-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .class-selected-count {
            font-weight: 600;
            color: #374151;
        }
        
        .class-action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .class-btn {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
        }
        
        .class-btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }
        
        .class-btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .class-btn-primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }
        
        .class-btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        /* Loading and Empty States */
        
        .loading-state,
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            color: #9ca3af;
            text-align: center;
        }
        
        .loading-state i,
        .empty-state i {
            font-size: 32px;
            margin-bottom: 12px;
            color: #d1d5db;
        }
        
        .loading-state span,
        .empty-state p {
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 8px 0;
        }
        
        .empty-state small {
            font-size: 14px;
            color: #9ca3af;
        }
        /* Animations */
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes pulse {
            0%,
            100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.5s ease-out;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        /* Responsive */
        
        @media (max-width: 768px) {
            .class-selection-panel {
                width: 100%;
                right: -100%;
            }
            .demo-container {
                margin: 20px;
                border-radius: 12px;
            }
            .filter-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-graduation-cap"></i> Enhanced Class Selection MVP</h1>
            <p>Experience the next-generation interface for educational reporting</p>
        </div>

        <div class="demo-content">
            <div class="filter-grid">
                <!-- Centers Filter Card -->
                <div class="filter-card active">
                    <div class="filter-card-header">
                        <div class="filter-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="filter-label">
                            <label>Centers</label>
                        </div>
                        <div class="filter-status">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="filter-card-body">
                        <div class="filter-value">
                            <span>3 centers selected</span>
                            <span class="count-badge has-selection">3</span>
                            <i class="fas fa-chevron-right filter-arrow"></i>
                        </div>
                    </div>
                </div>

                <!-- Classes Filter Card -->
                <div class="filter-card" id="classFilterCard">
                    <div class="filter-card-header">
                        <div class="filter-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="filter-label">
                            <label>Classes</label>
                        </div>
                        <div class="filter-status" id="classStatus">
                            <i class="fas fa-circle"></i>
                        </div>
                    </div>
                    <div class="filter-card-body">
                        <div class="filter-value" id="classFilterValue">
                            <span id="classSelectionText">Select classes</span>
                            <span class="count-badge" id="classCountBadge">0</span>
                            <i class="fas fa-chevron-right filter-arrow"></i>
                        </div>
                    </div>
                </div>

                <!-- Students Filter Card -->
                <div class="filter-card">
                    <div class="filter-card-header">
                        <div class="filter-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="filter-label">
                            <label>Students</label>
                        </div>
                        <div class="filter-status">
                            <i class="fas fa-circle"></i>
                        </div>
                    </div>
                    <div class="filter-card-body">
                        <div class="filter-value">
                            <span>Select students</span>
                            <span class="count-badge">0</span>
                            <i class="fas fa-chevron-right filter-arrow"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-actions">
                <button class="demo-btn" onclick="openClassPanel()">
                    <i class="fas fa-mouse-pointer"></i> Click Classes Card to Experience
                </button>
                <button class="demo-btn" onclick="resetDemo()">
                    <i class="fas fa-redo"></i> Reset Demo
                </button>
            </div>
        </div>
    </div>

    <!-- Class Selection Panel -->
    <div class="class-selection-panel" id="classPanel">
        <div class="class-panel-header">
            <div class="class-panel-title">
                <h4><i class="fas fa-chalkboard-teacher"></i> Class Selection</h4>
                <button class="class-panel-close" id="closeClassPanel">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="class-panel-subtitle">From 3 selected centers • Step 2 of 3</div>
            <div class="class-panel-progress">
                <div class="class-panel-progress-bar"></div>
            </div>
        </div>

        <div class="class-panel-body">
            <div class="class-search-box">
                <i class="fas fa-search class-search-icon"></i>
                <input type="text" id="classSearchInput" class="class-search-input" placeholder="Search classes, teachers, or schedules..." autocomplete="off">
            </div>

            <div class="class-stats">
                <div class="class-stats-text">
                    <i class="fas fa-info-circle"></i>
                    <span id="classStatsText">45 classes available across 3 centers</span>
                </div>
            </div>

            <div class="class-quick-filters">
                <button class="class-filter-btn active" data-filter="all">
                    <i class="fas fa-list"></i> All Classes
                </button>
                <button class="class-filter-btn" data-filter="active">
                    <i class="fas fa-check-circle"></i> Active Only
                </button>
                <button class="class-filter-btn" data-filter="high-enrollment">
                    <i class="fas fa-users"></i> High Enrollment
                </button>
                <button class="class-filter-btn" data-filter="recent">
                    <i class="fas fa-clock"></i> Recent Activity
                </button>
            </div>

            <div class="class-list-container" id="classListContainer">
                <!-- Classes will be populated here -->
            </div>
        </div>

        <div class="class-panel-summary">
            <div class="class-summary-content">
                <div class="class-selected-count">
                    <span id="classSelectedCount">0</span> classes selected
                </div>
                <div class="class-action-buttons">
                    <button class="class-btn class-btn-outline" id="clearClassSelection">Clear</button>
                    <button class="class-btn class-btn-primary" id="applyClassSelection">Apply Selection</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Panel Overlay -->
    <div class="class-panel-overlay" id="classPanelOverlay"></div>

    <script>
        // Demo Data
        const demoData = {
            centers: [{
                id: 1,
                name: "Idaman Male Center",
                classes: [{
                    id: 101,
                    name: "Advanced Quran Memorization",
                    teacher: "Ahmad Ali",
                    students: 24,
                    schedule: "Mon-Wed-Fri",
                    time: "2:00 PM",
                    level: "Advanced",
                    status: "active"
                }, {
                    id: 102,
                    name: "Beginner Tajweed",
                    teacher: "Omar Hassan",
                    students: 18,
                    schedule: "Tue-Thu",
                    time: "10:00 AM",
                    level: "Beginner",
                    status: "active"
                }, {
                    id: 103,
                    name: "Intermediate Quran",
                    teacher: "Yusuf Ahmed",
                    students: 32,
                    schedule: "Daily",
                    time: "4:00 PM",
                    level: "Intermediate",
                    status: "active"
                }, {
                    id: 104,
                    name: "Islamic Studies",
                    teacher: "Mahmoud Ali",
                    students: 15,
                    schedule: "Sat-Sun",
                    time: "9:00 AM",
                    level: "All Levels",
                    status: "active"
                }, {
                    id: 105,
                    name: "Quran Recitation",
                    teacher: "Ibrahim Khan",
                    students: 28,
                    schedule: "Mon-Wed-Fri",
                    time: "6:00 PM",
                    level: "Intermediate",
                    status: "active"
                }]
            }, {
                id: 2,
                name: "Idaman Female Center",
                classes: [{
                    id: 201,
                    name: "Women's Quran Circle",
                    teacher: "Fatima Hassan",
                    students: 22,
                    schedule: "Tue-Thu",
                    time: "10:00 AM",
                    level: "All Levels",
                    status: "active"
                }, {
                    id: 202,
                    name: "Advanced Tajweed",
                    teacher: "Aisha Ahmed",
                    students: 16,
                    schedule: "Mon-Wed-Fri",
                    time: "11:00 AM",
                    level: "Advanced",
                    status: "active"
                }, {
                    id: 203,
                    name: "Children's Quran",
                    teacher: "Maryam Ali",
                    students: 35,
                    schedule: "Daily",
                    time: "3:00 PM",
                    level: "Children",
                    status: "active"
                }, {
                    id: 204,
                    name: "Evening Memorization",
                    teacher: "Khadija Omar",
                    students: 19,
                    schedule: "Mon-Wed-Fri",
                    time: "7:00 PM",
                    level: "Intermediate",
                    status: "active"
                }]
            }, {
                id: 3,
                name: "Online Learning Center",
                classes: [{
                    id: 301,
                    name: "Virtual Quran Sessions",
                    teacher: "Dr. Abdullah",
                    students: 45,
                    schedule: "Daily",
                    time: "8:00 PM",
                    level: "All Levels",
                    status: "active"
                }, {
                    id: 302,
                    name: "Online Tajweed Course",
                    teacher: "Ustadh Bilal",
                    students: 38,
                    schedule: "Tue-Thu-Sat",
                    time: "9:00 PM",
                    level: "Intermediate",
                    status: "active"
                }, {
                    id: 303,
                    name: "Weekend Islamic Studies",
                    teacher: "Sheikh Hamza",
                    students: 27,
                    schedule: "Sat-Sun",
                    time: "2:00 PM",
                    level: "Advanced",
                    status: "active"
                }]
            }]
        };

        // State Management
        let classPanel = {
            isOpen: false,
            selectedClasses: [],
            filteredClasses: [],
            searchTerm: '',
            activeFilter: 'all'
        };

        // DOM Elements
        const elements = {
            classFilterCard: document.getElementById('classFilterCard'),
            classPanel: document.getElementById('classPanel'),
            classPanelOverlay: document.getElementById('classPanelOverlay'),
            closeClassPanel: document.getElementById('closeClassPanel'),
            classSearchInput: document.getElementById('classSearchInput'),
            classListContainer: document.getElementById('classListContainer'),
            classSelectedCount: document.getElementById('classSelectedCount'),
            classSelectionText: document.getElementById('classSelectionText'),
            classCountBadge: document.getElementById('classCountBadge'),
            classStatus: document.getElementById('classStatus'),
            clearClassSelection: document.getElementById('clearClassSelection'),
            applyClassSelection: document.getElementById('applyClassSelection'),
            classStatsText: document.getElementById('classStatsText')
        };

        // Utility Functions
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function animateNumber(element, start, end, duration = 300) {
            const range = end - start;
            const increment = range / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
                    current = end;
                    clearInterval(timer);
                }
                element.textContent = Math.round(current);
            }, 16);
        }

        // Panel Management Functions
        function openClassPanel() {
            classPanel.isOpen = true;
            elements.classPanel.classList.add('open');
            elements.classPanelOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Load classes with animation
            setTimeout(() => {
                loadClasses();
            }, 200);

            // Focus search input
            setTimeout(() => {
                elements.classSearchInput.focus();
            }, 400);
        }

        function closeClassPanel() {
            classPanel.isOpen = false;
            elements.classPanel.classList.remove('open');
            elements.classPanelOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        function loadClasses() {
            elements.classListContainer.innerHTML = '<div class="loading-state pulse"><i class="fas fa-spinner fa-spin"></i><span>Loading classes...</span></div>';

            setTimeout(() => {
                renderClasses();
            }, 800);
        }

        function renderClasses() {
            let html = '';

            demoData.centers.forEach(center => {
                const centerClasses = center.classes.filter(cls =>
                    filterClass(cls) && searchClass(cls, classPanel.searchTerm)
                );

                if (centerClasses.length === 0) return;

                const selectedInCenter = centerClasses.filter(cls =>
                    classPanel.selectedClasses.includes(cls.id)
                ).length;

                html += `
                    <div class="center-section fade-in-up">
                        <div class="center-section-header ${centerClasses.length > 0 ? 'expanded' : ''}"
                             onclick="toggleCenterSection(this)">
                            <div class="center-info">
                                <div class="center-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="center-details">
                                    <h5>${center.name}</h5>
                                    <span>${centerClasses.length} classes • ${selectedInCenter} selected</span>
                                </div>
                            </div>
                            <div class="center-actions">
                                <button class="select-all-btn" onclick="selectAllInCenter(event, ${center.id})">
                                    ${selectedInCenter === centerClasses.length ? 'Deselect All' : 'Select All'}
                                </button>
                                <i class="fas fa-chevron-right expand-icon"></i>
                            </div>
                        </div>
                        <div class="center-classes expanded">
                            ${centerClasses.map(cls => renderClassItem(cls)).join('')}
                        </div>
                    </div>
                `;
            });

            if (html === '') {
                html = `
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <p>No classes found</p>
                        <small>Try adjusting your search or filters</small>
                    </div>
                `;
            }

            elements.classListContainer.innerHTML = html;
            updateStats();
        }

        function renderClassItem(cls) {
            const isSelected = classPanel.selectedClasses.includes(cls.id);
            const highlightedName = highlightSearchTerm(cls.name, classPanel.searchTerm);
            const highlightedTeacher = highlightSearchTerm(cls.teacher, classPanel.searchTerm);

            return `
                <div class="class-item ${isSelected ? 'selected' : ''}" onclick="toggleClassSelection(${cls.id})">
                    <div class="class-checkbox">
                        ${isSelected ? '<i class="fas fa-check"></i>' : ''}
                    </div>
                    <div class="class-info">
                        <div class="class-name">${highlightedName}</div>
                        <div class="class-meta">
                            <div class="class-meta-item">
                                <i class="fas fa-user-tie"></i>
                                <span>${highlightedTeacher}</span>
                            </div>
                            <div class="class-meta-item">
                                <i class="fas fa-users"></i>
                                <span>${cls.students} students</span>
                            </div>
                            <div class="class-meta-item">
                                <i class="fas fa-calendar"></i>
                                <span>${cls.schedule}</span>
                            </div>
                            <div class="class-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>${cls.time}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function highlightSearchTerm(text, searchTerm) {
            if (!searchTerm) return text;
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return text.replace(regex, '<mark style="background: #fef08a; padding: 1px 2px; border-radius: 2px;">$1</mark>');
        }

        function filterClass(cls) {
            switch (classPanel.activeFilter) {
                case 'active':
                    return cls.status === 'active';
                case 'high-enrollment':
                    return cls.students >= 25;
                case 'recent':
                    return true; // For demo, all classes are "recent"
                default:
                    return true;
            }
        }

        function searchClass(cls, searchTerm) {
            if (!searchTerm) return true;
            const term = searchTerm.toLowerCase();
            return cls.name.toLowerCase().includes(term) ||
                cls.teacher.toLowerCase().includes(term) ||
                cls.schedule.toLowerCase().includes(term) ||
                cls.time.toLowerCase().includes(term);
        }

        function toggleClassSelection(classId) {
            const index = classPanel.selectedClasses.indexOf(classId);
            if (index > -1) {
                classPanel.selectedClasses.splice(index, 1);
            } else {
                classPanel.selectedClasses.push(classId);
            }

            renderClasses();
            updateSelectionDisplay();
        }

        function selectAllInCenter(event, centerId) {
            event.stopPropagation();

            const center = demoData.centers.find(c => c.id === centerId);
            const centerClasses = center.classes.filter(cls =>
                filterClass(cls) && searchClass(cls, classPanel.searchTerm)
            );

            const allSelected = centerClasses.every(cls =>
                classPanel.selectedClasses.includes(cls.id)
            );

            if (allSelected) {
                // Deselect all in center
                centerClasses.forEach(cls => {
                    const index = classPanel.selectedClasses.indexOf(cls.id);
                    if (index > -1) {
                        classPanel.selectedClasses.splice(index, 1);
                    }
                });
            } else {
                // Select all in center
                centerClasses.forEach(cls => {
                    if (!classPanel.selectedClasses.includes(cls.id)) {
                        classPanel.selectedClasses.push(cls.id);
                    }
                });
            }

            renderClasses();
            updateSelectionDisplay();
        }

        function toggleCenterSection(header) {
            const section = header.parentElement;
            const classes = section.querySelector('.center-classes');
            const icon = header.querySelector('.expand-icon');

            header.classList.toggle('expanded');
            classes.classList.toggle('expanded');
        }

        function updateSelectionDisplay() {
            const count = classPanel.selectedClasses.length;

            // Update counter with animation
            const currentCount = parseInt(elements.classSelectedCount.textContent);
            if (currentCount !== count) {
                animateNumber(elements.classSelectedCount, currentCount, count);
            }

            // Update filter card
            if (count > 0) {
                elements.classSelectionText.textContent = `${count} classes selected`;
                elements.classCountBadge.textContent = count;
                elements.classCountBadge.classList.add('has-selection');
                elements.classFilterCard.classList.add('active');
                elements.classStatus.innerHTML = '<i class="fas fa-check-circle"></i>';
            } else {
                elements.classSelectionText.textContent = 'Select classes';
                elements.classCountBadge.textContent = '0';
                elements.classCountBadge.classList.remove('has-selection');
                elements.classFilterCard.classList.remove('active');
                elements.classStatus.innerHTML = '<i class="fas fa-circle"></i>';
            }
        }

        function updateStats() {
            const totalClasses = demoData.centers.reduce((sum, center) => sum + center.classes.length, 0);
            const visibleClasses = elements.classListContainer.querySelectorAll('.class-item').length;

            elements.classStatsText.textContent = `${visibleClasses} classes available across ${demoData.centers.length} centers`;
        }

        function clearAllSelections() {
            classPanel.selectedClasses = [];
            renderClasses();
            updateSelectionDisplay();
        }

        function applySelections() {
            // Simulate applying selections
            const count = classPanel.selectedClasses.length;

            // Show success animation
            elements.applyClassSelection.innerHTML = '<i class="fas fa-check"></i> Applied!';
            elements.applyClassSelection.style.background = 'linear-gradient(135deg, #10b981, #059669)';

            setTimeout(() => {
                closeClassPanel();

                // Reset button after panel closes
                setTimeout(() => {
                    elements.applyClassSelection.innerHTML = 'Apply Selection';
                    elements.applyClassSelection.style.background = '';
                }, 500);
            }, 1000);
        }

        // Search functionality
        const debouncedSearch = debounce((searchTerm) => {
            classPanel.searchTerm = searchTerm;
            renderClasses();
        }, 300);

        // Event Listeners
        elements.classFilterCard.addEventListener('click', openClassPanel);
        elements.closeClassPanel.addEventListener('click', closeClassPanel);
        elements.classPanelOverlay.addEventListener('click', closeClassPanel);
        elements.clearClassSelection.addEventListener('click', clearAllSelections);
        elements.applyClassSelection.addEventListener('click', applySelections);

        elements.classSearchInput.addEventListener('input', (e) => {
            debouncedSearch(e.target.value);
        });

        // Filter buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.class-filter-btn')) {
                // Update active filter
                document.querySelectorAll('.class-filter-btn').forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');

                classPanel.activeFilter = e.target.dataset.filter;
                renderClasses();
            }
        });

        // Global functions for demo buttons
        function resetDemo() {
            classPanel.selectedClasses = [];
            classPanel.searchTerm = '';
            classPanel.activeFilter = 'all';

            elements.classSearchInput.value = '';
            document.querySelectorAll('.class-filter-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector('.class-filter-btn[data-filter="all"]').classList.add('active');

            updateSelectionDisplay();

            if (classPanel.isOpen) {
                renderClasses();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (classPanel.isOpen) {
                if (e.key === 'Escape') {
                    closeClassPanel();
                } else if (e.key === 'Enter' && e.ctrlKey) {
                    applySelections();
                }
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateSelectionDisplay();
        });
    </script>
</body>

</html>