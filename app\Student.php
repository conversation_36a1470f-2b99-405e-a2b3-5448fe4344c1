<?php

namespace App;

use App\Scopes\OrganizationScope;

use Illuminate\Notifications\Notifiable;
use App\Notifications\StudentResetPassword;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use \Askedio\SoftCascade\Traits\SoftCascadeTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;


//class Student extends Authenticatable
class Student extends Model
{
    use SoftDeletes,Notifiable,SoftCascadeTrait, HasFactory;

    const ATTENDANCE_LATE = 1;
    const ATTENDANCE_ON_TIME = 2;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */


    protected $fillable = ['display_name'
        , 'email'
        , 'password'
        , 'organization_id'
        , 'guardian_id'
        , 'student_number'
        , 'full_name'
        , 'full_name_trans'
        , 'gender'
        , 'date_of_birth'
        , 'identity_number'
        , 'nationality'
        , 'image'
        , 'identity'
        , 'mobile'
        ,'status'
        , 'archived_by'
        , 'archived_by_ip'
        , 'user_id'
        , 'gender_id'
        , 'student_email'
        , 'guardian_id'
        , 'created_by'
        , 'updated_by'
        , 'delete_reason'
        , 'identity_file'
        , 'active_status'
        , 'guardian_relationship'
        ,'bloodgroup_id'
        ,'ready_for_exam',
        'level',
        'mobile_2',
        'exam_readiness_date',
        'attendance_id'





    ];
    protected $softCascade  = ['admissions@update'];
    protected $casts = ['deleted_at','date_of_birth','exam_readiness_date'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];
    protected $appends = ["age","current_hefz_plan"];





    public function studentProgramLevels()
    {
        return $this->hasMany(StudentProgramLevel::class, 'student_id');
    }


    public function setActive()
    {
        $this->status = 'active';
        $this->save();
    }

    public function getFormattedNameAttribute()
    {
        return formatNameProperly($this->full_name);
    }
    public function programLevel()
    {
        return $this->belongsTo(ProgramLevel::class,'level','id')->with('arabic');
    }

    public function gender()
    {
        return $this->belongsTo('App\BaseSetup', 'gender_id', 'id');
    }
    public function onlineExam()
    {
        return $this->hasMany(OnlineExam::class,'student_id')->with('arabic');
    }


    /**
     * Bootstrap the model and its traits.
     * 
     * Registers model events:
     * - Adds OrganizationScope globally
     * - Sets created_by on model creation
     * - Sets updated_by on model updates
     * - Invalidates relevant caches on model changes
     * 
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);

        static::creating(function ($model) {
            $model->created_by = auth()->user()->id;
            $model->updated_by = NULL;
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->user()->id;
        });
    }
    public function getLastSurahAttribute()
    {

        $surahId = $this->hefz()->orderByDesc('hefz_to_surat')->first()->hefz_to_surat;
        return MoshafSurah::where('id',$surahId)->first()->name;


    }
    public function getAgeAttribute(){


        //date in mm-dd-yyyy format; or it can be in other formats as well
        $birthDate = $this->attributes['date_of_birth'];
//        dd(\Carbon\Carbon::parse($birthDate)->diff(\Carbon\Carbon::now())->format('%y years, %m months and %d days'));
        $age = \Carbon\Carbon::parse($birthDate)->diff(\Carbon\Carbon::now())->format('%y');
        return $age;

    }
    
    /**
     * Get the URL for the student's photo
     * 
     * @return string
     */
    public function getStudentPhotoAttribute(): string
    {


        
        // ensure we have a real Student instance (not relying on auth)
        $id = $this->getKey();
       
        // reload the model in case $this was a partial/empty instance
        $student = self::withoutGlobalScopes()->find($id);
        
        return app(\App\Services\StudentImageService::class)
            ->getStudentImageUrl($student);
    }
    
    public function documents(){

        return $this->morphMany('App\Document','documentable');

    }
    public function parents()
    {
        return $this->belongsTo('App\Guardian', 'guardian_id', 'id');
    }

    /**
     * Send the password reset notification.
     *
     * @param string $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new StudentResetPassword($token));
    }

    public function admissions()
    {
        return $this->hasMany('App\Admission');
    }
    public function admission()
    {
        return $this->hasOne(Admission::class)->whereNull('deleted_at');
    }
    public function newAdmissions()
    {
        return $this->hasMany('App\Admission')->where('status','new_admission');
    }


    public function admissionInterviews(){
        return $this->hasManyThrough(AdmissionInterview::class, Admission::class,'student_id','admission_id','id');
    }
    public function payments()
    {
        return $this->hasMany('App\StudentPayment');
    }



    protected static function booted()
    {
        static::saved(function ($student) {
            // Invalidate cache when a student is added or updated
            \Cache::forget("students_in_class_{$student->class_id}");
        });

        static::deleted(function ($student) {
            // Invalidate cache when a student is deleted
            \Cache::forget("students_in_class_{$student->class_id}");
        });

        static::saved(function ($model) {
            \Cache::forget('missing_data_count');
        });

        static::deleted(function ($model) {
            \Cache::forget('missing_data_count');
        });
    }

    public function joint_classes()
    {
        return $this->belongsToMany('App\Classes', 'class_students', 'student_id', 'class_id')
            ->whereNull('class_students.deleted_at')

            ->withPivot('end_date','transfer_from','transfer_at','added_at','start_date')->withTimestamps();
    }

    public function hefz_plans()
    {
        return $this->hasMany(StudentHefzPlan::class);
    }

    public function hefzPlans()
    {
        return $this->hasMany(StudentHefzPlan::class);
    }
    public function ijazasanad_memorization_plans()
    {
        return $this->hasMany(IjazasanadMemorizationPlan::class);
    }
    public function nouranya_plans()
    {
        return $this->hasMany(StudentNouranyaPlan::class,'student_id','id');
    }
    public function getRevisionReportAttendancePercentageAttribute()
    {
        $attendanceCount = $this->revision->reduce(function ($carry, $item) {
            if ($item->attendance_id == 2) {
                $carry++;
            }
            return $carry;
        }, 0);
        $daysCountPerMonth = $this->joint_classes->first()->timetable->days_count_per_month;


        if ($daysCountPerMonth > 0) {
            $attendancePercentage = $attendanceCount / $daysCountPerMonth * 100;
        } else {
            $attendancePercentage = 0;
        }
        return $attendancePercentage;
    }

    /**
     * Calculate attendance percentage for a specific month and year.
     * 
     * @param int $month The month (1-12)
     * @param int $year The year (YYYY)
     * @return float|null Returns the attendance percentage or null if no classes scheduled
     * 
     * Calculates attendance based on:
     * - Student's assigned class timetable
     * - Actual attendance records for hefz classes
     * - Both late (1) and on-time (2) attendance are counted as present
     */
    public function attendancePercentageForMonth($month, $year)
    {
        // Get the joint class for the student
        $class = $this->joint_classes->first();

        // If there's no class for the student, return null
        if (!$class) {
            return null;
        }

        // Get the timetable for the class
        $timetable = $class->timetable;

        // If there's no timetable for the class, return null
        if (!$timetable) {
            return null;
        }

        // Calculate the total classes for the month based on the class timetable
        $totalClasses = $timetable->daysCountPerMonth($month, $year);

        // If there were no classes, return null
        if ($totalClasses === 0) {
            return null;
        }

        // Get the total attended classes for the month
        $attendedClasses = $this->hefz()->whereMonth('created_at', $month)
            ->whereYear('created_at', $year)
            ->whereIn('attendance_id', [1, 2]) // 1 = late, 2 = on time
            ->count();

        // Calculate the percentage, capped at 100
        $percentage = min(100, ($attendedClasses / $totalClasses) * 100);

        // Return the attendance percentage rounded to two decimal places
        return round($percentage, 2);
    }
    public function ijazasanadAttendancePercentageForMonth($month, $year) {
        // Get the joint class for the student
        $class = $this->joint_classes->first();

        // If there's no class for the student, return null
        if (!$class) {
            return null;
        }

        // Get the timetable for the class
        $timetable = $class->timetable;

        // If there's no timetable for the class, return null
        if (!$timetable) {
            return null;
        }

        // Calculate the total classes for the month based on the class timetable
        $totalClasses = $timetable->daysCountPerMonth($month, $year);

        // If there were no classes, return null
        if ($totalClasses === 0) {
            return null;
        }

        // Get the total attended classes for the month
        $attendedClasses = $this->ijazaMemorizationReport()->whereMonth('created_at', $month)
            ->whereYear('created_at', $year)
            ->whereIn('attendance_id', [1, 2]) // 1 = late, 2 = on time
            ->count();

        // Calculate the percentage, capped at 100
        $percentage = min(100, ($attendedClasses / $totalClasses) * 100);

        // Return the attendance percentage rounded to two decimal places
        return round($percentage, 2);
    }

    /**
     * Calculate revision attendance percentage for a specific month and year.
     * 
     * @param int $month The month (1-12)
     * @param int $year The year (YYYY)
     * @return float|null Returns the revision attendance percentage or null if no classes scheduled
     */
    public function revisionAttendancePercentageForMonth($month, $year)
    {
        // Get the joint class for the student
        $class = $this->joint_classes->first();

        // If there's no class for the student, return null
        if (!$class) {
            return null;
        }

        // Get the timetable for the class
        $timetable = $class->timetable;

        // If there's no timetable for the class, return null
        if (!$timetable) {
            return null;
        }
//        $monthNumber = date('n', strtotime($month));

        // Calculate the total classes for the month based on the class timetable
//        $totalClasses = $timetable->days_count_per_month;
        $totalClasses = $timetable->daysCountPerMonth($month, $year);

        // If there were no classes, return null
        if ($totalClasses === 0) {
            return null;
        }


        // Get the total attended classes for the month
        $attendedClasses = $this->revision()
            ->whereMonth('created_at', $month)
            ->whereYear('created_at', $year)
            ->whereIn('attendance_id', [1, 2]) // 1 = late, 2 = on time
            ->count();


        // Calculate the percentage
        $percentage = min(100, ($attendedClasses / $totalClasses) * 100);


        return $percentage;
    }

    /**
     * Calculate the student's attendance percentage.
     * 
     * @return float Percentage of attendance (0-100)
     * 
     * Calculates attendance based on the following rules:
     * - Considers both on-time (2) and late (1) attendance as present
     * - Uses the class timetable's days_count_per_month as the denominator
     * - Returns 0 if days_count_per_month is 0 or not available
     * - Percentage is calculated as: (attended days / total days) * 100
     */
    public function getAttendancePercentageAttribute(): float
    {
        // Initialize attendance count
        $attendanceCount = 0;

        $attendanceCount = $this->hefz->reduce(function ($carry, $item) {
            if ($item->attendance_id == 2 || $item->attendance_id == 1) {
                $carry++;
            }
            return $carry;
        }, 0);


        // Get the days count per month; return 0 if there's any null value along the way
        $daysCountPerMonth = $this->joint_classes->first()?->timetable?->days_count_per_month ?? 0;


        // Avoid division by zero
        if ($daysCountPerMonth <= 0) {
            return 0;
        }

        // Calculate and return the attendance percentage
        return ($attendanceCount / $daysCountPerMonth) * 100;
    }


    public function admissionPlan()
    {
        return $this->hasOne(StudentAdmissionHefzPlan::class);
    }

    public function latest_hefz_plans()
    {
        return $this->hefz_plans()->orderByDesc('updated_at')->first()->updated_at->diffForHumans();
    }
    public function latest_ijazasanad_hefz_plans()
    {
        return $this->ijazasanad_memorization_plans()->orderByDesc('updated_at')->first()->updated_at->diffForHumans();
    }

    public function planDate()
    {
        return $this->hefz_plans()->first()->start_date->format('F Y');
    }

    public function revision_plans()
    {
        return $this->hasMany(StudentRevisionPlan::class);
    }

    public function revisionPlans()
    {
        return $this->hasMany(StudentRevisionPlan::class);
    }

    public function ijazasanad_revision_plans()
    {
        return $this->hasMany('App\IjazasanadRevisionPlan');
    }


    public function hefzPlanPerYearMonthPerClassPerStudent($studentId,$classId,$year,$month)
    {



        return $this->hasMany('App\StudentHefzPlan')
            ->where('student_id',$studentId)
            ->where('class_id',$classId)
            ->whereYear('start_date',$year)
            ->whereMonth('start_date',$month);
    }

    public function ijazasanadRevisionPlanPerYearMonthPerClassPerStudent($studentId,$classId,$year,$month)
    {



        return $this->hasMany('App\IjazasanadRevisionPlan')
            ->where('student_id',$studentId)
            ->where('class_id',$classId)
            ->whereYear('start_date',$year)
            ->whereMonth('start_date',$month);
    }

    public function ijazasanadMemorizationPlanPerYearMonthPerClassPerStudent($studentId,$classId,$year,$month)
    {


        return $this->hasMany('App\IjazasanadMemorizationPlan')
            ->where('student_id',$studentId)
            ->where('class_id',$classId)
            ->whereYear('start_date',$year)
            ->whereMonth('start_date',$month);
    }

    public function revisionPlanPerYearMonthPerClassPerStudent($studentId,$classId,$year,$month)
    {



        return $this->hasMany('App\StudentRevisionPlan')
            ->where('student_id',$studentId)
            ->where('class_id',$classId)
            ->whereYear('start_date',$year)
            ->whereMonth('start_date',$month);
    }


//    public function completedHefzPlans($classId)
    public function completedHefzPlans($classId,$month)
    {
        return $this->hasMany('App\StudentHefzPlan')->where('class_id',$classId)->where(function($q) use ($month) {
            $q->whereNotNull('study_direction')
                ->whereNotNull('start_from_surat')->whereNotNull('start_from_ayat')
                ->whereMonth('start_date',$month)
                ->whereNotNull('to_surat')->whereNotNull('to_ayat');

        });
    }

    /**
     * Check if a student's Hefz plan is complete for a given class and date.
     * 
     * @param int $classId The class ID
     * @param int $studentId The student ID
     * @param \DateTime $fromDate The date to check from
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     * 
     * A plan is considered complete when it has:
     * - study_direction
     * - start_from_surat and start_from_ayat
     * - to_surat and to_ayat
     */
    public function checkifHefzPlanisComplete($classId, $studentId, $fromDate)
    {
        return $this->hasMany('App\StudentHefzPlan')->where('class_id',$classId)->where(function($q) use ($studentId,$fromDate) {
            $q->whereNotNull('study_direction')
                ->whereNotNull('start_from_surat')->whereNotNull('start_from_ayat')
                ->where('student_id',$studentId)
                ->whereYear('start_date',$fromDate->year)
                ->whereMonth('start_date',$fromDate->month)
                ->whereNotNull('to_surat')->whereNotNull('to_ayat');

        });
    }


    public function completedHefzReport()
    {
        return $this->hasMany('App\StudentHefzReport','student_id','id')->where(function($q) {
            $q->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_from_ayat')
                ->whereNotNull('hefz_to_surat')
                ->whereNotNull('hefz_to_ayat');

        });
    }
    public function completedHefzReportforCurrentMonth()
    {
        return $this->hasMany('App\StudentHefzReport', 'student_id', 'id')->where(function ($q) {
            $q->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_from_ayat')
                ->whereNotNull('hefz_to_surat')
                ->whereNotNull('hefz_to_ayat')
                ->currentMonthAndYear();
        });
    }



    /**
     * Get the latest revision report for a student.
     *
     * This method is used to fetch the most recent revision report for a student.
     * The "most recent" is determined by either the date the report was created
     * or the date it was last updated, whichever is later.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function last_revision()
    {
        // Start a "hasOne" relationship, meaning each student has one latest revision report.
        // Replace 'App\StudentRevisionReport' with the actual namespace of your Revision Report model if it's different.
        return $this->hasOne('App\StudentLastRevisionRecord');
    }
    public function last_ijazasanad_revision()
    {
        // Start a "hasOne" relationship, meaning each student has one latest revision report.
        // Replace 'App\StudentRevisionReport' with the actual namespace of your Revision Report model if it's different.
        return $this->hasOne('App\StudentIjazasanadRevisionReport')->latest('created_at');
    }
    public function last_nouranya()
    {
        return $this->hasOne('App\StudentLastNouranyaRecord');
    }

    public function last_report(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        // Start a "hasOne" relationship, meaning each student has one latest Hefz report.
        // Replace 'App\StudentHefzReport' with the actual namespace of your Hefz Report model if it's different.
        return $this->hasOne('App\StudentLastMemorizationRecord');

    }

    public function last_ijazasanad_memorization_report(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        // Start a "hasOne" relationship, meaning each student has one latest Hefz report.
        // Replace 'App\StudentHefzReport' with the actual namespace of your Hefz Report model if it's different.
        return $this->hasOne('App\StudentIjazasanadMemorizationReport')->latest('created_at');

    }


    public function lastApprovedMemorizationPlan(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne('App\StudentLastApprovedPlan');

    }
    public function lastApprovedIjazasanadMemorizationPlan(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne('App\StudentIjazasanadMemorizationLastApprovedPlan');

    }
    public function lastApprovedNouranyaPlan(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne('App\StudentLastApprovedNouranyaPlan', 'student_id', 'id')->latest('plan_year_month_day');
    }

    public function StudentLastNouranyaReportRecord($year = null, $month = null): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne('App\StudentLastNouranyaRecord')->latest();


    }
    public function StudentLastIjazasanadReportRecord($year = null, $month = null): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne('App\StudentLastIjazasanadMemorizationRecord')->latest();


    }
    public function lastApprovedRevisionPlan(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne('App\StudentRevisionPlan');

    }
    public function lastApprovedIjazasanadRevisionPlan(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne('App\StudentIjazasanadRevisionLastApprovedPlan');
    }

    public function completedRevisionReport()
    {
        return $this->hasMany('App\StudentRevisionReport','student_id','id')->where(function($q) {
            $q->whereNotNull('revision_from_surat')
                ->whereNotNull('revision_from_ayat')
                ->whereNotNull('revision_to_surat')
                ->whereNotNull('revision_to_ayat');

        });
    }

    public function completedIjazasanadRevisionReport()
    {
        return $this->hasMany('App\StudentIjazasanadRevisionReport','student_id','id')->where(function($q) {
            $q->whereNotNull('revision_from_surat')
                ->whereNotNull('revision_from_ayat')
                ->whereNotNull('revision_to_surat')
                ->whereNotNull('revision_to_ayat');

        });
    }


    public function completedHefzReportPerMonth($classId, $year, $month)
    {

//        dd(\DB::table('student_hefz_report')
         return $this->hasMany('App\StudentHefzReport','student_id','id')
            ->where('class_id',$classId)
            ->whereYear('created_at',$year)
            ->whereMonth('created_at',$month)
            ->where(function($q) {
                $q->whereNotNull('hefz_from_surat')
                    ->whereNotNull('hefz_from_ayat')
                    ->whereNotNull('hefz_to_surat')
                    ->whereNotNull('hefz_to_ayat');

            });
    }

    public function completedNouranyaReportPerMonth($classId, $year, $month)
    {

//        dd(\DB::table('student_hefz_report')
        return $this->hasMany(StudentNouranyaReport::class,'student_id','id')
            ->where('class_id',$classId)
            ->whereYear('created_at',$year)
            ->whereMonth('created_at',$month)
            ->where(function($q) {
                $q->whereNotNull('talaqqi_from_lesson')
                    ->whereNotNull('talaqqi_to_lesson')
                    ->whereNotNull('talqeen_from_lesson')
                    ->whereNotNull('talqeen_to_lesson');

            });
    }



    public function completedIjazasanadMemorizationReportPerMonth($classId,$year, $month)
    {
        return $this->hasMany('App\StudentIjazasanadMemorizationReport', 'student_id', 'id')
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where(function ($query) {
                // Check for Level 1
                $query->where(function ($q) {
                    $q->whereNotNull('talqeen_from_lesson')
                        ->whereNotNull('talqeen_to_lesson')
                        ->whereNotNull('revision_from_lesson')
                        ->whereNotNull('revision_to_lesson')
                        ->whereNotNull('jazariyah_from_lesson')
                        ->whereNotNull('jazariyah_to_lesson')
                        ->whereNotNull('seminars_from_lesson')
                        ->whereNotNull('seminars_to_lesson');
                })
                    // Check for Level 2
                    ->orWhere(function ($q) {
                        $q->whereNotNull('hefz_from_surat')
                            ->whereNotNull('hefz_from_ayat')
                            ->whereNotNull('hefz_to_surat')
                            ->whereNotNull('hefz_to_ayat');
                    });
            });
    }



    /**
     * Get the student's current Hefz plan.
     * 
     * @return \Illuminate\Database\Eloquent\Model|null
     * 
     * Returns the active Hefz plan where end_date is null.
     * Returns null if no active plan exists.
     */
    public function getCurrentHefzPlanAttribute()
    {
        $current_plan = $this->hefz_plans->filter(function ($record) {
            return $record->end_date == null;
        });
        return \Illuminate\Support\Arr::first($current_plan);
    }


    public function latest_hefz()
    {
        return $this->hasOne('App\StudentHefzReport', 'student_id', 'id')->latest();
    }
    public function revision()
    {
        return $this->hasMany('App\StudentRevisionReport', 'student_id', 'id');
    }



    public function getDisplayNameAttribute(){
        return Str::title($this->attributes['display_name']);
    }
    
    public function getFullNameAttribute(){
        return Str::title($this->attributes['full_name'] ?? '');
    }
    
    public function setFullNameAttribute($value){
        $this->attributes['full_name'] = ucwords(strtolower($value));
    }
    
    public function setDisplayNameAttribute($value){
        $this->attributes['display_name'] = ucwords($value);
    }
    public function setIdentityNumberAttribute($value){

        $this->attributes['identity_number'] = strtoupper($value);
    }
    /**
     * Get the student's current admission record.
     * 
     * @return \Illuminate\Database\Eloquent\Model|null
     * 
     * Returns the current admission record where leaving_date is null.
     * Returns null if no active admission exists.
     */
    public function getCurrentAdmissionAttribute()
    {
        $current_admission = $this->admissions->filter(function ($record) {
            return $record->leaving_date == null;
        });
        return \Illuminate\Support\Arr::first($current_admission);
    }
    public function getAdmissionHistoryAttribute()
    {
        $admissions = $this->admissions->filter(function ($record) {
            return $record->leaving_date != null;
        });
        return $admissions;
    }
    public function getCurrentClassAttribute()
    {
        $current_class = $this->joint_classes->filter(function ($record) {
            return $record->pivot->end_date <> null;
        });
        return \Illuminate\Support\Arr::first($current_class);
    }
    /**
     * written by hashmat Waziri on 19/Sep/2020 - Accessor
     *
     * @param $value
     * @return string
     */
    public function getStatusAttribute($value)
    {
        switch ($this->attributes['status']) {


            case "accepted":
                $value = "Accepted";
                break;
            case "preparing_for_orientation":
                $value = "preparing_for_orientation";
                break;
            case "active":
                $value = "Active";
                break;
            case "new_admission":
                $value = "New Admission";
                break;
            case "profile_completed":
                $value = "Profile Completed";
                break;
            case "update_guardian":
                $value = "Update Guardian";
                break;
            case "offered":
                $value = "Offered";
                break;
            case "waiting_for_interview":
                $value = "Waiting for Interview";
                break;
            case "rejected":
                $value = "Rejected";
                break;

        }
        return $this->attributes['status'] = $value;


    }
    public function user()
    {
        return $this->belongsTo('App\User', 'user_id', 'id');
    }
    public static function getExamResult($exam_id, $student)
    {
        $eligible_subjects       = AssignSubject::where('class_id', $student->admission->class_id)->where('section_id', $student->section_id)->where('organization_id', \Auth::user()->organization_id)->get();


        foreach ($eligible_subjects as $subject) {

            $getMark = ResultStore::where([
                ['exam_type_id',   $exam_id],
                ['class_id',       $student->class_id],
                ['section_id',     $student->section_id],
                ['student_id',     $student->id],
                ['subject_id',     $subject->subject_id]
            ])->first();

            if ($getMark == "") {
                return false;
            }


            $result = ResultStore::where([
                ['exam_type_id',   $exam_id],
                ['class_id',       $student->class_id],
                ['section_id',     $student->section_id],
                ['student_id',     $student->id]
            ])->get();

            return $result;
        }
    }
    public function bloodGroup()
    {
        return $this->belongsTo('App\BaseSetup', 'bloodgroup_id', 'id');
    }
    public function class(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {

        return $this->belongsToMany(Classes::class,'class_students','student_id','class_id')->withTimestamps();
    }
    public function hefz()
    {
        return $this->hasMany('App\StudentHefzReport', 'student_id', 'id');
    }
    public function ijazaMemorizationReport()
    {


        return $this->hasMany('App\StudentIjazasanadMemorizationReport', 'student_id', 'id');
    }
    public function ijazaRevisionReport()
    {


        return $this->hasMany('App\StudentIjazasanadRevisionReport', 'student_id', 'id');
    }

    public function nouranya()
    {
        return $this->hasMany('App\StudentNouranyaReport', 'student_id', 'id');
    }
    public function reports()
    {
        return $this->hasMany(StudentHefzReport::class,'student_id','id');
    }


    public function lastReportedSurat()
    {
        return $this->hasOne(StudentHefzReport::class,'student_id','id')->latest('hefz_to_surat');
    }

    public function completedNouranyaReportforCurrentMonth()
    {
        $currentMonth = now()->month;
        $currentYear = now()->year;
        
        return $this->hasMany('App\StudentNouranyaReport')
            ->whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->where(function($q) {
                $q->whereNotNull('from_lesson')
                    ->whereNotNull('to_lesson')
                    ->whereNotNull('nouranya_evaluation_id');
            });
    }

    public function completedIjazasanadReportforCurrentMonth()
    {
        $currentMonth = now()->month;
        $currentYear = now()->year;
        
        return $this->hasMany('App\StudentIjazasanadMemorizationReport')
            ->whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->where(function($q) {
                $q->whereNotNull('memorization_from_surat')
                    ->whereNotNull('memorization_from_ayat')
                    ->whereNotNull('memorization_to_surat')
                    ->whereNotNull('memorization_to_ayat');
            });
    }

    
     /**
     * Helper method to count unique days based on the latest report of that type for that day.
     *
     * @param string $reportModelClass The class name of the report model (e.g., StudentHefzReport::class).
     * @param int|null $classId Optional class ID to filter by.
     * @return int
     */
    private function countLatestUniqueDaysForReportModel(string $reportModelClass, ?int $classId): int
    {
        /** @var Model $reportModelInstance */
        $reportModelInstance = new $reportModelClass; // Instantiate to get table name etc.
        $reportTable = $reportModelInstance->getTable(); // Get the table name dynamically

        
        // 1. Subquery to find the max created_at for each date for this student
        // Start query from the specific report model class
        $latestReportsSubquery = $reportModelClass::query()
            ->selectRaw("DATE(created_at) as report_date, MAX(created_at) as max_created_at")
            ->where('student_id', $this->id); // Filter for the current student instance

            
        // --- If the report model uses SoftDeletes, add this to the subquery ---
        // --- to potentially exclude deleted reports from determining the MAX ---
        // if (method_exists($reportModelClass, 'getDeletedAtColumn')) {
        //     $latestReportsSubquery->whereNull($reportModelInstance->getDeletedAtColumn());
        // }
        // --- Or just hardcode if necessary ---
        // $latestReportsSubquery->whereNull('deleted_at');

        $latestReportsSubquery->groupByRaw("DATE(created_at)");


        // 2. Build the main query starting from the base model
        $query = $reportModelClass::query()
            ->joinSub($latestReportsSubquery, 'latest_reports', function ($join) use ($reportTable) {
                // Join on the date part
                $join->on(DB::raw("DATE({$reportTable}.created_at)"), '=', 'latest_reports.report_date')
                    // Join on the exact max timestamp for that date
                    ->on("{$reportTable}.created_at", '=', 'latest_reports.max_created_at');
            })
            // Filter the *main* query for *this* student
            // Although the subquery already did this, it's safer and clearer to filter the main query too
            ->where("{$reportTable}.student_id", $this->id);

        
            $query->where("{$reportTable}.class_id", $classId);
        

        // --- Soft delete handling for the MAIN query ---
        // If the report model uses SoftDeletes, this is automatically handled by Eloquent.
        // If NOT using SoftDeletes, uncomment the line below:
        // if (!method_exists($reportModelClass, 'getDeletedAtColumn')) {
        //      $query->whereNull("{$reportTable}.deleted_at");
        // }


        // 3. Count the resulting rows (each row represents one unique day with its latest report)
        return $query->count();
    }



    /**
     * Count unique days based on the latest Hefz report per day for this student.
     *
     * @param int|null $classId
     * @return int
     */
    public function countUniqueHefzReportDays(?int $classId = null): int
    {
        return $this->countLatestUniqueDaysForReportModel(StudentHefzReport::class, $classId);
    }

    /**
     * Count unique days based on the latest Nouranya report per day for this student.
     *
     * @param int|null $classId
     * @return int
     */
    public function countUniqueNouranyaReportDays(?int $classId = null): int
    {
        return $this->countLatestUniqueDaysForReportModel(StudentNouranyaReport::class, $classId);
    }

    /**
     * Count unique days based on the latest Ijazah/Sanad report per day for this student.
     *
     * @param int|null $classId
     * @return int
     */
    public function countUniqueIjazasanadReportDays(?int $classId = null): int
    {
        return $this->countLatestUniqueDaysForReportModel(StudentIjazasanadMemorizationReport::class, $classId);
    }





}
