---
description: 
globs: 
alwaysApply: true
---
# Laravel Project Guidelines

This document outlines the key architectural principles, conventions, and technologies used in this Laravel 10 project.

## Core Technologies

*   **Backend**: [Laravel 10](mdc:https:/laravel.com/docs/10.x), PHP 8.1+
*   **Frontend**: Bootstrap 3, jQuery
*   **Database**: MySQL (Use Eloquent for data interaction, direct SQL for schema/seeding)
*   **Connection Details**:    
        * Database: itqan
        * Username: root
        * Password: (none)
        * Command: mysql -u root itqan
        
*   **Modular Structure**: [nwidart/laravel-modules](mdc:https:/nwidart.com/laravel-modules/v10/introduction)

## Project Structure & Conventions

*   **Modular Development**: The project uses the `nwidart/laravel-modules` package. All module-specific code (Controllers, Models, Views, Routes, etc.) should reside within the corresponding directory under `[Modules/](mdc:Modules)`.
    *   **Example**: Code for the Admission module is located in `[Modules/Admission/](mdc:Modules/Admission)`.
*   **Routing**: Module-specific web routes are defined in `Modules/<ModuleName>/Http/routes.php` (e.g., `[Modules/Admission/Http/routes.php](mdc:Modules/Admission/Http/routes.php)`). API routes are in `Modules/<ModuleName>/Http/api.php`. Ensure the module's `RouteServiceProvider` is configured correctly.
*   **Controllers**: Place controllers within the `Http/Controllers` directory of the respective module. Controllers should be `final` and read-only. Use method injection for dependencies or dedicated Service classes.
*   **Models**: Place Eloquent models within the `Entities` directory of the respective module (e.g., `[Modules/Admission/Entities/](mdc:Modules/Admission/Entities)`). Models should be `final`.
*   **Views**: Blade templates are located in `resources/views/` or within the `Resources/views` directory of each module (e.g., `[Modules/Admission/Resources/views/](mdc:Modules/Admission/Resources/views)`). Use Bootstrap 3 conventions.
*   **Frontend Assets**: CSS, JS, and images are typically managed via `webpack.mix.js` and located in `[public/](mdc:public)` and `[resources/assets/](mdc:resources/assets)`.
*   **Configuration**: Core application configuration is in `[config/](mdc:config)`. Module-specific configuration is in `Modules/<ModuleName>/Config/config.php`.
*   **Database Schema/Seeding**: **Do not** use Laravel migrations or seeders. Perform schema changes and initial data seeding directly via MySQL queries. Use Eloquent ORM for all application-level data manipulation (CRUD).
*   **Logging**: Implement extensive logging. Logs are stored in `[storage/logs/](mdc:storage/logs)`. Log method entry/exit points, contextual information, warnings, and errors.
*   **Coding Standards**: Adhere strictly to PSR-12 coding standards. Use `declare(strict_types=1);` and explicit type hints.
*   **Testing**: Write unit and feature tests using PHPUnit. Place tests within the `Tests/` directory of each module or the main `[tests/](mdc:tests)` directory.

## Key Practices

*   **Validation**: Use Laravel's Form Request validation (`app/Http/Requests/` or module-specific request classes).
*   **Security**: Implement CSRF protection, sanitize inputs, use Eloquent/prepared statements to prevent SQL injection. Use Laravel's authorization features (Policies).
*   **Performance**: Use eager loading (`with()`) to prevent N+1 issues. Leverage caching where appropriate. Minify assets.
*   **Services & Repositories**: Employ Service and Repository patterns for complex logic and data abstraction, placing them in `app/Services/`, `app/Repositories/` or module-specific directories.
*   **Error Handling**: Use Laravel's exception handling. Create custom exceptions as needed.
