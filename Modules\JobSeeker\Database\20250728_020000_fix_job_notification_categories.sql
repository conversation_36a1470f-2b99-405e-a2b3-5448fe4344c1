/*
 * Description: Fix job notification system to use provider_job_categories instead of job_categories
 * Module: <PERSON><PERSON>eeker
 * Author: System Architecture Fix
 * Date: 2025-07-28
 * 
 * IMPORTANT: This file must be reviewed and executed by authorized database administrators
 * 
 * This fixes the critical mismatch where:
 * - CommandScheduleFilter uses provider_job_categories IDs
 * - JobNotificationSetup was using job_categories IDs
 * - This caused notifications to never match fetched jobs
 */

-- Step 1: Create new table for provider-specific notification categories
CREATE TABLE IF NOT EXISTS `job_notification_provider_category` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `setup_id` bigint(20) unsigned NOT NULL COMMENT 'References job_notification_setups.id',
    `provider_category_id` int unsigned NOT NULL COMMENT 'References provider_job_categories.id',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_setup_provider_category` (`setup_id`, `provider_category_id`),
    KEY `idx_setup_id` (`setup_id`),
    KEY `idx_provider_category_id` (`provider_category_id`),
    CONSTRAINT `fk_jnpc_setup` FOREIGN KEY (`setup_id`) REFERENCES `job_notification_setups` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_jnpc_provider_category` FOREIGN KEY (`provider_category_id`) REFERENCES `provider_job_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Links notification setups to provider-specific job categories';

-- Step 2: Migrate existing data from job_notification_category to job_notification_provider_category
-- We need to map job_categories to provider_job_categories via canonical_category_id

INSERT INTO job_notification_provider_category (setup_id, provider_category_id, created_at, updated_at)
SELECT DISTINCT
    jnc.setup_id,
    pjc.id as provider_category_id,
    NOW() as created_at,
    NOW() as updated_at
FROM job_notification_category jnc
JOIN job_categories jc ON jnc.category_id = jc.id
JOIN provider_job_categories pjc ON pjc.canonical_category_id = jc.id
WHERE pjc.canonical_category_id IS NOT NULL
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- Step 3: Add provider_name column to job_notification_setups to track which provider this setup is for
ALTER TABLE job_notification_setups 
ADD COLUMN `provider_name` varchar(50) DEFAULT NULL COMMENT 'Which job provider this setup targets (acbar, jobs.af, etc.)' 
AFTER `name`;

-- Step 4: Update existing setups to determine their provider based on their categories
-- For setups that have ACBAR categories
UPDATE job_notification_setups jns
SET provider_name = 'acbar'
WHERE jns.id IN (
    SELECT DISTINCT jnpc.setup_id
    FROM job_notification_provider_category jnpc
    JOIN provider_job_categories pjc ON jnpc.provider_category_id = pjc.id
    WHERE pjc.provider_name = 'acbar'
);

-- For setups that have Jobs.af categories
UPDATE job_notification_setups jns
SET provider_name = 'jobs.af'
WHERE jns.id IN (
    SELECT DISTINCT jnpc.setup_id
    FROM job_notification_provider_category jnpc
    JOIN provider_job_categories pjc ON jnpc.provider_category_id = pjc.id
    WHERE pjc.provider_name = 'jobs.af'
);

-- For setups that have mixed or no specific provider, set to 'all' (they'll get notifications from all providers)
UPDATE job_notification_setups 
SET provider_name = 'all' 
WHERE provider_name IS NULL;

-- Step 5: Create indexes for better performance
CREATE INDEX `idx_provider_name` ON `job_notification_setups` (`provider_name`);
CREATE INDEX `idx_provider_setup_active` ON `job_notification_setups` (`provider_name`, `is_active`);

-- Step 6: Add a view for easier querying of notification setups with their provider categories
CREATE OR REPLACE VIEW `v_notification_setup_categories` AS
SELECT 
    jns.id as setup_id,
    jns.name as setup_name,
    jns.provider_name,
    jns.is_active,
    js.email as jobseeker_email,
    pjc.id as provider_category_id,
    pjc.name as provider_category_name,
    pjc.provider_identifier,
    pjc.canonical_category_id,
    jc.name as canonical_category_name
FROM job_notification_setups jns
JOIN job_seekers js ON jns.job_seeker_id = js.id
JOIN job_notification_provider_category jnpc ON jns.id = jnpc.setup_id
JOIN provider_job_categories pjc ON jnpc.provider_category_id = pjc.id
LEFT JOIN job_categories jc ON pjc.canonical_category_id = jc.id
WHERE jns.is_active = 1;

-- Step 7: Create a function to get provider identifiers for a notification setup
DELIMITER //
CREATE FUNCTION GetProviderIdentifiersForSetup(setup_id INT, provider_name VARCHAR(50))
RETURNS TEXT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result TEXT DEFAULT '';
    
    SELECT GROUP_CONCAT(DISTINCT pjc.provider_identifier ORDER BY pjc.provider_identifier)
    INTO result
    FROM job_notification_provider_category jnpc
    JOIN provider_job_categories pjc ON jnpc.provider_category_id = pjc.id
    WHERE jnpc.setup_id = setup_id 
    AND pjc.provider_name = provider_name;
    
    RETURN COALESCE(result, '');
END//
DELIMITER ;

-- Step 8: Insert sample data to test the new structure
-- This will help verify the migration worked correctly
INSERT IGNORE INTO job_notification_provider_category (setup_id, provider_category_id, created_at, updated_at)
SELECT 
    37 as setup_id, -- Existing setup ID
    pjc.id as provider_category_id,
    NOW(),
    NOW()
FROM provider_job_categories pjc
WHERE pjc.provider_name = 'acbar' 
AND pjc.canonical_category_id IN (1, 2, 5) -- Technology, Management, Education
LIMIT 5;

-- Step 9: Create a stored procedure to sync notification categories with command schedule filters
DELIMITER //
CREATE PROCEDURE SyncNotificationWithScheduleCategories(
    IN p_setup_id INT,
    IN p_provider_name VARCHAR(50)
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_category_id INT;
    DECLARE category_cursor CURSOR FOR
        SELECT DISTINCT JSON_UNQUOTE(JSON_EXTRACT(csf.categories, CONCAT('$[', idx.i, ']'))) as category_id
        FROM command_schedule_filters csf
        JOIN command_schedule_rules csr ON csf.schedule_rule_id = csr.id
        JOIN (
            SELECT 0 as i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION 
            SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
            SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
            SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19
        ) idx ON JSON_UNQUOTE(JSON_EXTRACT(csf.categories, CONCAT('$[', idx.i, ']'))) IS NOT NULL
        WHERE csr.command LIKE CONCAT('%', p_provider_name, '%')
        AND JSON_UNQUOTE(JSON_EXTRACT(csf.categories, CONCAT('$[', idx.i, ']'))) REGEXP '^[0-9]+$';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Clear existing categories for this setup
    DELETE FROM job_notification_provider_category WHERE setup_id = p_setup_id;
    
    -- Add categories from command schedule filters
    OPEN category_cursor;
    read_loop: LOOP
        FETCH category_cursor INTO v_category_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Insert if the category exists in provider_job_categories
        INSERT IGNORE INTO job_notification_provider_category (setup_id, provider_category_id, created_at, updated_at)
        SELECT p_setup_id, v_category_id, NOW(), NOW()
        FROM provider_job_categories pjc
        WHERE pjc.id = v_category_id AND pjc.provider_name = p_provider_name;
    END LOOP;
    
    CLOSE category_cursor;
END//
DELIMITER ;

-- Step 10: Update the job_notification_setups table structure for better tracking
ALTER TABLE job_notification_setups 
ADD COLUMN `last_sync_at` timestamp NULL DEFAULT NULL COMMENT 'When categories were last synced with schedule filters' AFTER `last_notified_at`;

-- Step 11: Create summary statistics view
CREATE OR REPLACE VIEW `v_notification_system_stats` AS
SELECT 
    'Total Active Setups' as metric,
    COUNT(*) as value
FROM job_notification_setups 
WHERE is_active = 1

UNION ALL

SELECT 
    CONCAT('Active Setups - ', COALESCE(provider_name, 'Unknown')) as metric,
    COUNT(*) as value
FROM job_notification_setups 
WHERE is_active = 1
GROUP BY provider_name

UNION ALL

SELECT 
    'Total Provider Categories Targeted' as metric,
    COUNT(DISTINCT jnpc.provider_category_id) as value
FROM job_notification_provider_category jnpc
JOIN job_notification_setups jns ON jnpc.setup_id = jns.id
WHERE jns.is_active = 1

UNION ALL

SELECT 
    'Total Jobseekers with Active Setups' as metric,
    COUNT(DISTINCT jns.job_seeker_id) as value
FROM job_notification_setups jns
WHERE jns.is_active = 1;

-- Verification queries (commented out for production)
-- SELECT 'Migration Verification' as status;
-- SELECT * FROM v_notification_system_stats;
-- SELECT * FROM v_notification_setup_categories LIMIT 5;

-- Rollback procedure (commented out for safety)
/*
-- To rollback this migration:
-- DROP VIEW IF EXISTS v_notification_system_stats;
-- DROP VIEW IF EXISTS v_notification_setup_categories;
-- DROP PROCEDURE IF EXISTS SyncNotificationWithScheduleCategories;
-- DROP FUNCTION IF EXISTS GetProviderIdentifiersForSetup;
-- ALTER TABLE job_notification_setups DROP COLUMN provider_name;
-- ALTER TABLE job_notification_setups DROP COLUMN last_sync_at;
-- DROP TABLE IF EXISTS job_notification_provider_category;
*/
