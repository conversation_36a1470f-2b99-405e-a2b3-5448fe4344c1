<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Queue;
use App\Jobs\SendEmailJob;
use <PERSON><PERSON>les\JobSeeker\Entities\JobSeekerSetting;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\EmailSendingLog;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\OutgoingEmail;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception as PHPMailerException;
use Mailtrap\MailtrapClient;
use Mailtrap\Mime\MailtrapEmail;
use Symfony\Component\Mime\Address;
use Mailtrap\Helper\ResponseHelper;
use Resend\Resend;
use Exception;

/**
 * Email Service - Dynamic Email Provider Switchboard with Circuit Breaker Protection
 * 
 * Acts as a central switchboard for email sending, dynamically choosing
 * between providers (Gmail/Mailtrap) and modes (sync/async) based on
 * configuration stored in the jobseeker_settings table.
 * 
 * Features enterprise-grade resilience with database-persistent circuit breaker
 * pattern that prevents cascading failures and implements automatic failover.
 */
final class EmailService
{
    /**
     * Circuit Breaker Service for provider health monitoring
     */
    private CircuitBreakerService $circuitBreaker;

    /**
     * Constructor - Initialize circuit breaker service
     */
    public function __construct(CircuitBreakerService $circuitBreaker)
    {
        $this->circuitBreaker = $circuitBreaker;
    }

    /**
     * Send email using dynamic provider and mode selection with transactional outbox pattern
     * 
     * Implements Oracle's transactional outbox pattern: "Write data into a table and publish 
     * an event as a single transaction" - ensuring atomic persistence before any sending attempts.
     * 
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $body HTML email body
     * @param array $viewData Additional data for email template
     * @param string $view Blade view for email template
     * @param array $attachments Optional attachments
     * @param array $cc Optional CC recipients
     * @param string|null $fromEmail Optional custom from email
     * @param string|null $fromName Optional custom from name
     * @return bool|array Returns true for sync success, array for async queued
     * @throws Exception
     */
    public function send(
        string $to,
        string $subject,
        string $body,
        array $viewData = [],
        string $view = '',
        array $attachments = [],
        array $cc = [],
        ?string $fromEmail = null,
        ?string $fromName = null
    ): bool|array {
        $correlationId = 'email_' . uniqid();

        Log::channel('email')->info('EmailService: Starting email send process with transactional outbox pattern', [
            'correlation_id' => $correlationId,
            'to' => $to,
            'subject' => $subject,
        ]);

        try {
            // Emergency Pause Check - Global Kill Switch
            $mailPaused = JobSeekerSetting::getValue('mail_sending_paused', 'false');
            if ($mailPaused === 'true' || $mailPaused === true) {
                Log::channel('email')->warning('EmailService: Mail sending is globally paused via emergency control', [
                    'correlation_id' => $correlationId,
                    'to' => $to,
                    'subject' => $subject,
                ]);
                
                // Create outbox entry even for paused emails (for audit trail)
                $outgoingEmail = OutgoingEmail::createInOutbox(
                    $to,
                    $subject,
                    $body,
                    'emergency_paused',
                    'emergency_pause',
                    [
                        'view_data' => $viewData,
                        'view' => $view,
                        'attachments' => $attachments,
                        'cc' => $cc,
                        'from_email' => $fromEmail,
                        'from_name' => $fromName,
                        'correlation_id' => $correlationId,
                    ]
                );
                
                $outgoingEmail->markSendFailed('Email sending is globally paused via emergency controls');
                
                // Also create legacy log entry for backward compatibility
                EmailSendingLog::create([
                    'recipient' => $to,
                    'subject' => $subject,
                    'provider' => 'emergency_paused',
                    'mode' => 'emergency_pause',
                    'status' => 'paused',
                    'error_message' => 'Email sending is globally paused via emergency controls',
                ]);
                
                throw new Exception('Email sending is currently paused due to emergency controls. Please contact system administrator.');
            }
            // Step 1: Fetch current configuration from jobseeker_settings (JSON format with fallback)
            $emailConfig = JobSeekerSetting::getEmailConfiguration();
            $emailSendingMode = $emailConfig['mode'];
            $providersPriority = $emailConfig['providers'];

            // Step 1.5: Filter providers through circuit breaker health check
            $healthyProviders = $this->circuitBreaker->getHealthyProviders($providersPriority);
            
            if (empty($healthyProviders)) {
                Log::channel('email')->error('EmailService: All providers are unhealthy according to circuit breaker', [
                    'correlation_id' => $correlationId,
                    'requested_providers' => $providersPriority,
                    'circuit_breaker_states' => $this->circuitBreaker->getAllStates(),
                ]);
                throw new Exception('All email providers are currently unavailable due to circuit breaker protection');
            }

            $primaryProvider = $healthyProviders[0]; // Use first healthy provider as primary

            Log::channel('email')->info('EmailService: Configuration loaded with circuit breaker filtering', [
                'correlation_id' => $correlationId,
                'primary_provider' => $primaryProvider,
                'requested_providers' => $providersPriority,
                'healthy_providers' => $healthyProviders,
                'mode' => $emailSendingMode,
                'using_json_config' => JobSeekerSetting::isUsingJsonConfiguration(),
            ]);

            // Step 2: TRANSACTIONAL OUTBOX - Atomic Email Persistence BEFORE Send Attempt
            // This implements Oracle's pattern: "Write data into a table and publish an event as a single transaction"
            $outgoingEmail = OutgoingEmail::createInOutbox(
                $to,
                $subject,
                $body,
                $primaryProvider,
                $emailSendingMode,
                [
                    'view_data' => $viewData,
                    'view' => $view,
                    'attachments' => $attachments,
                    'cc' => $cc,
                    'from_email' => $fromEmail,
                    'from_name' => $fromName,
                    'correlation_id' => $correlationId,
                    'healthy_providers' => $healthyProviders,
                ]
            );

            Log::channel('email')->info('EmailService: Email stored in transactional outbox before send attempt', [
                'correlation_id' => $correlationId,
                'outgoing_email_id' => $outgoingEmail->id,
                'primary_provider' => $primaryProvider,
                'mode' => $emailSendingMode,
            ]);

            // Step 3: Mode Decision - Async vs Sync
            if ($emailSendingMode === 'async') {
                return $this->sendAsync(
                    $to, $subject, $body, $viewData, $view, $attachments, $cc,
                    $fromEmail, $fromName, $primaryProvider, $correlationId, $outgoingEmail
                );
            }

            // Step 4: Sync Mode - Send immediately with failover and circuit breaker protection
            return $this->sendSyncWithFailover(
                $to, $subject, $body, $viewData, $view, $attachments, $cc,
                $fromEmail, $fromName, $healthyProviders, $correlationId, $outgoingEmail
            );

        } catch (Exception $e) {
            Log::channel('email')->error('EmailService: Critical error in send method', [
                'correlation_id' => $correlationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    /**
     * Handle asynchronous email sending with transactional outbox pattern
     */
    private function sendAsync(
        string $to,
        string $subject,
        string $body,
        array $viewData,
        string $view,
        array $attachments,
        array $cc,
        ?string $fromEmail,
        ?string $fromName,
        string $emailProvider,
        string $correlationId,
        OutgoingEmail $outgoingEmail
    ): array {
        Log::channel('email')->info('EmailService: Processing async email send with transactional outbox', [
            'correlation_id' => $correlationId,
            'provider' => $emailProvider,
            'outgoing_email_id' => $outgoingEmail->id,
        ]);

        // Mark outgoing email as queued
        $outgoingEmail->markAsQueued();

        // Create legacy log entry for backward compatibility
        $logEntry = EmailSendingLog::create([
            'recipient' => $to,
            'subject' => $subject,
            'provider' => $emailProvider,
            'mode' => 'async',
            'status' => 'queued',
            'error_message' => null,
        ]);

        // Prepare email data for the job - now includes outgoing email reference
        $emailData = [
            'to' => ['email' => $to, 'name' => ''],
            'subject' => $subject,
            'body' => $body,
            'viewData' => $viewData,
            'view' => $view,
            'attachments' => $attachments,
            'cc' => $cc,
            'from' => [
                'email' => $fromEmail ?: env('MAIL_FROM_ADDRESS'),
                'name' => $fromName ?: env('MAIL_FROM_NAME'),
            ],
            'provider' => $emailProvider,
            'provider_config' => $this->getProviderConfiguration($emailProvider),
            'log_entry_id' => $logEntry->id,
            'outgoing_email_id' => $outgoingEmail->id, // Include transactional outbox reference
            'correlation_id' => $correlationId,
        ];

        // Dispatch the job
        SendEmailJob::dispatch($emailData);

        Log::channel('email')->info('EmailService: Email job dispatched successfully with transactional outbox', [
            'correlation_id' => $correlationId,
            'log_entry_id' => $logEntry->id,
            'outgoing_email_id' => $outgoingEmail->id,
        ]);

        return [
            'status' => 'queued',
            'log_entry_id' => $logEntry->id,
            'outgoing_email_id' => $outgoingEmail->id,
            'correlation_id' => $correlationId,
        ];
    }

    /**
     * Handle synchronous email sending with failover support and transactional outbox pattern
     */
    private function sendSyncWithFailover(
        string $to,
        string $subject,
        string $body,
        array $viewData,
        string $view,
        array $attachments,
        array $cc,
        ?string $fromEmail,
        ?string $fromName,
        array $providersPriority,
        string $correlationId,
        OutgoingEmail $outgoingEmail
    ): bool {
        Log::channel('email')->info('EmailService: Starting sync email send with transactional outbox failover', [
            'correlation_id' => $correlationId,
            'outgoing_email_id' => $outgoingEmail->id,
            'providers_priority' => $providersPriority,
            'circuit_breaker_states' => $this->circuitBreaker->getAllStates(),
        ]);

        $lastException = null;
        
        foreach ($providersPriority as $index => $provider) {
            Log::channel('email')->info("EmailService: Attempting provider #{$index} with circuit breaker check", [
                'correlation_id' => $correlationId,
                'outgoing_email_id' => $outgoingEmail->id,
                'provider' => $provider,
                'attempt' => $index + 1,
                'total_providers' => count($providersPriority),
            ]);

            // Check circuit breaker before attempting
            if ($this->circuitBreaker->isTripped($provider)) {
                Log::channel('email')->warning("EmailService: Provider #{$index} circuit breaker is OPEN, skipping", [
                    'correlation_id' => $correlationId,
                    'outgoing_email_id' => $outgoingEmail->id,
                    'provider' => $provider,
                    'circuit_breaker_state' => $this->circuitBreaker->getProviderState($provider),
                ]);
                continue;
            }

            try {
                // Mark sending attempt started in outbox
                $outgoingEmail->markSendingStarted();
                
                // Use circuit breaker execute method for automatic success/failure recording
                $result = $this->circuitBreaker->execute($provider, function() use (
                    $to, $subject, $body, $viewData, $view, $attachments, $cc,
                    $fromEmail, $fromName, $provider, $correlationId
                ) {
                    return $this->sendSync(
                        $to, $subject, $body, $viewData, $view, $attachments, $cc,
                        $fromEmail, $fromName, $provider, $correlationId
                    );
                });

                if ($result) {
                    // Mark as successfully sent in outbox
                    $outgoingEmail->markAsSent();
                    
                    Log::channel('email')->info('EmailService: Email sent successfully with transactional outbox failover', [
                        'correlation_id' => $correlationId,
                        'outgoing_email_id' => $outgoingEmail->id,
                        'successful_provider' => $provider,
                        'attempt_number' => $index + 1,
                        'circuit_breaker_state' => $this->circuitBreaker->getProviderState($provider),
                    ]);
                    return true;
                }
            } catch (Exception $e) {
                $lastException = $e;
                Log::channel('email')->warning("EmailService: Provider #{$index} failed with circuit breaker recording", [
                    'correlation_id' => $correlationId,
                    'outgoing_email_id' => $outgoingEmail->id,
                    'provider' => $provider,
                    'error' => $e->getMessage(),
                    'remaining_providers' => count($providersPriority) - $index - 1,
                    'circuit_breaker_state' => $this->circuitBreaker->getProviderState($provider),
                ]);

                // If this is not the last provider, continue to the next one
                if ($index < count($providersPriority) - 1) {
                    continue;
                }
            }
        }

        // All providers failed - mark in outbox
        $errorMessage = $lastException ? $lastException->getMessage() : 'All email providers failed';
        $outgoingEmail->markSendFailed("All providers failed with circuit breaker protection. Last error: {$errorMessage}");
        
        Log::channel('email')->error('EmailService: All providers failed with transactional outbox protection', [
            'correlation_id' => $correlationId,
            'outgoing_email_id' => $outgoingEmail->id,
            'providers_tried' => $providersPriority,
            'final_error' => $errorMessage,
            'final_circuit_breaker_states' => $this->circuitBreaker->getAllStates(),
        ]);

        // Legacy log entry for backward compatibility
        EmailSendingLog::create([
            'recipient' => $to,
            'subject' => $subject,
            'provider' => 'failover_failed_circuit_breaker',
            'mode' => 'sync',
            'status' => 'failure',
            'error_message' => "All providers failed with circuit breaker protection. Last error: {$errorMessage}",
        ]);

        throw new Exception("Email sending failed after trying all healthy providers with circuit breaker protection: {$errorMessage}");
    }

    /**
     * Handle synchronous email sending
     */
    private function sendSync(
        string $to,
        string $subject,
        string $body,
        array $viewData,
        string $view,
        array $attachments,
        array $cc,
        ?string $fromEmail,
        ?string $fromName,
        string $emailProvider,
        string $correlationId
    ): bool {
        Log::channel('email')->info('EmailService: Processing sync email send', [
            'correlation_id' => $correlationId,
            'provider' => $emailProvider,
        ]);

        try {
            // Provider Decision - Gmail, Mailtrap, or Mail
            $result = match ($emailProvider) {
                'gmail' => $this->sendViaGmail(
                    $to, $subject, $body, $viewData, $view, $attachments, $cc,
                    $fromEmail, $fromName, $correlationId
                ),
                'mailtrap' => $this->sendViaMailtrap(
                    $to, $subject, $body, $viewData, $view, $attachments, $cc,
                    $fromEmail, $fromName, $correlationId
                ),
                'mail' => $this->sendViaMail(
                    $to, $subject, $body, $viewData, $view, $attachments, $cc,
                    $fromEmail, $fromName, $correlationId
                ),
                'resend' => $this->sendViaResend(
                    $to, $subject, $body, $viewData, $view, $attachments, $cc,
                    $fromEmail, $fromName, $correlationId
                ),
                default => throw new Exception("Unsupported email provider: {$emailProvider}")
            };

            // Log successful send
            EmailSendingLog::create([
                'recipient' => $to,
                'subject' => $subject,
                'provider' => $emailProvider,
                'mode' => 'sync',
                'status' => 'success',
                'error_message' => null,
            ]);

            Log::channel('email')->info('EmailService: Email sent successfully via sync', [
                'correlation_id' => $correlationId,
                'provider' => $emailProvider,
            ]);

            return true;

        } catch (Exception $e) {
            // Log failed send
            EmailSendingLog::create([
                'recipient' => $to,
                'subject' => $subject,
                'provider' => $emailProvider,
                'mode' => 'sync',
                'status' => 'failure',
                'error_message' => $e->getMessage(),
            ]);

            Log::channel('email')->error('EmailService: Sync email send failed', [
                'correlation_id' => $correlationId,
                'provider' => $emailProvider,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Send email via Gmail using PHPMailer
     */
    private function sendViaGmail(
        string $to,
        string $subject,
        string $body,
        array $viewData,
        string $view,
        array $attachments,
        array $cc,
        ?string $fromEmail,
        ?string $fromName,
        string $correlationId
    ): bool {
        Log::channel('email')->info('EmailService: Sending via Gmail', [
            'correlation_id' => $correlationId,
        ]);

        // Get Gmail credentials from settings
        $gmailConfig = $this->getProviderConfiguration('gmail');

        if (empty($gmailConfig['host']) || empty($gmailConfig['username']) || empty($gmailConfig['password'])) {
            throw new Exception('Gmail SMTP configuration is incomplete. Check Gmail settings in jobseeker_settings.');
        }

        // Generate email content if view is provided and exists
        $htmlContent = (!empty($view) && View::exists($view)) ? 
            View::make($view, array_merge($viewData, ['body' => $body]))->render() : 
            $body;

        // Create PHPMailer instance
        $mail = new PHPMailer(true);

        // Gmail SMTP configuration
        $mail->isSMTP();
        $mail->Host = $gmailConfig['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $gmailConfig['username'];
        $mail->Password = $gmailConfig['password'];
        $mail->SMTPSecure = $gmailConfig['encryption'];
        $mail->Port = (int)$gmailConfig['port'];

        // Set sender and recipient
        $mail->setFrom($fromEmail ?: $gmailConfig['from_email'], $fromName ?: $gmailConfig['from_name']);
        $mail->addAddress($to);

        // Add CC recipients
        foreach ($cc as $ccEmail) {
            $mail->addCC($ccEmail);
        }

        // Add attachments
        foreach ($attachments as $attachment) {
            if (isset($attachment['path']) && file_exists($attachment['path'])) {
                $mail->addAttachment($attachment['path'], $attachment['name'] ?? basename($attachment['path']));
            }
        }

        // Set email content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $htmlContent;
        $mail->AltBody = strip_tags($htmlContent);

        // Add correlation ID to headers
        $mail->addCustomHeader('X-Correlation-ID', $correlationId);

        $mail->send();

        return true;
    }

    /**
     * Send email via Mailtrap using official SDK
     */
    private function sendViaMailtrap(
        string $to,
        string $subject,
        string $body,
        array $viewData,
        string $view,
        array $attachments,
        array $cc,
        ?string $fromEmail,
        ?string $fromName,
        string $correlationId
    ): bool {
        Log::channel('email')->info('EmailService: Sending via Mailtrap', [
            'correlation_id' => $correlationId,
        ]);

        // Get Mailtrap credentials from settings
        $mailtrapConfig = $this->getProviderConfiguration('mailtrap');

        if (empty($mailtrapConfig['api_key'])) {
            throw new Exception('Mailtrap API configuration is incomplete. Check Mailtrap settings in jobseeker_settings.');
        }

        // Generate email content if view is provided and exists
        $htmlContent = (!empty($view) && View::exists($view)) ? 
            View::make($view, array_merge($viewData, ['body' => $body]))->render() : 
            $body;

        // Create Mailtrap client
        $mailtrap = MailtrapClient::initSendingEmails(apiKey: $mailtrapConfig['api_key']);

        // Create email
        $email = (new MailtrapEmail())
            ->from(new Address($fromEmail ?: $mailtrapConfig['from_email'], $fromName ?: $mailtrapConfig['from_name']))
            ->to(new Address($to))
            ->subject($subject)
            ->html($htmlContent)
            ->text(strip_tags($htmlContent));

        // Add CC recipients
        foreach ($cc as $ccEmail) {
            $email->addCc($ccEmail);
        }

        // Add attachments
        foreach ($attachments as $attachment) {
            if (isset($attachment['path']) && file_exists($attachment['path'])) {
                $email->attachFromPath($attachment['path'], $attachment['name'] ?? basename($attachment['path']));
            }
        }

        // Add custom headers
        $email->getHeaders()->addTextHeader('X-Correlation-ID', $correlationId);

        // Send email
        $response = $mailtrap->send($email);

        return true;
    }

    /**
     * Send email via PHP's native mail() function (ideal for local/dev/testing with Mailpit)
     */
    private function sendViaMail(
        string $to,
        string $subject,
        string $body,
        array $viewData,
        string $view,
        array $attachments,
        array $cc,
        ?string $fromEmail,
        ?string $fromName,
        string $correlationId
    ): bool {
        Log::channel('email')->info('EmailService: Sending via PHP mail() function', [
            'correlation_id' => $correlationId,
            'environment' => app()->environment(),
        ]);

        try {
            // Get Mail configuration from settings or fallback to env
            $mailConfig = $this->getProviderConfiguration('mail');
            
            // Generate email content if view is provided and exists
            $htmlContent = (!empty($view) && View::exists($view)) ? 
                View::make($view, array_merge($viewData, ['body' => $body]))->render() : 
                $body;

            // Set up recipient
            $recipientEmail = $to;
            
            // Set up sender
            $senderEmail = $fromEmail ?: $mailConfig['from_email'];
            $senderName = $fromName ?: $mailConfig['from_name'];
            
            // Build headers
            $headers = [
                "From: {$senderName} <{$senderEmail}>",
                "Reply-To: {$senderEmail}",
                "X-Mailer: PHP/" . phpversion(),
                "X-Correlation-ID: {$correlationId}",
                "MIME-Version: 1.0",
            ];

            // Add CC recipients
            if (!empty($cc)) {
                $ccList = implode(', ', $cc);
                $headers[] = "Cc: " . $ccList;
            }

            $messageBody = $htmlContent;

            // Handle attachments
            if (!empty($attachments)) {
                $boundary = "----=" . uniqid(md5(microtime()), true);
                $headers[] = "Content-Type: multipart/mixed; boundary=\"{$boundary}\"";

                $messageBody = "--{$boundary}\r\n";
                $messageBody .= "Content-Type: text/html; charset=UTF-8\r\n";
                $messageBody .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
                $messageBody .= $htmlContent . "\r\n";

                foreach ($attachments as $attachment) {
                    if (isset($attachment['path']) && file_exists($attachment['path'])) {
                        $fileContent = file_get_contents($attachment['path']);
                        $fileName = $attachment['name'] ?? basename($attachment['path']);
                        $mimeType = $attachment['mime'] ?? mime_content_type($attachment['path']) ?: 'application/octet-stream';
                        
                        $messageBody .= "--{$boundary}\r\n";
                        $messageBody .= "Content-Type: {$mimeType}; name=\"{$fileName}\"\r\n";
                        $messageBody .= "Content-Transfer-Encoding: base64\r\n";
                        $messageBody .= "Content-Disposition: attachment; filename=\"{$fileName}\"\r\n\r\n";
                        $messageBody .= chunk_split(base64_encode($fileContent)) . "\r\n";
                    }
                }
                $messageBody .= "--{$boundary}--";
            } else {
                $headers[] = "Content-Type: text/html; charset=UTF-8";
            }

            // Send email using PHP's mail() function
            $sent = mail($recipientEmail, $subject, $messageBody, implode("\r\n", $headers));

            if ($sent) {
                Log::channel('email')->info('EmailService: Email sent successfully via mail() function', [
                    'correlation_id' => $correlationId,
                    'to' => $recipientEmail,
                    'subject' => $subject,
                    'environment' => app()->environment(),
                ]);
                return true;
            } else {
                $error = error_get_last();
                $errorMessage = $error ? $error['message'] : 'Unknown mail() function error';
                throw new Exception("mail() function failed: {$errorMessage}");
            }

        } catch (Exception $e) {
            Log::channel('email')->error('EmailService: Mail function failed', [
                'correlation_id' => $correlationId,
                'to' => $to,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Send email via Resend
     */
    private function sendViaResend(
        string $to,
        string $subject,
        string $body,
        array $viewData,
        string $view,
        array $attachments,
        array $cc,
        ?string $fromEmail,
        ?string $fromName,
        string $correlationId
    ): bool {
        Log::channel('email')->info('EmailService: Sending via Resend', [
            'correlation_id' => $correlationId,
        ]);

        // Get Resend credentials from settings
        $resendConfig = $this->getProviderConfiguration('resend');

        if (empty($resendConfig['api_key'])) {
            throw new Exception('Resend API key is not configured. Please configure your Resend API key in the Email Control Board settings.');
        }

        if (empty($resendConfig['from_email'])) {
            throw new Exception('Resend from email is not configured. Please configure a from email address from a verified domain.');
        }

        // Validate that the from email domain is likely verified
        $domain = substr(strrchr($resendConfig['from_email'], "@"), 1);
        if (in_array($domain, ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'])) {
            throw new Exception("Resend requires domain verification. You cannot use public email domains like {$domain}. Please use an email address from a domain you own and have verified with Resend.");
        }

        // Generate email content if view is provided and exists
        $htmlContent = (!empty($view) && View::exists($view)) ? 
            View::make($view, array_merge($viewData, ['body' => $body]))->render() : 
            $body;

        // Create Resend client
        $resend = Resend::client($resendConfig['api_key']);

        // Prepare email data
        $emailData = [
            'from' => $fromEmail ?: $resendConfig['from_email'],
            'to' => [$to],
            'subject' => $subject,
            'html' => $htmlContent,
            'text' => strip_tags($htmlContent),
        ];

        // Add CC recipients
        if (!empty($cc)) {
            $emailData['cc'] = $cc;
        }

        // Add attachments
        if (!empty($attachments)) {
            $emailData['attachments'] = [];
            foreach ($attachments as $attachment) {
                if (isset($attachment['path']) && file_exists($attachment['path'])) {
                    $emailData['attachments'][] = [
                        'filename' => $attachment['name'] ?? basename($attachment['path']),
                        'content' => base64_encode(file_get_contents($attachment['path'])),
                        'type' => $attachment['mime'] ?? mime_content_type($attachment['path']) ?: 'application/octet-stream',
                    ];
                }
            }
        }

        // Add custom headers
        $emailData['headers'] = [
            'X-Correlation-ID' => $correlationId,
        ];

        // Send email
        $response = $resend->emails->send($emailData);

        Log::channel('email')->info('EmailService: Email sent successfully via Resend', [
            'correlation_id' => $correlationId,
            'resend_id' => $response['id'] ?? null,
        ]);

        return true;
    }

    /**
     * Legacy sendEmail method for backward compatibility
     * 
     * Maintains compatibility with existing codebase while using new dynamic email system
     * 
     * @param array $to Recipient information ['email' => '<EMAIL>', 'name' => 'Name']
     * @param string $subject Email subject
     * @param string $view Blade view path for email template
     * @param array $data Data to pass to the email view
     * @param array $attachments Optional attachments
     * @param array $cc Optional CC recipients
     * @return array Result array with success status and additional info
     * @throws Exception
     */
    public function sendEmail(
        array $to,
        string $subject,
        string $view = '',
        array $data = [],
        array $attachments = [],
        array $cc = []
    ): array {
        $correlationId = 'legacy_email_' . uniqid();

        Log::channel('email')->info('EmailService: Legacy sendEmail method called', [
            'correlation_id' => $correlationId,
            'to' => $to,
            'subject' => $subject,
            'view' => $view,
        ]);

        try {
            // Extract email and name from legacy format
            $recipientEmail = $to['email'] ?? '';
            $recipientName = $to['name'] ?? '';

            if (empty($recipientEmail)) {
                throw new Exception('Recipient email is required');
            }

            // Call the new send() method with proper parameters
            $result = $this->send(
                $recipientEmail,    // to
                $subject,           // subject
                '',                 // body (empty since we're using view)
                $data,              // viewData
                $view,              // view
                $attachments,       // attachments
                $cc,                // cc
                null,               // fromEmail
                null                // fromName
            );

            // Return legacy-compatible response format
            if ($result === true) {
                return [
                    'success' => true,
                    'message' => 'Email sent successfully',
                    'correlation_id' => $correlationId,
                ];
            } elseif (is_array($result)) {
                // Async response
                return [
                    'success' => true,
                    'message' => 'Email queued successfully',
                    'correlation_id' => $correlationId,
                    'status' => $result['status'] ?? 'queued',
                    'log_entry_id' => $result['log_entry_id'] ?? null,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Unknown response from email service',
                    'correlation_id' => $correlationId,
                ];
            }

        } catch (Exception $e) {
            Log::channel('email')->error('EmailService: Legacy sendEmail method failed', [
                'correlation_id' => $correlationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'correlation_id' => $correlationId,
            ];
        }
    }

    /**
     * Safely decrypt a value if it's encrypted, otherwise return as-is
     */
    private function safelyDecrypt(?string $value, ?string $fallback = null): ?string
    {
        if (empty($value)) {
            return $fallback;
        }

        try {
            // Check if the value looks like an encrypted Laravel value
            $decoded = base64_decode($value, true);
            if ($decoded !== false) {
                $json = json_decode($decoded, true);
                if (is_array($json) && isset($json['iv']) && isset($json['value']) && isset($json['mac'])) {
                    // This looks like an encrypted value, try to decrypt it
                    return decrypt($value);
                }
            }
            
            // If it doesn't look encrypted, return as-is
            return $value;
        } catch (Exception $e) {
            // If decryption fails, log the error and return the fallback
            Log::warning('EmailService: Failed to decrypt credential', [
                'error' => $e->getMessage(),
                'fallback_used' => !empty($fallback),
            ]);
            return $fallback ?: $value;
        }
    }

    /**
     * Get provider configuration from jobseeker_settings with credential decryption
     */
    private function getProviderConfiguration(string $provider): array
    {
        return match ($provider) {
            'gmail' => [
                'host' => JobSeekerSetting::getValue('gmail_host', env('GMAIL_HOST')),
                'port' => JobSeekerSetting::getValue('gmail_port', env('GMAIL_PORT', 587)),
                'encryption' => JobSeekerSetting::getValue('gmail_encryption', env('GMAIL_ENCRYPTION', 'tls')),
                'username' => $this->safelyDecrypt(JobSeekerSetting::getValue('gmail_username'), env('GMAIL_USERNAME')),
                'password' => $this->safelyDecrypt(JobSeekerSetting::getValue('gmail_password'), env('GMAIL_PASSWORD')),
                'from_email' => JobSeekerSetting::getValue('gmail_from_email', env('MAIL_FROM_ADDRESS')),
                'from_name' => JobSeekerSetting::getValue('gmail_from_name', env('MAIL_FROM_NAME')),
            ],
            'mailtrap' => [
                'api_key' => $this->safelyDecrypt(JobSeekerSetting::getValue('mailtrap_api_key'), env('MAILTRAP_API_KEY')),
                'from_email' => JobSeekerSetting::getValue('mailtrap_from_email', env('MAIL_FROM_ADDRESS')),
                'from_name' => JobSeekerSetting::getValue('mailtrap_from_name', env('MAIL_FROM_NAME')),
            ],
            'mail' => [
                'from_email' => JobSeekerSetting::getValue('mail_from_email', env('MAIL_FROM_ADDRESS', '<EMAIL>')),
                'from_name' => JobSeekerSetting::getValue('mail_from_name', env('MAIL_FROM_NAME', 'Example App')),
            ],
            'resend' => [
                'api_key' => $this->safelyDecrypt(JobSeekerSetting::getValue('resend_api_key'), env('RESEND_API_KEY')),
                'from_email' => JobSeekerSetting::getValue('resend_from_email', env('MAIL_FROM_ADDRESS')),
                'from_name' => JobSeekerSetting::getValue('resend_from_name', env('MAIL_FROM_NAME')),
            ],
            default => throw new Exception("Unknown email provider: {$provider}")
        };
    }
}
