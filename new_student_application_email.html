<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Student Application</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            line-height: 1.5;
        }
        .email-wrapper {
            width: 100%;
            background-color: #f0f2f5;
            padding: 20px 0;
        }
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background-color: #f0f2f5;
        }
        .header {
            background-color: #6f42c1;
            color: #ffffff;
            padding: 30px;
            text-align: center;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            margin: 0 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }
        .header p {
            margin: 5px 0 0;
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        .content-wrapper {
            margin: 0 20px;
            padding: 0;
        }
        .cards-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .profile-card {
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 35%;
            flex-shrink: 0;
        }
        .main-card {
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 30px;
            flex: 1;
        }
        .profile-pic {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: block;
        }
        .profile-name {
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            margin: 0 0 15px;
            color: #212529;
        }
        .status-badge {
            background-color: #fff3cd;
            color: #856404;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            width: fit-content;
            margin: 0 auto 25px;
        }
        .status-badge svg {
            width: 12px;
            height: 12px;
            margin-right: 4px;
            fill: currentColor;
        }
        .profile-info {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .profile-info li {
            margin-bottom: 12px;
            font-size: 14px;
            color: #212529;
            display: flex;
            align-items: flex-start;
        }
        .profile-info li svg {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            fill: #6c757d;
            flex-shrink: 0;
            margin-top: 2px;
        }
        .section {
            margin-bottom: 25px;
        }
        .section:last-child {
            margin-bottom: 0;
        }
        .section-title {
            font-size: 18px;
            font-weight: 700;
            margin: 0 0 20px;
            color: #212529;
            display: flex;
            align-items: center;
        }
        .section-title svg {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            fill: #6f42c1;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .info-item {
            margin-bottom: 15px;
        }
        .info-item:last-child {
            margin-bottom: 0;
        }
        .info-item-label {
            color: #6c757d;
            margin-bottom: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        .info-item-value {
            color: #212529;
            font-weight: 600;
            font-size: 14px;
            line-height: 1.4;
        }
        .info-item-value.gpa {
            display: flex;
            align-items: center;
        }
        .info-item-value.gpa svg {
            width: 16px;
            height: 16px;
            margin-left: 5px;
            fill: #ffc107;
        }
        .documents-section {
            margin-top: 5px;
        }
        .document-tag {
            background-color: #e2f5ea;
            color: #347d3f;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            margin: 0 8px 8px 0;
        }
        .document-tag svg {
            width: 14px;
            height: 14px;
            margin-right: 6px;
            fill: #347d3f;
        }
        .notes-box {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-size: 14px;
            color: #495057;
            margin-left: 15px;
        }
        .footer-section {
            margin: 20px 20px 0;
            display: flex;
            gap: 10px;
        }
        .footer-btn {
            padding: 12px 20px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
        }
        .footer-btn svg {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
        .btn-primary {
            background-color: #6f42c1;
            color: #ffffff;
            border: none;
        }
        .btn-secondary {
            background-color: #e9ecef;
            color: #495057;
            border: 1px solid #dee2e6;
        }
        .btn-outline {
            background-color: #ffffff;
            color: #495057;
            border: 1px solid #dee2e6;
        }
        
        /* Mobile Responsiveness */
        @media only screen and (max-width: 768px) {
            .container {
                max-width: 100%;
            }
            .header {
                margin: 0 10px;
                padding: 25px 20px;
            }
            .header h1 {
                font-size: 24px;
            }
            .header p {
                font-size: 14px;
            }
            .content-wrapper {
                margin: 0 10px;
            }
            .cards-container {
                flex-direction: column;
                gap: 15px;
                margin-top: 15px;
            }
            .profile-card {
                width: auto;
                padding: 20px;
            }
            .main-card {
                padding: 20px;
            }
            .info-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .footer-section {
                margin: 15px 10px 0;
                flex-direction: column;
                gap: 8px;
            }
            .footer-btn {
                padding: 12px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <table class="container" cellpadding="0" cellspacing="0" role="presentation">
            <tr>
                <td>
                    <div class="header">
                        <h1>New Student Application</h1>
                        <p>Complete applicant profile for review</p>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div class="content-wrapper">
                        <table cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                            <tr>
                                <td>
                                    <div class="cards-container">
                                        <!-- Profile Card (35% width) -->
                                        <div class="profile-card">
                                            <img src="https://i.imgur.com/O1c3t7a.png" alt="Sarah Johnson" class="profile-pic">
                                            <h2 class="profile-name">Sarah Johnson</h2>
                                            <div class="status-badge">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"/>
                                                    <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z"/>
                                                </svg>
                                                Under Review
                                            </div>
                                            <ul class="profile-info">
                                                <li>
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                                    </svg>
                                                    <span><EMAIL></span>
                                                </li>
                                                <li>
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                        <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.02.74-.25 1.02l-2.2 2.2z"/>
                                                    </svg>
                                                    <span>+1 (555) 123-4567</span>
                                                </li>
                                                <li>
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L8.03 12H12v8.93zM17.93 10H12V2.07c3.95.49 7 3.85 7 7.93 0 .62-.08 1.21-.21 1.79L15.97 10z"/>
                                                    </svg>
                                                    <span>United States</span>
                                                </li>
                                                <li>
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                        <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
                                                    </svg>
                                                    <span>Born: March 15, 1998</span>
                                                </li>
                                                <li>
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                                                    </svg>
                                                    <span>123 Main Street, Los Angeles, CA 90210</span>
                                                </li>
                                            </ul>
                                        </div>

                                        <!-- Main Information Card (65% width) -->
                                        <div class="main-card">
                                            <div class="section">
                                                <h3 class="section-title">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                        <path d="M12 3L1 9l11 6 9-4.91V17h2V9L12 3zm0 8.48L6.26 8 12 5.08 17.74 8 12 11.48zM18 17v2h-5v-2h5z"/>
                                                    </svg>
                                                    Academic & Application Information
                                                </h3>
                                                <div class="info-grid">
                                                    <div>
                                                        <div class="info-item">
                                                            <div class="info-item-label">Program Applied</div>
                                                            <div class="info-item-value">Master of Business Administration</div>
                                                        </div>
                                                        <div class="info-item">
                                                            <div class="info-item-label">Previous Education</div>
                                                            <div class="info-item-value">Bachelor of Arts in Economics, UCLA</div>
                                                        </div>
                                                        <div class="info-item">
                                                            <div class="info-item-label">GPA</div>
                                                            <div class="info-item-value gpa">
                                                                3.85/4.0
                                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                                                                </svg>
                                                            </div>
                                                        </div>
                                                        <div class="info-item">
                                                            <div class="info-item-label">English Proficiency</div>
                                                            <div class="info-item-value">IELTS 7.5</div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="info-item">
                                                            <div class="info-item-label">Application Date</div>
                                                            <div class="info-item-value">December 1, 2024</div>
                                                        </div>
                                                        <div class="info-item">
                                                            <div class="info-item-label">Admission ID</div>
                                                            <div class="info-item-value">ADM-2024-001234</div>
                                                        </div>
                                                        <div class="info-item">
                                                            <div class="info-item-label">Emergency Contact</div>
                                                            <div class="info-item-value">Michael Johnson (Father)<br>+1 (555) 987-6543</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="section">
                                                <h3 class="section-title">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                                                    </svg>
                                                    Documents Submitted
                                                </h3>
                                                <div class="documents-section">
                                                    <span class="document-tag">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                                                        </svg>
                                                        Transcripts
                                                    </span>
                                                    <span class="document-tag">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                                                        </svg>
                                                        Personal Statement
                                                    </span>
                                                    <span class="document-tag">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                                                        </svg>
                                                        Letters of Recommendation
                                                    </span>
                                                    <span class="document-tag">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                                                        </svg>
                                                        English Proficiency Test
                                                    </span>
                                                    <span class="document-tag">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                                                        </svg>
                                                        Passport Copy
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="section">
                                                <h3 class="section-title">Additional Notes</h3>
                                                <div class="notes-box">
                                                    Strong academic background with relevant work experience in finance.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div class="footer-section">
                        <a href="#" class="footer-btn btn-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M6 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm-5 6s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H1zM11 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5zm.5 2.5a.5.5 0 0 0 0 1h4a.5.5 0 0 0 0-1h-4zm2 3a.5.5 0 0 0 0 1h2a.5.5 0 0 0 0-1h-2zm-2 3a.5.5 0 0 0 0 1h4a.5.5 0 0 0 0-1h-4z"/>
                            </svg>
                            View Complete Profile
                        </a>
                        <a href="#" class="footer-btn btn-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-1 0V14.5a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h6.636a.5.5 0 0 0 .5-.5z"/>
                                <path fill-rule="evenodd" d="M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0v-5z"/>
                            </svg>
                            Access Student Portal
                        </a>
                        <a href="#" class="footer-btn btn-outline">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M.05 3.555A2 2 0 0 1 2 2h12a2 2 0 0 1 1.95 1.555L8 8.414.05 3.555zM0 4.697v7.104l5.803-3.558L0 4.697zM6.761 8.83l-6.57 4.027A2 2 0 0 0 2 14h12a2 2 0 0 0 1.808-1.144l-6.57-4.027L8 9.586l-1.239-.757zm3.436-.586L16 11.801V4.697l-5.803 3.546z"/>
                            </svg>
                            Contact Student
                        </a>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
