<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Student;
use App\StudentRevisionPlan;
use App\Classes;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class MonthlyPlansRevisionSheet implements WithTitle, WithStyles, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get revision plans data - includes ALL students with active plans
     */
    private function getRevisionPlans(): Collection
    {
        $classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $studentId = $this->filters['studentId'] ?? null;

        // Use plan_year_and_month for filtering
        $planYearMonth = sprintf('%d-%02d', $year, $month);

        // Get all students in the selected classes who have active revision plans
        $studentsWithPlans = Student::with([
            'revisionPlans' => function($query) use ($planYearMonth) {
                $query->where('plan_year_and_month', $planYearMonth)
                      ->where('status', 'active')
                      ->with(['class.center', 'class.programs', 'class.teachers', 'fromSurat', 'toSurat']);
            }
        ])
        ->whereHas('revisionPlans', function($query) use ($classIds, $planYearMonth) {
            $query->whereIn('class_id', $classIds)
                  ->where('plan_year_and_month', $planYearMonth)
                  ->where('status', 'active');
        })
        ->when($studentId, function($query, $studentId) {
            return $query->where('id', $studentId);
        })
        ->get();

        $results = collect();

        foreach ($studentsWithPlans as $student) {
            foreach ($student->revisionPlans as $plan) {
                if (in_array($plan->class_id, $classIds)) {
                    $classProgram = $plan->class->programs->first()->title ?? 'N/A';
                    $teacherNames = $plan->class->teachers->pluck('full_name')->join(', ');
                    $numberOfPages = $this->calculateRevisionPages($plan);

                    $results->push([
                        'centre_id' => $plan->class->center->id ?? 'N/A',
                        'centre_name' => $plan->class->center->name ?? 'N/A',
                        'class_id' => $plan->class_id,
                        'class_program' => $classProgram,
                        'teacher_name' => $teacherNames ?: 'N/A',
                        'month' => $plan->created_at->format('F Y'),
                        'student_id' => $plan->student_id,
                        'student_name' => $student->full_name ?? 'N/A',
                        'from_surah' => $plan->fromSurat->name ?? 'N/A',
                        'from_verse' => $plan->start_from_ayat ?? 'N/A',
                        'to_surah' => $plan->toSurat->name ?? 'N/A',
                        'to_verse' => $plan->to_ayat ?? 'N/A',
                        'no_of_pages' => $numberOfPages,
                        'status' => $plan->status ?? 'N/A',
                        'study_direction' => $plan->study_direction ?? 'N/A',
                    ]);
                }
            }
        }

        return $results;
    }

    /**
     * Calculate number of pages using stored procedure for revision
     */
    private function calculateRevisionPages(StudentRevisionPlan $plan): int
    {
        try {
            if ($plan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating pages for revision plan: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        return [
            'Centre ID',
            'Centre Name',
            'Class ID',
            'Class Program',
            'Teacher Name',
            'Month',
            'Student ID',
            'Student Name',
            'From Surah',
            'From Verse',
            'To Surah',
            'To Verse',
            'No. of Pages',
            'Status',
            'Study Direction',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Revision Plans';
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [];
    }

    /**
     * Register events for creating the table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createTable($event->sheet);
            },
        ];
    }

    /**
     * Create table on the worksheet
     */
    private function createTable($sheet)
    {
        $worksheet = $sheet->getDelegate();

        // Get data
        $revisionData = $this->getRevisionPlans();
        $headings = $this->getTableHeadings();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'] ?? [])->pluck('name')->join(', ');

        $worksheet->setCellValue('A1', "REVISION PLANS - {$classNames} - {$monthName} {$year}");
        $worksheet->mergeCells('A1:O1');

        // Style main title
        $worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E8F4F8']]
        ]);

        $currentRow = 3;

        // Create Table
        $this->createDataTable($worksheet, $currentRow, 'REVISION PLANS', $headings, $revisionData);

        // Auto-size columns
        foreach (range('A', 'O') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    /**
     * Create a single table with title, headers, and data
     */
    private function createDataTable($worksheet, $startRow, $title, $headings, $data): int
    {
        // Set table title
        $worksheet->setCellValue("A{$startRow}", $title);
        $worksheet->mergeCells("A{$startRow}:O{$startRow}");

        // Style table title
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);

        $headerRow = $startRow + 1;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $headerRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$headerRow}:O{$headerRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '4CAF50']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $headerRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
        foreach ($data as $row) {
            $col = 'A';
            foreach ($row as $value) {
                $worksheet->setCellValue($col . $currentDataRow, $value);
                $col++;
            }
            $currentDataRow++;
        }

        // Style data rows
        if ($data->count() > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:O{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Alternate row colors
            for ($row = $dataStartRow; $row <= $dataEndRow; $row++) {
                if (($row - $dataStartRow) % 2 == 1) {
                    $worksheet->getStyle("A{$row}:O{$row}")->applyFromArray([
                        'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'F5F5F5']]
                    ]);
                }
            }

            return $dataEndRow;
        }

        // If no data, add "No data available" message
        $worksheet->setCellValue("A{$dataStartRow}", 'No data available');
        $worksheet->mergeCells("A{$dataStartRow}:O{$dataStartRow}");
        $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'font' => ['italic' => true],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        return $dataStartRow;
    }
}