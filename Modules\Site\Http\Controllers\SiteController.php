<?php

namespace Modules\Site\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Setting;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Modules\Admission\Facades\Settings;
use Illuminate\Validation\ValidationException;
use Config;
use Illuminate\Support\Arr;
use Mail;

class SiteController extends Controller
{
    private $theme_config;

    public function __construct(){
        $this->theme_config = require(module_path('Site') . '/Resources/views/templates/' . config('website_theme') . '/config.php');
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function index()
    {
        $theme_config = $this->theme_config;

        $widgets = unserialize(config('settings.homepage_widgets'));



        if(!$widgets){
            $widgets = [];
        }

        $widgets_settings = $theme_config['widgets'];


        $widgets_vars = [];

        foreach($widgets as $widget){


            $widget_setting = $widgets_settings[$widget] ?? [];
            // dd($widget_setting['widget_settings']);
            // foreach($widget_setting as $setting => $type){
            //     $widgets_vars[$widget.'_'.$setting] = config('settings.'. $widget . '_' . $setting .'_' . config('app.locale') , config('settings.' . $widget . '_' . $setting));
            // }
            // dd($widget_setting);


            if (!empty($widget_setting['number_of_blocks'])) {
                $number_of_blocks = Arr::first($widget_setting['number_of_blocks']['options'] ?? [1]);
            } else {
                $number_of_blocks = 1;
            }

            if(!empty($widget_setting['name']))
              //  dd($widget_setting);

            $widgets_vars[$widget_setting['name'].'_number_of_blocks'] = config('settings.'. $widget_setting['name'].'_number_of_blocks', $number_of_blocks);


            if(!empty($widget_setting['block_elements'])){
                for($i = 1 ; $i <= config('settings.' . $widget . '_number_of_blocks',1) ; $i++ ){
                    foreach ($widget_setting['block_elements'] as $element => $type) {
                        $widgets_vars[$widget . '_' . $element.'_'. $i] = config('settings.' . $widget . '_' . $element.'_'. $i . '_' . config('app.locale'), config('settings.' . $widget . '_' . $element.'_'. $i));                        
                    }
                    
                }
            }
            if (!empty($widget_setting['widget_settings'])) {
                foreach ($widget_setting['widget_settings'] as $element => $type) {
                    $widgets_vars[$widget . '_' . $element ] = config('settings.' . $widget . '_' . $element  . '_' . config('app.locale'), config('settings.' . $widget . '_' . $element ));
                }
            }
            if(!empty($widget_setting['data_source'])){
                $widgets_vars[$widget . '_special_data'] = app('Modules\Site\Http\Controllers\\'.ucwords($widget_setting['data_source']).'Controller')->widget_data();
            }
        }



        extract($widgets_vars);

        
        $slider = [];
        $features = [];
        $posts = [];
        $testimentals = [];


        // return view(theme_path('home') , compact('slider', 'features', 'posts', 'testimentals'));
        return view(theme_path('home') , compact('widgets' , array_keys($widgets_vars)));
    }


    /**
     * Show the specified resource.
     * @return View
     */
    public function registeration()
    {
        // dd(theme_path('registeration'));
        return view(theme_path('registration'));
    }

    /**
     * Show the specified resource.
     * @return View
     */
    public function contact()
    {
        // dd(theme_path('registeration'));
        return view(theme_path('contact_page'));
    }

    public function sendContact(\App\Http\Requests\ContactFormRequest $request)
    {
        try {
            // Start database transaction
            \DB::beginTransaction();
            
            // Manually verify Turnstile token
            $verification = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
                'secret'   => config('services.turnstile.secret'),
                'response' => $request->input('cf-turnstile-response'),
                'remoteip' => $request->ip(),
            ]);

            $verificationResult = $verification->json();
            
            if (!($verificationResult['success'] ?? false)) {
                \Log::warning('Contact form Turnstile validation failed', [
                    'ip' => $request->ip(),
                    'response' => $verificationResult
                ]);
                
                session()->flash('error', 'CAPTCHA verification failed. Please try again.');
                return redirect()->back()->withInput();
            }

            // Prepare email content
            $emailBody = "
                <h2>New Contact Form Submission</h2>
                <p><strong>Name:</strong> {$request->name}</p>
                <p><strong>Email:</strong> {$request->email}</p>
                <p><strong>Phone:</strong> " . ($request->phone ?: 'Not provided') . "</p>
                <p><strong>Subject:</strong> {$request->subject}</p>
                <p><strong>Department:</strong> {$request->department}</p>
                <p><strong>Message:</strong></p>
                <div style='border-left: 4px solid #ccc; padding-left: 15px; margin: 10px 0;'>
                    " . nl2br(e($request->message)) . "
                </div>
                <hr>
                <p><small>This message was sent from the contact form on " . config('app.url') . " at " . now()->format('Y-m-d H:i:s') . "</small></p>
            ";

            // Send email using EmailService
            $emailService = app(\App\Services\EmailService::class);
            
            $result = $emailService->send(
                to: config('settings.support_email', '<EMAIL>'),
                subject: "Contact Form: {$request->subject}",
                body: $emailBody,
                viewData: [
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'subject' => $request->subject,
                    'department' => $request->department,
                    'message' => $request->message,
                ],
                view: '',
                attachments: [],
                cc: [],
                fromEmail: $request->email,
                fromName: $request->name
            );

            // Log successful contact form submission
            \Log::info('Contact form submission successful', [
                'name' => $request->name,
                'email' => $request->email,
                'subject' => $request->subject,
                'department' => $request->department,
                'ip' => $request->ip()
            ]);

            // Commit transaction
            \DB::commit();

            session()->flash('success', 'Thank you for contacting us. We received your message and will reply shortly.');
            
            return redirect()->back();
            
        } catch (\Exception $e) {
            // Rollback transaction on error
            if (\DB::transactionLevel() > 0) {
                \DB::rollBack();
            }
            
            // Log error
            \Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'email' => $request->email ?? 'unknown',
                'subject' => $request->subject ?? 'unknown'
            ]);

            session()->flash('error', 'Sorry, there was an error sending your message. Please try again or contact us directly.');
            
            return redirect()->back()->withInput();
        }
    }

    public function payment()
    {
        
        return view(theme_path('payment_form'));
    }

    public function addpayment(Request $request)
    {
       
        return view(theme_path('payment_form'));
    }
    

}
