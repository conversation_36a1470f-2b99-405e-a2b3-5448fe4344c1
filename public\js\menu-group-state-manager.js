/**
 * Menu Group State Manager
 * 
 * Handles menu group expand/collapse state management with persistence
 * Auto-expands groups when navigating to contained pages
 * Creates visual indicators for active menu items
 */
class MenuGroupStateManager {
    constructor() {
        this.preferenceManager = new PreferenceManager();
        this.config = {
            selectors: {
                menuGroups: '[data-toggle="collapse"], [data-bs-toggle="collapse"]',
                menuItems: '.nav-link, a[href]',
                collapseTargets: '.collapse',
                sidebar: '#sidebar',
                activeClass: 'active',
                expandedClass: 'show',
                arrowClass: 'nav-group-arrow'
            },
            attributes: {
                target: 'data-target',
                bsTarget: 'data-bs-target',
           