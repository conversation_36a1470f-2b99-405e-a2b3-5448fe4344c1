<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redesigned - Students With Missing Data</title>
    <!-- Bootstrap 3.3.7 CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .modal-dialog {
            width: 95%;
            max-width: 1200px;
        }
        .panel-heading .panel-title {
            font-size: 16px;
            font-weight: bold;
        }
        .panel-filter {
            background-color: #f9f9f9;
            border-bottom: 1px solid #ddd;
        }
        .panel-bulk-actions {
            background-color: #f0f8ff;
            border: 1px solid #bce8f1;
            margin-bottom: 15px;
        }
        .panel-bulk-actions .panel-body {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .table-actions {
            white-space: nowrap;
            width: 1%;
        }
        .table>thead>tr>th {
            vertical-align: middle;
            font-weight: bold;
        }
        .table>tbody>tr>td {
            vertical-align: middle;
        }
        .form-group {
            margin-bottom: 0;
        }
        .filter-controls {
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }
        .v-divider {
            border-left: 1px solid #ccc;
            height: 34px;
            margin: 0 10px;
        }
        #check-all {
            cursor: pointer;
        }
    </style>
</head>
<body>

<!-- This is a simulation of the modal content. In a real app, this would be inside a .modal-content div. -->
<div class="modal-content">
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">Students With Missing Data</h4>
    </div>
    <div class="modal-body">

        <!-- Filters Panel -->
        <div class="panel panel-default panel-filter">
            <div class="panel-body">
                <form class="form-inline">
                    <div class="filter-controls">
                        <div class="form-group">
                            <label for="userStatus">User Status</label>
                            <select id="userStatus" class="form-control">
                                <option selected>Students without User</option>
                                <option>Students with User</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="programFilter">Program</label>
                            <select id="programFilter" class="form-control">
                                <option selected>All Programs</option>
                                <option>Program A</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="centerFilter">Center</label>
                            <select id="centerFilter" class="form-control">
                                <option selected>All Centers</option>
                                <option>Center A</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="classFilter">Class</label>
                            <select id="classFilter" class="form-control">
                                <option selected>All Classes</option>
                                <option>Class A</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary"><i class="fa fa-filter"></i> Apply Filters</button>
                        <button type="button" class="btn btn-default"><i class="fa fa-eraser"></i> Clear</button>
                        <button type="button" class="btn btn-default"><i class="fa fa-refresh"></i> Refresh</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Bulk Actions Panel -->
        <div class="panel panel-info panel-bulk-actions">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-cogs"></i> Bulk Actions</h3>
            </div>
            <div class="panel-body">
                <div class="form-group">
                    <label for="bulkProgram">Assign Program</label>
                    <select id="bulkProgram" class="form-control">
                        <option value="">Select Program</option>
                        <option>Computer Science</option>
                        <option>English Literature</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="bulkCenter">Assign Center</label>
                    <select id="bulkCenter" class="form-control">
                        <option value="">Select Center</option>
                        <option>Main Campus</option>
                        <option>Downtown Center</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="bulkClass">Assign Class</label>
                    <select id="bulkClass" class="form-control">
                        <option value="">Select Class</option>
                        <option>CS 101</option>
                        <option>ENG 201</option>
                    </select>
                </div>
                <button class="btn btn-success"><i class="fa fa-check"></i> Apply to Selected</button>
                <div class="v-divider"></div>
                <button class="btn btn-primary"><i class="fa fa-user-plus"></i> Create Users for Selected</button>
            </div>
        </div>

        <!-- Students Table -->
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <thead>
                    <tr>
                        <th class="text-center" style="width: 1%;"><input type="checkbox" id="check-all" title="Select All"></th>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th>Program</th>
                        <th>Center</th>
                        <th>Class</th>
                        <th>Username</th>
                        <th>Created At</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Sample Row 1 -->
                    <tr>
                        <td class="text-center"><input type="checkbox" class="student-check"></td>
                        <td>Moumen Yaser Najib</td>
                        <td><EMAIL></td>
                        <td><span class="text-danger">Not Assigned</span></td>
                        <td><span class="text-danger">Not Assigned</span></td>
                        <td><span class="text-danger">Not Assigned</span></td>
                        <td><span class="text-muted">No User</span></td>
                        <td>3 years ago</td>
                        <td class="text-center table-actions">
                            <button class="btn btn-sm btn-primary" title="Create User"><i class="fa fa-user-plus"></i> Create</button>
                        </td>
                    </tr>
                    <!-- Sample Row 2 -->
                    <tr>
                        <td class="text-center"><input type="checkbox" class="student-check"></td>
                        <td>Renad Alaa Mohamad Alfares</td>
                        <td><EMAIL></td>
                        <td>Computer Science</td>
                        <td><span class="text-danger">Not Assigned</span></td>
                        <td><span class="text-danger">Not Assigned</span></td>
                        <td><span class="text-muted">No User</span></td>
                        <td>3 years ago</td>
                        <td class="text-center table-actions">
                            <button class="btn btn-sm btn-primary" title="Create User"><i class="fa fa-user-plus"></i> Create</button>
                        </td>
                    </tr>
                    <!-- Sample Row 3 -->
                    <tr>
                        <td class="text-center"><input type="checkbox" class="student-check"></td>
                        <td>Saedd Ahmed</td>
                        <td><EMAIL></td>
                        <td><span class="text-danger">Not Assigned</span></td>
                        <td>Main Campus</td>
                        <td><span class="text-danger">Not Assigned</span></td>
                        <td><span class="text-muted">No User</span></td>
                        <td>2 years ago</td>
                        <td class="text-center table-actions">
                            <button class="btn btn-sm btn-primary" title="Create User"><i class="fa fa-user-plus"></i> Create</button>
                        </td>
                    </tr>
                    <!-- Sample Row 4 (All data assigned) -->
                    <tr>
                        <td class="text-center"><input type="checkbox" class="student-check"></td>
                        <td>Saeed Ahmed</td>
                        <td><EMAIL></td>
                        <td>English Literature</td>
                        <td>Downtown Center</td>
                        <td>ENG 201</td>
                        <td><span class="text-muted">No User</span></td>
                        <td>2 years ago</td>
                        <td class="text-center table-actions">
                            <button class="btn btn-sm btn-primary" title="Create User"><i class="fa fa-user-plus"></i> Create</button>
                        </td>
                    </tr>
                    <!-- Add more sample rows as needed -->
                </tbody>
            </table>
        </div>
        <div class="row">
            <div class="col-md-6">
                Showing 1 to 4 of 10 entries
            </div>
            <div class="col-md-6 text-right">
                <ul class="pagination" style="margin: 0;">
                    <li class="disabled"><span>&laquo;</span></li>
                    <li class="active"><span>1</span></li>
                    <li><a href="#">2</a></li>
                    <li><a href="#">3</a></li>
                    <li><a href="#">&raquo;</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
    </div>
</div>

<!-- jQuery and Bootstrap 3.3.7 JS -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
<script>
    $(document).ready(function(){
        // Toggle all checkboxes
        $("#check-all").click(function(){
            $(".student-check").prop('checked', $(this).prop('checked'));
        });
    });
</script>

</body>
</html>
