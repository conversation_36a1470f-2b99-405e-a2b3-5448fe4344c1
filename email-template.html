<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Professional Email Template</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset and base styles for email clients */
        
        body,
        table,
        td,
        p,
        a,
        li,
        blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table,
        td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border-collapse: collapse;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f5f7fa;
            font-family: Arial, Helvetica, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333333;
        }
        /* Preheader */
        
        .preheader {
            display: none;
            max-height: 0;
            max-width: 0;
            opacity: 0;
            overflow: hidden;
            mso-hide: all;
            font-size: 1px;
            line-height: 1px;
        }
        /* Main wrapper */
        
        .email-wrapper {
            width: 100%;
            background-color: #f5f7fa;
            padding: 20px 0;
        }
        
        .email-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        /* Header */
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            text-align: center;
        }
        
        .logo {
            width: 120px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            margin: 0 auto 20px;
            line-height: 40px;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin: 0 0 8px 0;
            letter-spacing: -0.5px;
            color: white;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
            margin: 0;
            color: white;
        }
        /* Content wrapper */
        
        .content-wrapper {
            padding: 40px 30px;
            background-color: #f8fafc;
        }
        /* Table-based layout for email compatibility */
        
        .main-table {
            width: 100%;
            border-collapse: collapse;
        }
        /* Card base styles */
        
        .card {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 28px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
            vertical-align: top;
        }
        /* Profile card */
        
        .profile-card {
            border-left: 4px solid #667eea;
            width: 280px;
        }
        
        .profile-header {
            text-align: center;
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0 auto 16px;
            line-height: 80px;
            text-align: center;
            color: white;
            font-size: 32px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: 600;
            color: #1a202c;
            margin: 0 0 4px 0;
        }
        
        .profile-title {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
            margin: 0;
        }
        
        .detail-item {
            margin-bottom: 16px;
        }
        
        .detail-label {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0 0 4px 0;
        }
        
        .detail-value {
            font-size: 14px;
            color: #1a202c;
            font-weight: 500;
            margin: 0;
        }
        /* Main content card */
        
        .main-card {
            border-left: 4px solid #10b981;
        }
        
        .main-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a202c;
            margin: 0 0 8px 0;
        }
        
        .main-subtitle {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
            margin: 0;
        }
        
        .form-section {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin: 0 0 16px 0;
            padding-left: 16px;
            border-left: 4px solid #10b981;
        }
        
        .form-grid {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 16px;
        }
        
        .form-row {
            border-bottom: 1px solid #f1f5f9;
        }
        
        .form-label {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 8px 12px 4px 0;
            width: 30%;
            vertical-align: top;
        }
        
        .form-value {
            font-size: 14px;
            color: #1a202c;
            font-weight: 500;
            padding: 8px 12px;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            margin: 4px 0;
        }
        /* Status badge */
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status-approved {
            background-color: #d1fae5;
            color: #065f46;
        }
        /* Action buttons */
        
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background-color: #667eea;
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background-color: #f8fafc;
            color: #374151;
            border: 1px solid #e2e8f0;
        }
        /* Company signature */
        
        .signature {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
        
        .signature-name {
            font-size: 16px;
            font-weight: 600;
            color: #1a202c;
            margin: 0 0 4px 0;
        }
        
        .signature-title {
            font-size: 14px;
            color: #64748b;
            margin: 0 0 8px 0;
        }
        
        .signature-company {
            font-size: 14px;
            font-weight: 600;
            color: #667eea;
            margin: 0;
        }
        /* Footer */
        
        .footer {
            padding: 24px 30px;
            background-color: #f1f5f9;
            border-top: 1px solid #e2e8f0;
            text-align: center;
        }
        
        .footer p {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .footer-content {
            margin-bottom: 16px;
        }
        
        .social-links {
            margin: 16px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 8px;
            padding: 8px;
            background-color: #667eea;
            color: white;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
        }
        /* Tab navigation */
        
        .tab-navigation {
            background-color: #ffffff;
            border-top: 1px solid #e2e8f0;
            padding: 0;
            margin: 0;
        }
        
        .tab-container {
            display: table;
            width: 100%;
            table-layout: fixed;
        }
        
        .tab-item {
            display: table-cell;
            text-align: center;
            border-right: 1px solid #e2e8f0;
            vertical-align: middle;
        }
        
        .tab-item:last-child {
            border-right: none;
        }
        
        .tab-link {
            display: block;
            padding: 16px 12px;
            text-decoration: none;
            color: #64748b;
            font-size: 13px;
            font-weight: 500;
            border-bottom: 3px solid transparent;
        }
        
        .tab-link:hover {
            background-color: #f8fafc;
            color: #374151;
        }
        
        .tab-link.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background-color: #f8fafc;
        }
        
        .tab-icon {
            display: block;
            font-size: 16px;
            margin-bottom: 4px;
        }
        /* Responsive design */
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0 10px;
                border-radius: 8px;
            }
            .content-wrapper {
                padding: 24px 20px;
            }
            .main-table td {
                display: block;
                width: 100% !important;
            }
            .profile-card {
                width: 100% !important;
                margin-bottom: 20px;
            }
            .btn {
                display: block;
                margin: 8px 0;
            }
        }
    </style>
</head>

<body>
    <!-- Preheader text -->
    <div class="preheader">
        Your leave application has been processed. View details and take action.
    </div>

    <!-- Email wrapper -->
    <div class="email-wrapper">
        <div class="email-container">
            <!-- Header Section -->
            <div class="header">
                <div class="logo">COMPANY</div>
                <h1>Application Notification</h1>
                <p>Your application has been processed</p>
            </div>

            <!-- Content Wrapper -->
            <div class="content-wrapper">
                <!-- Main content table -->
                <table class="main-table" cellpadding="0" cellspacing="0">
                    <tr>
                        <!-- Profile Card -->
                        <td class="card profile-card">
                            <div class="profile-header">
                                <div class="profile-avatar">JD</div>
                                <div class="profile-name">John Doe</div>
                                <div class="profile-title">Software Engineer</div>
                            </div>

                            <div class="profile-details">
                                <div class="detail-item">
                                    <div class="detail-label">Employee ID</div>
                                    <div class="detail-value">EMP-2024-001</div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Department</div>
                                    <div class="detail-value">Engineering</div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Email</div>
                                    <div class="detail-value"><EMAIL></div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Phone</div>
                                    <div class="detail-value">+****************</div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-label">Status</div>
                                    <div class="detail-value">
                                        <span class="status-badge status-approved">Active</span>
                                    </div>
                                </div>
                            </div>
                        </td>

                        <!-- Spacer -->
                        <td style="width: 24px;"></td>

                        <!-- Main Content Card -->
                        <td class="card main-card">
                            <div class="main-header">
                                <div class="main-title">Leave Application</div>
                                <div class="main-subtitle">Application #LA-2024-0156</div>
                            </div>

                            <div class="form-section">
                                <div class="section-title">Application Details</div>

                                <table class="form-grid" cellpadding="0" cellspacing="0">
                                    <tr class="form-row">
                                        <td class="form-label">Leave Type</td>
                                        <td>
                                            <div class="form-value">Annual Leave</div>
                                        </td>
                                    </tr>
                                    <tr class="form-row">
                                        <td class="form-label">Duration</td>
                                        <td>
                                            <div class="form-value">5 Days</div>
                                        </td>
                                    </tr>
                                    <tr class="form-row">
                                        <td class="form-label">Start Date</td>
                                        <td>
                                            <div class="form-value">March 15, 2024</div>
                                        </td>
                                    </tr>
                                    <tr class="form-row">
                                        <td class="form-label">End Date</td>
                                        <td>
                                            <div class="form-value">March 19, 2024</div>
                                        </td>
                                    </tr>
                                    <tr class="form-row">
                                        <td class="form-label">Reason</td>
                                        <td>
                                            <div class="form-value">Family vacation and personal time off</div>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="form-section">
                                <div class="section-title">Approval Status</div>

                                <table class="form-grid" cellpadding="0" cellspacing="0">
                                    <tr class="form-row">
                                        <td class="form-label">Current Status</td>
                                        <td>
                                            <div class="form-value">
                                                <span class="status-badge status-pending">Pending Review</span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="form-row">
                                        <td class="form-label">Submitted Date</td>
                                        <td>
                                            <div class="form-value">March 10, 2024</div>
                                        </td>
                                    </tr>
                                    <tr class="form-row">
                                        <td class="form-label">Reviewer</td>
                                        <td>
                                            <div class="form-value">Sarah Johnson</div>
                                        </td>
                                    </tr>
                                    <tr class="form-row">
                                        <td class="form-label">Expected Response</td>
                                        <td>
                                            <div class="form-value">March 12, 2024</div>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <a href="#" class="btn btn-primary">View Full Application</a>
                                <a href="#" class="btn btn-secondary">Download PDF</a>
                            </div>

                            <!-- Company Signature -->
                            <div class="signature">
                                <div class="signature-name">Sarah Johnson</div>
                                <div class="signature-title">HR Manager</div>
                                <div class="signature-company">Company Name Inc.</div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Tab Navigation -->
            <div class="tab-navigation">
                <div class="tab-container">
                    <div class="tab-item">
                        <a href="#" class="tab-link active">
                            <span class="tab-icon">📋</span> Applications
                        </a>
                    </div>
                    <div class="tab-item">
                        <a href="#" class="tab-link">
                            <span class="tab-icon">📊</span> Reports
                        </a>
                    </div>
                    <div class="tab-item">
                        <a href="#" class="tab-link">
                            <span class="tab-icon">⚙️</span> Settings
                        </a>
                    </div>
                    <div class="tab-item">
                        <a href="#" class="tab-link">
                            <span class="tab-icon">👤</span> Profile
                        </a>
                    </div>
                    <div class="tab-item">
                        <a href="#" class="tab-link">
                            <span class="tab-icon">📞</span> Support
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-content">
                <p>This is an automated message from the HR Management System</p>
                <p>For questions, please contact <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>

            <div class="social-links">
                <a href="#">LinkedIn</a>
                <a href="#">Twitter</a>
                <a href="#">Facebook</a>
            </div>

            <p>&copy; 2024 Company Name. All rights reserved.</p>
            <p><a href="#">Unsubscribe</a> | <a href="#">Privacy Policy</a></p>
        </div>
    </div>
    </div>
</body>

</html>