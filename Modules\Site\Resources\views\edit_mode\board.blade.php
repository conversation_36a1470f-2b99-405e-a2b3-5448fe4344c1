@if(Auth::guard('employee')->check() && $edit_mode)
<editor-container class="">
	<editor-h2 class="col-md-2 text-center">Edit Mode</editor-h2>
	<editor-div class="col-md-10">
		<editor-btn class="btn btn-success" >Site Setting</editor-btn>
		<a class="btn btn-success" data-toggle="modal" href='#add_menu_page'>Add Menu Page</a>
		<button class="btn btn-success" onclick="viewAvailableWidgets()" >View Avialable Widgets</button>
	</editor-div>
</editor-container>

<div class="modal fade" id="add_menu_page">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">Add Menu Page</h4>
			</div>
			<div class="modal-body">
				<form action="" method="POST" role="form">
					<legend>Form title</legend>
				
					<div class="form-group">
						<label for="">label</label>
						<input type="text" class="form-control" id="" placeholder="Input field">
					</div>
				
					
				
					<button type="submit" class="btn btn-primary">Submit</button>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				<button type="button" class="btn btn-primary">Save changes</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="widget_editor_modal">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">Edit Widget</h4>
			</div>
			<div class="modal-body" id="widget_editor_content">

			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

@if(isset($menu))
<div class="editor-footer">
	<button class="btn btn-danger" id="updateMenuContent">Save</button> <span id="loading" style="display:none"><i class="fa fa-spinner fa-spin "></i></span>
</div>
@endif

<style type="text/css">
	body{
		margin-top: 50px;
	}
	editor-h2{
		display: block;
		font-size: 18px;
	}
	editor-container {
		height: 50px;
		background-color: #000;
		position: fixed;
		top: 0px;
		width: 100%;
		z-index: 10000;
		color: #fff;
	}
	#header.fixed {
    	top: 50px;
	}
	.editor-footer{
		position: fixed;
		bottom: 0;
		height: 50px;
		background: #000;
		width: 100%;
		padding:10px;
		z-index: 10000;
		text-align: center;
	}

	.edit_widget {
		position: relative;
		top: 0;
		left: 0;
		float: right;
		z-index:1000;
		margin: 0 5px;
	}
	#loading_bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #0000007d;
    text-align: center;
    color: #fff;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20%;
    font-size: 50px;
}
</style>

@section('js')
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>


`<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.6/summernote.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.8/summernote.js"></script>`
{{--  <script src="{{ asset('assets/common/js/summernote.js') }}"></script>  --}}
<script src="{{ asset('assets/common/js/summernote-cleaner.js') }}"></script>
<script src="{{ asset('assets/lfm/js/lfm.js') }}"></script>

<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

<script>
	var page_data = [];

	// Define function to open filemanager window
	var lfm = function(options, cb) {
		var route_prefix = (options && options.prefix) ? options.prefix : '/en/manage/uploader';
		window.open(route_prefix + '?type=' + options.type || 'file', 'FileManager', 'width=900,height=600');
		window.SetUrl = cb;
	};
	
	var LFMImgButton = function(context) {
        var ui = $.summernote.ui;
          var button = ui.button({
            contents: '<i class="note-icon-picture"></i> ',
            tooltip: 'Insert image with filemanager',
            click: function() {
	    
                lfm({type: 'image', prefix: '/en/manage/uploader'}, function(url, path) {
                    context.invoke('insertImage', url);
                });

            }
        });
        return button.render();
    };
	var LFMDocButton = function(context) {
        var ui = $.summernote.ui;

        var button = ui.button({
            contents: '<i class="fa fa-file-pdf-o"></i> ',
            tooltip: 'Upload PDF with filemanager',
            click: function() {
	    
                lfm({type: 'link', prefix: '/en/manage/uploader'}, function(url, path) {
                    context.invoke('createLink',{
                    text: 'Doc Link',
                    url: url,
                    isNewWindow: true
                    });
                });


            }
        });
        return button.render();
    };

	var fullEditorConfig = {
		toolbar:[
			['cleaner',['cleaner']], // The Button
			['style',['style']],
			['font',['bold','italic','underline','clear']],
			['color',['color']],
			['para',['ul','ol','paragraph']],
			['height',['height']],
			['table',['table']],
			['insert',['media','video','link','hr']],
			['view',['fullscreen','codeview']],
			['popovers', ['lfm']],
			['popovers', ['lfmdoc']],
			['help',['help']]
		]
		,buttons: {
			lfm: LFMImgButton,
			lfmdoc: LFMDocButton
		},
		cleaner:{
			notTime: 2400, // Time to display Notifications.
			action: 'both', // both|button|paste 'button' only cleans via toolbar button, 'paste' only clean when pasting content, both does both options.
			newline: '<br>', // Summernote's default is to use '<p><br></p>'
			notStyle: 'position:absolute;top:0;left:0;right:0', // Position of Notification
			icon: '<i class="fa fa-eraser"></i>',
			keepHtml: false, // Remove all Html formats
			{{--  keepOnlyTags: ['<p>', '<br>', '<ul>', '<li>', '<b>', '<strong>','<i>', '<a>'], // If keepHtml is true, remove all tags except these  --}}
			keepClasses: false, // Remove Classes
			badTags: ['style', 'script', 'applet', 'embed', 'noframes', 'noscript', 'html'], // Remove full tags with contents
			badAttributes: ['style', 'start'] // Remove attributes from remaining tags
		},
		minHeight : 300
		
	};

	var getData = function () {

		var data = {};

		data['_token'] = "{{ csrf_token() }}";
		data['language'] = "{{ config('app.locale') }}";

		$.each(page_data , function(index, el) {
			// console.log(el.dataField, quillGetHTML(el.editor.getContents()));
			if(el.type == 'plain'){
				data[$(el).attr('data-field')] = $(el).summernote('text');

			}else{
				data[$(el).attr('data-field')] = $(el).summernote('code'); //$("['data-field="+$(el).attr('data-field')+"']"]).html();
			}
		});
		return data;

	}
	$(function () {
		setTimeout(function() {
			$('[editor]').each(function(index, el) {
				if($(el).attr('editor-type') == 'plain'){
					page_data[index] = $(el).summernote({
						toolbar:[]
					});
				}else{
					page_data[index] = $(el).summernote(fullEditorConfig);
				}
			});
				
		}, 1000);
		
	});

	@if(isset($menu))

	$('#updateMenuContent').click(function(){
		$.ajax({
			type: "post",
			url: "{{ route('menu.update_inplace', $menu->id) }}",
			data: getData(),
			dataType: "json",
			beforeSend: function () {
				$('#loading').show();
			},
			complete: function () {
				$('#loading').hide();				
			},
			success: function (response) {
				if(response.status == "success"){
					swal("Good job!", "The Page Has been Saved Succesfully!", "success",{
						timer: 2000,
					});
				}else{
					swal("Sorry!", "An error has been occured, Please check all the page data!", "danger");
				}
				console.log(response);
			},
			error:function (response) {
				swal("Sorry!", "An error has been occured, Please check all the page data!", "danger");
			}
		});
	});
	@endif

	$(document).ready(function(){
		$.each($('#widgets').children(), function(i ,el){
			$(el).prepend("<button class='edit_widget btn-xs btn-danger' onclick=\"deleteWidget('"+el.id+"')\">Delete Widget</button>");
			$(el).prepend("<button class='edit_widget btn-xs btn-success' onclick=\"openWidgetEditor('"+el.id+"')\">Edit Widget</button>");
		})
	})

	var openWidgetEditor = function(widget_name , save = false){
		var openWidget = function(widget_name){
					$('widget_editor_content').text('');

		$.ajax({
			type: "get",
			url: "{{ route('edit_widget' , '') }}/"+widget_name,
			success: function (response) {
				$('#widget_editor_content').html(response);
				$('#widget_editor_modal').modal('show');
				{{--  console.log(response);  --}}
			}
		});

		}
		if(save){
			$('#'+widget_name).removeClass('view');
			done = sortWidgets().then(function(){ 
				openWidget(widget_name);
			    $('#'+widget_name+'_btns').html("<button class='edit_widget btn-xs btn-success' onclick=\"openWidgetEditor('"+widget_name+"')\">Edit Widget</button>");

			});
			
		}else{
			openWidget(widget_name)
		}		
	}

	var deleteWidget = function(widget_name){
		var msg = confirm("Do you want to remove this widget?");

		if (msg){
			$('#'+widget_name).remove();
			sortWidgets();			
		}

	}
	        
	$("#widgets").sortable({
		update: function (event, ui) {
			sortWidgets();
		}
	});
	var sortWidgets = function () {  
		var data = {};
		data._token = "{{ csrf_token() }}";
		data.widgets = [];
		$.each($('#widgets').children(), function (i, el) {
			if (!$(el).hasClass('view')) {
				data.widgets.push(el.id);
			}
		});
		return $.ajax({
			type: "post",
			url: "{{ route('sort_widgets') }}",
			data: data,
			success: function (response) {
				swal("Good job!", "Widgets Have been Sorted Succesfully!", "success", {
					timer: 2000,
				});
				return 'done';
			}
		});	
	}
	var viewAvailableWidgets = function () {  
		$.ajax({
				type: "get",
				url: "{{ route('get_widgets') }}",
				success: function (response) {
					// swal("Good job!", "Widgets Have been Sorted Succesfully!", "success", {
					// 	timer: 2000,
					// });
					$('#widgets').append(response);
						$($(response)).each(function (index, el) {
							if(el.id){
								$('#'+el.id).addClass('view');
								$('#'+el.id).prepend("<span id='"+el.id+"_btns'><button class='edit_widget btn-xs btn-danger' id='"+el.id+"_use_btn' onclick=\"openWidgetEditor('" + el.id + "', true )\">Use This Widget</button></span>");			
							}
							console.log(el.id) 
						})
				}
			});	
	}	

</script>
@append
@endif