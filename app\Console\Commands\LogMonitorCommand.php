<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;
use Carbon\Carbon;

class LogMonitorCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:monitor
                            {--last-lines=100 : Number of lines to check from the end of each log file}
                            {--date=today : Specific date to check logs for (format: Y-m-d or "today")}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor log files for errors and send email notification if issues are found';

    /**
     * Keywords that indicate problematic issues in logs
     * 
     * @var array
     */
    protected $problemKeywords = [
        'error',
        'exception',
        'fatal',
        'failed',
        'critical',
        'emergency',
        'alert',
        'unexpected',
        'crash',
        'timeout'
    ];

    /**
     * Target date for the command
     *
     * @var string
     */
    private $targetDate;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            // Initialize the target date
            $dateOption = $this->option('date');
            if ($dateOption === 'today') {
                $this->targetDate = Carbon::today()->format('Y-m-d');
            } else {
                // Validate date format
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateOption)) {
                    $this->error('Invalid date format. Please use Y-m-d format (e.g., 2025-04-14)');
                    return Command::FAILURE;
                }
                $this->targetDate = $dateOption;
            }
            
            Log::info("Starting logs:monitor command for date: {$this->targetDate}");
            $this->info("Scanning log files for issues on {$this->targetDate}...");
            
            $lastLines = $this->option('last-lines');
            $logPath = storage_path('logs');
            
            // Get all log files
            $allLogFiles = glob($logPath . '/*.log');
            $this->info("Found " . count($allLogFiles) . " total log files in logs directory");
            
            // Filter to only include today's dated log files and continuous logs
            $logFiles = $this->filterRelevantLogFiles($allLogFiles);
            
            if (empty($logFiles)) {
                $this->info("No relevant log files found for {$this->targetDate}.");
                Log::info("No relevant log files found for {$this->targetDate}.");
                return Command::SUCCESS;
            }
            
            $this->info("Found " . count($logFiles) . " log files to scan for {$this->targetDate}");
            
            // List the files being scanned
            foreach ($logFiles as $logFile) {
                $this->line("- " . basename($logFile));
            }
            
            $issues = $this->scanLogFiles($logFiles, $lastLines);
            
            $this->info("Found " . $this->countTotalIssues($issues) . " issues across " . count($issues) . " files");
            
            if (empty($issues)) {
                $this->info("No issues detected in log files for {$this->targetDate}.");
                Log::info("Log monitoring completed successfully. No issues found for {$this->targetDate}.");
                return Command::SUCCESS;
            }
            
            // Issues detected, send email notification
            $this->sendAlertEmail($issues);
            
            $this->info("Issues detected for {$this->targetDate} and alert email sent. Check your inbox.");
            Log::info("Log monitoring detected issues for {$this->targetDate} and sent alert email.");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::error('Log monitoring command failed: ' . $e->getMessage());
            $this->error('Command failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
    
    /**
     * Scan log files for potential issues
     *
     * @param array $logFiles
     * @param int $lastLines
     * @return array
     */
    private function scanLogFiles(array $logFiles, int $lastLines): array
    {
        $issues = [];
        $totalLinesChecked = 0;
        $totalLinesMatched = 0;
        
        foreach ($logFiles as $logFile) {
            $filename = basename($logFile);
            $this->info("Scanning {$filename}...");
            
            // Read the last N lines of the log file
            $lines = $this->getLastLines($logFile, $lastLines);
            $linesCount = count($lines);
            $totalLinesChecked += $linesCount;
            
            $this->info("- Read {$linesCount} lines from {$filename}");
            
            $fileIssues = [];
            $issueCount = 0;
            $dateMatchCount = 0;
            
            // Check each line for today's date and problem keywords
            foreach ($lines as $lineNum => $line) {
                // Only process lines from the target date
                if ($this->isFromTargetDate($line)) {
                    $dateMatchCount++;
                    
                    if ($this->containsProblemKeywords($line)) {
                        $fileIssues[] = [
                            'line' => $lineNum + 1,
                            'content' => $line
                        ];
                        $issueCount++;
                    }
                }
            }
            
            $totalLinesMatched += $dateMatchCount;
            $this->info("- Found {$dateMatchCount} lines from {$this->targetDate} in {$filename}");
            
            if (!empty($fileIssues)) {
                $this->info("- Detected {$issueCount} issues in {$filename}");
                $issues[$filename] = $fileIssues;
            } else {
                $this->info("- No issues found in {$filename}");
            }
        }
        
        $this->info("Total lines checked: {$totalLinesChecked}, lines matched date {$this->targetDate}: {$totalLinesMatched}");
        
        return $issues;
    }
    
    /**
     * Get the last N lines from a file
     *
     * @param string $filePath
     * @param int $n
     * @return array
     */
    private function getLastLines(string $filePath, int $n): array
    {
        $lines = [];
        
        // Check if file exists and is readable
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return $lines;
        }
        
        // Get file size
        $filesize = filesize($filePath);
        if ($filesize === 0) {
            return $lines;
        }
        
        // Open the file
        $file = fopen($filePath, 'r');
        if ($file === false) {
            return $lines;
        }
        
        // If the file is large, seek to a position near the end
        $seekPos = max(0, $filesize - ($n * 500)); // Estimate ~500 bytes per line
        if ($seekPos > 0) {
            fseek($file, $seekPos);
            // Discard first incomplete line
            fgets($file);
        }
        
        // Read all remaining lines
        while (!feof($file)) {
            $lines[] = fgets($file);
        }
        
        fclose($file);
        
        // Return only the last n lines
        return array_slice($lines, -$n);
    }
    
    /**
     * Check if a line contains any problem keywords
     *
     * @param string $line
     * @return bool
     */
    private function containsProblemKeywords(string $line): bool
    {
        $line = strtolower($line);
        
        foreach ($this->problemKeywords as $keyword) {
            if (strpos($line, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Send alert email about detected issues
     *
     * @param array $issues
     * @return void
     */
    private function sendAlertEmail(array $issues): void
    {
        $emailService = app(\App\Services\EmailService::class);
        
        // Set the IT department email as recipient
        $to = [
            'email' => '<EMAIL>',
            'name' => 'IT Department',
        ];
        
        $subject = "[ALERT] System Log Issues Detected on {$this->targetDate} - " . config('app.name');
        $view = 'emails.log_alert';
        
        $viewData = [
            'issues' => $issues,
            'totalIssues' => $this->countTotalIssues($issues),
            'systemName' => config('app.name'),
            'detectedAt' => now()->format('Y-m-d H:i:s'),
            'serverName' => gethostname(),
            'targetDate' => $this->targetDate,
        ];
        
        $emailService->sendEmail($to, $subject, $view, $viewData);
        
        $this->info("Alert email sent to {$to['email']}");
    }
    
    /**
     * Count total number of issues detected
     *
     * @param array $issues
     * @return int
     */
    private function countTotalIssues(array $issues): int
    {
        $count = 0;
        
        foreach ($issues as $fileIssues) {
            $count += count($fileIssues);
        }
        
        return $count;
    }

    /**
     * Filter log files to only include those relevant for the target date
     *
     * @param array $allLogFiles
     * @return array
     */
    private function filterRelevantLogFiles(array $allLogFiles): array
    {
        $relevantFiles = [];
        
        foreach ($allLogFiles as $file) {
            $filename = basename($file);
            
            // Case 1: Daily logs with date in filename (e.g., laravel-2025-04-14.log)
            if (preg_match('/-' . preg_quote($this->targetDate) . '\.log$/', $filename)) {
                $this->info("Including dated log file: {$filename} (matches target date)");
                $relevantFiles[] = $file;
                continue;
            }
            
            // Case 2: Generic logs without dates (e.g. laravel.log, email.log)
            if (!preg_match('/\d{4}-\d{2}-\d{2}/', $filename)) {
                $this->info("Including continuous log file: {$filename}");
                $relevantFiles[] = $file;
                continue;
            }
            
            // Case 3: Logs from other dates - skip these
            $this->info("Skipping log file from different date: {$filename}");
        }
        
        return $relevantFiles;
    }

    /**
     * Check if the log line is from the target date
     *
     * @param string $line
     * @return bool
     */
    private function isFromTargetDate(string $line): bool
    {
        // Extract date from log line (format: [2025-04-14 13:45:22])
        if (preg_match('/\[(\d{4}-\d{2}-\d{2})/', $line, $matches)) {
            $logDate = $matches[1];
            return $logDate === $this->targetDate;
        }
        
        // If no date found in the line, consider it not relevant
        return false;
    }
} 