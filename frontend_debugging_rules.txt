The 10-Request Frontend Debugging Manifesto

The guiding principle is this: The browser is your primary debugger. The LLM is your expert consultant. You must do the initial investigation yourself and then present a high-quality, condensed case file to the LLM for the final solution.

---

#### Rule #1: The Browser is Your First-Line of Defense (Saves ~150 Requests)

Never ask the LLM a question that the browser can answer for you in seconds. The biggest waste of requests is asking the LLM to guess what's happening in your browser.

*   Why it Saves Requests: You avoid dozens of back-and-forth questions like "What's in the console?", "Is the API call failing?", "What CSS is being applied?".
*   How to Do It:
    1.  Check the Console: Before writing a single word to the LLM, open the browser's developer tools (F12) and look at the Console tab. If there is an error message, that is your starting point.
    2.  Check the Network Tab: If your issue involves data not loading, check the Network tab. Is your API call returning a 404, 500, or 403 error? Is it returning the data you expect?
    3.  Check the Elements Inspector: If your issue is a visual bug (CSS), right-click the broken element and "Inspect." Look at the Styles panel to see which CSS rules are active, which are being overridden, and if there are any layout issues (e.g., margin, padding).

#### Rule #2: Isolate the Problem with a Minimal Reproduction (Saves ~80 Requests)

Never ask the LLM to debug a problem inside your entire application. The context is too large and noisy.

*   Why it Saves Requests: The LLM receives a clean, self-contained problem without the complexity of your full app, leading to a correct answer on the first or second try.
*   How to Do It:
    1.  Create a new, minimal test case on a platform like JSFiddle, CodePen, or even just a single, local .html file.
    2.  Copy only the absolute minimum HTML, CSS, and JavaScript needed to reproduce the bug. Remove all irrelevant code, components, and libraries.
    3.  Often, in the process of creating this minimal reproduction, you will find the bug yourself. If you don't, you now have a perfect, isolated test case to give to the LLM.

#### Rule #3: Master the "One-Shot" Context Dump (Saves ~50 Requests)

Your first prompt should be your best prompt. The goal is to give the LLM everything it needs to solve the problem in a single response.

*   Why it Saves Requests: It eliminates the entire chain of the LLM asking for the code, the error, the library versions, etc.
*   How to Do It: Use the following template for your first request.

---

### The Ultimate "One-Shot" Frontend Debugging Template

**1. The Goal:**
*   A clear, one-sentence description of what should be happening.
*   Example: "When I click the 'Add Remark' button, a SweetAlert popup should appear, and it should be closeable."

**2. The Environment:**
*   List the key frameworks and libraries with their versions.
*   Example: "I am using Laravel with Blade, Bootstrap 3.3.7, jQuery 3.5.1, and SweetAlert2 v11."

**3. The Code (Minimal & Relevant):**
*   Provide the isolated HTML, JavaScript, and CSS from your minimal reproduction (Rule #2).
*   Example:
    *   "Here is the relevant part of my Blade file..."
    *   "Here is the JavaScript that triggers the SweetAlert..."
    *   "Here is the custom CSS I am applying..."

**4. The Bug & The Evidence:**
*   Describe what's actually happening and provide the evidence you gathered from the browser (Rule #1).
*   Example: "The SweetAlert appears, but clicking the 'Cancel' button or the backdrop does nothing. The browser console shows no errors. The Elements Inspector shows my custom CSS is being applied."

**5. The Specific Question:**
*   Tell the LLM exactly what you need.
*   Example: "Based on the provided code and the SweetAlert2 documentation, what is the most likely conflict in my custom CSS or JavaScript that is preventing the library's native close events from firing? Please provide a corrected version of the Swal.fire configuration."

---

By following these rules, you transform your interaction. Instead of a 300-request chat, you have a 1-to-3 request consultation. You do the legwork, and the LLM provides the expert analysis, saving you time, frustration, and money.