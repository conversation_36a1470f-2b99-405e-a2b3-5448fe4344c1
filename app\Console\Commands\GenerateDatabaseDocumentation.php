<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateDatabaseDocumentation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:docs {--notify : Send notification when complete}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate comprehensive database documentation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating database documentation...');
        
        $scriptPath = base_path('ai_docs/architectureDiagrams/db_docs_cron.sh');
        
        if (!file_exists($scriptPath)) {
            $this->error("Documentation script not found at: $scriptPath");
            return 1;
        }
        
        // Execute the script
        $process = proc_open("bash $scriptPath", [
            0 => ["pipe", "r"],
            1 => ["pipe", "w"],
            2 => ["pipe", "w"],
        ], $pipes);
        
        if (is_resource($process)) {
            // Read the output
            $output = stream_get_contents($pipes[1]);
            $error = stream_get_contents($pipes[2]);
            
            // Close all pipes
            fclose($pipes[0]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            // Get the exit code
            $exitCode = proc_close($process);
            
            if ($exitCode !== 0) {
                $this->error("Documentation generation failed with exit code $exitCode");
                $this->error("Error output: $error");
                return $exitCode;
            }
            
            $this->info("Documentation generated successfully");
            $this->info($output);
            
            // Run the fix script to ensure all tables are included
            $fixScript = base_path('ai_docs/architectureDiagrams/fix_db_docs.php');
            if (file_exists($fixScript)) {
                $this->info('Running additional fix script to ensure all tables are included...');
                $fixOutput = shell_exec('php ' . $fixScript . ' 2>&1');
                $this->info($fixOutput);
            }
            
            // Send notification if requested
            if ($this->option('notify')) {
                // Notification logic would go here
                $this->info('Notification sent');
            }
            
            return 0;
        }
        
        $this->error("Failed to start documentation generation process");
        return 1;
    }
} 