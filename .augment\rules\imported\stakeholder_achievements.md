---
type: "manual"
---

- **Update Achievement Log**: After any significant feature implementation, bug fix, or module update, you MUST add an entry to `[stakeholder_achievements.md](mdc:ai_docs/stakeholder_achievements.md)`.

- **Entry Format**:
    - Add the achievement under the relevant module section.
    - If a section for the module doesn't exist, create a new one.
    - Keep the description concise and clear for stakeholders.
    - Example:
      ```markdown
      ## JobSeeker Module

      - Implemented dynamic command scheduling and management UI.
      ```

- **Purpose**: This ensures that key stakeholders are kept informed of project progress and that a continuous log of achievements is maintained. 