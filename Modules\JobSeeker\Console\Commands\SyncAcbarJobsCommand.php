<?php

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Services\AcbarJobService;
use Modules\JobSeeker\Entities\CommandScheduleExecution;
use Modules\JobSeeker\Repositories\JobRepository;
use Carbon\Carbon;

class SyncAcbarJobsCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'jobseeker:sync-acbar-jobs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync jobs from ACBAR.org with sequential category processing and health tracking (prevents rate limiting)';

    /**
     * The signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:sync-acbar-jobs
                          {--category=* : Optional specific category IDs to sync}
                          {--schedule-rule-id= : Optional schedule rule ID for custom filters}';

    /**
     * @var AcbarJobService
     */
    protected $acbarJobService;

    /**
     * @var JobRepository
     */
    protected $jobRepository;

    /**
     * Create a new command instance.
     *
     * @param AcbarJobService $acbarJobService
     * @param JobRepository $jobRepository
     */
    public function __construct(AcbarJobService $acbarJobService, JobRepository $jobRepository)
    {
        parent::__construct();
        $this->acbarJobService = $acbarJobService;
        $this->jobRepository = $jobRepository;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $categoryIds = $this->option('category'); // This is now an array
        $scheduleRuleId = $this->option('schedule-rule-id') ? (int) $this->option('schedule-rule-id') : null;
        $executionId = null;
        $startTime = Carbon::now();
        
        try {
            $this->info('Starting ACBAR jobs synchronization...');
            Log::info('Starting ACBAR jobs synchronization via command', [
                'category_ids' => $categoryIds,
                'schedule_rule_id' => $scheduleRuleId,
                'start_time' => $startTime->toDateTimeString()
            ]);

            // ALWAYS create execution record for health tracking
            $execution = CommandScheduleExecution::create([
                'schedule_rule_id' => $scheduleRuleId, // Can be null for manual executions
                'command' => 'jobseeker:sync-acbar-jobs',
                'status' => 'running',
                'started_at' => $startTime,
                'output' => json_encode([
                    'categories' => $categoryIds,
                    'schedule-rule-id' => $scheduleRuleId,
                    'manual_execution' => !$scheduleRuleId
                ])
            ]);
            $executionId = $execution->id;
            $this->info("Created execution record with ID: {$executionId}");
            
            if ($scheduleRuleId) {
                $this->info("Using custom filters for schedule rule ID: {$scheduleRuleId}");
            } else {
                $this->info("Manual execution - using default filters");
            }
            
            // Display sync target
            if (!empty($categoryIds)) {
                $categoryIdsString = implode(', ', $categoryIds);
                $this->info("Fetching jobs for ACBAR categories: {$categoryIdsString}...");
            } else {
                $this->info('Fetching jobs from all ACBAR categories...');
            }

            // Call the service with optional parameters
            $stats = $this->acbarJobService->syncAcbarJobs($categoryIds, $scheduleRuleId);
            
            // Check if sync was successful
            if (!$stats['success']) {
                throw new \Exception($stats['error_message'] ?? 'Unknown error occurred during ACBAR sync');
            }
            
            // Validate results
            if (empty($stats['created']) && empty($stats['updated']) && empty($stats['categories_processed'])) {
                $this->handleEmptyResults($stats);
                
                // Complete execution record for empty results
                if ($executionId) {
                    $this->completeExecution($executionId, $stats, true, 'No jobs processed - empty results');
                }
                
                return 1;
            }

            // Display comprehensive results with health metrics
            $this->displaySyncResults($stats);
            
            // Get additional database statistics
            $dbStats = $this->getDatabaseStatistics();
            $this->displayDatabaseSummary($dbStats);
            
            // Log comprehensive results
            Log::info('ACBAR synchronization completed via command', [
                'sync_stats' => $stats,
                'db_stats' => $dbStats,
                'execution_id' => $executionId
            ]);
            
            // Complete execution record with health metrics
            if ($executionId) {
                $this->completeExecution($executionId, $stats, true);
            }

            return 0;

        } catch (\Exception $e) {
            Log::error('Error in ACBAR jobs sync command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'execution_id' => $executionId
            ]);
            $this->error('Error: ' . $e->getMessage());
            
            // Complete execution record with error
            if ($executionId) {
                $errorStats = [
                    'success' => false,
                    'jobs_fetched' => 0,
                    'jobs_by_category' => [],
                    'error_types' => ['COMMAND_ERROR' => 1],
                    'api_response_time' => 0,
                    'created' => 0,
                    'updated' => 0,
                    'errors' => 1
                ];
                $this->completeExecution($executionId, $errorStats, false, $e->getMessage());
            }
            
            return 1;
        }
    }

    /**
     * Handle empty sync results
     * 
     * @param array $stats
     * @return void
     */
    protected function handleEmptyResults(array $stats): void
    {
        $this->warn('No jobs processed from ACBAR');
        
        if ($stats['skipped_no_category_map'] > 0) {
            $this->warn("Skipped {$stats['skipped_no_category_map']} categories due to missing category mapping");
        }
        
        if ($stats['errors'] > 0) {
            $this->error("Encountered {$stats['errors']} errors during processing");
            
            // Display error breakdown if available
            if (!empty($stats['error_types'])) {
                $this->line('Error breakdown:');
                foreach ($stats['error_types'] as $errorType => $count) {
                    $this->line("  • {$errorType}: {$count}");
                }
            }
        }
    }

    /**
     * Display synchronization results including health metrics
     * 
     * @param array $stats
     * @return void
     */
    protected function displaySyncResults(array $stats): void
    {
        $this->info('ACBAR Synchronization Results:');
        $this->line("  - Jobs created: {$stats['created']}");
        $this->line("  - Jobs updated: {$stats['updated']}");
        $this->line("  - Categories processed: {$stats['categories_processed']}");
        
        if ($stats['errors'] > 0) {
            $this->warn("  - Errors encountered: {$stats['errors']}");
        }
        if ($stats['skipped_no_category_map'] > 0) {
            $this->warn("  - Categories skipped: {$stats['skipped_no_category_map']}");
        }

        // Health dashboard metrics
        if (isset($stats['jobs_fetched'])) {
            $this->info('Health Dashboard Metrics:');
            $this->line('  - Total jobs fetched: ' . $stats['jobs_fetched']);
            $this->line('  - API response time: ' . round($stats['api_response_time'] ?? 0, 2) . 's');
            
            if (!empty($stats['jobs_by_category'])) {
                $this->line('  - Jobs by category:');
                foreach ($stats['jobs_by_category'] as $category => $count) {
                    $this->line("    • {$category}: {$count}");
                }
            }
            
            if (!empty($stats['error_types'])) {
                $this->line('  - Error breakdown:');
                foreach ($stats['error_types'] as $errorType => $count) {
                    $this->line("    • {$errorType}: {$count}");
                }
            }
            
            if (!empty($stats['filtered_categories'])) {
                $this->line('  - Filtered categories: ' . implode(', ', $stats['filtered_categories']));
            }
        }
    }

    /**
     * Get database statistics
     * 
     * @return array
     */
    protected function getDatabaseStatistics(): array
    {
        return [
            'total_acbar_jobs' => \Modules\JobSeeker\Entities\Job::where('source', 'ACBAR')->count(),
            'recent_acbar_jobs' => \Modules\JobSeeker\Entities\Job::where('source', 'ACBAR')
                ->where('publish_date', '>=', now()->subDays(7))
                ->count(),
            'total_categories' => \Modules\JobSeeker\Entities\JobCategory::where('source', 'ACBAR')->count()
        ];
    }

    /**
     * Display database summary
     * 
     * @param array $dbStats
     * @return void
     */
    protected function displayDatabaseSummary(array $dbStats): void
    {
        $this->info('');
        $this->info('=== ACBAR SYNCHRONIZATION SUMMARY ===');
        $this->line('  - Total ACBAR jobs in database: ' . $dbStats['total_acbar_jobs']);
        $this->line('  - Jobs published in last 7 days: ' . $dbStats['recent_acbar_jobs']);
        $this->line('  - Total ACBAR categories: ' . $dbStats['total_categories']);
        $this->line('  - Synchronization completed successfully');
    }

    /**
     * Complete execution record with health metrics
     * 
     * @param int $executionId
     * @param array $stats
     * @param bool $success
     * @param string|null $errorMessage
     * @return void
     */
    protected function completeExecution(int $executionId, array $stats, bool $success, ?string $errorMessage = null): void
    {
        try {
            $execution = CommandScheduleExecution::find($executionId);
            if (!$execution) {
                Log::warning('Execution record not found for completion', ['execution_id' => $executionId]);
                return;
            }

            // Format health metrics using the service
            $healthMetrics = $this->acbarJobService->formatExecutionStats($stats);
            
            $execution->markCompleted(
                $success ? 0 : 1,                            // $exitCode (int): 0 = success, 1 = failure
                $errorMessage,                                // $output (string): error message or null
                null,                                         // $memoryUsageMb (float): not tracked yet
                $healthMetrics['jobs_fetched'],               // $jobsFetched (int)
                $healthMetrics['jobs_by_category'],           // $jobsByCategory (array)
                $healthMetrics['error_type'],                 // $errorType (string)
                $healthMetrics['error_details']               // $errorDetails (array)
            );

            $this->info("Execution record {$executionId} completed with health metrics");
            
        } catch (\Exception $e) {
            Log::error('Error completing execution record', [
                'execution_id' => $executionId,
                'error' => $e->getMessage()
            ]);
        }
    }
} 