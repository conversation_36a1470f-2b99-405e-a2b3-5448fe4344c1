-- Add ACBAR-specific configuration fields to command_schedule_filters table
-- These fields support ACBAR job synchronization configuration

ALTER TABLE command_schedule_filters 
ADD COLUMN max_retries int DEFAULT NULL COMMENT 'Maximum retry attempts for ACBAR requests',
ADD COLUMN timeout int DEFAULT NULL COMMENT 'Request timeout in seconds for <PERSON>BA<PERSON>',
ADD COLUMN base_delay bigint DEFAULT NULL COMMENT 'Base delay in microseconds between ACBAR requests';

-- Create indexes for performance
CREATE INDEX command_schedule_filters_acbar_config_index ON command_schedule_filters (max_retries, timeout, base_delay);

-- Verify the table structure
DESCRIBE command_schedule_filters; 