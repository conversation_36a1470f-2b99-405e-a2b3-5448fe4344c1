<?php

// Simple test to verify the filter flow works
// Run with: php artisan tinker test_simple.php

use <PERSON><PERSON><PERSON>\JobSeeker\Repositories\FilterRepository;
use Mo<PERSON>les\JobSeeker\Entities\CommandScheduleFilter;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\ProviderJobCategory;
use Mo<PERSON>les\JobSeeker\Entities\ProviderJobLocation;

echo "=== Testing Category and Location Fetching Flow ===\n";
echo "Schedule Rule ID: 32\n";

// Step 1: Get filter
$filter = CommandScheduleFilter::where('schedule_rule_id', 32)->first();
echo "Filter categories: " . json_encode($filter->categories) . "\n";
echo "Filter locations: " . json_encode($filter->locations) . "\n";

// Step 2: Test direct translation
$jobsAfCats = ProviderJobCategory::getProviderIdentifiers($filter->categories, 'jobs.af');
$jobsAfLocs = ProviderJobLocation::getProviderIdentifiers($filter->locations, 'jobs.af');
echo "Jobs.af categories: " . json_encode($jobsAfCats) . "\n";
echo "Jobs.af locations: " . json_encode($jobsAfLocs) . "\n";

$acbarCats = ProviderJobCategory::getProviderIdentifiers($filter->categories, 'acbar');
$acbarLocs = ProviderJobLocation::getProviderIdentifiers($filter->locations, 'acbar');
echo "ACBAR categories: " . json_encode($acbarCats) . "\n";
echo "ACBAR locations: " . json_encode($acbarLocs) . "\n";

// Step 3: Test FilterRepository
$repo = app(FilterRepository::class);
$jobsAfFilters = $repo->getJobsAfTranslatedFilters(32);
$acbarFilters = $repo->getAcbarTranslatedFilters(32);

echo "FilterRepository Jobs.af categories: " . json_encode($jobsAfFilters['searchFilters']['categories']) . "\n";
echo "FilterRepository Jobs.af locations: " . json_encode($jobsAfFilters['searchFilters']['locations']) . "\n";
echo "FilterRepository ACBAR categories: " . json_encode($acbarFilters['category_ids']) . "\n";
echo "FilterRepository ACBAR locations: " . json_encode($acbarFilters['location_ids']) . "\n";

// Step 4: Validation
$success = true;

if ($jobsAfFilters['searchFilters']['categories'] !== $jobsAfCats) {
    echo "❌ Jobs.af categories mismatch!\n";
    $success = false;
} else {
    echo "✅ Jobs.af categories match\n";
}

if ($jobsAfFilters['searchFilters']['locations'] !== $jobsAfLocs) {
    echo "❌ Jobs.af locations mismatch!\n";
    $success = false;
} else {
    echo "✅ Jobs.af locations match\n";
}

$expectedAcbarCats = array_map('intval', $acbarCats);
if ($acbarFilters['category_ids'] !== $expectedAcbarCats) {
    echo "❌ ACBAR categories mismatch!\n";
    $success = false;
} else {
    echo "✅ ACBAR categories match\n";
}

$expectedAcbarLocs = array_map('intval', $acbarLocs);
if ($acbarFilters['location_ids'] !== $expectedAcbarLocs) {
    echo "❌ ACBAR locations mismatch!\n";
    $success = false;
} else {
    echo "✅ ACBAR locations match\n";
}

if ($success) {
    echo "🎉 SUCCESS: Complete flow working correctly!\n";
    echo "   - No hardcoded values detected\n";
    echo "   - Dynamic category and location fetching operational\n";
    echo "   - Both Jobs.af and ACBAR providers supported\n";
} else {
    echo "❌ FAILURE: Issues detected in the flow\n";
}
