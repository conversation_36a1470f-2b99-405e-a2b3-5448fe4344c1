@extends('modules.jobseeker.layouts.app')

@section('title', 'Bulk Edit Command Schedule Rules')
@section('page-title', 'Bulk Edit Command Schedule Rules')

@push('styles')
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
:root {
    --primary-color: #4f46e5;
    --primary-dark: #3b38e5;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --secondary-color: #64748b;
    --border-color: #e2e8f0;
    --light-bg: #f8fafc;
}

/* Chess Board Layout */
.chess-board-view {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
    background: var(--light-bg);
}

/* Rule Cards */
.rule-card {
    background: white;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.rule-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
    transform: translateY(-2px);
}

/* Navigation Enhancements */
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.375rem;
}

/* Enhanced Card Header */
.rule-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.rule-title-section {
    flex: 1;
}

.rule-name-display {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
}

.rule-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.rule-status-badge.active {
    background: #d1fae5;
    color: #065f46;
}

.rule-status-badge.inactive {
    background: #fee2e2;
    color: #991b1b;
}

.rule-meta-info {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.rule-meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.rule-meta-item i {
    width: 14px;
    text-align: center;
}

/* Schedule Display Enhancement */
.schedule-display {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.schedule-display-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.schedule-display-title {
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.schedule-expression {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    background: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    margin-bottom: 0.5rem;
}

.schedule-human-readable {
    font-size: 0.875rem;
    color: #059669;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.schedule-human-readable i {
    color: #10b981;
}

/* Key Differentiators Section */
.rule-differentiators {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.differentiators-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.differentiator-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.differentiator-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

/* Enhanced Provider Badge */
.provider-badge {
    position: absolute;
    top: -1px;
    right: -1px;
    padding: 0.5rem 1rem;
    border-radius: 0 0.75rem 0 0.75rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.provider-badge.provider-jobsaf {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.provider-badge.provider-acbar {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.provider-badge.provider-unknown {
    background: #6b7280;
    color: white;
}

/* Form Section Headers */
.form-section-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
    font-size: 1rem;
}

.form-section-header i {
    color: #6366f1;
    font-size: 1.1rem;
}

/* Enhanced Form Controls */
.form-control.inline-edit:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.1);
}

.form-select.inline-edit:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.1);
}

/* Enhanced Labels */
.form-label.fw-bold {
    color: #374151;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.form-label.fw-bold i {
    color: #6b7280;
    width: 16px;
    text-align: center;
}

/* Filter Configuration Styling */
.filter-config-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
}

.filter-config-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #374151;
    font-size: 1rem;
}

.filter-config-title i {
    color: #8b5cf6;
}

/* Execution Settings Styling */
.execution-settings-section {
    background: #fef7ff;
    border: 1px solid #e9d5ff;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
}

.execution-settings-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #374151;
    font-size: 1rem;
}

.execution-settings-title i {
    color: #a855f7;
}

/* Animation for card loading */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.rule-card.fade-in {
    animation: slideInUp 0.4s ease-out;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .rule-card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .rule-meta-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .schedule-display {
        padding: 0.75rem;
    }

    .rule-differentiators {
        padding: 0.75rem;
    }
}
    
/* Provider Badges */
.provider-badge {
    position: absolute;
    top: -8px;
    right: 15px;
    padding: 0.25rem 0.75rem;
    border-radius: 1.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.provider-jobsaf {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.provider-acbar {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #86efac;
}

/* Inline Editing */
.inline-edit {
    border: 1px solid transparent;
    background: transparent;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    width: 100%;
    font-size: 0.875rem;
}

.inline-edit:focus, .inline-edit:hover {
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.1);
    outline: none;
}
    
/* Status Indicators */
.saving-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--warning-color);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.7rem;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.saving-indicator.show {
    opacity: 1;
}

/* Validation */
.field-error {
    border-color: var(--danger-color) !important;
    background: #fef2f2 !important;
}

.error-message {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: 500;
}
    
/* Clone Superpower Button */
.clone-superpower {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1050;
}

.clone-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 3rem;
    padding: 1rem 1.5rem;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    box-shadow: 0 0.625rem 1.875rem rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clone-btn:hover {
    transform: translateY(-0.1875rem);
    box-shadow: 0 0.9375rem 2.5rem rgba(102, 126, 234, 0.6);
    color: white;
}
    
/* Clone Modal */
.clone-modal {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(0.625rem);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1055;
}

.clone-panel {
    background: white;
    border-radius: 1.25rem;
    max-width: 500px;
    width: 90%;
    padding: 2rem;
    box-shadow: 0 1.5625rem 3.125rem rgba(0, 0, 0, 0.3);
    position: relative;
}

/* Provider Selection */
.provider-selector {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1rem;
    align-items: center;
    margin: 1.5rem 0;
}

.provider-option {
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.provider-option:hover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
}

.provider-option.selected {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.1);
}
    
/* Time Offset Selection */
.time-offset-selector {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
    flex-wrap: wrap;
}

.offset-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    background: white;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    flex: 1;
    min-width: 0;
}

.offset-btn:hover {
    border-color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
}

.offset-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

/* Stats Bar */
.stats-bar {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.stat-item {
    text-align: center;
    min-width: 0;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1.2;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chess-board-view {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1rem;
    }

    .stats-bar {
        flex-direction: column;
        text-align: center;
    }

    .clone-superpower {
        bottom: 1rem;
        right: 1rem;
    }

    .clone-panel {
        margin: 1rem;
        padding: 1.5rem;
    }

    .provider-selector {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Loading states */
.loading {
    pointer-events: none;
    opacity: 0.7;
}

/* Success states */
.success-flash {
    background: rgba(16, 185, 129, 0.1) !important;
    border-color: var(--success-color) !important;
    transition: all 0.3s ease;
}

/* Focus management */
.rule-card:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.1);
}

/* Next run banner styling - moved to top */
.next-run-banner {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.next-run-banner.time-status-imminent {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
    animation: pulse-warning 2s infinite;
}

.next-run-banner.time-status-today {
    background: linear-gradient(135deg, #d1edff 0%, #a7d8f0 100%);
    border-color: #17a2b8;
}

.next-run-banner.time-status-tomorrow {
    background: linear-gradient(135deg, #d4edda 0%, #b8dabc 100%);
    border-color: #28a745;
}

.next-run-banner.time-status-future {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: #6c757d;
}

@keyframes pulse-warning {
    0%, 100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
    50% { box-shadow: 0 0 0 8px rgba(255, 193, 7, 0); }
}

.next-run-display {
    font-weight: 600;
    color: var(--primary-color);
}

.time-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
}

/* ===== SWEETALERT CATEGORY MANAGEMENT STYLING ===== */
.category-management-container {
    position: relative;
}

.btn-category-manager {
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
    text-align: left;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.btn-category-manager:hover {
    border-color: #0d6efd;
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
}

.btn-category-manager:active {
    transform: translateY(0);
}

.category-count-display {
    font-weight: 500;
    flex-grow: 1;
}

.category-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
}

.category-preview .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

/* SweetAlert Custom Styling */
/* Apple-Level Category Modal Styling */
.swal2-container.category-selector-modal {
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
}

.category-selector-modal .swal2-popup {
    width: 90vw;
    max-width: 1200px;
    height: 95vh; /* Increased from max-height: 90vh */
    padding: 0;
    border-radius: 20px; /* More Apple-like radius */
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    backdrop-filter: blur(40px);
}

.category-selector-header {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%); /* Apple blue gradient */
    color: white;
    padding: 32px 24px 24px;
    border-radius: 20px 20px 0 0;
    text-align: center;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.category-selector-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

.category-selector-header h2 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    position: relative;
    z-index: 1;
}

.category-selector-header .provider-info {
    margin-top: 12px;
    opacity: 0.95;
    font-size: 0.95rem;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.category-selector-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fafafa;
}

/* Fixed Controls Section - Apple-style glassmorphism */
.category-controls-section {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    padding: 24px 24px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    z-index: 100;
    flex-shrink: 0;
}

.category-controls-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

/* Scrollable Content Section - Apple-style smooth scrolling */
.category-content-section {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px 24px 24px;
    scroll-behavior: smooth;
    background: #fafafa;
}

/* Custom scrollbar styling - Apple-like */
.category-content-section::-webkit-scrollbar {
    width: 8px;
}

.category-content-section::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.category-content-section::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.category-content-section::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Search Input - Apple-style design */
.category-search-input {
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    padding: 16px 20px 16px 50px;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    width: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.category-search-input:focus {
    border-color: #007AFF;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.15);
    outline: none;
    background: white;
    transform: translateY(-1px);
}

.category-search-input::placeholder {
    color: rgba(0, 0, 0, 0.5);
    font-weight: 400;
}

.category-search-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(0, 0, 0, 0.4);
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.category-search-input:focus + .category-search-icon {
    color: #007AFF;
}

/* Category Grid - Apple-style layout */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 24px;
    padding: 8px 0;
}

/* Category Item - Apple Card Design */
.category-item {
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    padding: 20px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.category-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

/* Apple-style hover and interaction states */
.category-item:hover {
    border-color: rgba(0, 122, 255, 0.3);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    background: white;
}

.category-item:hover::before {
    opacity: 1;
}

.category-item:active {
    transform: translateY(-1px) scale(1.01);
    transition: all 0.1s ease;
}

/* Selected state - Apple green with subtle gradient */
.category-item.selected {
    border-color: #34C759;
    background: linear-gradient(135deg, rgba(52, 199, 89, 0.08) 0%, rgba(52, 199, 89, 0.04) 100%);
    box-shadow: 0 4px 16px rgba(52, 199, 89, 0.15);
}

.category-item.selected::before {
    background: linear-gradient(135deg, rgba(52, 199, 89, 0.2) 0%, rgba(52, 199, 89, 0.1) 100%);
    opacity: 1;
}

/* Selected indicator removed - using checkboxes only for cleaner UI */

/* Apple-style animations */
@keyframes selectedBounce {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    60% {
        transform: scale(1.3) rotate(0deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes selectedRipple {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    100% {
        transform: scale(2.5);
        opacity: 0;
    }
}

/* Selected Categories Header - Apple-style */
.selected-categories-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding-bottom: 16px;
    margin-bottom: 24px;
    background: linear-gradient(135deg, rgba(52, 199, 89, 0.05) 0%, transparent 100%);
    border-radius: 12px;
    padding: 16px;
    margin: 0 -8px 24px -8px;
}

.selected-categories-header h6 {
    margin: 0;
    font-weight: 700;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    color: #1d1d1f;
    letter-spacing: -0.01em;
}

/* Apple-style Quick Action Buttons */
.category-controls-section .btn {
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 10px 16px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
}

.category-controls-section .btn-outline-success {
    background: rgba(52, 199, 89, 0.1);
    border-color: rgba(52, 199, 89, 0.3);
    color: #34C759;
}

.category-controls-section .btn-outline-success:hover {
    background: #34C759;
    border-color: #34C759;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(52, 199, 89, 0.3);
}

.category-controls-section .btn-outline-warning {
    background: rgba(255, 149, 0, 0.1);
    border-color: rgba(255, 149, 0, 0.3);
    color: #FF9500;
}

.category-controls-section .btn-outline-warning:hover {
    background: #FF9500;
    border-color: #FF9500;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 149, 0, 0.3);
}

.category-controls-section .btn-outline-danger {
    background: rgba(255, 59, 48, 0.1);
    border-color: rgba(255, 59, 48, 0.3);
    color: #FF3B30;
}

.category-controls-section .btn-outline-danger:hover {
    background: #FF3B30;
    border-color: #FF3B30;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 59, 48, 0.3);
}

/* Location-specific Apple-style buttons */
.category-controls-section .btn-outline-info {
    background: rgba(0, 122, 255, 0.1);
    border-color: rgba(0, 122, 255, 0.3);
    color: #007AFF;
}

.category-controls-section .btn-outline-info:hover {
    background: #007AFF;
    border-color: #007AFF;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 122, 255, 0.3);
}

/* Apple-style Statistics Cards */
.apple-stat-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 12px;
    padding: 16px 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.apple-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    border-radius: 12px 12px 0 0;
    transition: all 0.3s ease;
}

.apple-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 4px;
    letter-spacing: -0.02em;
}

.stat-label {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

/* Specific stat card colors */
.selected-stat::before { background: linear-gradient(90deg, #007AFF, #5856D6); }
.selected-stat .stat-number { color: #007AFF; }

.high-activity-stat::before { background: linear-gradient(90deg, #34C759, #30D158); }
.high-activity-stat .stat-number { color: #34C759; }

.medium-activity-stat::before { background: linear-gradient(90deg, #FF9500, #FFCC02); }
.medium-activity-stat .stat-number { color: #FF9500; }

.low-activity-stat::before { background: linear-gradient(90deg, #5856D6, #AF52DE); }
.low-activity-stat .stat-number { color: #5856D6; }

.no-activity-stat::before { background: linear-gradient(90deg, #8E8E93, #AEAEB2); }
.no-activity-stat .stat-number { color: #8E8E93; }

.total-stat::before { background: linear-gradient(90deg, #1D1D1F, #48484A); }
.total-stat .stat-number { color: #1D1D1F; }

/* Location statistics use same stat card colors as categories */

/* Apple-style Summary Stats */
.apple-summary-stats {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 0.9rem;
    color: rgba(0, 0, 0, 0.7);
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 8px 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
}

.summary-item i {
    color: #007AFF;
    font-size: 0.8rem;
}

.summary-divider {
    color: rgba(0, 0, 0, 0.3);
    font-weight: 300;
}

/* ===== LOCATION MANAGEMENT STYLING ===== */
.location-management-container {
    position: relative;
}

/* Apple-style Location Manager Button */
.btn-location-manager {
    border: 2px solid rgba(52, 199, 89, 0.2);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-align: left;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(52, 199, 89, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
    backdrop-filter: blur(10px);
    font-weight: 600;
    color: #1d1d1f;
}

.btn-location-manager:hover {
    border-color: rgba(52, 199, 89, 0.4);
    background: linear-gradient(135deg, rgba(52, 199, 89, 0.08) 0%, white 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(52, 199, 89, 0.2);
    color: #1d1d1f;
}

.btn-location-manager:active {
    transform: translateY(-1px);
    transition: all 0.1s ease;
}

/* Apple-style Location Display */
.location-count-display {
    font-weight: 600;
    flex-grow: 1;
    color: #1d1d1f;
    letter-spacing: -0.01em;
}

.location-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    margin-top: 12px;
}

.location-preview .badge {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 12px;
    background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
    color: white;
    border: none;
    animation: fadeInScale 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

.location-preview .badge.bg-secondary {
    background: linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%);
    box-shadow: 0 2px 8px rgba(142, 142, 147, 0.3);
}

/* Location Modal uses same styling as Category Modal - no separate CSS needed */

/* Location items use same styling as category items - no separate CSS needed */

/* Location Type Indicators - using category-item base class */
.category-item.location-type-capital {
    border-left: 4px solid #FFCC02; /* Apple yellow */
    background: linear-gradient(135deg, rgba(255, 204, 2, 0.05) 0%, transparent 100%);
}

.category-item.location-type-major {
    border-left: 4px solid #007AFF; /* Apple blue */
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, transparent 100%);
}

.category-item.location-type-province {
    border-left: 4px solid #8E8E93; /* Apple gray */
    background: linear-gradient(135deg, rgba(142, 142, 147, 0.05) 0%, transparent 100%);
}

/* Location content uses same styling as categories - no separate CSS needed */

/* ===== APPLE-LEVEL FILTER COPY SYSTEM ===== */

/* Apple-style button */
.btn-apple-copy {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 8px 16px;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.btn-apple-copy::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-apple-copy:hover::before {
    left: 100%;
}

.btn-apple-copy:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
    color: white;
}

.btn-apple-copy:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.btn-apple-content {
    display: flex;
    align-items: center;
    gap: 6px;
}

.sf-symbol-copy::before {
    content: "⧉";
    font-size: 1.1rem;
    font-weight: 500;
}

/* Apple-style modal */
.apple-filter-modal .swal2-popup {
    width: min(90vw, 800px);
    max-height: 90vh;
    padding: 0;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(40px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

/* Apple-style header */
.apple-filter-header {
    padding: 32px;
    text-align: center;
    background: linear-gradient(135deg, #F2F2F7 0%, #FFFFFF 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.apple-filter-header h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1D1D1F;
    margin: 0 0 8px 0;
    letter-spacing: -0.02em;
}

.apple-filter-header .subtitle {
    font-size: 1rem;
    color: #86868B;
    font-weight: 400;
    margin: 0;
}

/* Apple-style content area */
.apple-filter-content {
    padding: 24px 32px;
    max-height: 60vh;
    overflow-y: auto;
    background: #FFFFFF;
}

/* Apple-style sections */
.apple-section {
    background: #F2F2F7;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    border: none;
    transition: all 0.2s ease;
}

.apple-section:hover {
    background: #EBEBF0;
    transform: translateY(-1px);
}

.apple-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1D1D1F;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.apple-section-title i {
    font-size: 1rem;
    color: #007AFF;
}

/* Apple-style form controls */
.apple-select {
    background: #FFFFFF;
    border: 1px solid #D1D1D6;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 1rem;
    color: #1D1D1F;
    width: 100%;
    transition: all 0.2s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.apple-select:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* Apple-style preview */
.apple-preview {
    background: linear-gradient(135deg, #E3F2FD 0%, #F3E5F5 100%);
    border: 1px solid #007AFF;
    border-radius: 16px;
    padding: 20px;
    margin: 16px 0;
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
}

.apple-preview h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #007AFF;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Apple-style options grid */
.apple-options-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 16px 0;
}

.apple-option {
    background: #FFFFFF;
    border: 1px solid #D1D1D6;
    border-radius: 16px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.apple-option:hover {
    border-color: #007AFF;
    background: #F8F9FF;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.apple-option.selected {
    border-color: #007AFF;
    background: linear-gradient(135deg, #E3F2FD 0%, #F8F9FF 100%);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.apple-option input[type="checkbox"],
.apple-option input[type="radio"] {
    width: 20px;
    height: 20px;
    margin-bottom: 12px;
    accent-color: #007AFF;
}

.apple-option-icon {
    font-size: 2rem;
    color: #007AFF;
    margin-bottom: 8px;
}

.apple-option h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #1D1D1F;
    margin: 0 0 4px 0;
}

.apple-option small {
    color: #86868B;
    font-size: 0.875rem;
}

/* Apple-style conflict resolution */
.apple-conflict-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin: 16px 0;
}

.apple-conflict-option {
    background: #FFFFFF;
    border: 1px solid #D1D1D6;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.apple-conflict-option:hover {
    border-color: #007AFF;
    background: #F8F9FF;
}

.apple-conflict-option.selected {
    border-color: #007AFF;
    background: linear-gradient(135deg, #E3F2FD 0%, #F8F9FF 100%);
}

.apple-conflict-option input[type="radio"] {
    width: 18px;
    height: 18px;
    margin-bottom: 8px;
    accent-color: #007AFF;
}

.apple-conflict-option .option-icon {
    font-size: 1.2rem;
    color: #007AFF;
    margin-bottom: 6px;
}

.apple-conflict-option h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1D1D1F;
    margin: 0 0 2px 0;
}

.apple-conflict-option small {
    color: #86868B;
    font-size: 0.8rem;
}

.clone-filters-header.provider-jobs-af {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.clone-filters-header.provider-acbar {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

.clone-filters-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: headerShimmer 3s ease-in-out infinite;
}

@keyframes headerShimmer {
    0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
    50% { transform: rotate(180deg) scale(1.1); opacity: 0.1; }
}

.clone-filters-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
}

.clone-filters-header .provider-badge {
    display: inline-block;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-top: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.clone-filters-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
    background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
}

.clone-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.clone-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.clone-section:hover::before {
    transform: scaleX(1);
}

.clone-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.provider-jobs-af .section-title i {
    color: #667eea;
}

.provider-acbar .section-title i {
    color: #11998e;
}

.source-rule-select {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.source-rule-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.source-preview {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px solid #2196f3;
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    0% { opacity: 0; transform: translateY(-20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.clone-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
}

.clone-option {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.clone-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.clone-option:hover::before {
    left: 100%;
}

.clone-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.clone-option.selected {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.clone-option input[type="checkbox"] {
    transform: scale(1.3);
    margin-bottom: 10px;
    accent-color: #28a745;
}

.conflict-resolution {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin: 20px 0;
}

.conflict-option {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.conflict-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.conflict-option.selected {
    border-color: #007bff;
    background: linear-gradient(135deg, #cce5ff 0%, #e6f3ff 100%);
}

.conflict-option input[type="radio"] {
    margin-bottom: 8px;
    transform: scale(1.2);
    accent-color: #007bff;
}

/* Apple-style target rules container */
.apple-targets-container {
    max-height: 300px;
    overflow-y: auto;
    background: #FFFFFF;
    border: 1px solid #D1D1D6;
    border-radius: 16px;
    padding: 16px;
}

.apple-targets-container::-webkit-scrollbar {
    width: 6px;
}

.apple-targets-container::-webkit-scrollbar-track {
    background: #F2F2F7;
    border-radius: 3px;
}

.apple-targets-container::-webkit-scrollbar-thumb {
    background: #C7C7CC;
    border-radius: 3px;
}

.apple-targets-container::-webkit-scrollbar-thumb:hover {
    background: #AEAEB2;
}

.apple-target-rule {
    background: #F2F2F7;
    border: 1px solid transparent;
    border-radius: 12px;
    padding: 12px 16px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.apple-target-rule:hover {
    background: #E5E5EA;
    border-color: #007AFF;
    transform: translateX(2px);
}

.apple-target-rule.selected {
    background: linear-gradient(135deg, #E3F2FD 0%, #F8F9FF 100%);
    border-color: #007AFF;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);
}

.apple-target-checkbox {
    width: 18px;
    height: 18px;
    margin-right: 12px;
    accent-color: #007AFF;
}

.apple-target-info {
    flex-grow: 1;
}

.apple-target-name {
    font-weight: 600;
    color: #1D1D1F;
    font-size: 0.95rem;
    margin-bottom: 2px;
}

.apple-target-stats {
    display: flex;
    gap: 8px;
    align-items: center;
}

.apple-stat-badge {
    background: rgba(0, 122, 255, 0.1);
    color: #007AFF;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.apple-provider-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 6px;
    font-weight: 600;
    background: #007AFF;
    color: white;
}

/* Apple-style quick actions */
.apple-quick-actions {
    display: flex;
    gap: 8px;
    margin: 12px 0;
    flex-wrap: wrap;
}

.apple-quick-btn {
    background: #F2F2F7;
    border: 1px solid #D1D1D6;
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 0.8rem;
    font-weight: 500;
    color: #1D1D1F;
    cursor: pointer;
    transition: all 0.2s ease;
}

.apple-quick-btn:hover {
    background: #007AFF;
    color: white;
    border-color: #007AFF;
    transform: translateY(-1px);
}

/* Apple-style summary */
.apple-summary {
    background: linear-gradient(135deg, #FFF8E1 0%, #FFFBF0 100%);
    border: 1px solid #FFB300;
    border-radius: 16px;
    padding: 20px;
    margin: 16px 0;
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
}

.apple-summary h6 {
    color: #E65100;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Apple-style footer */
.apple-filter-footer {
    background: linear-gradient(135deg, #F2F2F7 0%, #FFFFFF 100%);
    padding: 20px 32px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.apple-target-count {
    font-size: 0.9rem;
    color: #86868B;
    font-weight: 500;
}

.apple-footer-actions {
    display: flex;
    gap: 12px;
}

.apple-btn-secondary {
    background: #F2F2F7;
    border: 1px solid #D1D1D6;
    color: #1D1D1F;
    padding: 10px 20px;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.apple-btn-secondary:hover {
    background: #E5E5EA;
    color: #1D1D1F;
}

.apple-btn-primary {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    border: none;
    color: white;
    padding: 10px 24px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.apple-btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
    color: white;
}

.apple-btn-primary:disabled {
    background: #AEAEB2;
    box-shadow: none;
    cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
    .apple-options-grid {
        grid-template-columns: 1fr;
    }

    .apple-conflict-grid {
        grid-template-columns: 1fr;
    }

    .apple-filter-content {
        padding: 20px;
    }

    .apple-filter-header {
        padding: 24px 20px;
    }

    .apple-filter-footer {
        padding: 16px 20px;
        flex-direction: column;
        gap: 12px;
    }

    .apple-footer-actions {
        width: 100%;
        justify-content: center;
    }
}

.target-rule-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.target-rule-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #20c997);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.target-rule-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.target-rule-item:hover::before {
    transform: scaleX(1);
}

.target-rule-item.selected {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.target-rule-item.selected::before {
    transform: scaleX(1);
}

.target-rule-checkbox {
    transform: scale(1.2);
    margin-right: 12px;
    accent-color: #28a745;
}

.rule-info {
    display: flex;
    justify-content: between;
    align-items: center;
    width: 100%;
}

.rule-name {
    font-weight: 600;
    color: #495057;
    flex-grow: 1;
}

.rule-stats {
    display: flex;
    gap: 8px;
    align-items: center;
}

.stat-badge {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 500;
}

.provider-badge {
    font-size: 0.7rem;
    padding: 3px 8px;
    border-radius: 10px;
    font-weight: 600;
}

.provider-jobs-af {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.provider-acbar {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    color: white;
}

.quick-actions {
    display: flex;
    gap: 10px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.quick-action-btn {
    padding: 8px 16px;
    border-radius: 20px;
    border: 2px solid #e9ecef;
    background: white;
    color: #6c757d;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quick-action-btn.primary:hover {
    border-color: #007bff;
    background: #007bff;
    color: white;
}

.quick-action-btn.success:hover {
    border-color: #28a745;
    background: #28a745;
    color: white;
}

.quick-action-btn.info:hover {
    border-color: #17a2b8;
    background: #17a2b8;
    color: white;
}

.clone-summary {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.clone-filters-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 25px 30px;
    border-top: 1px solid #dee2e6;
}

.execute-clone-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.execute-clone-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
    color: white;
}

.execute-clone-btn:disabled {
    background: #6c757d;
    box-shadow: none;
    cursor: not-allowed;
}

.target-count-display {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .clone-options {
        grid-template-columns: 1fr;
    }

    .conflict-resolution {
        grid-template-columns: 1fr;
    }

    .clone-filters-body {
        padding: 20px;
    }

    .clone-section {
        padding: 20px;
    }

    .quick-actions {
        justify-content: center;
    }
}

.category-checkbox {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 20px;
    height: 20px;
    accent-color: #198754;
}

.category-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.category-icon {
    margin-right: 12px;
    font-size: 1.2rem;
}

.category-title {
    font-weight: 600;
    font-size: 1rem;
    color: #212529;
    margin: 0;
    flex-grow: 1;
}

.category-stats {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.category-stat-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 8px;
    font-weight: 500;
}

.category-description {
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 8px;
    line-height: 1.4;
}

/* Apple-style Footer */
.category-selector-footer {
    padding: 24px 32px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 0 0 20px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.selection-summary {
    display: flex;
    align-items: center;
    gap: 16px;
}

.selection-count {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 0.9rem;
    font-weight: 700;
    min-width: 90px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
    letter-spacing: -0.01em;
}

.footer-actions {
    display: flex;
    gap: 16px;
}

/* Apple-style Footer Buttons */
.btn-footer {
    padding: 12px 28px;
    border-radius: 16px;
    font-weight: 700;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    letter-spacing: -0.01em;
    min-width: 120px;
}

.btn-footer.btn-secondary {
    background: rgba(0, 0, 0, 0.08);
    color: #1d1d1f;
}

.btn-footer.btn-secondary:hover {
    background: rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.btn-footer.btn-primary {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
}

.btn-footer.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 122, 255, 0.4);
}

/* Apple-style Loading and Empty States */
.category-loading {
    text-align: center;
    padding: 60px 40px;
    color: rgba(0, 0, 0, 0.5);
}

.category-loading .spinner-border {
    width: 2.5rem;
    height: 2.5rem;
    border-width: 3px;
    border-color: #007AFF;
    border-right-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.category-empty {
    text-align: center;
    padding: 60px 40px;
    color: rgba(0, 0, 0, 0.5);
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.02) 0%, transparent 100%);
    border-radius: 16px;
    margin: 20px 0;
}

.category-empty i {
    font-size: 3.5rem;
    margin-bottom: 20px;
    opacity: 0.3;
    color: #007AFF;
}

.category-empty h5 {
    font-weight: 700;
    color: #1d1d1f;
    margin-bottom: 8px;
}

.category-empty p {
    font-size: 0.95rem;
    margin: 0;
    opacity: 0.7;
}

/* Apple-style Responsive Design */
@media (max-width: 768px) {
    .category-selector-modal .swal2-popup {
        width: 95vw;
        height: 90vh;
        margin: 20px 10px;
        border-radius: 16px;
    }

    .category-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .category-selector-header {
        padding: 24px 20px 20px;
    }

    .category-selector-header h2 {
        font-size: 1.5rem;
    }

    .category-controls-section {
        padding: 20px 20px 16px;
    }

    .category-content-section {
        padding: 16px 20px 20px;
    }

    .category-selector-footer {
        padding: 20px;
        flex-direction: column;
        gap: 16px;
    }

    .footer-actions {
        width: 100%;
        justify-content: center;
        gap: 12px;
    }

    .btn-footer {
        flex: 1;
        min-width: auto;
    }

    .category-controls-section .btn {
        font-size: 0.85rem;
        padding: 8px 12px;
    }
}

/* Tablet responsive design */
@media (min-width: 769px) and (max-width: 1024px) {
    .category-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 18px;
    }

    .category-selector-modal .swal2-popup {
        width: 85vw;
        max-width: 900px;
    }
}

/* Time input styling */
input[type="time"].inline-edit {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 1rem;
}

/* Overdue indicator */
.badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

/* Button states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Custom scrollbar for modal */
.clone-panel::-webkit-scrollbar {
    width: 6px;
}

.clone-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.clone-panel::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.clone-panel::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Select2 Integration */
.select2-container {
    width: 100% !important;
}

.select2-container--bootstrap-5 .select2-selection {
    border: 1px solid transparent;
    background: transparent;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    min-height: 38px;
}

.select2-container--bootstrap-5 .select2-selection:hover,
.select2-container--bootstrap-5.select2-container--focus .select2-selection {
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.1);
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    font-size: 0.75rem;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
    color: rgba(255, 255, 255, 0.8);
    margin-right: 0.25rem;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: white;
}

.select2-dropdown {
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.select2-results__option--highlighted {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Conditional Schedule Fields */
.conditional-schedule-fields {
    background: rgba(var(--primary-color-rgb), 0.02);
    border: 1px solid rgba(var(--primary-color-rgb), 0.1);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.conditional-group {
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.conditional-group:last-child {
    margin-bottom: 0;
}

.conditional-group.show {
    opacity: 1;
    transform: translateY(0);
}

.conditional-group:not(.show) {
    opacity: 0.7;
    transform: translateY(-5px);
}

.cron-human-readable {
    font-style: italic;
    color: var(--success-color);
    font-weight: 500;
}

/* Undo Panel Styles */
.undo-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: 2px solid var(--warning-color);
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    max-width: 400px;
    max-height: 300px;
    overflow-y: auto;
}

.undo-history {
    max-height: 150px;
    overflow-y: auto;
}

.undo-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.undo-item:last-child {
    margin-bottom: 0;
}

.undo-item .undo-action {
    color: var(--primary-color);
    cursor: pointer;
    text-decoration: underline;
}

.undo-item .undo-action:hover {
    color: var(--primary-dark);
}

/* Progress Indicator Styles */
.progress-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid var(--primary-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 1060;
    min-width: 350px;
}

.progress-indicator::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

.progress-content {
    position: relative;
    z-index: 1;
}

/* Enhanced Search and Filter Styles */
.input-group .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.1);
}

.dropdown-menu {
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Enhanced Responsive Design */

/* Mobile stats toggle */
.stats-content {
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .stats-content {
        border-top: 1px solid var(--border-color);
        padding-top: 1rem;
        margin-top: 1rem;
    }
}

@media (max-width: 1200px) {
    .chess-board-view {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }

    .rule-card {
        padding: 1.25rem;
    }
}

@media (max-width: 992px) {
    .chess-board-view {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }

    .stats-bar {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .stats-bar .d-flex:first-child {
        justify-content: center;
        flex-wrap: wrap;
    }

    .stats-bar .d-flex:last-child {
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
    }

    .input-group {
        width: 100% !important;
    }

    .dropdown {
        width: 100%;
    }

    .dropdown .btn {
        width: 100%;
        justify-content: space-between;
    }
}

@media (max-width: 768px) {
    .chess-board-view {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .rule-card {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .rule-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .rule-meta-info {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .rule-meta-item {
        width: 100%;
    }

    .schedule-display {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .filter-config-section,
    .execution-settings-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-row {
        flex-direction: column;
    }

    .form-row .col-md-6 {
        width: 100%;
        margin-bottom: 0.75rem;
    }

    /* Touch-friendly controls */
    .inline-edit {
        min-height: 44px;
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.75rem;
    }

    .btn {
        min-height: 44px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .btn-sm {
        min-height: 38px;
        padding: 0.5rem 0.75rem;
    }

    .form-select {
        min-height: 44px;
        font-size: 16px;
        padding: 0.75rem;
    }

    .form-control {
        min-height: 44px;
        font-size: 16px;
        padding: 0.75rem;
    }

    /* Mobile-optimized modals */
    .clone-modal {
        padding: 1rem;
    }

    .clone-panel {
        max-width: 100%;
        margin: 0;
        border-radius: 0.5rem;
        max-height: 90vh;
        overflow-y: auto;
    }

    .provider-selector {
        flex-direction: column;
        gap: 1rem;
    }

    .provider-option {
        width: 100%;
        padding: 1.5rem;
        min-height: 80px;
    }

    .time-offset-options {
        flex-direction: column;
        gap: 0.5rem;
    }

    .time-offset-option {
        width: 100%;
        padding: 1rem;
    }

    /* Mobile-optimized panels */
    .undo-panel {
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        max-width: none;
        max-height: 60vh;
        overflow-y: auto;
    }

    .progress-indicator {
        left: 10px;
        right: 10px;
        transform: translateY(-50%);
        min-width: auto;
        padding: 1rem;
    }

    /* Mobile pagination */
    .pagination-controls {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination .page-link {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.5rem;
    }

    .chess-board-view {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .rule-card {
        padding: 0.75rem;
        border-radius: 0.5rem;
    }

    .stats-bar {
        padding: 0.75rem;
    }

    .stat-item {
        min-width: 80px;
        padding: 0.5rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    /* Compact form layout */
    .form-label {
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }

    .conditional-schedule-fields {
        padding: 0.75rem;
    }

    /* Mobile-friendly dropdowns */
    .dropdown-menu {
        width: 100%;
        max-height: 60vh;
        overflow-y: auto;
    }

    .select2-container {
        font-size: 16px;
    }

    .select2-selection {
        min-height: 44px !important;
    }

    /* Clone superpower button */
    .clone-superpower {
        bottom: 20px;
        right: 20px;
    }

    .clone-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        padding: 0;
    }

    .clone-btn span {
        display: none;
    }

    .clone-btn i {
        font-size: 1.5rem;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .rule-card:hover {
        transform: none;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .inline-edit:hover {
        border-color: transparent;
        background: transparent;
    }

    .btn:hover {
        transform: none;
    }

    /* Larger touch targets */
    .inline-edit,
    .btn,
    .form-select,
    .form-control {
        min-height: 48px;
    }

    .btn-sm {
        min-height: 42px;
    }

    /* Remove hover effects that don't work on touch */
    .provider-option:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .time-offset-option:hover {
        transform: none;
    }
}

/* Search Highlighting */
.search-highlight {
    background: linear-gradient(120deg, #fff59d 0%, #ffeb3b 100%);
    color: #1a1a1a;
    padding: 0.1rem 0.2rem;
    border-radius: 0.25rem;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(255, 235, 59, 0.3);
    animation: highlightPulse 0.6s ease-out;
}

@keyframes highlightPulse {
    0% {
        background: linear-gradient(120deg, #fff59d 0%, #ffeb3b 100%);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(120deg, #ffeb3b 0%, #ffc107 100%);
        transform: scale(1.05);
    }
    100% {
        background: linear-gradient(120deg, #fff59d 0%, #ffeb3b 100%);
        transform: scale(1);
    }
}

/* Search Result Counter */
.search-results-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 1rem;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Sort Indicator */
.sort-indicator {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    margin-left: 0.5rem;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
    animation: sortPulse 0.4s ease-out;
}

@keyframes sortPulse {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}

/* Enhanced Filter States */
.filter-active {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-color: #f59e0b;
    box-shadow: 0 0 0 0.2rem rgba(245, 158, 11, 0.25);
}

.filter-active:focus {
    box-shadow: 0 0 0 0.2rem rgba(245, 158, 11, 0.5);
}

/* Loading States for Search */
.search-loading {
    position: relative;
}

.search-loading::after {
    content: '';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Enhanced Card Transitions */
.rule-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rule-card.filtered-out {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    pointer-events: none;
    margin: 0;
    height: 0;
    padding: 0;
    border: none;
    overflow: hidden;
}

.rule-card.filtered-in {
    opacity: 1;
    transform: scale(1) translateY(0);
    animation: filterIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes filterIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .rule-card {
        border-width: 1px;
    }

    .inline-edit {
        border-width: 1px;
    }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .chess-board-view {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .clone-panel {
        max-height: 80vh;
    }

    .undo-panel {
        max-height: 50vh;
    }
}
</style>
@endpush

@section('content')
<div class="container-fluid p-0">
    <!-- Stats Bar with Mobile Toggle -->
    <div class="stats-bar">
        <div class="d-flex justify-content-between align-items-center mb-2 d-md-none">
            <div class="d-flex align-items-center gap-2">
                <h6 class="mb-0">Command Schedule Rules</h6>
                <a href="{{ route('admin.jobseeker.command_schedule.index') }}"
                   class="btn btn-xs btn-outline-primary"
                   title="Go to Dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                </a>
            </div>
            <button class="btn btn-sm btn-outline-secondary" id="toggleStats" type="button">
                <i class="fas fa-chart-bar me-1"></i> Stats
            </button>
        </div>

        <div class="stats-content" id="statsContent">
            <!-- Navigation Breadcrumb for Desktop -->
            <div class="d-none d-md-flex align-items-center justify-content-between mb-3 pb-2 border-bottom">
                <div class="d-flex align-items-center gap-2">
                    <i class="fas fa-edit text-primary"></i>
                    <span class="fw-bold text-primary">Bulk Edit Mode</span>
                    <i class="fas fa-chevron-right text-muted mx-2"></i>
                    <span class="text-muted">Chess Board Interface</span>
                </div>
                <a href="{{ route('admin.jobseeker.command_schedule.index') }}"
                   class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-tachometer-alt me-1"></i>
                    <span class="d-none d-lg-inline">Health Dashboard</span>
                    <span class="d-lg-none">Dashboard</span>
                </a>
            </div>

            <div class="d-flex flex-wrap gap-3 justify-content-center justify-content-md-start">
                <div class="stat-item">
                    <div class="stat-number" id="totalRules">0</div>
                    <div class="stat-label">Total Rules</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="jobsAfRules">0</div>
                    <div class="stat-label">Jobs.af</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="acbarRules">0</div>
                    <div class="stat-label">ACBAR</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="activeRules">0</div>
                    <div class="stat-label">Active</div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-wrap gap-2 align-items-center">
            <!-- Search and Filter Controls -->
            <div class="input-group input-group-sm" style="width: 300px;">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchInput" placeholder="Search rules by name, command, or description...">
                <button class="btn btn-outline-secondary" type="button" id="clearSearch" title="Clear search">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Search Results Info -->
            <div class="search-results-info" id="searchResultsInfo" style="display: none;">
                <i class="fas fa-search"></i>
                <span id="searchResultsText">0 results found</span>
            </div>

            <!-- Sort Controls -->
            <div class="dropdown">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown">
                    <i class="fas fa-sort me-1"></i> Sort
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-sort="name">Name (A-Z)</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="name-desc">Name (Z-A)</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="priority">Priority (Low-High)</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="priority-desc">Priority (High-Low)</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="next_run">Next Run (Earliest)</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="next_run-desc">Next Run (Latest)</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="last_execution">Last Execution</a></li>
                    <li><a class="dropdown-item" href="#" data-sort="created_at">Created Date</a></li>
                </ul>
            </div>

            <!-- Filter Controls -->
            <div class="dropdown">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown">
                    <i class="fas fa-filter me-1"></i> Filter
                </button>
                <ul class="dropdown-menu p-3" style="min-width: 250px;">
                    <li class="mb-2">
                        <label class="form-label fw-bold">Provider</label>
                        <select class="form-select form-select-sm" id="providerFilter">
                            <option value="">All Providers</option>
                            <option value="jobsaf">Jobs.af</option>
                            <option value="acbar">ACBAR</option>
                        </select>
                    </li>
                    <li class="mb-2">
                        <label class="form-label fw-bold">Status</label>
                        <select class="form-select form-select-sm" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </li>
                    <li class="mb-2">
                        <label class="form-label fw-bold">Schedule Type</label>
                        <select class="form-select form-select-sm" id="scheduleTypeFilter">
                            <option value="">All Types</option>
                            <option value="daily_at">Daily</option>
                            <option value="weekly_at">Weekly</option>
                            <option value="cron">Custom Cron</option>
                        </select>
                    </li>
                    <li>
                        <button class="btn btn-sm btn-outline-secondary w-100" id="clearFilters">
                            <i class="fas fa-times me-1"></i> Clear Filters
                        </button>
                    </li>
                </ul>
            </div>

            <!-- Apple-Style Filter Copy Button -->
            <button type="button" class="btn btn-apple-copy btn-sm" onclick="openAppleFilterCopyModal()" title="Copy categories and locations between rules">
                <div class="btn-apple-content">
                    <i class="sf-symbol-copy"></i>
                    <span>Copy Filters</span>
                </div>
            </button>

            <div class="vr mx-2"></div>

            <!-- Enhanced Navigation Link to Main Dashboard -->
            <a href="{{ route('admin.jobseeker.command_schedule.index') }}"
               class="btn btn-primary btn-sm fw-bold"
               title="Return to Command Schedule Dashboard with Health Monitoring">
                <i class="fas fa-tachometer-alt me-1"></i> Dashboard
            </a>
            <div class="vr mx-2"></div>
            <button class="btn btn-outline-primary btn-sm" onclick="filterProvider('all')">
                <i class="fas fa-eye me-1"></i> All
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="filterProvider('jobsaf')">
                <i class="fas fa-briefcase me-1"></i> Jobs.af
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="filterProvider('acbar')">
                <i class="fas fa-building me-1"></i> ACBAR
            </button>
            <div class="vr mx-2"></div>
            <small class="text-muted" id="sortIndicatorText">
                <i class="fas fa-sort-amount-down me-1"></i>
                Sorted by next run time
            </small>
            <div class="sort-indicator" id="sortIndicator" style="display: none;">
                <i class="fas fa-sort"></i>
                <span id="sortIndicatorLabel">Default</span>
            </div>
        </div>
    </div>

    <!-- Undo/Rollback Panel -->
    <div class="undo-panel" id="undoPanel" style="display: none;">
        <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
                <i class="fas fa-undo text-warning me-2"></i>
                <span class="fw-bold">Recent Changes</span>
                <span class="badge bg-secondary ms-2" id="undoCount">0</span>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-warning" id="undoLastChange" disabled>
                    <i class="fas fa-undo me-1"></i> Undo Last
                </button>
                <button class="btn btn-sm btn-outline-danger" id="clearUndoHistory" disabled>
                    <i class="fas fa-trash me-1"></i> Clear History
                </button>
                <button class="btn btn-sm btn-outline-secondary" id="hideUndoPanel">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="undo-history mt-2" id="undoHistory">
            <!-- Undo history items will be populated here -->
        </div>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-indicator" id="progressIndicator" style="display: none;">
        <div class="progress-content">
            <div class="d-flex align-items-center justify-content-between mb-2">
                <span class="fw-bold" id="progressTitle">Processing...</span>
                <button class="btn btn-sm btn-outline-secondary" id="cancelOperation" style="display: none;">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
            </div>
            <div class="progress mb-2">
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     id="progressBar" role="progressbar" style="width: 0%"></div>
            </div>
            <div class="d-flex justify-content-between">
                <small class="text-muted" id="progressStatus">Initializing...</small>
                <small class="text-muted" id="progressPercent">0%</small>
            </div>
        </div>
    </div>

    <!-- Chess Board View -->
    <div class="chess-board-view" id="rulesContainer">
        <!-- Rules will be loaded dynamically -->
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted fs-6">Loading rules...</p>
        </div>
    </div>

    <!-- Clone Superpower Button -->
    <div class="clone-superpower">
        <button class="btn clone-btn" onclick="openCloneModal()">
            <i class="fas fa-magic"></i>
            <span>Clone Superpower</span>
        </button>
    </div>

    <!-- Clone Modal -->
    <div class="clone-modal" id="cloneModal" style="display:none;">
        <div class="clone-panel">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-magic text-primary me-2"></i>
                    Clone Rules Between Providers
                </h4>
                <button type="button" class="btn-close" onclick="closeCloneModal()" aria-label="Close"></button>
            </div>

            <div class="provider-selector">
                <div class="provider-option" id="fromProvider" onclick="selectFromProvider('jobsaf')">
                    <i class="fas fa-briefcase fa-2x mb-2 text-primary"></i>
                    <div class="fw-bold fs-6">Jobs.af</div>
                    <div class="text-muted small" id="fromProviderCount">0 rules</div>
                </div>

                <div class="text-center align-self-center">
                    <i class="fas fa-arrow-right fa-2x text-muted"></i>
                </div>

                <div class="provider-option" id="toProvider" onclick="selectToProvider('acbar')">
                    <i class="fas fa-building fa-2x mb-2 text-success"></i>
                    <div class="fw-bold fs-6">ACBAR</div>
                    <div class="text-muted small" id="toProviderCount">0 rules</div>
                </div>
            </div>

            <div class="mb-4">
                <label class="form-label fw-bold">Time Offset</label>
                <div class="time-offset-selector">
                    <button type="button" class="offset-btn active" onclick="selectOffset(15)">15 min</button>
                    <button type="button" class="offset-btn" onclick="selectOffset(30)">30 min</button>
                    <button type="button" class="offset-btn" onclick="selectOffset(60)">60 min</button>
                </div>
            </div>

            <div class="d-flex gap-2 justify-content-end">
                <button type="button" class="btn btn-secondary" onclick="closeCloneModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="executeClone()" id="cloneExecuteBtn">
                    <i class="fas fa-magic me-2"></i>
                    Clone Rules
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Select2 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- Cronstrue for cron expression translation -->
<script src="https://cdn.jsdelivr.net/npm/cronstrue@2.50.0/dist/cronstrue.min.js"></script>
<script>
// Bulk Edit Command Schedule Rules - Production Ready
(function() {
    'use strict';

    // Global state
    const state = {
        allRules: [],
        selectedOffset: 15,
        fromProvider: 'jobsaf',
        toProvider: 'acbar',
        isLoading: false,
        saveQueue: new Map(), // Track ongoing save operations
        categories: [], // Available categories for dropdowns
        locations: [], // Available locations for dropdowns
        isInitializing: false, // Flag to prevent saves during initialization
        isRerendering: false // Flag to prevent saves during search re-rendering
    };

    // API Configuration
    const API_BASE = '{{ route("admin.jobseeker.command_schedule.api.bulk_data") }}'.replace('/bulk-data', '');
    const CSRF_TOKEN = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // Validation
    if (!CSRF_TOKEN) {
        console.error('CSRF token not found');
        showError('Security token missing. Please refresh the page.');
        return;
    }

    // Initialize the application
    document.addEventListener('DOMContentLoaded', function() {
        initializeApp();
    });

    async function initializeApp() {
        try {
            // Load dropdown data first, then load bulk data
            await loadDropdownData();
            loadBulkData();
            setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize app:', error);
            showError('Failed to initialize application. Please refresh the page.');
        }
    }

    function setupEventListeners() {
        // Handle escape key for modal
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && document.getElementById('cloneModal').style.display !== 'none') {
                closeCloneModal();
            }
        });

        // Handle click outside modal
        document.getElementById('cloneModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCloneModal();
            }
        });

        // Category Manager Button Event Listener
        document.addEventListener('click', function(e) {
            if (e.target.closest('.btn-category-manager')) {
                const button = e.target.closest('.btn-category-manager');
                const ruleId = parseInt(button.dataset.ruleId);
                const provider = button.dataset.provider;

                // Get current categories from the button's data attribute (updated after saves)
                let currentCategories = [];
                try {
                    currentCategories = JSON.parse(button.dataset.currentCategories || '[]');
                } catch (e) {
                    console.warn('Failed to parse current categories, using empty array:', e);
                    currentCategories = [];
                }

                // Also try to get from state as backup
                const rule = state.allRules?.find(r => r.id === ruleId);
                if (rule && rule.categories && currentCategories.length === 0) {
                    currentCategories = rule.categories;
                    console.log('📋 Using categories from state as fallback:', currentCategories);
                }

                console.log('🎯 Category manager button clicked', {
                    ruleId,
                    provider,
                    currentCategories,
                    fromButton: button.dataset.currentCategories,
                    fromState: rule?.categories
                });

                // Add visual feedback
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);

                // Open the category manager
                openCategoryManager(ruleId, provider, currentCategories);
            }
        });

        // Location Manager Button Event Listener
        document.addEventListener('click', function(e) {
            if (e.target.closest('.btn-location-manager')) {
                const button = e.target.closest('.btn-location-manager');
                const ruleId = parseInt(button.dataset.ruleId);
                const provider = button.dataset.provider;

                // Get current locations from the button's data attribute (updated after saves)
                let currentLocations = [];
                try {
                    currentLocations = JSON.parse(button.dataset.currentLocations || '[]');
                } catch (e) {
                    console.warn('Failed to parse current locations, using empty array:', e);
                    currentLocations = [];
                }

                // Also try to get from state as backup
                const rule = state.allRules?.find(r => r.id === ruleId);
                if (rule && rule.locations && currentLocations.length === 0) {
                    currentLocations = rule.locations;
                    console.log('📋 Using locations from state as fallback:', currentLocations);
                }

                console.log('🗺️ Location manager button clicked', {
                    ruleId,
                    provider,
                    currentLocations,
                    fromButton: button.dataset.currentLocations,
                    fromState: rule?.locations
                });

                // Add visual feedback
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);

                // Open the location manager
                openLocationManager(ruleId, provider, currentLocations);
            }
        });

        // Mobile-specific enhancements
        setupMobileEnhancements();

        // Setup search functionality
        const searchInput = document.getElementById('searchInput');
        const clearSearch = document.getElementById('clearSearch');

        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
        }

        if (clearSearch) {
            clearSearch.addEventListener('click', clearSearchAndFilters);
        }

        // Setup sort functionality
        const sortItems = document.querySelectorAll('[data-sort]');
        sortItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const sortBy = this.dataset.sort;
                applySorting(sortBy);
            });
        });

        // Setup filter functionality
        const providerFilter = document.getElementById('providerFilter');
        const statusFilter = document.getElementById('statusFilter');
        const scheduleTypeFilter = document.getElementById('scheduleTypeFilter');
        const clearFilters = document.getElementById('clearFilters');

        [providerFilter, statusFilter, scheduleTypeFilter].forEach(filter => {
            if (filter) {
                filter.addEventListener('change', applyFilters);
            }
        });

        if (clearFilters) {
            clearFilters.addEventListener('click', clearSearchAndFilters);
        }

        // Setup undo functionality
        setupUndoListeners();

        // Setup mobile stats toggle
        setupMobileStatsToggle();
    }

    function setupMobileStatsToggle() {
        const toggleBtn = document.getElementById('toggleStats');
        const statsContent = document.getElementById('statsContent');

        if (toggleBtn && statsContent) {
            // Initially hide stats on mobile
            if (window.innerWidth <= 768) {
                statsContent.style.display = 'none';
            }

            toggleBtn.addEventListener('click', function() {
                const isVisible = statsContent.style.display !== 'none';

                if (isVisible) {
                    statsContent.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-chart-bar me-1"></i> Show Stats';
                } else {
                    statsContent.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-chart-bar me-1"></i> Hide Stats';
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    statsContent.style.display = 'block';
                    toggleBtn.innerHTML = '<i class="fas fa-chart-bar me-1"></i> Stats';
                } else if (statsContent.style.display === 'block') {
                    toggleBtn.innerHTML = '<i class="fas fa-chart-bar me-1"></i> Hide Stats';
                } else {
                    toggleBtn.innerHTML = '<i class="fas fa-chart-bar me-1"></i> Show Stats';
                }
            });
        }
    }

    // Undo/Rollback functionality
    const undoHistory = [];
    const MAX_UNDO_HISTORY = 20;

    function setupUndoListeners() {
        const undoLastChangeBtn = document.getElementById('undoLastChange');
        const clearUndoHistoryBtn = document.getElementById('clearUndoHistory');
        const hideUndoPanelBtn = document.getElementById('hideUndoPanel');

        if (undoLastChangeBtn) {
            undoLastChangeBtn.addEventListener('click', undoLastChangeAction);
        }

        if (clearUndoHistoryBtn) {
            clearUndoHistoryBtn.addEventListener('click', clearUndoHistoryAction);
        }

        if (hideUndoPanelBtn) {
            hideUndoPanelBtn.addEventListener('click', hideUndoPanelAction);
        }
    }

    function addToUndoHistory(action) {
        undoHistory.unshift({
            ...action,
            timestamp: new Date(),
            id: Date.now() + Math.random()
        });

        // Limit history size
        if (undoHistory.length > MAX_UNDO_HISTORY) {
            undoHistory.splice(MAX_UNDO_HISTORY);
        }

        updateUndoPanel();
        showUndoPanel();
    }

    function updateUndoPanel() {
        const undoCount = document.getElementById('undoCount');
        const undoHistoryElement = document.getElementById('undoHistory');
        const undoLastChange = document.getElementById('undoLastChange');
        const clearUndoHistoryBtn = document.getElementById('clearUndoHistory');

        if (undoCount) {
            undoCount.textContent = undoHistory.length;
        }

        if (undoHistoryElement) {
            undoHistoryElement.innerHTML = undoHistory.slice(0, 5).map(item => `
                <div class="undo-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <strong>${item.field}</strong> changed from
                            <code>${item.oldValue}</code> to <code>${item.newValue}</code>
                            <br><small class="text-muted">Rule: ${item.ruleName}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary undo-action"
                                onclick="undoSpecificChange('${item.id}')">
                            Undo
                        </button>
                    </div>
                    <small class="text-muted">${formatTimeAgo(item.timestamp)}</small>
                </div>
            `).join('');
        }

        // Enable/disable buttons
        const hasHistory = undoHistory.length > 0;
        if (undoLastChange) {
            undoLastChange.disabled = !hasHistory;
        }
        if (clearUndoHistoryBtn) {
            clearUndoHistoryBtn.disabled = !hasHistory;
        }
    }

    function showUndoPanel() {
        const panel = document.getElementById('undoPanel');
        if (panel) {
            panel.style.display = 'block';
        }
    }

    function hideUndoPanel() {
        const panel = document.getElementById('undoPanel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    // Search and Filter functionality
    let currentFilters = {
        search: '',
        provider: '',
        status: '',
        scheduleType: ''
    };

    let currentSort = {
        field: 'schedule_sort_key',
        direction: 'asc'
    };

    function handleSearch(event) {
        currentFilters.search = event.target.value;
        applyFiltersAndSort();
    }

    function applyFilters() {
        const providerFilter = document.getElementById('providerFilter');
        const statusFilter = document.getElementById('statusFilter');
        const scheduleTypeFilter = document.getElementById('scheduleTypeFilter');

        currentFilters.provider = providerFilter?.value || '';
        currentFilters.status = statusFilter?.value || '';
        currentFilters.scheduleType = scheduleTypeFilter?.value || '';

        // Add visual feedback for active filters
        [providerFilter, statusFilter, scheduleTypeFilter].forEach(filter => {
            if (filter) {
                if (filter.value) {
                    filter.classList.add('filter-active');
                } else {
                    filter.classList.remove('filter-active');
                }
            }
        });

        applyFiltersAndSort();
    }

    function clearSearchAndFilters() {
        // Clear search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
            searchInput.classList.remove('search-loading');
        }

        // Clear filter selects and remove active styling
        const providerFilter = document.getElementById('providerFilter');
        const statusFilter = document.getElementById('statusFilter');
        const scheduleTypeFilter = document.getElementById('scheduleTypeFilter');

        if (providerFilter) {
            providerFilter.value = '';
            providerFilter.classList.remove('filter-active');
        }
        if (statusFilter) {
            statusFilter.value = '';
            statusFilter.classList.remove('filter-active');
        }
        if (scheduleTypeFilter) {
            scheduleTypeFilter.value = '';
            scheduleTypeFilter.classList.remove('filter-active');
        }

        // Reset filters
        currentFilters = {
            search: '',
            provider: '',
            status: '',
            scheduleType: ''
        };

        // Remove search highlights
        removeSearchHighlights();

        // Hide search results info
        const searchResultsInfo = document.getElementById('searchResultsInfo');
        if (searchResultsInfo) {
            searchResultsInfo.style.display = 'none';
        }

        applyFiltersAndSort();
    }

    function applySorting(sortBy) {
        const [field, direction] = sortBy.includes('-desc')
            ? [sortBy.replace('-desc', ''), 'desc']
            : [sortBy, 'asc'];

        currentSort = { field, direction };
        applyFiltersAndSort();

        // Update sort indicator
        updateSortIndicator(sortBy);
    }

    function updateSortIndicator(sortBy) {
        const sortText = document.getElementById('sortIndicatorText');
        const sortIndicator = document.getElementById('sortIndicator');
        const sortIndicatorLabel = document.getElementById('sortIndicatorLabel');

        const sortLabels = {
            'name': 'Name (A-Z)',
            'name-desc': 'Name (Z-A)',
            'priority': 'Priority (Low-High)',
            'priority-desc': 'Priority (High-Low)',
            'next_run': 'Next Run (Earliest)',
            'next_run-desc': 'Next Run (Latest)',
            'last_execution': 'Last Execution',
            'created_at': 'Created Date',
            'schedule_sort_key': 'Schedule Time'
        };

        const sortLabel = sortLabels[sortBy] || 'Schedule Time';

        if (sortText) {
            sortText.innerHTML = `
                <i class="fas fa-sort-amount-down me-1"></i>
                Sorted by ${sortLabel}
            `;
        }

        if (sortIndicator && sortIndicatorLabel) {
            sortIndicatorLabel.textContent = sortLabel;
            sortIndicator.style.display = 'inline-flex';

            // Add animation
            sortIndicator.style.animation = 'none';
            setTimeout(() => {
                sortIndicator.style.animation = 'sortPulse 0.4s ease-out';
            }, 10);
        }
    }

    function applyFiltersAndSort() {
        // Use client-side filtering for better performance and highlighting
        applyFiltersAndSortClientSide();
    }

    function applyFiltersAndSortClientSide() {
        const container = document.getElementById('rulesContainer');
        if (!container || !state.allRules || !Array.isArray(state.allRules)) {
            console.log('No rules to filter:', {
                container: !!container,
                allRules: state.allRules,
                isArray: Array.isArray(state.allRules)
            });
            return;
        }

        // Add loading state
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.classList.add('search-loading');
        }

        // Filter rules with enhanced search
        let filteredRules = state.allRules.filter(rule => {
            // Search filter with multiple field support
            if (currentFilters.search) {
                const searchText = currentFilters.search.toLowerCase().trim();
                if (searchText) {
                    const searchableFields = [
                        rule.name || '',
                        rule.command || '',
                        rule.description || '',
                        rule.display_name || '',
                        (rule.categories || []).join(' '),
                        (rule.locations || []).join(' '),
                        rule.provider || ''
                    ];

                    const searchableText = searchableFields.join(' ').toLowerCase();

                    // Support multiple search terms
                    const searchTerms = searchText.split(/\s+/).filter(term => term.length > 0);
                    const matchesAllTerms = searchTerms.every(term =>
                        searchableText.includes(term)
                    );

                    if (!matchesAllTerms) {
                        return false;
                    }
                }
            }

            // Provider filter
            if (currentFilters.provider && rule.provider !== currentFilters.provider) {
                return false;
            }

            // Status filter
            if (currentFilters.status) {
                const isActive = rule.is_active;
                if (currentFilters.status === 'active' && !isActive) return false;
                if (currentFilters.status === 'inactive' && isActive) return false;
            }

            // Schedule type filter
            if (currentFilters.scheduleType && rule.schedule_type !== currentFilters.scheduleType) {
                return false;
            }

            return true;
        });

        // Enhanced sorting with better data type handling
        filteredRules.sort((a, b) => {
            let aValue = a[currentSort.field];
            let bValue = b[currentSort.field];

            // Handle special sorting cases
            if (currentSort.field === 'next_run') {
                aValue = a.next_run_timestamp || Number.MAX_SAFE_INTEGER;
                bValue = b.next_run_timestamp || Number.MAX_SAFE_INTEGER;
            } else if (currentSort.field === 'last_execution') {
                aValue = a.last_execution?.started_at || '';
                bValue = b.last_execution?.started_at || '';
            } else if (currentSort.field === 'priority') {
                aValue = parseInt(a.priority) || 0;
                bValue = parseInt(b.priority) || 0;
            } else if (currentSort.field === 'schedule_sort_key') {
                // Handle schedule_sort_key as numeric value (minutes since midnight)
                aValue = parseInt(a.schedule_sort_key) || Number.MAX_SAFE_INTEGER;
                bValue = parseInt(b.schedule_sort_key) || Number.MAX_SAFE_INTEGER;

                // Debug logging for contextual cron-based sorting
                if (Math.random() < 0.01) { // Log 1% of comparisons to avoid spam
                    console.log(`Contextual cron sorting: ${a.name} (sort_key: ${aValue}) vs ${b.name} (sort_key: ${bValue})`);
                }
            } else if (currentSort.field === 'created_at' || currentSort.field === 'updated_at') {
                aValue = new Date(a[currentSort.field]).getTime();
                bValue = new Date(b[currentSort.field]).getTime();
            }

            // Convert strings to lowercase for comparison
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            if (aValue > bValue) comparison = 1;

            return currentSort.direction === 'desc' ? -comparison : comparison;
        });

        // Update search results counter
        updateSearchResultsInfo(filteredRules.length, state.allRules.length);

        // Remove loading state
        if (searchInput) {
            searchInput.classList.remove('search-loading');
        }

        // Re-render filtered and sorted rules with highlighting
        renderFilteredRules(filteredRules);
    }

    function renderFilteredRules(rules) {
        const container = document.getElementById('rulesContainer');
        if (!container) return;

        if (rules.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center p-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No rules found</h5>
                    <p class="text-muted">Try adjusting your search or filter criteria.</p>
                    <button class="btn btn-outline-primary" onclick="clearSearchAndFilters()">
                        <i class="fas fa-times me-1"></i> Clear Filters
                    </button>
                </div>
            `;
            return;
        }

        // Clear container and render filtered rules
        container.innerHTML = '';

        // Set both initialization and re-rendering flags to prevent saves
        state.isInitializing = true;
        state.isRerendering = true;

        console.log('Starting card re-rendering, preventing saves...');

        rules.forEach(rule => {
            const card = createRuleCard(rule);
            container.appendChild(card);
        });

        // Clear flags after rendering and dropdown initialization
        setTimeout(() => {
            state.isInitializing = false;
            console.log('Initialization flag cleared');

            // Clear re-rendering flag after additional delay for Select2
            setTimeout(() => {
                state.isRerendering = false;
                console.log('Re-rendering flag cleared, saves now allowed');
            }, 500);

            // Apply search highlighting after rendering
            if (currentFilters.search && currentFilters.search.trim()) {
                highlightSearchTerms(currentFilters.search.trim());
            }
        }, 1000);
    }

    // Search highlighting functions
    function highlightSearchTerms(searchText) {
        if (!searchText) return;

        const searchTerms = searchText.toLowerCase().split(/\s+/).filter(term => term.length > 0);
        const container = document.getElementById('rulesContainer');
        if (!container) return;

        // Remove existing highlights
        removeSearchHighlights();

        // Find and highlight text in rule cards
        const ruleCards = container.querySelectorAll('.rule-card');
        ruleCards.forEach(card => {
            highlightInElement(card, searchTerms);
        });
    }

    function highlightInElement(element, searchTerms) {
        // Target specific elements for highlighting
        const targetSelectors = [
            '.rule-name-display',
            '.rule-description',
            '.schedule-expression',
            '.differentiator-tag',
            '.rule-provider',
            '.rule-meta-info',
            '.schedule-human-readable',
            'input[data-field="name"]',
            'textarea[data-field="description"]'
        ];

        targetSelectors.forEach(selector => {
            try {
                const targets = element.querySelectorAll(selector);
                targets.forEach(target => {
                    highlightTextInNode(target, searchTerms);
                });
            } catch (error) {
                console.warn(`Error highlighting selector ${selector}:`, error);
            }
        });
    }

    function highlightTextInNode(node, searchTerms) {
        try {
            if (node.nodeType === Node.TEXT_NODE) {
                let text = node.textContent;
                let highlightedText = text;

                searchTerms.forEach(term => {
                    const regex = new RegExp(`(${escapeRegExp(term)})`, 'gi');
                    highlightedText = highlightedText.replace(regex, '<span class="search-highlight">$1</span>');
                });

                if (highlightedText !== text && node.parentNode) {
                    const wrapper = document.createElement('span');
                    wrapper.innerHTML = highlightedText;
                    node.parentNode.replaceChild(wrapper, node);
                }
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                // Skip already highlighted elements and form inputs
                if (node.classList && (node.classList.contains('search-highlight') ||
                    node.tagName === 'INPUT' || node.tagName === 'SELECT' || node.tagName === 'TEXTAREA')) {
                    return;
                }

                const childNodes = Array.from(node.childNodes);
                childNodes.forEach(child => {
                    highlightTextInNode(child, searchTerms);
                });
            }
        } catch (error) {
            console.warn('Error highlighting text node:', error);
        }
    }

    function removeSearchHighlights() {
        try {
            const highlights = document.querySelectorAll('.search-highlight');
            highlights.forEach(highlight => {
                const parent = highlight.parentNode;
                if (parent) {
                    parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                    parent.normalize();
                }
            });
        } catch (error) {
            console.warn('Error removing search highlights:', error);
        }
    }

    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // Update search results info
    function updateSearchResultsInfo(filteredCount, totalCount) {
        const searchResultsInfo = document.getElementById('searchResultsInfo');
        const searchResultsText = document.getElementById('searchResultsText');

        if (!searchResultsInfo || !searchResultsText) return;

        if (currentFilters.search && currentFilters.search.trim()) {
            searchResultsText.textContent = `${filteredCount} of ${totalCount} rules found`;
            searchResultsInfo.style.display = 'inline-flex';
        } else if (filteredCount < totalCount) {
            searchResultsText.textContent = `${filteredCount} of ${totalCount} rules shown`;
            searchResultsInfo.style.display = 'inline-flex';
        } else {
            searchResultsInfo.style.display = 'none';
        }
    }

    // Pagination Controls
    function renderPaginationControls(pagination) {
        let paginationContainer = document.getElementById('paginationControls');

        if (!paginationContainer) {
            // Create pagination container
            paginationContainer = document.createElement('div');
            paginationContainer.id = 'paginationControls';
            paginationContainer.className = 'pagination-controls mt-4 d-flex justify-content-between align-items-center';

            const rulesContainer = document.getElementById('rulesContainer');
            rulesContainer.parentNode.insertBefore(paginationContainer, rulesContainer.nextSibling);
        }

        const { current_page, last_page, total, from, to } = pagination;

        paginationContainer.innerHTML = `
            <div class="pagination-info">
                <span class="text-muted">
                    Showing ${from || 0} to ${to || 0} of ${total || 0} rules
                </span>
            </div>
            <nav aria-label="Rules pagination">
                <ul class="pagination pagination-sm mb-0">
                    <li class="page-item ${current_page <= 1 ? 'disabled' : ''}">
                        <button class="page-link" onclick="loadPage(${current_page - 1})" ${current_page <= 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                    </li>
                    ${generatePageNumbers(current_page, last_page)}
                    <li class="page-item ${current_page >= last_page ? 'disabled' : ''}">
                        <button class="page-link" onclick="loadPage(${current_page + 1})" ${current_page >= last_page ? 'disabled' : ''}>
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </li>
                </ul>
            </nav>
        `;
    }

    function generatePageNumbers(currentPage, lastPage) {
        const maxVisible = 5;
        let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
        let end = Math.min(lastPage, start + maxVisible - 1);

        if (end - start + 1 < maxVisible) {
            start = Math.max(1, end - maxVisible + 1);
        }

        let pages = '';

        if (start > 1) {
            pages += `<li class="page-item"><button class="page-link" onclick="loadPage(1)">1</button></li>`;
            if (start > 2) {
                pages += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = start; i <= end; i++) {
            pages += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <button class="page-link" onclick="loadPage(${i})">${i}</button>
                </li>
            `;
        }

        if (end < lastPage) {
            if (end < lastPage - 1) {
                pages += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            pages += `<li class="page-item"><button class="page-link" onclick="loadPage(${lastPage})">${lastPage}</button></li>`;
        }

        return pages;
    }

    function loadPage(page) {
        if (page < 1 || (state.pagination && page > state.pagination.last_page)) return;
        loadBulkData(0, page, false);
    }

    // Mobile-specific enhancements
    function setupMobileEnhancements() {
        // Detect if device is mobile
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

        if (isMobile || isTouchDevice) {
            // Add mobile class to body
            document.body.classList.add('mobile-device');

            // Prevent zoom on input focus for iOS
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.style.fontSize === '' || parseFloat(input.style.fontSize) < 16) {
                    input.style.fontSize = '16px';
                }
            });

            // Add touch feedback to interactive elements
            setupTouchFeedback();

            // Optimize scroll behavior
            setupMobileScrolling();

            // Handle orientation changes
            window.addEventListener('orientationchange', handleOrientationChange);
        }

        // Setup swipe gestures for mobile navigation
        if (isTouchDevice) {
            setupSwipeGestures();
        }
    }

    function setupTouchFeedback() {
        // Add touch feedback to buttons and interactive elements
        const interactiveElements = document.querySelectorAll('.btn, .rule-card, .provider-option, .time-offset-option');

        interactiveElements.forEach(element => {
            element.addEventListener('touchstart', function() {
                this.classList.add('touch-active');
            });

            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('touch-active');
                }, 150);
            });

            element.addEventListener('touchcancel', function() {
                this.classList.remove('touch-active');
            });
        });
    }

    function setupMobileScrolling() {
        // Smooth scrolling for mobile
        document.documentElement.style.scrollBehavior = 'smooth';

        // Prevent overscroll bounce on iOS
        document.body.style.overscrollBehavior = 'none';

        // Optimize scroll performance
        const scrollElements = document.querySelectorAll('.chess-board-view, .undo-history, .clone-panel');
        scrollElements.forEach(element => {
            element.style.webkitOverflowScrolling = 'touch';
        });
    }

    function handleOrientationChange() {
        // Delay to allow for orientation change to complete
        setTimeout(() => {
            // Recalculate layout if needed
            const container = document.getElementById('rulesContainer');
            if (container) {
                // Force reflow to fix any layout issues
                container.style.display = 'none';
                container.offsetHeight; // Trigger reflow
                container.style.display = '';
            }

            // Close any open dropdowns or modals that might be mispositioned
            const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
            openDropdowns.forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }, 100);
    }

    function setupSwipeGestures() {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;

        const container = document.getElementById('rulesContainer');
        if (!container) return;

        container.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        container.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;

            const deltaX = endX - startX;
            const deltaY = endY - startY;

            // Only process horizontal swipes that are longer than vertical
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    // Swipe right - go to previous page
                    if (state.pagination && state.pagination.current_page > 1) {
                        loadPage(state.pagination.current_page - 1);
                    }
                } else {
                    // Swipe left - go to next page
                    if (state.pagination && state.pagination.current_page < state.pagination.last_page) {
                        loadPage(state.pagination.current_page + 1);
                    }
                }
            }
        });
    }

    // Add CSS for touch feedback
    const touchStyles = document.createElement('style');
    touchStyles.textContent = `
        .touch-active {
            transform: scale(0.98);
            opacity: 0.8;
            transition: all 0.1s ease;
        }

        .mobile-device .rule-card {
            transition: transform 0.1s ease;
        }

        .mobile-device .btn {
            transition: transform 0.1s ease;
        }

        .mobile-device .provider-option {
            transition: transform 0.1s ease;
        }

        /* iOS specific fixes */
        @supports (-webkit-touch-callout: none) {
            .form-control,
            .form-select,
            .inline-edit {
                font-size: 16px !important;
                transform: translateZ(0);
            }
        }
    `;
    document.head.appendChild(touchStyles);

    // Load dropdown data (categories and locations) from existing provider endpoints
    async function loadDropdownData() {
        try {
            // Load categories and locations for both providers
            const providers = ['jobsaf', 'acbar'];
            state.categories = [];
            state.locations = [];

            // Create promises for all provider requests
            const providerPromises = providers.map(async (provider) => {
                const url = `{{ url('admin/jobseeker/command-schedule/provider-categories') }}/${provider}`;
                const response = await fetch(url, {
                    headers: {
                        'X-CSRF-TOKEN': CSRF_TOKEN,
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        // Flatten categories from optgroups and add provider info
                        if (data.categories) {
                            data.categories.forEach(category => {
                                if (category.children) {
                                    // Add child categories
                                    category.children.forEach(child => {
                                        state.categories.push({
                                            id: child.id,
                                            text: child.text,
                                            provider: provider
                                        });
                                    });
                                } else if (category.id !== 'All') {
                                    // Add parent category if it's not "All"
                                    state.categories.push({
                                        id: category.id,
                                        text: category.text,
                                        provider: provider
                                    });
                                }
                            });
                        }

                        // Add locations with provider info
                        if (data.locations) {
                            data.locations.forEach(location => {
                                state.locations.push({
                                    id: location.id,
                                    text: location.text,
                                    provider: provider
                                });
                            });
                        }
                    }
                } else {
                    console.warn(`Failed to load data for provider ${provider}:`, response.status);
                }
            });

            // Wait for all provider requests to complete
            await Promise.all(providerPromises);

            console.log(`Loaded ${state.categories.length} categories and ${state.locations.length} locations`);

            // Ensure we have some data
            if (state.categories.length === 0 && state.locations.length === 0) {
                console.warn('No dropdown data loaded from any provider');
            }

        } catch (error) {
            console.error('Error loading dropdown data:', error);
            // Continue without dropdown data - will fall back to textareas
        }
    }

    // Load all rules data with error handling, retry, and pagination support
    async function loadBulkData(retryCount = 0, page = 1, append = false) {
        if (state.isLoading) return;

        state.isLoading = true;
        const container = document.getElementById('rulesContainer');

        try {
            console.log('Loading bulk data with dropdown state:', {
                categoriesCount: state.categories.length,
                locationsCount: state.locations.length,
                page: page,
                append: append
            });

            // Build query parameters - only add filters if they exist and are not empty
            const params = new URLSearchParams({
                page: page,
                per_page: 50
            });

            // Only add filters if currentFilters is defined and has values
            if (typeof currentFilters !== 'undefined') {
                if (currentFilters.search) params.append('search', currentFilters.search);
                if (currentFilters.provider) params.append('provider', currentFilters.provider);
                if (currentFilters.status) params.append('status', currentFilters.status);
                if (currentFilters.scheduleType) params.append('schedule_type', currentFilters.scheduleType);
            }

            const url = `${API_BASE}/bulk-data?${params}`;

            const response = await fetch(url, {
                headers: {
                    'X-CSRF-TOKEN': CSRF_TOKEN,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();

            if (data.success) {
                if (append && page > 1) {
                    // Append new rules to existing ones
                    state.allRules = [...state.allRules, ...(data.rules || [])];
                } else {
                    // Replace all rules
                    state.allRules = data.rules || [];
                }

                // Store pagination info
                state.pagination = data.pagination || {};

                updateStats(data.stats || {});
                renderRules(state.allRules, append);

                // Add pagination controls if needed
                if (data.pagination && data.pagination.last_page > 1) {
                    renderPaginationControls(data.pagination);
                }

                console.log(`Loaded ${data.rules?.length || 0} rules (page ${page}) successfully with dropdown data available:`, {
                    categoriesCount: state.categories.length,
                    locationsCount: state.locations.length,
                    totalRules: state.allRules.length,
                    pagination: data.pagination
                });
            } else {
                throw new Error(data.message || 'Unknown server error');
            }
        } catch (error) {
            console.error('Error loading bulk data:', error);

            // Retry logic for network errors
            if (retryCount < 2 && (error.name === 'TypeError' || error.message.includes('fetch'))) {
                console.log(`Retrying... (${retryCount + 1}/2)`);
                setTimeout(() => loadBulkData(retryCount + 1), 1000 * (retryCount + 1));
                return;
            }

            // Show error state
            container.innerHTML = `
                <div class="text-center p-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5 class="text-muted">Failed to Load Rules</h5>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-primary" onclick="loadBulkData()">
                        <i class="fas fa-refresh me-2"></i>Try Again
                    </button>
                </div>
            `;
            showError('Failed to load rules: ' + error.message);
        } finally {
            state.isLoading = false;
        }
    }
    
    // Update statistics display with animation
    function updateStats(stats) {
        const elements = {
            totalRules: stats.total_rules || 0,
            jobsAfRules: stats.jobsaf_rules || 0,
            acbarRules: stats.acbar_rules || 0,
            activeRules: stats.active_rules || 0
        };

        // Animate number changes
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                const currentValue = parseInt(element.textContent) || 0;
                if (currentValue !== value) {
                    animateNumber(element, currentValue, value);
                }
            }
        });

        // Update clone modal counts
        const fromCount = document.getElementById('fromProviderCount');
        const toCount = document.getElementById('toProviderCount');
        if (fromCount) fromCount.textContent = `${elements.jobsAfRules} rules`;
        if (toCount) toCount.textContent = `${elements.acbarRules} rules`;
    }

    function animateNumber(element, from, to) {
        const duration = 500;
        const steps = 20;
        const stepValue = (to - from) / steps;
        let current = from;
        let step = 0;

        const timer = setInterval(() => {
            step++;
            current += stepValue;
            element.textContent = Math.round(current);

            if (step >= steps) {
                clearInterval(timer);
                element.textContent = to;
            }
        }, duration / steps);
    }
    
    // Render rules in chess board view with performance optimization
    function renderRules(rules, append = false) {
        const container = document.getElementById('rulesContainer');

        if (!container) {
            console.error('rulesContainer not found!');
            return;
        }

        if (!rules || rules.length === 0) {
            container.innerHTML = `
                <div class="text-center p-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Rules Found</h5>
                    <p class="text-muted">Create your first command schedule rule to get started.</p>
                    <a href="{{ route('admin.jobseeker.command_schedule.index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Rule
                    </a>
                </div>
            `;
            return;
        }

        // Set initialization flag to prevent saves during card creation
        state.isInitializing = true;

        // Use DocumentFragment for better performance
        const fragment = document.createDocumentFragment();

        rules.forEach((rule, index) => {
            try {
                const ruleCard = createRuleCard(rule);
                fragment.appendChild(ruleCard);
            } catch (error) {
                console.error(`Error creating card for rule ${rule.id}:`, error);
            }
        });

        // Clear and append all at once
        if (!append) {
            container.innerHTML = '';
        }
        container.appendChild(fragment);

        // Clear initialization flag after a short delay to allow dropdowns to initialize
        setTimeout(() => {
            state.isInitializing = false;
            console.log(`Rendered ${rules.length} rule cards - initialization complete`);
        }, 1000);
    }
    
    // Create individual rule card with enhanced features
    function createRuleCard(rule) {
        if (!rule || !rule.id) {
            console.error('Invalid rule data:', rule);
            throw new Error('Invalid rule data provided to createRuleCard');
        }

        const card = document.createElement('div');
        card.className = 'rule-card filtered-in';
        card.dataset.provider = rule.provider || 'unknown';
        card.dataset.ruleId = rule.id;
        card.id = `rule-card-${rule.id}`;
        card.setAttribute('data-testid', `rule-card-${rule.id}`);
        card.setAttribute('aria-label', `Command schedule rule: ${rule.name || rule.display_name}`);
        card.setAttribute('role', 'article');

        // Determine provider display name and icon
        const providerInfo = {
            jobsaf: { name: 'Jobs.af', icon: 'fas fa-briefcase' },
            acbar: { name: 'ACBAR', icon: 'fas fa-building' },
            unknown: { name: 'Unknown', icon: 'fas fa-question' }
        };

        const provider = providerInfo[rule.provider] || providerInfo.unknown;

        // Generate differentiators and human-readable schedule
        const differentiators = generateRuleDifferentiators(rule);
        const humanReadableSchedule = cronToHuman(rule.schedule_expression);

        // Determine time status styling
        const timeStatusClass = rule.time_status || 'future';
        const timeStatusIcon = {
            'imminent': 'fas fa-exclamation-triangle text-warning',
            'today': 'fas fa-clock text-success',
            'tomorrow': 'fas fa-calendar-day text-info',
            'future': 'fas fa-calendar text-muted'
        }[timeStatusClass] || 'fas fa-calendar text-muted';

        card.innerHTML = `
            <div class="saving-indicator" id="saving-${rule.id}">
                <i class="fas fa-spinner fa-spin me-1"></i>Saving...
            </div>
            <div class="provider-badge provider-${rule.provider} rule-provider">
                <i class="${provider.icon} me-1"></i>${provider.name}
            </div>

            <!-- NEXT RUN INFORMATION - MOVED TO TOP -->
            <div class="next-run-banner time-status-${timeStatusClass}">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="next-run-info">
                        <i class="${timeStatusIcon} me-2"></i>
                        <strong>Next Run:</strong>
                        <span class="next-run-display ms-1">${rule.next_run_human || 'Not scheduled'}</span>
                        ${rule.next_run_relative ? `
                            <small class="text-muted ms-2">(${rule.next_run_relative})</small>
                        ` : ''}
                    </div>
                    ${rule.next_run ? `
                        <span class="badge bg-${timeStatusClass === 'imminent' ? 'warning' : timeStatusClass === 'today' ? 'success' : timeStatusClass === 'tomorrow' ? 'info' : 'secondary'} time-badge">
                            ${getTimeUntilNext(rule.next_run)}
                        </span>
                    ` : ''}
                </div>
            </div>

            <!-- Enhanced Card Header -->
            <div class="rule-card-header">
                <div class="rule-title-section">
                    <h3 class="rule-name-display">${escapeHtml(rule.display_name || rule.name || 'Unnamed Rule')}</h3>
                    <div class="d-flex align-items-center gap-2">
                        <span class="rule-status-badge ${rule.is_active ? 'active' : 'inactive'}">
                            <i class="fas fa-${rule.is_active ? 'play' : 'pause'} me-1"></i>
                            ${rule.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <div class="rule-meta-info">
                        <div class="rule-meta-item">
                            <i class="fas fa-hashtag"></i>
                            <span>ID: ${rule.id}</span>
                        </div>
                        <div class="rule-meta-item">
                            <i class="fas fa-sort-numeric-up"></i>
                            <span>Priority: ${rule.priority || 100}</span>
                        </div>
                        <div class="rule-meta-item">
                            <i class="fas fa-code"></i>
                            <span>Sort Key: ${rule.schedule_sort_key || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule Display -->
            <div class="schedule-display">
                <div class="schedule-display-header">
                    <i class="fas fa-calendar-alt text-primary"></i>
                    <h5 class="schedule-display-title">Schedule Configuration</h5>
                </div>
                <div class="schedule-expression">${rule.schedule_expression || 'Not configured'}</div>
                ${humanReadableSchedule ? `
                    <div class="schedule-human-readable">
                        <i class="fas fa-info-circle"></i>
                        <span>${humanReadableSchedule}</span>
                    </div>
                ` : ''}
            </div>

            <!-- Key Differentiators -->
            ${differentiators.length > 0 ? `
                <div class="rule-differentiators">
                    <div class="differentiators-title">
                        <i class="fas fa-star"></i>
                        <span>Key Features</span>
                    </div>
                    <div class="differentiator-tags">
                        ${differentiators.map(diff => `<span class="differentiator-tag">${diff}</span>`).join('')}
                    </div>
                </div>
            ` : ''}

            <!-- Editable Rule Name -->
            <div class="mb-3">
                <label class="form-label fw-bold">
                    <i class="fas fa-edit me-1"></i>Edit Rule Name
                </label>
                <input type="text"
                       class="form-control inline-edit"
                       value="${escapeHtml(rule.name || '')}"
                       data-field="name"
                       data-rule-id="${rule.id}"
                       placeholder="Enter rule name..."
                       maxlength="255">
                <div class="error-message" style="display:none;"></div>
            </div>

            <!-- Schedule Configuration -->
            <div class="row mb-3">
                <div class="col-md-6 mb-2">
                    <label class="form-label fw-bold">
                        <i class="fas fa-calendar-alt me-1"></i>Schedule Type
                    </label>
                    <select class="form-select inline-edit schedule-type-select"
                            data-field="schedule_type"
                            data-rule-id="${rule.id}">
                        <option value="cron" ${rule.schedule_type === 'cron' ? 'selected' : ''}>Cron Expression</option>
                        <option value="daily_at" ${rule.schedule_type === 'daily_at' ? 'selected' : ''}>Daily At Time</option>
                        <option value="weekly_at" ${rule.schedule_type === 'weekly_at' ? 'selected' : ''}>Weekly At Time</option>
                        <option value="custom" ${rule.schedule_type === 'custom' ? 'selected' : ''}>Custom/Dependent</option>
                    </select>
                    <div class="error-message" style="display:none;"></div>
                </div>
                <div class="col-md-6 mb-2">
                    <label class="form-label fw-bold">
                        <i class="fas fa-globe me-1"></i>Timezone
                    </label>
                    <select class="form-select inline-edit"
                            data-field="timezone"
                            data-rule-id="${rule.id}">
                        <option value="Asia/Kabul" ${rule.timezone === 'Asia/Kabul' ? 'selected' : ''}>Asia/Kabul (Afghanistan Time)</option>
                        <option value="UTC" ${rule.timezone === 'UTC' ? 'selected' : ''}>UTC (Coordinated Universal Time)</option>
                        <option value="Asia/Dubai" ${rule.timezone === 'Asia/Dubai' ? 'selected' : ''}>Asia/Dubai (Gulf Standard Time)</option>
                        <option value="Europe/London" ${rule.timezone === 'Europe/London' ? 'selected' : ''}>Europe/London (GMT/BST)</option>
                        <option value="America/New_York" ${rule.timezone === 'America/New_York' ? 'selected' : ''}>America/New_York (EST/EDT)</option>
                    </select>
                    <div class="error-message" style="display:none;"></div>
                </div>
            </div>

            <!-- Conditional Schedule Fields -->
            <div class="conditional-schedule-fields mb-3">
                <!-- Days of Week Group (for weekly_at) -->
                <div class="conditional-group" id="days-of-week-group-${rule.id}" style="display: none;">
                    <label class="form-label fw-bold">
                        <i class="fas fa-calendar-week me-1"></i>Day of Week
                    </label>
                    <select class="form-select inline-edit days-of-week-select"
                            data-field="day_of_week"
                            data-rule-id="${rule.id}">
                        <option value="">Select Day</option>
                        <option value="0">Sunday</option>
                        <option value="1">Monday</option>
                        <option value="2">Tuesday</option>
                        <option value="3">Wednesday</option>
                        <option value="4">Thursday</option>
                        <option value="5">Friday</option>
                        <option value="6">Saturday</option>
                    </select>
                    <div class="error-message" style="display:none;"></div>
                </div>

                <!-- Time Group (for daily_at and weekly_at) -->
                <div class="conditional-group" id="time-group-${rule.id}" style="display: none;">
                    <label class="form-label fw-bold">
                        <i class="fas fa-clock me-1"></i>Time (HH:MM)
                    </label>
                    <input type="time"
                           class="form-control inline-edit time-input"
                           value="${rule.display_time || ''}"
                           data-field="schedule_time"
                           data-rule-id="${rule.id}">
                    <div class="error-message" style="display:none;"></div>
                </div>

                <!-- Cron Expression Group (for cron and custom) -->
                <div class="conditional-group" id="expression-group-${rule.id}" style="display: none;">
                    <label class="form-label fw-bold">
                        <i class="fas fa-code me-1"></i>Cron Expression
                    </label>
                    <input type="text"
                           class="form-control inline-edit expression-input"
                           value="${rule.schedule_expression || ''}"
                           data-field="schedule_expression"
                           data-rule-id="${rule.id}"
                           placeholder="0 7 * * 6">
                    <div class="form-text cron-human-readable text-muted mt-1"></div>
                    <small class="text-muted">Enter a 5-part cron expression (minute hour day month weekday)</small>
                    <div class="error-message" style="display:none;"></div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6 mb-2">
                    <label class="form-label fw-bold">
                        <i class="fas fa-sort-numeric-up me-1"></i>Priority
                    </label>
                    <input type="number"
                           class="form-control inline-edit"
                           value="${rule.priority || 100}"
                           min="1"
                           max="1000"
                           data-field="priority"
                           data-rule-id="${rule.id}">
                </div>
                <div class="col-md-6 mb-2">
                    <!-- Empty column for layout balance -->
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">
                    <i class="fas fa-toggle-on me-1"></i>Status
                </label>
                <select class="form-select inline-edit"
                        data-field="is_active"
                        data-rule-id="${rule.id}">
                    <option value="1" ${rule.is_active ? 'selected' : ''}>
                        <i class="fas fa-check-circle"></i> Active
                    </option>
                    <option value="0" ${!rule.is_active ? 'selected' : ''}>
                        <i class="fas fa-pause-circle"></i> Disabled
                    </option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label fw-bold">
                    <i class="fas fa-file-alt me-1"></i>Description
                </label>
                <textarea class="form-control inline-edit rule-description"
                          rows="2"
                          data-field="description"
                          data-rule-id="${rule.id}"
                          placeholder="Enter rule description..."
                          maxlength="500">${escapeHtml(rule.description || '')}</textarea>
                <div class="error-message" style="display:none;"></div>
            </div>

            <!-- Filter Configuration Section -->
            <div class="filter-config-section">
                <div class="filter-config-title">
                    <i class="fas fa-filter"></i>
                    <span>Filter Configuration</span>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6 mb-2">
                        <label class="form-label fw-bold">
                            <i class="fas fa-tags me-1"></i>Categories
                        </label>
                        <div class="category-management-container">
                            <button type="button"
                                    class="btn btn-outline-primary btn-category-manager w-100"
                                    data-rule-id="${rule.id}"
                                    data-provider="${rule.provider}"
                                    data-current-categories='${JSON.stringify(rule.categories || [])}'
                                    title="Click to manage categories">
                                <i class="fas fa-edit me-2"></i>
                                <span class="category-count-display">
                                    ${rule.categories && rule.categories.length > 0
                                        ? `${rule.categories.length} categories selected`
                                        : 'No categories selected'}
                                </span>
                                <i class="fas fa-chevron-right ms-2"></i>
                            </button>
                            <div class="category-preview mt-2" style="min-height: 24px;">
                                ${rule.categories && rule.categories.length > 0
                                    ? rule.categories.slice(0, 3).map(catId => {
                                        const category = state.categories.find(c => c.id === catId);
                                        return category ? `<span class="badge bg-primary me-1">${category.text}</span>` : '';
                                    }).join('') + (rule.categories.length > 3 ? `<span class="badge bg-secondary">+${rule.categories.length - 3} more</span>` : '')
                                    : '<small class="text-muted">Click button above to select categories</small>'}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label fw-bold">
                            <i class="fas fa-map-marker-alt me-1"></i>Locations
                        </label>
                        <div class="location-management-container">
                            <button type="button"
                                    class="btn btn-outline-success btn-location-manager w-100"
                                    data-rule-id="${rule.id}"
                                    data-provider="${rule.provider}"
                                    data-current-locations='${JSON.stringify(rule.locations || [])}'
                                    title="Click to manage locations">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <span class="location-count-display">
                                    ${rule.locations && rule.locations.length > 0
                                        ? `${rule.locations.length} locations selected`
                                        : 'No locations selected'}
                                </span>
                                <i class="fas fa-chevron-right ms-2"></i>
                            </button>
                            <div class="location-preview mt-2" style="min-height: 24px;">
                                ${rule.locations && rule.locations.length > 0
                                    ? rule.locations.slice(0, 3).map(locId => {
                                        const location = state.locations.find(l => l.id === locId);
                                        return location ? `<span class="badge bg-success me-1">${location.text}</span>` : '';
                                    }).join('') + (rule.locations.length > 3 ? `<span class="badge bg-secondary">+${rule.locations.length - 3} more</span>` : '')
                                    : '<small class="text-muted">Click button above to select locations</small>'}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6 mb-2">
                        <label class="form-label fw-bold">
                            <i class="fas fa-building me-1"></i>Companies
                        </label>
                        <textarea class="form-control inline-edit filter-field"
                                  rows="2"
                                  data-field="companies"
                                  data-rule-id="${rule.id}"
                                  placeholder="Enter companies (comma-separated)..."
                                  title="Enter company names separated by commas">${formatArrayField(rule.companies)}</textarea>
                        <small class="text-muted">Comma-separated company names</small>
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label fw-bold">
                            <i class="fas fa-graduation-cap me-1"></i>Experience Levels
                        </label>
                        <textarea class="form-control inline-edit filter-field"
                                  rows="2"
                                  data-field="experience_levels"
                                  data-rule-id="${rule.id}"
                                  placeholder="Enter experience levels (comma-separated)..."
                                  title="Enter experience levels separated by commas">${formatArrayField(rule.experience_levels)}</textarea>
                        <small class="text-muted">e.g., entry, mid, senior</small>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6 mb-2">
                        <label class="form-label fw-bold">
                            <i class="fas fa-search me-1"></i>Search Term
                        </label>
                        <input type="text"
                               class="form-control inline-edit"
                               value="${escapeHtml(rule.search_term || '')}"
                               data-field="search_term"
                               data-rule-id="${rule.id}"
                               placeholder="Enter search keywords..."
                               maxlength="255">
                        <small class="text-muted">Keywords to search for in job titles/descriptions</small>
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label fw-bold">
                            <i class="fas fa-briefcase me-1"></i>Work Type
                        </label>
                        <select class="form-select inline-edit"
                                data-field="work_type"
                                data-rule-id="${rule.id}">
                            <option value="" ${!rule.work_type ? 'selected' : ''}>All Types</option>
                            <option value="full-time" ${rule.work_type === 'full-time' ? 'selected' : ''}>Full-time</option>
                            <option value="part-time" ${rule.work_type === 'part-time' ? 'selected' : ''}>Part-time</option>
                            <option value="contract" ${rule.work_type === 'contract' ? 'selected' : ''}>Contract</option>
                            <option value="freelance" ${rule.work_type === 'freelance' ? 'selected' : ''}>Freelance</option>
                            <option value="internship" ${rule.work_type === 'internship' ? 'selected' : ''}>Internship</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Execution Settings Section -->
            <div class="execution-settings-section">
                <div class="execution-settings-title">
                    <i class="fas fa-cogs"></i>
                    <span>Execution Settings</span>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label fw-bold">
                            <i class="fas fa-hourglass-half me-1"></i>Max Execution Time (seconds)
                        </label>
                        <input type="number"
                               class="form-control inline-edit"
                               value="${rule.max_execution_time || ''}"
                               min="60"
                               max="86400"
                               data-field="max_execution_time"
                               data-rule-id="${rule.id}"
                               placeholder="3600">
                        <small class="text-muted">Maximum time allowed for execution (60-86400 seconds)</small>
                        <div class="error-message" style="display:none;"></div>
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label fw-bold">
                            <i class="fas fa-layer-group me-1"></i>Concurrent Executions
                        </label>
                        <input type="number"
                               class="form-control inline-edit"
                               value="${rule.concurrent_executions || 1}"
                               min="1"
                               max="10"
                               data-field="concurrent_executions"
                               data-rule-id="${rule.id}">
                        <small class="text-muted">Number of simultaneous executions allowed</small>
                        <div class="error-message" style="display:none;"></div>
                    </div>
                </div>
            </div>

            <div class="border-top pt-3 mt-3">
                <div class="row text-muted small">
                    ${rule.last_execution ? `
                        <div class="col-12">
                            <i class="fas fa-history text-info me-1"></i>
                            <strong>Last Run:</strong> ${formatDateTime(rule.last_execution.started_at)}
                            <span class="badge bg-${getStatusBadgeClass(rule.last_execution.status)} ms-2">
                                ${rule.last_execution.status}
                            </span>
                        </div>
                    ` : `
                        <div class="col-12 text-center text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            <em>No execution history available</em>
                        </div>
                    `}
                </div>
            </div>
        `;

        // Add event listeners for inline editing
        setupCardEventListeners(card);

        // Initialize dropdowns for this card
        initializeCardDropdowns(card, rule);

        // Initialize schedule type conditional fields
        initializeScheduleFields(card, rule);

        // Add cron expression input handler
        setupCronExpressionHandler(card);

        return card;
    }

    // Setup event listeners for card inline editing
    function setupCardEventListeners(card) {
        const inputs = card.querySelectorAll('.inline-edit');

        inputs.forEach(input => {
            // Debounced save on change
            let saveTimeout;
            input.addEventListener('change', function() {
                console.log('Change event triggered:', {
                    field: this.dataset.field,
                    ruleId: this.dataset.ruleId,
                    value: this.value,
                    isInitializing: state.isInitializing,
                    isRerendering: state.isRerendering
                });

                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    const ruleId = parseInt(this.dataset.ruleId);
                    const field = this.dataset.field;
                    const value = this.value;

                    if (ruleId && field && value !== undefined) {
                        // Update visual displays based on field changes
                        if (field === 'schedule_expression') {
                            updateCronDisplay(ruleId, value);
                        } else if (field === 'name') {
                            // Update the display name in the header
                            const card = this.closest('.rule-card');
                            const nameDisplay = card.querySelector('.rule-name-display');
                            if (nameDisplay) {
                                nameDisplay.textContent = value || 'Unnamed Rule';
                            }
                        } else if (field === 'is_active') {
                            // Update the status badge
                            const card = this.closest('.rule-card');
                            const statusBadge = card.querySelector('.rule-status-badge');
                            if (statusBadge) {
                                const isActive = value === '1' || value === true;
                                statusBadge.className = `rule-status-badge ${isActive ? 'active' : 'inactive'}`;
                                statusBadge.innerHTML = `
                                    <i class="fas fa-${isActive ? 'play' : 'pause'} me-1"></i>
                                    ${isActive ? 'Active' : 'Inactive'}
                                `;
                            }
                        } else if (field === 'priority') {
                            // Update priority display in meta info
                            const card = this.closest('.rule-card');
                            const priorityDisplay = card.querySelector('.rule-meta-info .rule-meta-item:nth-child(2) span');
                            if (priorityDisplay) {
                                priorityDisplay.textContent = `Priority: ${value || 100}`;
                            }
                        }

                        console.log('Attempting to save field:', { ruleId, field, value });
                        saveField(ruleId, field, value);
                    }
                }, 300);
            });

            // Validation on blur
            input.addEventListener('blur', function() {
                validateField(this);
            });

            // Clear errors on focus
            input.addEventListener('focus', function() {
                clearFieldError(this);
            });
        });
    }

    // Initialize Select2 dropdowns for a card
    function initializeCardDropdowns(card, rule) {
        const ruleId = rule.id;
        const ruleProvider = rule.provider || 'unknown';



        // Filter categories and locations by provider
        const providerCategories = state.categories.filter(cat => cat.provider === ruleProvider);
        const providerLocations = state.locations.filter(loc => loc.provider === ruleProvider);



        // Initialize categories dropdown
        const categoriesSelect = card.querySelector('.categories-dropdown');
        if (categoriesSelect) {
            if (providerCategories.length > 0) {
                $(categoriesSelect).select2({
                    theme: 'bootstrap-5',
                    placeholder: 'Select categories...',
                    allowClear: true,
                    data: providerCategories,
                    width: '100%'
                });

                // Set selected values
                if (rule.categories && Array.isArray(rule.categories) && rule.categories.length > 0) {
                    const selectedCategories = rule.categories.map(String);

                    // Use setTimeout to ensure Select2 is fully initialized
                    setTimeout(() => {
                        $(categoriesSelect).val(selectedCategories).trigger('change');
                    }, 100);
                }

                // Handle change events
                $(categoriesSelect).on('change', function() {
                    const selectedValues = $(this).val() || [];
                    saveField(ruleId, 'categories', selectedValues);
                });
            } else {
                // Hide the dropdown and show a message
                categoriesSelect.style.display = 'none';
                const message = document.createElement('div');
                message.className = 'text-muted small';
                message.textContent = 'Categories loading...';
                categoriesSelect.parentNode.appendChild(message);
            }
        }

        // Initialize locations dropdown
        const locationsSelect = card.querySelector('.locations-dropdown');
        if (locationsSelect) {
            if (providerLocations.length > 0) {
                $(locationsSelect).select2({
                    theme: 'bootstrap-5',
                    placeholder: 'Select locations...',
                    allowClear: true,
                    data: providerLocations,
                    width: '100%'
                });

                // Set selected values
                if (rule.locations && Array.isArray(rule.locations) && rule.locations.length > 0) {
                    const selectedLocations = rule.locations.map(String);
                    console.log(`Setting locations for rule ${ruleId}:`, selectedLocations);

                    // Use setTimeout to ensure Select2 is fully initialized
                    setTimeout(() => {
                        $(locationsSelect).val(selectedLocations).trigger('change');
                    }, 100);
                }

                // Handle change events
                $(locationsSelect).on('change', function() {
                    const selectedValues = $(this).val() || [];
                    saveField(ruleId, 'locations', selectedValues);
                });
            } else {
                console.warn(`No locations available for rule ${ruleId} provider ${ruleProvider}`);
                // Hide the dropdown and show a message
                locationsSelect.style.display = 'none';
                const message = document.createElement('div');
                message.className = 'text-muted small';
                message.textContent = `No locations available for ${ruleProvider}`;
                locationsSelect.parentNode.appendChild(message);
            }
        }
    }

    // Initialize schedule type conditional fields for a card
    function initializeScheduleFields(card, rule) {
        const ruleId = rule.id;
        const scheduleTypeSelect = card.querySelector('.schedule-type-select');

        if (scheduleTypeSelect) {
            // Set up change handler for schedule type
            $(scheduleTypeSelect).on('change', function() {
                const scheduleType = $(this).val();
                updateScheduleFieldsVisibility(card, scheduleType, rule);
                saveField(ruleId, 'schedule_type', scheduleType);
            });

            // Initialize visibility based on current schedule type
            const currentScheduleType = rule.schedule_type || 'cron';
            updateScheduleFieldsVisibility(card, currentScheduleType, rule);

            // Ensure time value is set after visibility update
            if (rule.display_time && (currentScheduleType === 'daily_at' || currentScheduleType === 'weekly_at')) {
                setTimeout(() => {
                    const timeInput = card.querySelector('.time-input');
                    if (timeInput) {
                        timeInput.value = rule.display_time;
                    }
                }, 100);
            }
        }
    }

    // Update visibility of conditional schedule fields
    function updateScheduleFieldsVisibility(card, scheduleType, rule) {
        const ruleId = rule.id;

        // Hide all conditional groups first
        card.querySelectorAll('.conditional-group').forEach(group => {
            group.style.display = 'none';
        });

        // Show appropriate fields based on schedule type
        switch (scheduleType) {
            case 'daily_at':
                const timeGroup = card.querySelector(`#time-group-${ruleId}`);
                if (timeGroup) {
                    timeGroup.style.display = 'block';
                }
                break;

            case 'weekly_at':
                const daysGroup = card.querySelector(`#days-of-week-group-${ruleId}`);
                const timeGroupWeekly = card.querySelector(`#time-group-${ruleId}`);

                if (daysGroup) {
                    daysGroup.style.display = 'block';
                    // Set the day of week from the cron expression
                    setDayOfWeekFromCron(card, rule.schedule_expression);
                }
                if (timeGroupWeekly) {
                    timeGroupWeekly.style.display = 'block';
                    // Set the time value if not already set
                    const timeInput = timeGroupWeekly.querySelector('.time-input');
                    if (timeInput && rule.display_time && !timeInput.value) {
                        timeInput.value = rule.display_time;
                    }
                }
                break;

            case 'cron':
            case 'custom':
                const expressionGroup = card.querySelector(`#expression-group-${ruleId}`);
                if (expressionGroup) {
                    expressionGroup.style.display = 'block';
                    // Update human-readable cron description
                    updateCronDescription(card, rule.schedule_expression);
                }
                break;
        }
    }

    // Extract and set day of week from cron expression
    function setDayOfWeekFromCron(card, cronExpression) {
        if (!cronExpression) return;

        const parts = cronExpression.split(' ');
        if (parts.length >= 5) {
            const dayOfWeek = parts[4]; // Last part is day of week
            const daySelect = card.querySelector('.days-of-week-select');
            if (daySelect && dayOfWeek !== '*') {
                daySelect.value = dayOfWeek;
            }
        }
    }

    // Update human-readable cron description
    function updateCronDescription(card, cronExpression) {
        const cronDescElement = card.querySelector('.cron-human-readable');
        if (!cronDescElement || !cronExpression) return;

        try {
            // Basic cron description (you can enhance this with a proper cron parser library)
            const parts = cronExpression.split(' ');
            if (parts.length === 5) {
                const [minute, hour, day, month, dayOfWeek] = parts;
                let description = 'Runs ';

                if (dayOfWeek !== '*') {
                    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                    description += `every ${days[parseInt(dayOfWeek)] || dayOfWeek} `;
                } else if (day !== '*') {
                    description += `on day ${day} of every month `;
                } else {
                    description += 'daily ';
                }

                if (hour !== '*' && minute !== '*') {
                    const hourStr = hour.padStart(2, '0');
                    const minuteStr = minute.padStart(2, '0');
                    description += `at ${hourStr}:${minuteStr}`;
                }

                cronDescElement.textContent = description;
            }
        } catch (error) {
            cronDescElement.textContent = 'Invalid cron expression';
        }
    }

    // Setup cron expression input handler
    function setupCronExpressionHandler(card) {
        const cronInput = card.querySelector('.expression-input');
        if (cronInput) {
            cronInput.addEventListener('input', function() {
                const cronExpression = this.value;
                updateCronDescription(card, cronExpression);
            });
        }
    }

    // Enhanced save field with queue management, undo history, and confirmations
    async function saveField(ruleId, field, value, skipConfirmation = false) {
        const saveKey = `${ruleId}-${field}`;

        // Prevent saves during initialization or re-rendering
        if (state.isInitializing || state.isRerendering) {
            console.log('Skipping save during initialization/re-rendering:', {
                ruleId,
                field,
                value,
                isInitializing: state.isInitializing,
                isRerendering: state.isRerendering
            });
            return;
        }

        // Prevent duplicate saves
        if (state.saveQueue.has(saveKey)) {
            console.log('Skipping duplicate save:', { ruleId, field, value });
            return;
        }

        // Get current rule data for undo history
        const currentRule = state.allRules.find(r => r.id === ruleId);
        if (!currentRule) {
            showError('Rule not found');
            return;
        }

        const oldValue = currentRule[field];

        // Check if confirmation is needed for critical changes
        const criticalFields = ['is_active', 'schedule_expression', 'schedule_type'];
        if (!skipConfirmation && criticalFields.includes(field)) {
            const confirmed = await showConfirmationDialog(
                'Confirm Change',
                `Are you sure you want to change ${field} from "${oldValue}" to "${value}"?`,
                'This change will affect when the job runs.'
            );

            if (!confirmed) {
                // Revert the input value
                const input = document.querySelector(`[data-rule-id="${ruleId}"][data-field="${field}"]`);
                if (input) {
                    input.value = oldValue;
                }
                return;
            }
        }

        state.saveQueue.set(saveKey, true);
        const indicator = document.getElementById(`saving-${ruleId}`);

        try {
            if (indicator) {
                indicator.classList.add('show');
            }

            const response = await fetch(`${API_BASE}/update-field`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': CSRF_TOKEN,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    rule_id: ruleId,
                    field: field,
                    value: value
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                // Add to undo history
                addToUndoHistory({
                    ruleId: ruleId,
                    ruleName: currentRule.name || `Rule ${ruleId}`,
                    field: field,
                    oldValue: oldValue,
                    newValue: value,
                    action: 'field_update'
                });

                // Update local rule data
                const rule = state.allRules.find(r => r.id === ruleId);
                if (rule) {
                    rule[field] = value;
                    if (data.rule && data.rule.next_run) {
                        rule.next_run_human = data.rule.next_run;
                        // Update next run display
                        updateNextRunDisplay(ruleId, data.rule.next_run);
                    }
                }

                // Show brief success feedback
                if (indicator) {
                    indicator.innerHTML = '<i class="fas fa-check me-1"></i>Saved';
                    indicator.style.background = 'var(--success-color)';
                    setTimeout(() => {
                        indicator.style.background = 'var(--warning-color)';
                        indicator.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
                    }, 1000);
                }

                console.log(`Saved ${field} for rule ${ruleId}`);
            } else {
                throw new Error(data.message || 'Save failed');
            }
        } catch (error) {
            console.error(`Error saving field ${field} for rule ${ruleId}:`, error);
            showError(`Failed to save ${field}: ${error.message}`);

            // Show error state
            if (indicator) {
                indicator.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Error';
                indicator.style.background = 'var(--danger-color)';
            }
        } finally {
            state.saveQueue.delete(saveKey);
            if (indicator) {
                setTimeout(() => {
                    indicator.classList.remove('show');
                }, 2000);
            }
        }
    }

    // Helper functions
    function updateNextRunDisplay(ruleId, nextRun) {
        const card = document.querySelector(`[data-rule-id="${ruleId}"]`);
        if (card) {
            const nextRunElement = card.querySelector('.next-run-display');
            if (nextRunElement) {
                nextRunElement.textContent = nextRun;
            }
        }
    }

    function formatDateTime(dateString) {
        if (!dateString) return 'Never';
        try {
            return new Date(dateString).toLocaleString();
        } catch {
            return dateString;
        }
    }

    function getStatusBadgeClass(status) {
        const statusMap = {
            'completed': 'success',
            'failed': 'danger',
            'running': 'primary',
            'pending': 'warning'
        };
        return statusMap[status] || 'secondary';
    }

    function getTimeUntilNext(nextRunString) {
        if (!nextRunString) return '';

        try {
            const nextRun = new Date(nextRunString);
            const now = new Date();
            const diffMs = nextRun.getTime() - now.getTime();

            if (diffMs <= 0) {
                return 'Overdue';
            }

            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMinutes / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffDays > 0) {
                return `${diffDays}d ${diffHours % 24}h`;
            } else if (diffHours > 0) {
                return `${diffHours}h ${diffMinutes % 60}m`;
            } else {
                return `${diffMinutes}m`;
            }
        } catch (error) {
            console.error('Error calculating time until next run:', error);
            return '';
        }
    }

    function escapeHtml(text) {
        if (!text) return '';
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.toString().replace(/[&<>"']/g, m => map[m]);
    }

    // Convert cron expression to human-readable format using cronstrue library
    function cronToHuman(cronExpression) {
        if (!cronExpression) return '';

        try {
            // Use the existing cronstrue library (same as in index.blade.php)
            return cronstrue.toString(cronExpression, {
                use24HourTimeFormat: true,
                verbose: true
            });
        } catch (error) {
            console.warn('Error translating cron expression:', error);
            return `Custom: ${cronExpression}`;
        }
    }

    // Generate rule differentiators based on rule properties
    function generateRuleDifferentiators(rule) {
        const differentiators = [];

        // High priority rules
        if (rule.priority && rule.priority < 50) {
            differentiators.push('High Priority');
        }

        // Frequent execution
        const cronParts = rule.schedule_expression ? rule.schedule_expression.split(' ') : [];
        if (cronParts.length === 5) {
            const [minute, hour] = cronParts;
            if (minute !== '*' && hour === '*') {
                differentiators.push('Hourly Execution');
            } else if (minute === '0' && hour !== '*') {
                differentiators.push('Top of Hour');
            }
        }

        // Has filters
        if (rule.categories && rule.categories.length > 0) {
            differentiators.push(`${rule.categories.length} Categories`);
        }

        if (rule.locations && rule.locations.length > 0) {
            differentiators.push(`${rule.locations.length} Locations`);
        }

        if (rule.search_term && rule.search_term.trim()) {
            differentiators.push('Keyword Filter');
        }

        // Schedule type specific
        if (rule.schedule_type === 'weekly_at') {
            differentiators.push('Weekly Schedule');
        } else if (rule.schedule_type === 'daily_at') {
            differentiators.push('Daily Schedule');
        } else if (rule.schedule_type === 'cron') {
            differentiators.push('Custom Cron');
        }

        // Execution settings
        if (rule.max_execution_time && rule.max_execution_time > 3600) {
            differentiators.push('Long Running');
        }

        if (rule.concurrent_executions && rule.concurrent_executions > 1) {
            differentiators.push('Concurrent');
        }

        return differentiators;
    }

    // Update cron human-readable display
    function updateCronDisplay(ruleId, cronExpression) {
        const card = document.querySelector(`[data-rule-id="${ruleId}"]`).closest('.rule-card');
        const humanReadableElement = card.querySelector('.schedule-human-readable span');
        const expressionElement = card.querySelector('.schedule-expression');

        if (expressionElement) {
            expressionElement.textContent = cronExpression || 'Not configured';
        }

        if (humanReadableElement) {
            const humanReadable = cronToHuman(cronExpression);
            humanReadableElement.textContent = humanReadable;

            // Show/hide the human readable section
            const humanReadableContainer = card.querySelector('.schedule-human-readable');
            if (humanReadableContainer) {
                humanReadableContainer.style.display = humanReadable ? 'flex' : 'none';
            }
        }
    }

    // Update rule differentiators
    function updateRuleDifferentiators(ruleId, updatedRule) {
        const card = document.querySelector(`[data-rule-id="${ruleId}"]`).closest('.rule-card');
        const differentiatorContainer = card.querySelector('.rule-differentiators');

        const newDifferentiators = generateRuleDifferentiators(updatedRule);

        if (newDifferentiators.length > 0) {
            if (!differentiatorContainer) {
                // Create new differentiators section
                const scheduleDisplay = card.querySelector('.schedule-display');
                const newSection = document.createElement('div');
                newSection.className = 'rule-differentiators';
                newSection.innerHTML = `
                    <div class="differentiators-title">
                        <i class="fas fa-star"></i>
                        <span>Key Features</span>
                    </div>
                    <div class="differentiator-tags">
                        ${newDifferentiators.map(diff => `<span class="differentiator-tag">${diff}</span>`).join('')}
                    </div>
                `;
                scheduleDisplay.insertAdjacentElement('afterend', newSection);
            } else {
                // Update existing differentiators
                const tagsContainer = differentiatorContainer.querySelector('.differentiator-tags');
                tagsContainer.innerHTML = newDifferentiators.map(diff => `<span class="differentiator-tag">${diff}</span>`).join('');
            }
        } else if (differentiatorContainer) {
            // Remove differentiators section if no differentiators
            differentiatorContainer.remove();
        }
    }

    // Format array fields for display (convert array to comma-separated string)
    function formatArrayField(arrayField) {
        if (!arrayField) return '';
        if (Array.isArray(arrayField)) {
            return arrayField.join(', ');
        }
        if (typeof arrayField === 'string') {
            return arrayField;
        }
        return '';
    }

    // Field validation with enhanced feedback
    function validateField(element) {
        const field = element.dataset.field;
        const value = element.value;
        const errorDiv = element.parentNode.querySelector('.error-message');

        let isValid = true;
        let errorMessage = '';

        // Field-specific validation
        switch (field) {
            case 'name':
                if (!value.trim()) {
                    isValid = false;
                    errorMessage = 'Rule name is required';
                } else if (value.length > 255) {
                    isValid = false;
                    errorMessage = 'Rule name must be less than 255 characters';
                }
                break;

            case 'priority':
                const priority = parseInt(value);
                if (isNaN(priority) || priority < 1 || priority > 1000) {
                    isValid = false;
                    errorMessage = 'Priority must be between 1 and 1000';
                }
                break;

            case 'schedule_time':
                if (!value.match(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid time (HH:MM)';
                }
                break;
        }

        // Update UI
        if (isValid) {
            element.classList.remove('field-error');
            if (errorDiv) {
                errorDiv.style.display = 'none';
                errorDiv.textContent = '';
            }
        } else {
            element.classList.add('field-error');
            if (errorDiv) {
                errorDiv.style.display = 'block';
                errorDiv.textContent = errorMessage;
            }
        }

        return isValid;
    }

    function clearFieldError(element) {
        element.classList.remove('field-error');
        const errorDiv = element.parentNode.querySelector('.error-message');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    // Provider filtering with smooth animations
    function filterProvider(provider) {
        const cards = document.querySelectorAll('.rule-card');
        let visibleCount = 0;

        cards.forEach((card, index) => {
            const shouldShow = provider === 'all' || card.dataset.provider === provider;

            if (shouldShow) {
                card.style.display = 'block';
                card.style.animationDelay = `${index * 50}ms`;
                card.classList.add('fade-in');
                visibleCount++;
            } else {
                card.style.display = 'none';
                card.classList.remove('fade-in');
            }
        });

        // Update filter button states
        document.querySelectorAll('[onclick*="filterProvider"]').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');

        console.log(`Filtered to show ${visibleCount} rules for provider: ${provider}`);
    }

    // Clone modal functions with enhanced UX
    function openCloneModal() {
        const modal = document.getElementById('cloneModal');
        if (modal) {
            modal.style.display = 'flex';
            modal.classList.add('fade-in');
            document.body.style.overflow = 'hidden'; // Prevent background scroll

            // Focus management
            const firstButton = modal.querySelector('button');
            if (firstButton) {
                setTimeout(() => firstButton.focus(), 100);
            }
        }
    }

    function closeCloneModal() {
        const modal = document.getElementById('cloneModal');
        if (modal) {
            modal.classList.remove('fade-in');
            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = ''; // Restore scroll
            }, 200);
        }
    }

    function selectOffset(minutes) {
        state.selectedOffset = minutes;
        document.querySelectorAll('.offset-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
        console.log(`Selected time offset: ${minutes} minutes`);
    }

    function selectFromProvider(provider) {
        state.fromProvider = provider;
        document.getElementById('fromProvider').classList.add('selected');
        document.getElementById('toProvider').classList.remove('selected');

        // Auto-select opposite provider for "to"
        const oppositeProvider = provider === 'jobsaf' ? 'acbar' : 'jobsaf';
        selectToProvider(oppositeProvider);
    }

    function selectToProvider(provider) {
        state.toProvider = provider;
        document.getElementById('toProvider').classList.add('selected');
        document.getElementById('fromProvider').classList.remove('selected');
    }

    // Execute clone with comprehensive error handling, confirmation, and progress
    async function executeClone() {
        if (state.fromProvider === state.toProvider) {
            showError('Please select different providers for cloning');
            return;
        }

        // Get source rules count for confirmation
        const sourceRules = state.allRules.filter(rule => rule.provider === state.fromProvider);
        const sourceCount = sourceRules.length;

        // Show confirmation dialog
        const confirmed = await showConfirmationDialog(
            'Confirm Clone Operation',
            `This will clone ${sourceCount} rules from ${state.fromProvider.toUpperCase()} to ${state.toProvider.toUpperCase()} with a ${state.selectedOffset} minute time offset.`,
            'This operation cannot be undone. Existing rules will not be affected.'
        );

        if (!confirmed) {
            return;
        }

        // Show progress indicator
        showProgressIndicator('Cloning Rules...', true);
        closeCloneModal();

        const btn = document.getElementById('cloneExecuteBtn');
        const originalText = btn.innerHTML;

        // Disable button and show loading
        btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Cloning...';
        btn.disabled = true;

        try {
            updateProgress(10, 'Preparing clone operation...');

            const response = await fetch(`${API_BASE}/clone-rules`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': CSRF_TOKEN,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    from_provider: state.fromProvider,
                    to_provider: state.toProvider,
                    time_offset_minutes: state.selectedOffset
                })
            });

            updateProgress(50, 'Processing clone request...');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            updateProgress(80, 'Finalizing clone operation...');
            const data = await response.json();

            if (data.success) {
                updateProgress(100, 'Clone completed successfully!');

                setTimeout(() => {
                    hideProgressIndicator();
                    showSuccess(`${data.message} (${data.cloned_count} rules cloned)`);
                }, 1000);

                // Reload data to show new rules
                await loadBulkData();

                // Show success feedback on clone button
                const cloneBtn = document.querySelector('.clone-btn');
                if (cloneBtn) {
                    const originalBg = cloneBtn.style.background;
                    cloneBtn.innerHTML = '<i class="fas fa-check"></i><span>Cloned Successfully!</span>';
                    cloneBtn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';

                    setTimeout(() => {
                        cloneBtn.innerHTML = '<i class="fas fa-magic"></i><span>Clone Superpower</span>';
                        cloneBtn.style.background = originalBg || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    }, 3000);
                }

                console.log(`Successfully cloned ${data.cloned_count} rules from ${state.fromProvider} to ${state.toProvider}`);
            } else {
                throw new Error(data.message || 'Clone operation failed');
            }
        } catch (error) {
            console.error('Clone operation failed:', error);
            hideProgressIndicator();
            showError(`Clone failed: ${error.message}`);
        } finally {
            // Restore button
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }

    // Enhanced notification system
    function showError(message) {
        console.error('Error:', message);

        // Try SweetAlert2 first, fallback to toast or alert
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true
            });
        } else {
            // Fallback to custom toast
            showToast(message, 'error');
        }
    }

    function showSuccess(message) {
        console.log('Success:', message);

        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: message,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        } else {
            showToast(message, 'success');
        }
    }

    function showToast(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    // Confirmation Dialog
    function showConfirmationDialog(title, message, details = '') {
        return new Promise((resolve) => {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: title,
                    text: message,
                    html: details ? `<p>${message}</p><small class="text-muted">${details}</small>` : message,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, proceed',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    resolve(result.isConfirmed);
                });
            } else {
                // Fallback to native confirm
                resolve(confirm(`${title}\n\n${message}\n\n${details}`));
            }
        });
    }

    // Progress Indicator Functions
    function showProgressIndicator(title = 'Processing...', cancellable = false) {
        const indicator = document.getElementById('progressIndicator');
        const titleElement = document.getElementById('progressTitle');
        const cancelButton = document.getElementById('cancelOperation');

        if (indicator && titleElement) {
            titleElement.textContent = title;
            indicator.style.display = 'block';

            if (cancelButton) {
                cancelButton.style.display = cancellable ? 'block' : 'none';
            }

            updateProgress(0, 'Initializing...');
        }
    }

    function hideProgressIndicator() {
        const indicator = document.getElementById('progressIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    function updateProgress(percent, status = '') {
        const progressBar = document.getElementById('progressBar');
        const progressStatus = document.getElementById('progressStatus');
        const progressPercent = document.getElementById('progressPercent');

        if (progressBar) {
            progressBar.style.width = `${percent}%`;
        }

        if (progressStatus && status) {
            progressStatus.textContent = status;
        }

        if (progressPercent) {
            progressPercent.textContent = `${Math.round(percent)}%`;
        }
    }

    // Utility Functions
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function formatTimeAgo(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        return `${diffDays}d ago`;
    }

    // Undo Action Functions
    function undoLastChangeAction() {
        if (undoHistory.length === 0) return;

        const lastChange = undoHistory[0];
        undoSpecificChange(lastChange.id);
    }

    function undoSpecificChange(changeId) {
        const change = undoHistory.find(c => c.id === changeId);
        if (!change) return;

        // Perform the undo by calling saveField with the old value
        saveField(change.ruleId, change.field, change.oldValue, true);

        // Remove from undo history
        const index = undoHistory.findIndex(c => c.id === changeId);
        if (index > -1) {
            undoHistory.splice(index, 1);
            updateUndoPanel();
        }

        showSuccess(`Undid change to ${change.field}`);
    }

    function clearUndoHistoryAction() {
        undoHistory.length = 0;
        updateUndoPanel();
        hideUndoPanel();
    }

    function hideUndoPanelAction() {
        hideUndoPanel();
    }

    // Global functions for inline event handlers
    window.loadBulkData = loadBulkData;
    // ===== SWEETALERT CATEGORY MANAGEMENT SYSTEM =====

    /**
     * Enhanced Category Management with SweetAlert
     * Provides a comprehensive multi-select interface with rich metadata
     */
    async function openCategoryManager(ruleId, provider, currentCategories = []) {
        try {
            console.log('🎯 Opening category manager', { ruleId, provider, currentCategories });

            // Show a subtle loading indicator in the button while fetching
            const button = document.querySelector(`[data-rule-id="${ruleId}"].btn-category-manager`);
            const originalText = button?.innerHTML;
            if (button) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                button.disabled = true;
            }

            // Fetch enhanced category data
            const response = await fetch(`{{ url('admin/jobseeker/command-schedule/provider-categories-sweetalert') }}/${provider}`, {
                headers: {
                    'X-CSRF-TOKEN': CSRF_TOKEN,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'Failed to load categories');
            }

            console.log('📊 Category data loaded', data.data);

            // Restore button state
            if (button && originalText) {
                button.innerHTML = originalText;
                button.disabled = false;
            }

            // Show the enhanced category selector
            await showCategorySelector(ruleId, data.data, currentCategories);

        } catch (error) {
            console.error('❌ Error loading categories:', error);

            // Restore button state on error
            if (button && originalText) {
                button.innerHTML = originalText;
                button.disabled = false;
            }

            Swal.fire({
                icon: 'error',
                title: 'Failed to Load Categories',
                text: error.message || 'An unexpected error occurred while loading categories.',
                confirmButtonText: 'Try Again',
                confirmButtonColor: '#dc3545'
            });
        }
    }

    /**
     * Display the comprehensive category selector interface
     */
    async function showCategorySelector(ruleId, categoryData, currentCategories = []) {
        const { categories, provider, provider_display, statistics } = categoryData;

        // Convert current categories to strings for comparison
        const selectedIds = new Set(currentCategories.map(String));

        // Store data globally for helper functions
        window.currentCategoryData = { categories, selectedIds, ruleId };

        // Generate category grid HTML
        const categoryGridHtml = generateCategoryGrid(categories, selectedIds);

        // Generate statistics HTML
        const statisticsHtml = generateStatisticsHtml(statistics, selectedIds.size);

        const result = await Swal.fire({
            html: `
                <div class="category-selector-header">
                    <h2><i class="fas fa-tags me-2"></i>Manage Categories</h2>
                    <div class="provider-info">
                        <i class="fas fa-building me-1"></i>
                        Provider: <strong>${provider_display}</strong> |
                        <i class="fas fa-chart-bar me-1"></i>
                        ${categories.length} categories available
                    </div>
                </div>

                <div class="category-selector-body">
                    <!-- Fixed Controls Section -->
                    <div class="category-controls-section">
                        <div class="position-relative">
                            <i class="fas fa-search category-search-icon"></i>
                            <input type="text"
                                   class="form-control category-search-input"
                                   placeholder="Search categories by name or description..."
                                   id="categorySearchInput">
                        </div>

                        <!-- Statistics Overview -->
                        <div class="mt-3">
                            ${statisticsHtml}
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-3 d-flex gap-2 flex-wrap">
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllCategories()">
                                <i class="fas fa-check-double me-1"></i>Select All
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="selectHighActivityCategories()">
                                <i class="fas fa-fire me-1"></i>High Activity Only
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllCategories()">
                                <i class="fas fa-times me-1"></i>Clear All
                            </button>
                        </div>
                    </div>

                    <!-- Scrollable Content Section -->
                    <div class="category-content-section">
                        ${selectedIds.size > 0 ? `
                            <div class="selected-categories-header">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Currently Selected (${selectedIds.size})
                                </h6>
                            </div>
                        ` : ''}
                        <div class="category-grid" id="categoryGrid">
                            ${categoryGridHtml}
                        </div>
                    </div>
                </div>

                <div class="category-selector-footer">
                    <div class="selection-summary">
                        <span class="selection-count" id="selectionCount">${selectedIds.size} selected</span>
                        <small class="text-muted" id="selectionPreview">
                            ${generateSelectionPreview(categories, selectedIds)}
                        </small>
                    </div>
                    <div class="footer-actions">
                        <button type="button" class="btn btn-secondary btn-footer" onclick="Swal.close()">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-primary btn-footer" onclick="saveCategorySelection(${ruleId})">
                            <i class="fas fa-save me-1"></i>Save Changes
                        </button>
                    </div>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: false,
            showCloseButton: true,
            allowOutsideClick: true,
            customClass: {
                container: 'category-selector-modal',
                popup: 'p-0'
            },
            width: '90vw',
            didOpen: () => {
                initializeCategorySelector(categories, selectedIds);
            }
        });
    }

    window.filterProvider = filterProvider;
    window.openCloneModal = openCloneModal;
    window.closeCloneModal = closeCloneModal;
    window.selectOffset = selectOffset;
    window.selectFromProvider = selectFromProvider;
    window.selectToProvider = selectToProvider;
    window.executeClone = executeClone;
    window.saveField = saveField;
    window.validateField = validateField;
    window.clearSearchAndFilters = clearSearchAndFilters;
    /**
     * Generate HTML for category grid with rich metadata
     * Selected categories are shown first with special styling
     */
    function generateCategoryGrid(categories, selectedIds) {
        // Sort categories: selected first, then by activity level and name
        const sortedCategories = [...categories].sort((a, b) => {
            const aSelected = selectedIds.has(a.id);
            const bSelected = selectedIds.has(b.id);

            // Selected categories first
            if (aSelected && !bSelected) return -1;
            if (!aSelected && bSelected) return 1;

            // Then by activity level (high to low)
            const activityOrder = { 'high': 0, 'medium': 1, 'low': 2, 'none': 3 };
            const activityDiff = activityOrder[a.activity_level] - activityOrder[b.activity_level];
            if (activityDiff !== 0) return activityDiff;

            // Finally by name
            return a.text.localeCompare(b.text);
        });

        return sortedCategories.map(category => {
            const isSelected = selectedIds.has(category.id);
            const activityClass = `activity-${category.activity_level}`;

            return `
                <div class="category-item ${isSelected ? 'selected' : ''} ${activityClass}"
                     data-category-id="${category.id}"
                     data-activity="${category.activity_level}"
                     data-selected="${isSelected ? 'true' : 'false'}"
                     data-search-text="${category.text.toLowerCase()} ${category.canonical_name.toLowerCase()} ${category.description.toLowerCase()}">

                    <input type="checkbox"
                           class="category-checkbox"
                           ${isSelected ? 'checked' : ''}
                           data-category-id="${category.id}">

                    <div class="category-header">
                        <i class="${category.icon_class} category-icon"></i>
                        <h6 class="category-title">${category.text}</h6>
                    </div>

                    <div class="category-stats">
                        <span class="badge ${category.badge_class} category-stat-badge">
                            <i class="fas fa-briefcase me-1"></i>${category.job_count} jobs
                        </span>
                        <span class="badge bg-info category-stat-badge">
                            <i class="fas fa-cog me-1"></i>${category.usage_count} rules
                        </span>
                        <span class="badge bg-secondary category-stat-badge">
                            ${category.activity_level}
                        </span>
                    </div>

                    ${category.description ? `
                        <div class="category-description">
                            ${category.description}
                        </div>
                    ` : ''}

                    ${category.canonical_name !== category.text ? `
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-link me-1"></i>Maps to: ${category.canonical_name}
                            </small>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    /**
     * Generate statistics overview HTML
     */
    function generateStatisticsHtml(statistics, selectedCount = 0) {
        return `
            <div class="row g-3 text-center">
                <div class="col-2">
                    <div class="apple-stat-card selected-stat">
                        <div class="stat-number">${selectedCount}</div>
                        <div class="stat-label">Selected</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card high-activity-stat">
                        <div class="stat-number">${statistics.high_activity}</div>
                        <div class="stat-label">High Activity</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card medium-activity-stat">
                        <div class="stat-number">${statistics.medium_activity}</div>
                        <div class="stat-label">Medium</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card low-activity-stat">
                        <div class="stat-number">${statistics.low_activity}</div>
                        <div class="stat-label">Low Activity</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card no-activity-stat">
                        <div class="stat-number">${statistics.no_activity}</div>
                        <div class="stat-label">No Activity</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card total-stat">
                        <div class="stat-number">${statistics.high_activity + statistics.medium_activity + statistics.low_activity + statistics.no_activity}</div>
                        <div class="stat-label">Total</div>
                    </div>
                </div>
            </div>
            <div class="mt-3 text-center">
                <div class="apple-summary-stats">
                    <span class="summary-item">
                        <i class="fas fa-briefcase"></i>
                        <strong>${statistics.total_jobs}</strong> Total Jobs
                    </span>
                    <span class="summary-divider">•</span>
                    <span class="summary-item">
                        <i class="fas fa-chart-line"></i>
                        <strong>${statistics.avg_jobs_per_category}</strong> Avg per Category
                    </span>
                </div>
            </div>
        `;
    }

    /**
     * Generate selection preview text
     */
    function generateSelectionPreview(categories, selectedIds) {
        if (selectedIds.size === 0) return 'No categories selected';

        const selectedCategories = categories.filter(cat => selectedIds.has(cat.id));
        const preview = selectedCategories.slice(0, 3).map(cat => cat.text).join(', ');

        if (selectedCategories.length > 3) {
            return `${preview} and ${selectedCategories.length - 3} more`;
        }

        return preview;
    }

    /**
     * Initialize category selector interactions
     */
    function initializeCategorySelector(categories, selectedIds) {
        // Search functionality
        const searchInput = document.getElementById('categorySearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                filterCategories(e.target.value.toLowerCase());
            });
        }

        // Category item click handlers
        document.querySelectorAll('.category-item').forEach(item => {
            const checkbox = item.querySelector('.category-checkbox');

            // Click on item toggles checkbox
            item.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    checkbox.checked = !checkbox.checked;
                    checkbox.dispatchEvent(new Event('change'));
                }
            });

            // Checkbox change handler
            checkbox.addEventListener('change', (e) => {
                const categoryId = e.target.dataset.categoryId;
                const isChecked = e.target.checked;

                // Update visual state
                if (isChecked) {
                    item.classList.add('selected');
                    selectedIds.add(categoryId);
                } else {
                    item.classList.remove('selected');
                    selectedIds.delete(categoryId);
                }

                // Update selection count and preview
                updateSelectionDisplay(categories, selectedIds);

                // Add visual feedback
                item.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    item.style.transform = '';
                }, 150);
            });
        });
    }

    /**
     * Filter categories based on search term
     */
    function filterCategories(searchTerm) {
        const categoryItems = document.querySelectorAll('.category-item');
        let visibleCount = 0;

        categoryItems.forEach(item => {
            const searchText = item.dataset.searchText || '';
            const isVisible = searchText.includes(searchTerm);

            if (isVisible) {
                item.style.display = 'block';
                item.style.animationDelay = `${visibleCount * 50}ms`;
                item.classList.add('fade-in');
                visibleCount++;
            } else {
                item.style.display = 'none';
                item.classList.remove('fade-in');
            }
        });

        console.log(`Filtered categories: ${visibleCount} visible for search: "${searchTerm}"`);
    }

    /**
     * Update selection count and preview display
     */
    function updateSelectionDisplay(categories, selectedIds) {
        const countElement = document.getElementById('selectionCount');
        const previewElement = document.getElementById('selectionPreview');

        if (countElement) {
            countElement.textContent = `${selectedIds.size} selected`;
        }

        if (previewElement) {
            previewElement.textContent = generateSelectionPreview(categories, selectedIds);
        }
    }

    /**
     * Quick action: Select all categories
     */
    window.selectAllCategories = function() {
        const checkboxes = document.querySelectorAll('.category-checkbox');
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                checkbox.checked = true;
                checkbox.dispatchEvent(new Event('change'));
            }
        });

        // Show success feedback
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true
        });

        Toast.fire({
            icon: 'success',
            title: `Selected all ${checkboxes.length} categories`
        });
    };

    /**
     * Quick action: Select only high activity categories
     */
    window.selectHighActivityCategories = function() {
        // First clear all
        document.querySelectorAll('.category-checkbox').forEach(checkbox => {
            checkbox.checked = false;
            checkbox.dispatchEvent(new Event('change'));
        });

        // Then select high activity ones
        const highActivityItems = document.querySelectorAll('.category-item[data-activity="high"]');
        highActivityItems.forEach(item => {
            const checkbox = item.querySelector('.category-checkbox');
            checkbox.checked = true;
            checkbox.dispatchEvent(new Event('change'));
        });

        // Show feedback
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true
        });

        Toast.fire({
            icon: 'info',
            title: `Selected ${highActivityItems.length} high-activity categories`
        });
    };

    /**
     * Quick action: Clear all selections
     */
    window.clearAllCategories = function() {
        const checkboxes = document.querySelectorAll('.category-checkbox');
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                checkbox.checked = false;
                checkbox.dispatchEvent(new Event('change'));
            }
        });

        // Show feedback
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true
        });

        Toast.fire({
            icon: 'warning',
            title: 'Cleared all category selections'
        });
    };

    /**
     * Save category selection and update the rule
     */
    window.saveCategorySelection = async function(ruleId) {
        try {
            const selectedIds = Array.from(window.currentCategoryData.selectedIds);

            console.log('💾 Saving category selection', { ruleId, selectedIds });

            // Show saving state
            Swal.fire({
                title: 'Saving Categories...',
                html: `
                    <div class="category-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Saving...</span>
                        </div>
                        <p class="mt-3">Updating ${selectedIds.length} categories</p>
                    </div>
                `,
                allowOutsideClick: false,
                showConfirmButton: false
            });

            // Save via existing saveField function
            await saveField(ruleId, 'categories', selectedIds);

            // Update the UI
            updateCategoryPreview(ruleId, selectedIds);

            // Show success
            Swal.fire({
                icon: 'success',
                title: 'Categories Updated!',
                text: `Successfully updated ${selectedIds.length} categories for rule ${ruleId}`,
                timer: 2000,
                showConfirmButton: false
            });

        } catch (error) {
            console.error('❌ Error saving categories:', error);

            Swal.fire({
                icon: 'error',
                title: 'Save Failed',
                text: error.message || 'Failed to save category selection',
                confirmButtonText: 'OK'
            });
        }
    };

    /**
     * Update category preview in the main interface
     */
    function updateCategoryPreview(ruleId, selectedIds) {
        const button = document.querySelector(`[data-rule-id="${ruleId}"].btn-category-manager`);
        const preview = button?.parentElement.querySelector('.category-preview');

        if (button) {
            // CRITICAL FIX: Update the data-current-categories attribute
            button.setAttribute('data-current-categories', JSON.stringify(selectedIds));

            const countDisplay = button.querySelector('.category-count-display');
            if (countDisplay) {
                countDisplay.textContent = selectedIds.length > 0
                    ? `${selectedIds.length} categories selected`
                    : 'No categories selected';
            }

            // Update the rule data in state as well
            const rule = state.allRules.find(r => r.id === ruleId);
            if (rule) {
                rule.categories = selectedIds;
                console.log('✅ Updated rule categories in state:', { ruleId, categories: selectedIds });
            }
        }

        if (preview) {
            if (selectedIds.length > 0) {
                const badges = selectedIds.slice(0, 3).map(catId => {
                    const category = state.categories.find(c => c.id === catId);
                    return category ? `<span class="badge bg-primary me-1">${category.text}</span>` : '';
                }).join('');

                const moreText = selectedIds.length > 3
                    ? `<span class="badge bg-secondary">+${selectedIds.length - 3} more</span>`
                    : '';

                preview.innerHTML = badges + moreText;
            } else {
                preview.innerHTML = '<small class="text-muted">Click button above to select categories</small>';
            }
        }
    }

    // ===== SWEETALERT LOCATION MANAGEMENT SYSTEM =====

    /**
     * Enhanced Location Management with SweetAlert
     * Provides a comprehensive multi-select interface with rich metadata
     */
    async function openLocationManager(ruleId, provider, currentLocations = []) {
        try {
            console.log('🗺️ Opening location manager', { ruleId, provider, currentLocations });

            // Show a subtle loading indicator in the button while fetching
            const button = document.querySelector(`[data-rule-id="${ruleId}"].btn-location-manager`);
            const originalText = button?.innerHTML;
            if (button) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                button.disabled = true;
            }

            // Fetch enhanced location data
            const response = await fetch(`{{ url('admin/jobseeker/command-schedule/provider-locations-sweetalert') }}/${provider}`, {
                headers: {
                    'X-CSRF-TOKEN': CSRF_TOKEN,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'Failed to load locations');
            }

            console.log('🗺️ Location data loaded', data.data);

            // Restore button state
            if (button && originalText) {
                button.innerHTML = originalText;
                button.disabled = false;
            }

            // Show the enhanced location selector
            await showLocationSelector(ruleId, data.data, currentLocations);

        } catch (error) {
            console.error('❌ Error loading locations:', error);

            // Restore button state on error
            if (button && originalText) {
                button.innerHTML = originalText;
                button.disabled = false;
            }

            Swal.fire({
                icon: 'error',
                title: 'Failed to Load Locations',
                text: error.message || 'An unexpected error occurred while loading locations.',
                confirmButtonText: 'Try Again',
                confirmButtonColor: '#dc3545'
            });
        }
    }

    /**
     * Display the comprehensive location selector interface
     */
    async function showLocationSelector(ruleId, locationData, currentLocations = []) {
        const { locations, provider, provider_display, statistics } = locationData;

        // Convert current locations to strings for comparison
        const selectedIds = new Set(currentLocations.map(String));

        // Store data globally for helper functions
        window.currentLocationData = { locations, selectedIds, ruleId };

        // Generate location grid HTML
        const locationGridHtml = generateLocationGrid(locations, selectedIds);

        // Generate statistics HTML
        const locationStatisticsHtml = generateLocationStatisticsHtml(statistics, selectedIds.size);

        const result = await Swal.fire({
            html: `
                <div class="category-selector-header">
                    <h2><i class="fas fa-map-marker-alt me-2"></i>Manage Locations</h2>
                    <div class="provider-info">
                        <i class="fas fa-building me-1"></i>
                        Provider: <strong>${provider_display}</strong> |
                        <i class="fas fa-map me-1"></i>
                        ${locations.length} locations available
                    </div>
                </div>

                <div class="category-selector-body">
                    <!-- Fixed Controls Section -->
                    <div class="category-controls-section">
                        <div class="position-relative">
                            <i class="fas fa-search category-search-icon"></i>
                            <input type="text"
                                   class="form-control category-search-input"
                                   placeholder="Search locations by name, province, or type..."
                                   id="locationSearchInput">
                        </div>

                        <!-- Statistics Overview -->
                        <div class="mt-3">
                            ${locationStatisticsHtml}
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-3 d-flex gap-2 flex-wrap">
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllLocations()">
                                <i class="fas fa-check-double me-1"></i>Select All
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="selectCapitalAndMajorLocations()">
                                <i class="fas fa-crown me-1"></i>Capital & Major Only
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="selectHighActivityLocations()">
                                <i class="fas fa-chart-line me-1"></i>High Activity Only
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllLocations()">
                                <i class="fas fa-times me-1"></i>Clear All
                            </button>
                        </div>
                    </div>

                    <!-- Scrollable Content Section -->
                    <div class="category-content-section">
                        ${selectedIds.size > 0 ? `
                            <div class="selected-locations-header">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Currently Selected (${selectedIds.size})
                                </h6>
                            </div>
                        ` : ''}
                        <div class="category-grid" id="locationGrid">
                            ${locationGridHtml}
                        </div>
                    </div>
                </div>

                <div class="category-selector-footer">
                    <div class="selection-summary">
                        <span class="selection-count" id="locationSelectionCount">${selectedIds.size} selected</span>
                        <small class="text-muted" id="locationSelectionPreview">
                            ${generateLocationSelectionPreview(locations, selectedIds)}
                        </small>
                    </div>
                    <div class="footer-actions">
                        <button type="button" class="btn btn-secondary btn-footer" onclick="Swal.close()">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-success btn-footer" onclick="saveLocationSelection(${ruleId})">
                            <i class="fas fa-save me-1"></i>Save Changes
                        </button>
                    </div>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: false,
            showCloseButton: true,
            allowOutsideClick: true,
            customClass: {
                container: 'category-selector-modal',
                popup: 'p-0'
            },
            width: '90vw',
            didOpen: () => {
                initializeLocationSelector(locations, selectedIds);
            }
        });
    }

    window.openCategoryManager = openCategoryManager;
    /**
     * Generate HTML for location grid with rich metadata
     * Selected locations are shown first with special styling
     */
    function generateLocationGrid(locations, selectedIds) {
        // Sort locations: selected first, then by type (capital, major, province) and name
        const sortedLocations = [...locations].sort((a, b) => {
            const aSelected = selectedIds.has(a.id);
            const bSelected = selectedIds.has(b.id);

            // Selected locations first
            if (aSelected && !bSelected) return -1;
            if (!aSelected && bSelected) return 1;

            // Then by location type (capital > major > province)
            const typeOrder = { 'capital': 0, 'major': 1, 'province': 2 };
            const typeDiff = typeOrder[a.location_type] - typeOrder[b.location_type];
            if (typeDiff !== 0) return typeDiff;

            // Finally by name
            return a.text.localeCompare(b.text);
        });

        return sortedLocations.map(location => {
            const isSelected = selectedIds.has(location.id);
            const typeClass = `location-type-${location.location_type}`;

            return `
                <div class="category-item ${isSelected ? 'selected' : ''} ${typeClass}"
                     data-category-id="${location.id}"
                     data-location-type="${location.location_type}"
                     data-selected="${isSelected ? 'true' : 'false'}"
                     data-search-text="${location.text.toLowerCase()} ${location.canonical_name.toLowerCase()} ${location.province.toLowerCase()}">

                    <input type="checkbox"
                           class="category-checkbox"
                           ${isSelected ? 'checked' : ''}
                           data-category-id="${location.id}">

                    <div class="category-header">
                        <i class="${location.icon_class} category-icon"></i>
                        <h6 class="category-title">${location.text}</h6>
                    </div>

                    <div class="category-stats">
                        <span class="badge ${location.badge_class} category-stat-badge">
                            <i class="fas fa-cog me-1"></i>${location.usage_count} rules
                        </span>
                        <span class="badge bg-secondary category-stat-badge">
                            ${location.activity_level}
                        </span>
                        <span class="badge bg-light text-dark category-stat-badge">
                            ${location.location_type}
                        </span>
                    </div>

                    ${location.province ? `
                        <div class="category-description">
                            <i class="fas fa-map me-1"></i>
                            Province: ${location.province}
                        </div>
                    ` : ''}

                    ${location.canonical_name !== location.text ? `
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-link me-1"></i>Maps to: ${location.canonical_name}
                            </small>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    /**
     * Generate statistics overview HTML for locations
     */
    function generateLocationStatisticsHtml(statistics, selectedCount = 0) {
        return `
            <div class="row g-3 text-center">
                <div class="col-2">
                    <div class="apple-stat-card selected-stat">
                        <div class="stat-number">${selectedCount}</div>
                        <div class="stat-label">Selected</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card high-activity-stat">
                        <div class="stat-number">${statistics.capital_locations}</div>
                        <div class="stat-label">Capital</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card medium-activity-stat">
                        <div class="stat-number">${statistics.major_locations}</div>
                        <div class="stat-label">Major Cities</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card low-activity-stat">
                        <div class="stat-number">${statistics.province_locations}</div>
                        <div class="stat-label">Provinces</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card no-activity-stat">
                        <div class="stat-number">${statistics.high_activity}</div>
                        <div class="stat-label">High Activity</div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="apple-stat-card total-stat">
                        <div class="stat-number">${statistics.capital_locations + statistics.major_locations + statistics.province_locations}</div>
                        <div class="stat-label">Total</div>
                    </div>
                </div>
            </div>
            <div class="mt-3 text-center">
                <div class="apple-summary-stats">
                    <span class="summary-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <strong>${statistics.total_usage}</strong> Rules Using Locations
                    </span>
                    <span class="summary-divider">•</span>
                    <span class="summary-item">
                        <i class="fas fa-globe"></i>
                        <strong>${statistics.capital_locations + statistics.major_locations + statistics.province_locations}</strong> Available Locations
                    </span>
                </div>
            </div>
        `;
    }

    /**
     * Generate selection preview text for locations
     */
    function generateLocationSelectionPreview(locations, selectedIds) {
        if (selectedIds.size === 0) return 'No locations selected';

        const selectedLocations = locations.filter(loc => selectedIds.has(loc.id));
        const preview = selectedLocations.slice(0, 3).map(loc => loc.text).join(', ');

        if (selectedLocations.length > 3) {
            return `${preview} and ${selectedLocations.length - 3} more`;
        }

        return preview;
    }

    /**
     * Initialize location selector interactions
     */
    function initializeLocationSelector(locations, selectedIds) {
        // Search functionality
        const searchInput = document.getElementById('locationSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                filterLocations(e.target.value.toLowerCase());
            });
        }

        // Location item click handlers (using category classes for consistency)
        document.querySelectorAll('.category-item').forEach(item => {
            const checkbox = item.querySelector('.category-checkbox');

            // Click on item toggles checkbox
            item.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    checkbox.checked = !checkbox.checked;
                    checkbox.dispatchEvent(new Event('change'));
                }
            });

            // Checkbox change handler
            checkbox.addEventListener('change', (e) => {
                const locationId = e.target.dataset.categoryId; // Using category-id for consistency
                const isChecked = e.target.checked;

                // Update visual state
                if (isChecked) {
                    item.classList.add('selected');
                    selectedIds.add(locationId);
                } else {
                    item.classList.remove('selected');
                    selectedIds.delete(locationId);
                }

                // Update selection count and preview
                updateLocationSelectionDisplay(locations, selectedIds);

                // Add visual feedback
                item.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    item.style.transform = '';
                }, 150);
            });
        });
    }

    /**
     * Filter locations based on search term
     */
    function filterLocations(searchTerm) {
        const locationItems = document.querySelectorAll('.category-item'); // Using category-item for consistency
        let visibleCount = 0;

        locationItems.forEach(item => {
            const searchText = item.dataset.searchText || '';
            const isVisible = searchText.includes(searchTerm);

            if (isVisible) {
                item.style.display = 'block';
                item.style.animationDelay = `${visibleCount * 50}ms`;
                item.classList.add('fade-in');
                visibleCount++;
            } else {
                item.style.display = 'none';
                item.classList.remove('fade-in');
            }
        });

        console.log(`Filtered locations: ${visibleCount} visible for search: "${searchTerm}"`);
    }

    /**
     * Update location selection count and preview display
     */
    function updateLocationSelectionDisplay(locations, selectedIds) {
        const countElement = document.getElementById('locationSelectionCount');
        const previewElement = document.getElementById('locationSelectionPreview');

        if (countElement) {
            countElement.textContent = `${selectedIds.size} selected`;
        }

        if (previewElement) {
            previewElement.textContent = generateLocationSelectionPreview(locations, selectedIds);
        }
    }

    /**
     * Quick action: Select all locations
     */
    window.selectAllLocations = function() {
        const checkboxes = document.querySelectorAll('.category-checkbox'); // Using category-checkbox for consistency
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                checkbox.checked = true;
                checkbox.dispatchEvent(new Event('change'));
            }
        });

        // Show success feedback
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true
        });

        Toast.fire({
            icon: 'success',
            title: `Selected all ${checkboxes.length} locations`
        });
    };

    /**
     * Quick action: Select capital and major cities only
     */
    window.selectCapitalAndMajorLocations = function() {
        // First clear all
        document.querySelectorAll('.category-checkbox').forEach(checkbox => {
            checkbox.checked = false;
            checkbox.dispatchEvent(new Event('change'));
        });

        // Then select capital and major locations
        const importantItems = document.querySelectorAll('.category-item[data-location-type="capital"], .category-item[data-location-type="major"]');
        importantItems.forEach(item => {
            const checkbox = item.querySelector('.category-checkbox');
            checkbox.checked = true;
            checkbox.dispatchEvent(new Event('change'));
        });

        // Show feedback
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true
        });

        Toast.fire({
            icon: 'info',
            title: `Selected ${importantItems.length} capital & major locations`
        });
    };

    /**
     * Quick action: Select only high activity locations
     */
    window.selectHighActivityLocations = function() {
        // First clear all
        document.querySelectorAll('.category-checkbox').forEach(checkbox => {
            checkbox.checked = false;
            checkbox.dispatchEvent(new Event('change'));
        });

        // Then select high activity ones
        const highActivityItems = document.querySelectorAll('.category-item').forEach(item => {
            const badge = item.querySelector('.category-stat-badge');
            if (badge && badge.textContent.includes('high')) {
                const checkbox = item.querySelector('.category-checkbox');
                checkbox.checked = true;
                checkbox.dispatchEvent(new Event('change'));
            }
        });

        // Show feedback
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true
        });

        Toast.fire({
            icon: 'info',
            title: 'Selected high-activity locations'
        });
    };

    /**
     * Quick action: Clear all location selections
     */
    window.clearAllLocations = function() {
        const checkboxes = document.querySelectorAll('.category-checkbox'); // Using category-checkbox for consistency
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                checkbox.checked = false;
                checkbox.dispatchEvent(new Event('change'));
            }
        });

        // Show feedback
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true
        });

        Toast.fire({
            icon: 'warning',
            title: 'Cleared all location selections'
        });
    };

    /**
     * Save location selection and update the rule
     */
    window.saveLocationSelection = async function(ruleId) {
        try {
            const selectedIds = Array.from(window.currentLocationData.selectedIds);

            console.log('💾 Saving location selection', { ruleId, selectedIds });

            // Show saving state
            Swal.fire({
                title: 'Saving Locations...',
                html: `
                    <div class="category-loading">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Saving...</span>
                        </div>
                        <p class="mt-3">Updating ${selectedIds.length} locations</p>
                    </div>
                `,
                allowOutsideClick: false,
                showConfirmButton: false
            });

            // Save via existing saveField function
            await saveField(ruleId, 'locations', selectedIds);

            // Update the UI
            updateLocationPreview(ruleId, selectedIds);

            // Show success
            Swal.fire({
                icon: 'success',
                title: 'Locations Updated!',
                text: `Successfully updated ${selectedIds.length} locations for rule ${ruleId}`,
                timer: 2000,
                showConfirmButton: false
            });

        } catch (error) {
            console.error('❌ Error saving locations:', error);

            Swal.fire({
                icon: 'error',
                title: 'Save Failed',
                text: error.message || 'Failed to save location selection',
                confirmButtonText: 'OK'
            });
        }
    };

    /**
     * Update location preview in the main interface
     */
    function updateLocationPreview(ruleId, selectedIds) {
        const button = document.querySelector(`[data-rule-id="${ruleId}"].btn-location-manager`);
        const preview = button?.parentElement.querySelector('.location-preview');

        if (button) {
            // CRITICAL FIX: Update the data-current-locations attribute
            button.setAttribute('data-current-locations', JSON.stringify(selectedIds));

            const countDisplay = button.querySelector('.location-count-display');
            if (countDisplay) {
                countDisplay.textContent = selectedIds.length > 0
                    ? `${selectedIds.length} locations selected`
                    : 'No locations selected';
            }

            // Update the rule data in state as well
            const rule = state.allRules.find(r => r.id === ruleId);
            if (rule) {
                rule.locations = selectedIds;
                console.log('✅ Updated rule locations in state:', { ruleId, locations: selectedIds });
            }
        }

        if (preview) {
            if (selectedIds.length > 0) {
                const badges = selectedIds.slice(0, 3).map(locId => {
                    const location = state.locations.find(l => l.id === locId);
                    return location ? `<span class="badge bg-success me-1">${location.text}</span>` : '';
                }).join('');

                const moreText = selectedIds.length > 3
                    ? `<span class="badge bg-secondary">+${selectedIds.length - 3} more</span>`
                    : '';

                preview.innerHTML = badges + moreText;
            } else {
                preview.innerHTML = '<small class="text-muted">Click button above to select locations</small>';
            }
        }
    }

    // ===== PROVIDER-SPECIFIC CLONE FILTERS SYSTEM =====

    /**
     * Open the provider-specific clone filters modal
     * Shows provider selection first, then provider-specific interface
     */
    async function openCloneFiltersModal() {
        try {
            console.log('🔄 Opening provider-specific clone filters modal');

            // Get all rules grouped by provider
            const allRules = state.allRules || [];

            if (allRules.length < 2) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Insufficient Rules',
                    text: 'You need at least 2 rules to perform cloning operations.',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#6c757d'
                });
                return;
            }

            // Group rules by provider
            const rulesByProvider = allRules.reduce((groups, rule) => {
                const provider = rule.provider || 'unknown';
                if (!groups[provider]) {
                    groups[provider] = [];
                }
                groups[provider].push(rule);
                return groups;
            }, {});

            // Show provider selection if multiple providers exist
            const providers = Object.keys(rulesByProvider);
            if (providers.length > 1) {
                const selectedProvider = await showProviderSelectionModal(rulesByProvider);
                if (selectedProvider) {
                    await showProviderCloneModal(selectedProvider, rulesByProvider[selectedProvider]);
                }
            } else {
                // Only one provider, go directly to clone modal
                const provider = providers[0];
                await showProviderCloneModal(provider, rulesByProvider[provider]);
            }

        } catch (error) {
            console.error('❌ Error opening clone modal:', error);

            Swal.fire({
                icon: 'error',
                title: 'Failed to Open Clone Modal',
                text: error.message || 'An unexpected error occurred.',
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc3545'
            });
        }
    }

    /**
     * Display the comprehensive clone filters interface
     */
    async function showCloneFiltersModal(allRules) {
        // Generate rule options for dropdowns
        const ruleOptions = allRules.map(rule => {
            const categoryCount = rule.categories ? rule.categories.length : 0;
            const locationCount = rule.locations ? rule.locations.length : 0;
            const hasData = categoryCount > 0 || locationCount > 0;

            return {
                id: rule.id,
                name: rule.display_name || rule.name,
                provider: rule.provider,
                categories: categoryCount,
                locations: locationCount,
                hasData: hasData,
                displayText: `${rule.display_name || rule.name} (${rule.provider.toUpperCase()}) - ${categoryCount}C, ${locationCount}L`
            };
        });

        // Separate rules with data (potential sources) and all rules (potential targets)
        const rulesWithData = ruleOptions.filter(rule => rule.hasData);

        if (rulesWithData.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'No Source Data',
                text: 'No rules have categories or locations to clone from.',
                confirmButtonText: 'OK'
            });
            return;
        }

        const result = await Swal.fire({
            html: `
                <div class="clone-filters-header">
                    <h2><i class="fas fa-clone me-2"></i>Clone Filters Between Rules</h2>
                    <p class="text-muted">Copy categories and locations from one rule to multiple other rules</p>
                </div>

                <div class="clone-filters-body">
                    <!-- Source Rule Selection -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-download me-1"></i>Source Rule (Copy From)
                        </label>
                        <select class="form-select" id="sourceRuleSelect">
                            <option value="">Select a rule to copy from...</option>
                            ${rulesWithData.map(rule => `
                                <option value="${rule.id}" data-categories="${rule.categories}" data-locations="${rule.locations}">
                                    ${rule.displayText}
                                </option>
                            `).join('')}
                        </select>
                        <small class="text-muted">Only rules with existing categories or locations are shown</small>
                    </div>

                    <!-- Source Preview -->
                    <div class="source-preview mb-4" id="sourcePreview" style="display: none;">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-eye me-1"></i>Source Data Preview</h6>
                            <div id="sourcePreviewContent"></div>
                        </div>
                    </div>

                    <!-- Clone Options -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-cog me-1"></i>What to Clone
                        </label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="cloneCategories" checked>
                                    <label class="form-check-label" for="cloneCategories">
                                        <i class="fas fa-tags me-1"></i>Categories
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="cloneLocations" checked>
                                    <label class="form-check-label" for="cloneLocations">
                                        <i class="fas fa-map-marker-alt me-1"></i>Locations
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Conflict Resolution -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-exclamation-triangle me-1"></i>If Target Rule Already Has Data
                        </label>
                        <div class="row">
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="conflictResolution" id="replaceData" value="replace" checked>
                                    <label class="form-check-label" for="replaceData">
                                        <strong>Replace</strong><br>
                                        <small class="text-muted">Overwrite existing data</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="conflictResolution" id="mergeData" value="merge">
                                    <label class="form-check-label" for="mergeData">
                                        <strong>Merge</strong><br>
                                        <small class="text-muted">Combine with existing</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="conflictResolution" id="skipData" value="skip">
                                    <label class="form-check-label" for="skipData">
                                        <strong>Skip</strong><br>
                                        <small class="text-muted">Leave existing unchanged</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Target Rules Selection -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-upload me-1"></i>Target Rules (Copy To)
                        </label>
                        <div class="target-rules-container" style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 8px; padding: 12px;">
                            ${ruleOptions.map(rule => `
                                <div class="form-check target-rule-option" data-rule-id="${rule.id}">
                                    <input class="form-check-input target-rule-checkbox" type="checkbox" id="target_${rule.id}" value="${rule.id}">
                                    <label class="form-check-label w-100" for="target_${rule.id}">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>${rule.name}</strong>
                                                <span class="badge bg-${rule.provider === 'jobs.af' ? 'primary' : 'success'} ms-2">${rule.provider.toUpperCase()}</span>
                                            </div>
                                            <div class="text-muted small">
                                                ${rule.categories}C, ${rule.locations}L
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        <div class="mt-2 d-flex gap-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllTargets()">
                                <i class="fas fa-check-double me-1"></i>Select All
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllTargets()">
                                <i class="fas fa-times me-1"></i>Clear All
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="selectSameProvider()">
                                <i class="fas fa-building me-1"></i>Same Provider
                            </button>
                        </div>
                        <small class="text-muted">Select one or more rules to copy the filters to</small>
                    </div>

                    <!-- Selection Summary -->
                    <div class="selection-summary" id="cloneSelectionSummary" style="display: none;">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-info-circle me-1"></i>Clone Summary</h6>
                            <div id="cloneSummaryContent"></div>
                        </div>
                    </div>
                </div>

                <div class="clone-filters-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            <small id="targetCountDisplay">No targets selected</small>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-secondary" onclick="Swal.close()">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-primary" onclick="executeCloneFilters()" id="executeCloneBtn" disabled>
                                <i class="fas fa-clone me-1"></i>Clone Filters
                            </button>
                        </div>
                    </div>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: false,
            allowOutsideClick: false,
            customClass: {
                container: 'clone-filters-modal',
                popup: 'p-0'
            },
            width: '90vw',
            didOpen: () => {
                initializeCloneFiltersModal();
            }
        });
    }

    // ===== APPLE-LEVEL FILTER COPY SYSTEM =====

    /**
     * Open the Apple-level filter copy modal
     * Clean, minimal, and beautiful - true Apple aesthetics
     */
    async function openAppleFilterCopyModal() {
        try {
            console.log('🍎 Opening Apple-level filter copy modal');

            // Get all rules grouped by provider
            const allRules = state.allRules || [];

            if (allRules.length < 2) {
                await Swal.fire({
                    html: `
                        <div style="text-align: center; padding: 40px 20px;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #FF9500 0%, #FF6B35 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto;">
                                <i class="fas fa-exclamation" style="color: white; font-size: 2rem;"></i>
                            </div>
                            <h2 style="color: #1D1D1F; font-weight: 700; margin-bottom: 12px; font-size: 1.5rem;">Not Enough Rules</h2>
                            <p style="color: #86868B; font-size: 1rem; margin: 0; line-height: 1.5;">You need at least 2 rules to copy filters between them.</p>
                        </div>
                    `,
                    showConfirmButton: true,
                    confirmButtonText: 'Got it',
                    confirmButtonColor: '#007AFF',
                    customClass: {
                        popup: 'apple-alert-popup'
                    },
                    width: '400px'
                });
                return;
            }

            // Group rules by provider
            const rulesByProvider = allRules.reduce((groups, rule) => {
                const provider = rule.provider || 'unknown';
                if (!groups[provider]) {
                    groups[provider] = [];
                }
                groups[provider].push(rule);
                return groups;
            }, {});

            // Show provider selection if multiple providers exist
            const providers = Object.keys(rulesByProvider);
            if (providers.length > 1) {
                const selectedProvider = await showAppleProviderSelection(rulesByProvider);
                if (selectedProvider) {
                    await showAppleFilterCopyModal(selectedProvider, rulesByProvider[selectedProvider]);
                }
            } else {
                // Only one provider, go directly to filter copy modal
                const provider = providers[0];
                await showAppleFilterCopyModal(provider, rulesByProvider[provider]);
            }

        } catch (error) {
            console.error('❌ Error opening Apple filter copy modal:', error);

            const result = await Swal.fire({
                html: `
                    <div style="text-align: center; padding: 40px 20px;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #FF3B30 0%, #FF6B35 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto;">
                            <i class="fas fa-times" style="color: white; font-size: 2rem;"></i>
                        </div>
                        <h2 style="color: #1D1D1F; font-weight: 700; margin-bottom: 12px; font-size: 1.5rem;">Something Went Wrong</h2>
                        <p style="color: #86868B; font-size: 1rem; margin: 0; line-height: 1.5;">${error.message || 'An unexpected error occurred.'}</p>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: 'Try Again',
                confirmButtonColor: '#FF3B30',
                customClass: {
                    popup: 'apple-alert-popup'
                },
                width: '400px'
            });

            // If user clicks "Try Again", reopen the modal
            if (result.isConfirmed) {
                openAppleFilterCopyModal();
            }
        }
    }

    /**
     * Show Apple-style provider selection modal
     */
    async function showAppleProviderSelection(rulesByProvider) {
        const providerOptions = Object.entries(rulesByProvider).map(([provider, rules]) => {
            const rulesWithData = rules.filter(rule =>
                (rule.categories && rule.categories.length > 0) ||
                (rule.locations && rule.locations.length > 0)
            );

            return {
                provider,
                totalRules: rules.length,
                rulesWithData: rulesWithData.length,
                displayName: provider.toUpperCase(),
                canCopy: rulesWithData.length > 0 && rules.length > 1,
                icon: provider === 'jobs.af' ? 'briefcase' : 'users'
            };
        });

        const result = await Swal.fire({
            html: `
                <div class="apple-filter-header">
                    <h2>Select Provider</h2>
                    <p class="subtitle">Choose which provider's rules you want to copy filters between</p>
                </div>

                <div class="apple-filter-content" style="padding: 32px;">
                    <div style="display: grid; gap: 16px; max-width: 480px; margin: 0 auto;">
                        ${providerOptions.map(option => `
                            <div class="apple-provider-card ${option.canCopy ? 'selectable' : 'disabled'}"
                                 style="
                                    background: ${option.canCopy ? '#FFFFFF' : '#F2F2F7'};
                                    border: 1px solid ${option.canCopy ? '#D1D1D6' : '#E5E5EA'};
                                    border-radius: 16px;
                                    padding: 24px;
                                    cursor: ${option.canCopy ? 'pointer' : 'not-allowed'};
                                    transition: all 0.2s ease;
                                    opacity: ${option.canCopy ? '1' : '0.6'};
                                 "
                                 ${option.canCopy ? `onclick="selectAppleProvider('${option.provider}')"` : ''}
                                 onmouseover="if(${option.canCopy}) { this.style.transform = 'translateY(-2px)'; this.style.borderColor = '#007AFF'; this.style.boxShadow = '0 4px 12px rgba(0, 122, 255, 0.15)'; }"
                                 onmouseout="this.style.transform = 'translateY(0)'; this.style.borderColor = '${option.canCopy ? '#D1D1D6' : '#E5E5EA'}'; this.style.boxShadow = 'none';">

                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="
                                            width: 48px;
                                            height: 48px;
                                            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
                                            border-radius: 12px;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            margin-right: 16px;
                                        ">
                                            <i class="fas fa-${option.icon}" style="color: white; font-size: 1.2rem;"></i>
                                        </div>
                                        <div style="text-align: left;">
                                            <h4 style="margin: 0 0 4px 0; color: #1D1D1F; font-weight: 600; font-size: 1.1rem;">${option.displayName}</h4>
                                            <div style="color: #86868B; font-size: 0.9rem; display: flex; gap: 16px;">
                                                <span>
                                                    <i class="fas fa-list me-1" style="font-size: 0.8rem;"></i>${option.totalRules} rules
                                                </span>
                                                <span>
                                                    <i class="fas fa-database me-1" style="font-size: 0.8rem;"></i>${option.rulesWithData} with data
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        ${option.canCopy ?
                                            `<i class="fas fa-chevron-right" style="color: #007AFF; font-size: 1.1rem;"></i>` :
                                            `<div style="color: #FF3B30; font-size: 0.8rem; text-align: center;">
                                                <i class="fas fa-exclamation-triangle" style="font-size: 1rem;"></i><br>
                                                <small style="font-size: 0.7rem;">Insufficient data</small>
                                            </div>`
                                        }
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="apple-filter-footer">
                    <div></div>
                    <button type="button" class="apple-btn-secondary" onclick="Swal.close()">
                        Cancel
                    </button>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: false,
            allowOutsideClick: false,
            customClass: {
                container: 'apple-filter-modal',
                popup: 'p-0'
            },
            width: '600px'
        });

        return window.selectedAppleProvider || null;
    }

    /**
     * Handle Apple provider selection
     */
    window.selectAppleProvider = function(provider) {
        window.selectedAppleProvider = provider;
        Swal.close();
    };

    /**
     * Show the Apple-level filter copy modal
     */
    async function showAppleFilterCopyModal(provider, providerRules) {
        // Filter rules with data for source selection
        const rulesWithData = providerRules.filter(rule =>
            (rule.categories && rule.categories.length > 0) ||
            (rule.locations && rule.locations.length > 0)
        );

        if (rulesWithData.length === 0) {
            await Swal.fire({
                html: `
                    <div style="text-align: center; padding: 40px 20px;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #FF9500 0%, #FF6B35 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto;">
                            <i class="fas fa-database" style="color: white; font-size: 2rem;"></i>
                        </div>
                        <h2 style="color: #1D1D1F; font-weight: 700; margin-bottom: 12px; font-size: 1.5rem;">No Source Data</h2>
                        <p style="color: #86868B; font-size: 1rem; margin: 0; line-height: 1.5;">No ${provider.toUpperCase()} rules have categories or locations to copy from.</p>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: 'Got it',
                confirmButtonColor: '#007AFF',
                customClass: {
                    popup: 'apple-alert-popup'
                },
                width: '400px'
            });
            return;
        }

        // Store provider data globally for the modal
        window.currentAppleFilterProvider = provider;
        window.currentAppleFilterRules = providerRules;

        const result = await Swal.fire({
            html: `
                <div class="apple-filter-header">
                    <h2>Copy Filters - ${provider.toUpperCase()}</h2>
                    <p class="subtitle">Copy categories and locations between ${provider.toUpperCase()} rules</p>
                </div>

                <div class="apple-filter-content">
                    <!-- Source Rule Selection -->
                    <div class="apple-section">
                        <div class="apple-section-title">
                            <i class="fas fa-download"></i>
                            Source Rule (Copy From)
                        </div>
                        <select class="apple-select" id="appleSourceRuleSelect">
                            <option value="">Select a rule to copy from...</option>
                            ${rulesWithData.map(rule => {
                                const categoryCount = rule.categories ? rule.categories.length : 0;
                                const locationCount = rule.locations ? rule.locations.length : 0;
                                return `
                                    <option value="${rule.id}"
                                            data-categories="${categoryCount}"
                                            data-locations="${locationCount}"
                                            data-rule-name="${escapeHtml(rule.display_name || rule.name)}">
                                        ${escapeHtml(rule.display_name || rule.name)} (${categoryCount}C, ${locationCount}L)
                                    </option>
                                `;
                            }).join('')}
                        </select>
                        <small style="color: #86868B; font-size: 0.875rem;">Only ${provider.toUpperCase()} rules with existing data are shown</small>
                    </div>

                    <!-- Source Preview -->
                    <div class="apple-preview" id="appleSourcePreview" style="display: none;">
                        <h6>
                            <i class="fas fa-eye"></i>
                            Source Data Preview
                        </h6>
                        <div id="appleSourcePreviewContent"></div>
                    </div>

                    <!-- Copy Options -->
                    <div class="apple-section">
                        <div class="apple-section-title">
                            <i class="fas fa-cog"></i>
                            What to Copy
                        </div>
                        <div class="apple-options-grid">
                            <div class="apple-option selected" onclick="toggleAppleCopyOption('categories')">
                                <input type="checkbox" id="applyCopyCategories" checked>
                                <div class="apple-option-icon">
                                    <i class="fas fa-tags"></i>
                                </div>
                                <h6>Categories</h6>
                                <small>Job categories and filters</small>
                            </div>
                            <div class="apple-option selected" onclick="toggleAppleCopyOption('locations')">
                                <input type="checkbox" id="applyCopyLocations" checked>
                                <div class="apple-option-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h6>Locations</h6>
                                <small>Geographic locations</small>
                            </div>
                        </div>
                    </div>

                    <!-- Conflict Resolution -->
                    <div class="apple-section">
                        <div class="apple-section-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            If Target Rule Already Has Data
                        </div>
                        <div class="apple-conflict-grid">
                            <div class="apple-conflict-option selected" onclick="selectAppleConflictResolution('replace')">
                                <input type="radio" name="appleConflictResolution" id="appleReplaceData" value="replace" checked>
                                <div class="option-icon">
                                    <i class="fas fa-sync-alt"></i>
                                </div>
                                <h6>Replace</h6>
                                <small>Overwrite existing data</small>
                            </div>
                            <div class="apple-conflict-option" onclick="selectAppleConflictResolution('merge')">
                                <input type="radio" name="appleConflictResolution" id="appleMergeData" value="merge">
                                <div class="option-icon">
                                    <i class="fas fa-plus-circle"></i>
                                </div>
                                <h6>Merge</h6>
                                <small>Combine with existing</small>
                            </div>
                            <div class="apple-conflict-option" onclick="selectAppleConflictResolution('skip')">
                                <input type="radio" name="appleConflictResolution" id="appleSkipData" value="skip">
                                <div class="option-icon">
                                    <i class="fas fa-ban"></i>
                                </div>
                                <h6>Skip</h6>
                                <small>Leave existing unchanged</small>
                            </div>
                        </div>
                    </div>

                    <!-- Target Rules Selection -->
                    <div class="apple-section">
                        <div class="apple-section-title">
                            <i class="fas fa-upload"></i>
                            Target Rules (Copy To)
                        </div>
                        <div class="apple-quick-actions">
                            <div class="apple-quick-btn" onclick="selectAllAppleTargets()">
                                <i class="fas fa-check-double me-1"></i>Select All
                            </div>
                            <div class="apple-quick-btn" onclick="selectEmptyAppleTargets()">
                                <i class="fas fa-plus me-1"></i>Empty Only
                            </div>
                            <div class="apple-quick-btn" onclick="selectWithDataAppleTargets()">
                                <i class="fas fa-database me-1"></i>With Data
                            </div>
                            <div class="apple-quick-btn" onclick="clearAllAppleTargets()">
                                <i class="fas fa-times me-1"></i>Clear All
                            </div>
                        </div>
                        <div class="apple-targets-container" id="appleTargetRulesContainer">
                            ${generateAppleTargetRulesHTML(providerRules, provider)}
                        </div>
                        <small style="color: #86868B; font-size: 0.875rem;">Select one or more ${provider.toUpperCase()} rules to copy the filters to</small>
                    </div>

                    <!-- Copy Summary -->
                    <div class="apple-summary" id="appleCopySummary" style="display: none;">
                        <h6>
                            <i class="fas fa-info-circle"></i>
                            Copy Summary
                        </h6>
                        <div id="appleCopySummaryContent"></div>
                    </div>
                </div>

                <div class="apple-filter-footer">
                    <div class="apple-target-count" id="appleTargetCountDisplay">
                        No targets selected
                    </div>
                    <div class="apple-footer-actions">
                        <button type="button" class="apple-btn-secondary" onclick="Swal.close()">
                            Cancel
                        </button>
                        <button type="button" class="apple-btn-primary" id="executeAppleFilterCopyBtn" onclick="executeAppleFilterCopy()" disabled>
                            <i class="fas fa-copy me-1"></i>Copy Filters
                        </button>
                    </div>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: false,
            allowOutsideClick: false,
            customClass: {
                container: 'apple-filter-modal',
                popup: 'p-0'
            },
            width: 'min(90vw, 800px)',
            didOpen: () => {
                initializeAppleFilterCopyModal();
            }
        });
    }

    /**
     * Generate Apple-style HTML for target rules
     */
    function generateAppleTargetRulesHTML(rules, provider) {
        return rules.map(rule => {
            const categoryCount = rule.categories ? rule.categories.length : 0;
            const locationCount = rule.locations ? rule.locations.length : 0;
            const hasData = categoryCount > 0 || locationCount > 0;

            return `
                <div class="apple-target-rule" data-rule-id="${rule.id}" onclick="toggleAppleTargetRule(${rule.id})">
                    <input type="checkbox" class="apple-target-checkbox" value="${rule.id}">
                    <div class="apple-target-info">
                        <div class="apple-target-name">
                            ${escapeHtml(rule.display_name || rule.name)}
                        </div>
                        <div class="apple-target-stats">
                            <span class="apple-stat-badge">${categoryCount}C</span>
                            <span class="apple-stat-badge">${locationCount}L</span>
                            ${hasData ?
                                '<i class="fas fa-database" style="color: #007AFF; font-size: 0.8rem;"></i>' :
                                '<i class="fas fa-circle" style="color: #AEAEB2; font-size: 0.6rem;"></i>'
                            }
                        </div>
                    </div>
                    <div class="apple-provider-badge">
                        ${provider.toUpperCase()}
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * Initialize the Apple filter copy modal interactions
     */
    function initializeAppleFilterCopyModal() {
        // Source rule selection handler
        const sourceSelect = document.getElementById('appleSourceRuleSelect');
        if (sourceSelect) {
            sourceSelect.addEventListener('change', function() {
                updateAppleSourcePreview();
                updateAppleCopySummary();
            });
        }

        // Target rule checkboxes
        document.querySelectorAll('.apple-target-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateAppleTargetSelection();
                updateAppleCopySummary();
            });
        });

        // Copy options
        document.getElementById('applyCopyCategories')?.addEventListener('change', updateAppleCopySummary);
        document.getElementById('applyCopyLocations')?.addEventListener('change', updateAppleCopySummary);

        // Conflict resolution
        document.querySelectorAll('input[name="appleConflictResolution"]').forEach(radio => {
            radio.addEventListener('change', updateAppleCopySummary);
        });
    }

    /**
     * Update Apple source preview
     */
    function updateAppleSourcePreview() {
        const sourceSelect = document.getElementById('appleSourceRuleSelect');
        const preview = document.getElementById('appleSourcePreview');
        const previewContent = document.getElementById('appleSourcePreviewContent');

        if (!sourceSelect || !preview || !previewContent) return;

        const selectedOption = sourceSelect.selectedOptions[0];
        if (!selectedOption || !selectedOption.value) {
            preview.style.display = 'none';
            return;
        }

        const categories = parseInt(selectedOption.dataset.categories) || 0;
        const locations = parseInt(selectedOption.dataset.locations) || 0;
        const ruleName = selectedOption.dataset.ruleName || 'Unknown Rule';

        previewContent.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <strong style="color: #007AFF; font-weight: 600;">${escapeHtml(ruleName)}</strong>
                <span style="color: #86868B; font-size: 0.875rem;">Source Rule</span>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                <div style="text-align: center; padding: 16px; background: rgba(0, 122, 255, 0.1); border-radius: 12px;">
                    <div style="font-size: 1.5rem; font-weight: 700; color: #007AFF;">${categories}</div>
                    <div style="color: #86868B; font-size: 0.875rem; margin-top: 4px;">Categories</div>
                </div>
                <div style="text-align: center; padding: 16px; background: rgba(88, 86, 214, 0.1); border-radius: 12px;">
                    <div style="font-size: 1.5rem; font-weight: 700; color: #5856D6;">${locations}</div>
                    <div style="color: #86868B; font-size: 0.875rem; margin-top: 4px;">Locations</div>
                </div>
            </div>
        `;

        preview.style.display = 'block';
    }

    /**
     * Update Apple target selection display
     */
    function updateAppleTargetSelection() {
        const selectedTargets = document.querySelectorAll('.apple-target-checkbox:checked');
        const countDisplay = document.getElementById('appleTargetCountDisplay');
        const executeBtn = document.getElementById('executeAppleFilterCopyBtn');

        const count = selectedTargets.length;

        if (countDisplay) {
            countDisplay.textContent = count === 0 ? 'No targets selected' :
                count === 1 ? '1 target selected' : `${count} targets selected`;
        }

        if (executeBtn) {
            executeBtn.disabled = count === 0 || !document.getElementById('appleSourceRuleSelect')?.value;
        }

        // Update visual selection
        document.querySelectorAll('.apple-target-rule').forEach(item => {
            const checkbox = item.querySelector('.apple-target-checkbox');
            if (checkbox?.checked) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
    }

    /**
     * Update Apple copy summary
     */
    function updateAppleCopySummary() {
        const sourceSelect = document.getElementById('appleSourceRuleSelect');
        const selectedTargets = document.querySelectorAll('.apple-target-checkbox:checked');
        const copyCategories = document.getElementById('applyCopyCategories')?.checked;
        const copyLocations = document.getElementById('applyCopyLocations')?.checked;
        const conflictResolution = document.querySelector('input[name="appleConflictResolution"]:checked')?.value;

        const summary = document.getElementById('appleCopySummary');
        const summaryContent = document.getElementById('appleCopySummaryContent');

        if (!sourceSelect?.value || selectedTargets.length === 0 || (!copyCategories && !copyLocations)) {
            summary.style.display = 'none';
            return;
        }

        const sourceOption = sourceSelect.selectedOptions[0];
        const sourceName = sourceOption.dataset.ruleName || 'Unknown';
        const sourceCategories = parseInt(sourceOption.dataset.categories) || 0;
        const sourceLocations = parseInt(sourceOption.dataset.locations) || 0;

        const copyItems = [];
        if (copyCategories && sourceCategories > 0) copyItems.push(`${sourceCategories} categories`);
        if (copyLocations && sourceLocations > 0) copyItems.push(`${sourceLocations} locations`);

        summaryContent.innerHTML = `
            <div style="margin-bottom: 16px; line-height: 1.5;">
                <div style="margin-bottom: 8px;"><strong style="color: #1D1D1F;">Source:</strong> <span style="color: #86868B;">${escapeHtml(sourceName)}</span></div>
                <div style="margin-bottom: 8px;"><strong style="color: #1D1D1F;">Targets:</strong> <span style="color: #86868B;">${selectedTargets.length} rule${selectedTargets.length !== 1 ? 's' : ''}</span></div>
                <div style="margin-bottom: 8px;"><strong style="color: #1D1D1F;">Copy:</strong> <span style="color: #86868B;">${copyItems.join(' and ') || 'Nothing selected'}</span></div>
                <div><strong style="color: #1D1D1F;">Resolution:</strong> <span style="color: #86868B;">${conflictResolution || 'Not selected'}</span></div>
            </div>
            <div style="padding: 12px; background: rgba(255, 149, 0, 0.1); border-radius: 12px; font-size: 0.875rem; color: #E65100;">
                <i class="fas fa-info-circle me-1"></i>
                This will copy the selected filters from <strong>${escapeHtml(sourceName)}</strong> to ${selectedTargets.length} target rule${selectedTargets.length !== 1 ? 's' : ''}.
            </div>
        `;

        summary.style.display = 'block';
    }

    // Apple quick action functions
    window.selectAllAppleTargets = function() {
        document.querySelectorAll('.apple-target-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        updateAppleTargetSelection();
        updateAppleCopySummary();
    };

    window.clearAllAppleTargets = function() {
        document.querySelectorAll('.apple-target-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        updateAppleTargetSelection();
        updateAppleCopySummary();
    };

    window.selectEmptyAppleTargets = function() {
        document.querySelectorAll('.apple-target-checkbox').forEach(checkbox => {
            const ruleItem = checkbox.closest('.apple-target-rule');
            const ruleId = parseInt(ruleItem.dataset.ruleId);
            const rule = window.currentAppleFilterRules?.find(r => r.id === ruleId);

            const hasData = (rule?.categories && rule.categories.length > 0) ||
                           (rule?.locations && rule.locations.length > 0);

            checkbox.checked = !hasData;
        });
        updateAppleTargetSelection();
        updateAppleCopySummary();
    };

    window.selectWithDataAppleTargets = function() {
        document.querySelectorAll('.apple-target-checkbox').forEach(checkbox => {
            const ruleItem = checkbox.closest('.apple-target-rule');
            const ruleId = parseInt(ruleItem.dataset.ruleId);
            const rule = window.currentAppleFilterRules?.find(r => r.id === ruleId);

            const hasData = (rule?.categories && rule.categories.length > 0) ||
                           (rule?.locations && rule.locations.length > 0);

            checkbox.checked = hasData;
        });
        updateAppleTargetSelection();
        updateAppleCopySummary();
    };

    window.toggleAppleTargetRule = function(ruleId) {
        const checkbox = document.querySelector(`.apple-target-checkbox[value="${ruleId}"]`);
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
            updateAppleTargetSelection();
            updateAppleCopySummary();
        }
    };

    window.toggleAppleCopyOption = function(option) {
        const checkbox = document.getElementById(`applyCopy${option.charAt(0).toUpperCase() + option.slice(1)}`);
        const optionElement = checkbox?.closest('.apple-option');

        if (checkbox && optionElement) {
            checkbox.checked = !checkbox.checked;

            if (checkbox.checked) {
                optionElement.classList.add('selected');
            } else {
                optionElement.classList.remove('selected');
            }

            updateAppleCopySummary();
        }
    };

    window.selectAppleConflictResolution = function(resolution) {
        // Update radio button
        const radio = document.getElementById(`apple${resolution.charAt(0).toUpperCase() + resolution.slice(1)}Data`);
        if (radio) {
            radio.checked = true;
        }

        // Update visual selection
        document.querySelectorAll('.apple-conflict-option').forEach(option => {
            option.classList.remove('selected');
        });

        const selectedOption = document.querySelector(`[onclick="selectAppleConflictResolution('${resolution}')"]`);
        if (selectedOption) {
            selectedOption.classList.add('selected');
        }

        updateAppleCopySummary();
    };

    /**
     * Execute the Apple filter copy operation
     */
    async function executeAppleFilterCopy() {
        try {
            const sourceRuleId = document.getElementById('appleSourceRuleSelect')?.value;
            const selectedTargets = Array.from(document.querySelectorAll('.apple-target-checkbox:checked')).map(cb => parseInt(cb.value));
            const copyCategories = document.getElementById('applyCopyCategories')?.checked;
            const copyLocations = document.getElementById('applyCopyLocations')?.checked;
            const conflictResolution = document.querySelector('input[name="appleConflictResolution"]:checked')?.value;
            const provider = window.currentAppleFilterProvider;

            if (!sourceRuleId || selectedTargets.length === 0 || !provider) {
                await Swal.fire({
                    html: `
                        <div style="text-align: center; padding: 40px 20px;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #FF9500 0%, #FF6B35 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto;">
                                <i class="fas fa-exclamation" style="color: white; font-size: 2rem;"></i>
                            </div>
                            <h2 style="color: #1D1D1F; font-weight: 700; margin-bottom: 12px; font-size: 1.5rem;">Incomplete Selection</h2>
                            <p style="color: #86868B; font-size: 1rem; margin: 0; line-height: 1.5;">Please select a source rule and at least one target rule.</p>
                        </div>
                    `,
                    showConfirmButton: true,
                    confirmButtonText: 'Got it',
                    confirmButtonColor: '#007AFF',
                    customClass: {
                        popup: 'apple-alert-popup'
                    },
                    width: '400px'
                });
                return;
            }

            // Show Apple progress
            Swal.fire({
                html: `
                    <div style="text-align: center; padding: 50px 30px;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto; animation: pulse 2s infinite;">
                            <i class="fas fa-copy" style="color: white; font-size: 2rem;"></i>
                        </div>
                        <h2 style="color: #1D1D1F; font-weight: 700; margin-bottom: 12px; font-size: 1.5rem;">Copying Filters</h2>
                        <p style="color: #86868B; font-size: 1rem; margin: 0; line-height: 1.5;">
                            Copying filters from rule ${sourceRuleId} to ${selectedTargets.length} target rule${selectedTargets.length !== 1 ? 's' : ''}...
                        </p>
                    </div>
                    <style>
                        @keyframes pulse {
                            0% { transform: scale(1); }
                            50% { transform: scale(1.05); }
                            100% { transform: scale(1); }
                        }
                    </style>
                `,
                allowOutsideClick: false,
                showConfirmButton: false,
                customClass: {
                    popup: 'apple-alert-popup'
                },
                width: '400px'
            });

            const response = await fetch(`${API_BASE}/copy-filters`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': CSRF_TOKEN,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    source_rule_id: parseInt(sourceRuleId),
                    target_rule_ids: selectedTargets,
                    clone_categories: copyCategories,
                    clone_locations: copyLocations,
                    conflict_resolution: conflictResolution,
                    provider: provider
                })
            });

            const data = await response.json();

            if (data.success) {
                // Show Apple success with detailed results
                const summary = data.data.summary;

                await Swal.fire({
                    html: `
                        <div style="text-align: center; padding: 40px 30px;">
                            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #34C759 0%, #30D158 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto;">
                                <i class="fas fa-check" style="color: white; font-size: 2rem;"></i>
                            </div>
                            <h2 style="color: #1D1D1F; font-weight: 700; margin-bottom: 16px; font-size: 1.5rem;">Filters Copied Successfully!</h2>

                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin: 24px 0; max-width: 300px; margin-left: auto; margin-right: auto;">
                                <div style="padding: 16px; background: rgba(52, 199, 89, 0.1); border-radius: 12px;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #34C759;">${summary.successful}</div>
                                    <div style="color: #86868B; font-size: 0.8rem; margin-top: 4px;">Successful</div>
                                </div>
                                <div style="padding: 16px; background: rgba(255, 149, 0, 0.1); border-radius: 12px;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #FF9500;">${summary.skipped}</div>
                                    <div style="color: #86868B; font-size: 0.8rem; margin-top: 4px;">Skipped</div>
                                </div>
                                <div style="padding: 16px; background: rgba(255, 59, 48, 0.1); border-radius: 12px;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #FF3B30;">${summary.errors}</div>
                                    <div style="color: #86868B; font-size: 0.8rem; margin-top: 4px;">Errors</div>
                                </div>
                            </div>

                            <p style="color: #86868B; font-size: 1rem; margin: 0; line-height: 1.5;">
                                ${data.message}
                            </p>
                        </div>
                    `,
                    showConfirmButton: true,
                    confirmButtonText: 'Perfect!',
                    confirmButtonColor: '#34C759',
                    customClass: {
                        popup: 'apple-alert-popup'
                    },
                    width: '500px'
                });

                // Refresh the rules data to show updated filters
                await loadBulkData(1, false);

            } else {
                throw new Error(data.message || 'Copy operation failed');
            }

        } catch (error) {
            console.error('❌ Error copying filters:', error);

            const result = await Swal.fire({
                html: `
                    <div style="text-align: center; padding: 40px 20px;">
                        <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #FF3B30 0%, #FF6B35 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 24px auto;">
                            <i class="fas fa-times" style="color: white; font-size: 2rem;"></i>
                        </div>
                        <h2 style="color: #1D1D1F; font-weight: 700; margin-bottom: 12px; font-size: 1.5rem;">Copy Failed</h2>
                        <p style="color: #86868B; font-size: 1rem; margin: 0; line-height: 1.5;">${error.message || 'An unexpected error occurred while copying filters.'}</p>
                    </div>
                `,
                showConfirmButton: true,
                confirmButtonText: 'Try Again',
                confirmButtonColor: '#FF3B30',
                customClass: {
                    popup: 'apple-alert-popup'
                },
                width: '400px'
            });

            // If user clicks "Try Again", reopen the modal
            if (result.isConfirmed) {
                openAppleFilterCopyModal();
            }
        }
    }

    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    window.openLocationManager = openLocationManager;
    window.openCloneFiltersModal = openCloneFiltersModal;
    window.openAppleFilterCopyModal = openAppleFilterCopyModal;
    window.executeAppleFilterCopy = executeAppleFilterCopy;

})(); // End IIFE
</script>
@endpush
