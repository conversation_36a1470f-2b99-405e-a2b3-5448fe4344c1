---
type: "always_apply"
---

# <PERSON><PERSON> Backend Development Standards

This document outlines the mandatory standards and best practices for all backend development. It combines general Laravel principles with project-specific conventions.

## 1. Core Principles & Coding Standards

-   **Strict Typing**: All PHP files MUST start with `declare(strict_types=1);`.
-   **PSR-12**: All code MUST adhere to the [PSR-12 coding standard](mdc:https:/www.php-fig.org/psr/psr-12).
-   **Final by Default**: All classes (Controllers, Models, Services, etc.) SHOULD be declared as `final` unless specifically designed for extension.
-   **SOLID Principles**: Code should be structured following SOLID principles to ensure it is maintainable, scalable, and testable.
-   **Clarity Over Brevity**: Write clean, readable code with descriptive names for variables, methods, and classes. Avoid overly clever or "magic" code that obscures intent.

## 2. Modular Architecture (`nwidart/laravel-modules`)

This project uses the `nwidart/laravel-modules` package. All new functionality must be encapsulated within a module.

### Module Directory Structure

Adhere strictly to the following structure within each module's directory (`Modules/<ModuleName>/`):

-   **Configuration**: `Config/` - Module-specific configuration files.
-   **Console Commands**: `Console/` - Custom Artisan commands.
-   **Database Schema**: `Database/` - Directory for raw SQL schema files.
-   **Models (`Entities`)**: `Entities/` - All Eloquent models reside here.
    -   Example: `[Modules/Admission/Entities/Admission.php](mdc:Modules/Admission/Entities/Admission.php)`
-   **Controllers**: `Http/Controllers/` - All module-specific controllers.
-   **Routes**:
    -   Web: `Http/routes.php`
    -   API: `Http/api.php`
-   **Service Providers**: `Providers/` - Where module services, routes, and views are registered.
-   **Repositories**: `Repositories/` - Data access layer logic.
-   **Tests**: `Tests/` - Unit and Feature tests for the module.

### **CRITICAL: Module View Location**

-   Module Blade views are **NOT** located in the default `Modules/<ModuleName>/Resources/views/` directory.
-   All module views **MUST** be placed in: `[resources/views/modules/<module-name-lowercase>/](mdc:resources/views/modules)`
-   The module's service provider (e.g., `[HumanResourceServiceProvider.php](mdc:Modules/HumanResource/Providers/HumanResourceServiceProvider.php)`) is configured to load views from this path.
-   **This is a non-negotiable project standard.**

## 3. Database Interactions

-   **NO MIGRATIONS**: Do not use Laravel's migration system.
-   **Raw SQL for Schema**: All database schema changes (new tables, columns, indexes) **MUST** be written as raw SQL files.
-   **SQL File Location**: Store schema change files in the module's `Modules/<ModuleName>/Database/` directory.
-   **Eloquent for Data**: Use the Eloquent ORM for all application-level data manipulation (CRUD). Leverage Eloquent relationships, scopes, and accessors/mutators. Avoid raw SQL queries for data access unless absolutely necessary for performance.

## 4. Critical Safety & Project Rules

-   **NEVER use `RefreshDatabase`**: The `Illuminate\Foundation\Testing\RefreshDatabase` trait is **STRICTLY FORBIDDEN**. It can cause irreversible data loss. Use database transactions for tests instead.
-   **NEVER modify `.env` programmatically**: The `.env` file must not be modified by application code. Use `config()` files for configuration management.
-   **Use `EmailService`**: All email sending **MUST** go through the centralized `[EmailService](mdc:app/Services/EmailService.php)`. Do not use Laravel's `Mail` facade directly.

## 5. Key Patterns & Practices

-   **Validation**: Use Form Request validation classes for all incoming data.
-   **Services & Repositories**: Employ the Service and Repository patterns to separate business logic from data access and controller layers.
-   **Error Handling & Logging**: Use Laravel's exception handling and logging facilities. Write meaningful log messages with context to aid in debugging.
-   **Dependency Injection**: Use constructor and method injection to manage dependencies. Let Laravel's service container do the work.




