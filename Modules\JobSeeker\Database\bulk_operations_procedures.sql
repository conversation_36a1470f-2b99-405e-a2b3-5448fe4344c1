-- =====================================================
-- Bulk Operations Stored Procedures for Command Schedule Rules
-- =====================================================

DELIMITER $$

-- =====================================================
-- Procedure: sp_bulk_update_schedule_rules
-- Purpose: Efficiently update multiple schedule rules in a single transaction
-- =====================================================
DROP PROCEDURE IF EXISTS sp_bulk_update_schedule_rules$$

CREATE PROCEDURE sp_bulk_update_schedule_rules(
    IN p_rule_ids JSON,
    IN p_field_name VARCHAR(100),
    IN p_field_value TEXT,
    IN p_updated_by VARCHAR(255)
)
BEGIN
    DECLARE v_rule_id INT;
    DECLARE v_done INT DEFAULT FALSE;
    DECLARE v_updated_count INT DEFAULT 0;
    DECLARE v_error_message TEXT DEFAULT '';
    
    -- Cursor to iterate through rule IDs
    DECLARE rule_cursor CURSOR FOR 
        SELECT JSON_UNQUOTE(JSON_EXTRACT(p_rule_ids, CONCAT('$[', idx, ']'))) as rule_id
        FROM (
            SELECT 0 as idx UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION 
            SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION
            SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION
            SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION
            SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION
            SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29
        ) as indices
        WHERE idx < JSON_LENGTH(p_rule_ids);
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        GET DIAGNOSTICS CONDITION 1 v_error_message = MESSAGE_TEXT;
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    OPEN rule_cursor;
    
    rule_loop: LOOP
        FETCH rule_cursor INTO v_rule_id;
        IF v_done THEN
            LEAVE rule_loop;
        END IF;
        
        -- Dynamic update based on field name
        CASE p_field_name
            WHEN 'name' THEN
                UPDATE command_schedule_rules 
                SET name = p_field_value, updated_by = p_updated_by, updated_at = NOW()
                WHERE id = v_rule_id;
                
            WHEN 'schedule_expression' THEN
                UPDATE command_schedule_rules 
                SET schedule_expression = p_field_value, updated_by = p_updated_by, updated_at = NOW()
                WHERE id = v_rule_id;
                
            WHEN 'priority' THEN
                UPDATE command_schedule_rules 
                SET priority = CAST(p_field_value AS SIGNED), updated_by = p_updated_by, updated_at = NOW()
                WHERE id = v_rule_id;
                
            WHEN 'is_active' THEN
                UPDATE command_schedule_rules 
                SET is_active = CASE WHEN p_field_value IN ('1', 'true', 'TRUE') THEN 1 ELSE 0 END,
                    updated_by = p_updated_by, updated_at = NOW()
                WHERE id = v_rule_id;
                
            WHEN 'description' THEN
                UPDATE command_schedule_rules 
                SET description = p_field_value, updated_by = p_updated_by, updated_at = NOW()
                WHERE id = v_rule_id;
                
            WHEN 'provider_category_ids' THEN
                UPDATE command_schedule_rules 
                SET provider_category_ids = CAST(p_field_value AS JSON), updated_by = p_updated_by, updated_at = NOW()
                WHERE id = v_rule_id;
                
            WHEN 'provider_location_ids' THEN
                UPDATE command_schedule_rules 
                SET provider_location_ids = CAST(p_field_value AS JSON), updated_by = p_updated_by, updated_at = NOW()
                WHERE id = v_rule_id;
                
            ELSE
                -- Invalid field name
                SET v_error_message = CONCAT('Invalid field name: ', p_field_name);
                SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = v_error_message;
        END CASE;
        
        SET v_updated_count = v_updated_count + ROW_COUNT();
    END LOOP;
    
    CLOSE rule_cursor;
    
    COMMIT;
    
    -- Return success result
    SELECT v_updated_count as updated_count, 'SUCCESS' as status, '' as error_message;
    
END$$

-- =====================================================
-- Procedure: sp_clone_schedule_rules
-- Purpose: Clone rules from one provider to another with time offset
-- =====================================================
DROP PROCEDURE IF EXISTS sp_clone_schedule_rules$$

CREATE PROCEDURE sp_clone_schedule_rules(
    IN p_from_command VARCHAR(255),
    IN p_to_command VARCHAR(255),
    IN p_time_offset_minutes INT,
    IN p_created_by VARCHAR(255),
    IN p_rule_ids JSON
)
BEGIN
    DECLARE v_rule_id INT;
    DECLARE v_done INT DEFAULT FALSE;
    DECLARE v_cloned_count INT DEFAULT 0;
    DECLARE v_error_message TEXT DEFAULT '';
    
    -- Variables for rule data
    DECLARE v_name VARCHAR(255);
    DECLARE v_schedule_expression VARCHAR(255);
    DECLARE v_schedule_type ENUM('cron','daily_at','weekly_at','custom');
    DECLARE v_days_of_week JSON;
    DECLARE v_time_slots JSON;
    DECLARE v_timezone VARCHAR(50);
    DECLARE v_is_active TINYINT(1);
    DECLARE v_priority INT;
    DECLARE v_description TEXT;
    DECLARE v_depends_on_command VARCHAR(255);
    DECLARE v_delay_after_dependency INT;
    DECLARE v_max_execution_time INT;
    DECLARE v_execution_timeout INT;
    DECLARE v_concurrent_executions INT;
    DECLARE v_provider_category_ids JSON;
    DECLARE v_provider_location_ids JSON;
    
    -- Cursor for rules to clone
    DECLARE rule_cursor CURSOR FOR 
        SELECT id, name, schedule_expression, schedule_type, days_of_week, time_slots,
               timezone, is_active, priority, description, depends_on_command,
               delay_after_dependency, max_execution_time, execution_timeout,
               concurrent_executions, provider_category_ids, provider_location_ids
        FROM command_schedule_rules 
        WHERE command = p_from_command
        AND (p_rule_ids IS NULL OR JSON_CONTAINS(p_rule_ids, CAST(id AS JSON)));
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        GET DIAGNOSTICS CONDITION 1 v_error_message = MESSAGE_TEXT;
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    OPEN rule_cursor;
    
    clone_loop: LOOP
        FETCH rule_cursor INTO v_rule_id, v_name, v_schedule_expression, v_schedule_type,
                              v_days_of_week, v_time_slots, v_timezone, v_is_active,
                              v_priority, v_description, v_depends_on_command,
                              v_delay_after_dependency, v_max_execution_time,
                              v_execution_timeout, v_concurrent_executions,
                              v_provider_category_ids, v_provider_location_ids;
        
        IF v_done THEN
            LEAVE clone_loop;
        END IF;
        
        -- Create cloned rule with modified schedule expression
        INSERT INTO command_schedule_rules (
            name, command, schedule_expression, schedule_type, days_of_week, time_slots,
            timezone, is_active, priority, description, depends_on_command,
            delay_after_dependency, max_execution_time, execution_timeout,
            concurrent_executions, provider_category_ids, provider_location_ids,
            created_by, updated_by, created_at, updated_at
        ) VALUES (
            CONCAT(v_name, ' (Cloned)'),
            p_to_command,
            fn_add_time_offset(v_schedule_expression, v_schedule_type, p_time_offset_minutes),
            v_schedule_type,
            v_days_of_week,
            v_time_slots,
            v_timezone,
            v_is_active,
            v_priority,
            CONCAT(IFNULL(v_description, ''), ' (Cloned rule)'),
            v_depends_on_command,
            v_delay_after_dependency,
            v_max_execution_time,
            v_execution_timeout,
            v_concurrent_executions,
            v_provider_category_ids,
            v_provider_location_ids,
            p_created_by,
            p_created_by,
            NOW(),
            NOW()
        );
        
        SET v_cloned_count = v_cloned_count + 1;
    END LOOP;
    
    CLOSE rule_cursor;
    
    COMMIT;
    
    -- Return success result
    SELECT v_cloned_count as cloned_count, 'SUCCESS' as status, '' as error_message;
    
END$$

-- =====================================================
-- Function: fn_add_time_offset
-- Purpose: Add time offset to schedule expressions
-- =====================================================
DROP FUNCTION IF EXISTS fn_add_time_offset$$

CREATE FUNCTION fn_add_time_offset(
    p_schedule_expression VARCHAR(255),
    p_schedule_type VARCHAR(50),
    p_offset_minutes INT
) RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_result VARCHAR(255);
    DECLARE v_hour INT;
    DECLARE v_minute INT;
    DECLARE v_new_hour INT;
    DECLARE v_new_minute INT;
    DECLARE v_total_minutes INT;
    
    CASE p_schedule_type
        WHEN 'daily_at' THEN
            -- Handle format like "09:00"
            SET v_hour = CAST(SUBSTRING_INDEX(p_schedule_expression, ':', 1) AS SIGNED);
            SET v_minute = CAST(SUBSTRING_INDEX(p_schedule_expression, ':', -1) AS SIGNED);
            SET v_total_minutes = (v_hour * 60) + v_minute + p_offset_minutes;
            SET v_new_hour = FLOOR(v_total_minutes / 60) % 24;
            SET v_new_minute = v_total_minutes % 60;
            SET v_result = CONCAT(LPAD(v_new_hour, 2, '0'), ':', LPAD(v_new_minute, 2, '0'));
            
        WHEN 'cron' THEN
            -- Handle cron format like "0 9 * * *"
            SET v_minute = CAST(SUBSTRING_INDEX(p_schedule_expression, ' ', 1) AS SIGNED);
            SET v_hour = CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(p_schedule_expression, ' ', 2), ' ', -1) AS SIGNED);
            SET v_total_minutes = (v_hour * 60) + v_minute + p_offset_minutes;
            SET v_new_hour = FLOOR(v_total_minutes / 60) % 24;
            SET v_new_minute = v_total_minutes % 60;
            
            SET v_result = CONCAT(
                v_new_minute, ' ', v_new_hour, ' ',
                SUBSTRING(p_schedule_expression, LOCATE(' ', p_schedule_expression, LOCATE(' ', p_schedule_expression) + 1) + 1)
            );
            
        ELSE
            -- Return original for unsupported types
            SET v_result = p_schedule_expression;
    END CASE;
    
    RETURN v_result;
END$$

-- =====================================================
-- Procedure: sp_get_bulk_edit_data
-- Purpose: Efficiently fetch all data needed for bulk editing interface
-- =====================================================
DROP PROCEDURE IF EXISTS sp_get_bulk_edit_data$$

CREATE PROCEDURE sp_get_bulk_edit_data()
BEGIN
    SELECT 
        csr.id,
        csr.name,
        csr.command,
        CASE 
            WHEN csr.command LIKE '%jobs-af%' THEN 'jobsaf'
            WHEN csr.command LIKE '%acbar%' THEN 'acbar'
            ELSE 'unknown'
        END as provider,
        csr.schedule_expression,
        csr.schedule_type,
        csr.timezone,
        csr.is_active,
        csr.priority,
        csr.description,
        csr.provider_category_ids,
        csr.provider_location_ids,
        csr.next_run_at,
        csr.created_at,
        csr.updated_at,
        -- Last execution info
        cse.started_at as last_execution_started,
        cse.status as last_execution_status,
        cse.duration_seconds as last_execution_duration
    FROM command_schedule_rules csr
    LEFT JOIN (
        SELECT rule_id, started_at, status, duration_seconds,
               ROW_NUMBER() OVER (PARTITION BY rule_id ORDER BY started_at DESC) as rn
        FROM command_schedule_executions
    ) cse ON csr.id = cse.rule_id AND cse.rn = 1
    ORDER BY csr.priority ASC, csr.created_at ASC;
END$$

DELIMITER ;

-- =====================================================
-- Create indexes for better performance
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_command_schedule_rules_command ON command_schedule_rules(command);
CREATE INDEX IF NOT EXISTS idx_command_schedule_rules_provider_lookup ON command_schedule_rules(command, is_active, priority);
CREATE INDEX IF NOT EXISTS idx_command_schedule_executions_rule_started ON command_schedule_executions(rule_id, started_at DESC);
