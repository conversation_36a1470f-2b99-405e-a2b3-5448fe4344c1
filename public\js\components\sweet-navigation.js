/**
 * Sweet Navigation Component JavaScript
 * Extracted from nouranya.blade.php for reusable navigation functionality
 * 
 * Usage: Add class "sweet-navigation-trigger" to any element with appropriate data attributes:
 * - data-ajax-url: Endpoint to fetch navigation data
 * - data-title: Title for the SweetAlert popup
 * - data-current-id: ID of current item (for highlighting)
 * - data-confirm-button-text: Custom confirm button text (optional)
 * - data-confirm-button-url: URL for confirm button action (optional)
 * - data-width: Custom popup width (optional, default: 800px)
 * 
 * Dependencies: jQuery, SweetAlert2
 */

(function($) {
        'use strict';

        // Global configuration
        window.SweetNavigationConfig = window.SweetNavigationConfig || {
            defaultWidth: '800px',
            animationDuration: 300,
            searchPlaceholder: 'Search...',
            hoverDelay: 200
        };

        // Track dropdown state
        let hoverTimeout;
        let isDropdownOpen = false;

        /**
         * Initialize Sweet Navigation functionality
         * Call this function after DOM is ready
         */
        function initializeSweetNavigation() {
            $('.sweet-navigation-trigger').each(function() {
                const $trigger = $(this);

                // Set up hover event handlers
                $trigger.off('mouseenter.sweet-nav mouseleave.sweet-nav')
                    .on('mouseenter.sweet-nav', function() {
                        handleTriggerHover($trigger);
                    })
                    .on('mouseleave.sweet-nav', function() {
                        clearTimeout(hoverTimeout);
                    });
            });
        }

        /**
         * Handle trigger hover event
         */
        function handleTriggerHover($trigger) {
            const currentId = $trigger.data('current-id');

            // Clear any existing timeout
            clearTimeout(hoverTimeout);

            // Set a small delay to prevent accidental triggers
            hoverTimeout = setTimeout(function() {
                if (!isDropdownOpen) {
                    showSweetNavigation($trigger);
                }
            }, window.SweetNavigationConfig.hoverDelay);
        }

        /**
         * Show the Sweet Navigation dropdown
         */
        function showSweetNavigation($trigger) {
            isDropdownOpen = true;

            // Get configuration from data attributes
            const config = {
                ajaxUrl: $trigger.data('ajax-url'),
                title: $trigger.data('title') || 'Navigation',
                currentId: $trigger.data('current-id'),
                confirmButtonText: $trigger.data('confirm-button-text') || '<i class="fa fa-external-link"></i> Go to Index',
                confirmButtonUrl: $trigger.data('confirm-button-url') || '#',
                width: $trigger.data('width') || window.SweetNavigationConfig.defaultWidth
            };

            // Validate required configuration
            if (!config.ajaxUrl) {
                console.error('Sweet Navigation: data-ajax-url is required');
                return;
            }

            // Show loading state
            Swal.fire({
                title: '<i class="fa fa-spinner fa-spin"></i> Loading...',
                html: '<div style="text-align: center; padding: 20px;">Fetching navigation data...</div>',
                allowOutsideClick: false,
                showConfirmButton: false,
                position: 'top',
                customClass: {
                    popup: 'classes-navigation-alert'
                }
            });

            // Fetch navigation data
            $.ajax({
                url: config.ajaxUrl,
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json'
                },
                data: {
                    current_id: config.currentId
                },
                success: function(response) {
                    if (response.success && response.data) {
                        // Transform original data format to expected format
                        let transformedData = transformResponseData(response.data, config.currentId);
                        displaySweetNavigation(transformedData, config);
                    } else {
                        showErrorMessage('Failed to load navigation data: ' + (response.message || 'Unknown error occurred'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Sweet Navigation Error:', xhr.responseText);
                    let errorMessage = 'Error loading navigation data. Please try again.';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.status === 500) {
                        errorMessage = 'Server error occurred. Please check server logs.';
                    } else if (xhr.status === 404) {
                        errorMessage = 'Navigation data endpoint not found.';
                    }

                    showErrorMessage(errorMessage);
                }
            });
        }

        /**
         * Transform original API response data to expected format
         */
        function transformResponseData(data, currentId) {
            // Check if data is already in the expected format (has groups property)
            if (data.groups) {
                return data;
            }

            // Transform original format to expected format
            let groups = [];

            if (Array.isArray(data)) {
                // Original format: array of program objects with program_title and classes
                data.forEach(function(program) {
                    if (program.classes && program.classes.length > 0) {
                        let items = program.classes.map(function(classItem) {
                            // Build teacher links HTML
                            let teacherLinksHTML = '';
                            if (classItem.teachers_data && classItem.teachers_data.length > 0) {
                                const teacherLinks = classItem.teachers_data.slice(0, 2).map(teacher => {
                                    const employeeShowUrl = `/workplace/human-resource/employees/${teacher.id}`;
                                    return `<a href="${employeeShowUrl}" target="_blank" class="teacher-link">${teacher.name}</a>`;
                                }).join(', ');

                                if (classItem.teachers_data.length > 2) {
                                    teacherLinksHTML = teacherLinks + ` +${classItem.teachers_data.length - 2}`;
                                } else {
                                    teacherLinksHTML = teacherLinks;
                                }
                            } else {
                                teacherLinksHTML = 'No teacher assigned';
                            }

                            // Build subtitle with class details
                            const subtitle = `<i class="fa fa-code"></i> ${classItem.class_code} | <i class="fa fa-map-marker"></i> ${classItem.center_name} | <i class="fa fa-user"></i> ${teacherLinksHTML}`;

                            // Build actions array
                            const actions = [{
                                    type: 'report',
                                    url: classItem.report_url,
                                    title: 'Class Report',
                                    label: 'R'
                                },
                                {
                                    type: 'show',
                                    url: classItem.class_show_url,
                                    title: 'View Class',
                                    label: 'V'
                                },
                                {
                                    type: 'plan',
                                    url: classItem.monthly_plan_url,
                                    title: 'Monthly Plan',
                                    label: 'P'
                                }
                            ];

                            return {
                                id: String(classItem.id),
                                name: classItem.name,
                                url: classItem.class_show_url,
                                is_current: classItem.is_current || (currentId && String(classItem.id) === String(currentId)),
                                subtitle: subtitle,
                                count: `${classItem.student_count} students`,
                                actions: actions,
                                // Additional data for search functionality
                                searchData: {
                                    className: classItem.name.toLowerCase(),
                                    classCode: classItem.class_code.toLowerCase(),
                                    centerName: classItem.center_name.toLowerCase(),
                                    teachers: classItem.teachers.toLowerCase()
                                }
                            };
                        });

                        groups.push({
                            name: `${program.program_title} (${program.classes.length} classes)`,
                            items: items
                        });
                    }
                });
            }

            return { groups: groups };
        }

        /**
         * Display the Sweet Navigation popup with data
         */
        function displaySweetNavigation(data, config) {
            // Build search HTML (matches original nouranya.blade.php structure)
            let searchHTML = `
            <div class="classes-search-container">
                <input type="text" id="classesSearchInput" class="classes-search-input"
                       placeholder="Search classes by name, code, center, or teacher...">
                <i class="fa fa-search classes-search-icon"></i>
            </div>
        `;

            // Build groups HTML
            let groupsHTML = '';

            if (!data.groups || data.groups.length === 0) {
                groupsHTML = '<div class="sweet-navigation-no-items-found">No items found.</div>';
            } else {
                data.groups.forEach(function(group) {
                    groupsHTML += buildGroupHTML(group, config.currentId);
                });
            }

            // Clean up any existing SweetAlert2 instances
            if (Swal.isVisible()) {
                Swal.close();
            }
            $('.swal2-container').remove();
            $('body').removeClass('swal2-shown swal2-height-auto');

            // Create SweetAlert2 configuration
            const swalConfig = {
                title: `<i class="fa fa-list-ul"></i> ${config.title}`,
                html: searchHTML + '<div id="sweetNavigationContainer">' + groupsHTML + '</div>',
                width: config.width,
                position: 'top',
                showCancelButton: true,
                confirmButtonText: config.confirmButtonText,
                cancelButtonText: '<i class="fa fa-times"></i> Close',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showCloseButton: true,
                backdrop: true,
                customClass: {
                    popup: 'classes-navigation-alert'
                },
                didOpen: function(popup) {
                    // Add body class for scoped CSS
                    document.body.classList.add('classes-navigation-active');

                    setupSearchFunctionality();
                    setupItemClickHandlers();
                    setupEventHandlers();

                    // Focus on search input
                    setTimeout(() => {
                        const searchInput = document.getElementById('classesSearchInput');
                        if (searchInput) {
                            searchInput.focus();
                        }
                    }, 100);
                },
                willClose: function() {
                    // Remove body class when closing
                    document.body.classList.remove('classes-navigation-active');
                    isDropdownOpen = false;
                },
                didClose: function() {
                    // Ensure body class is removed
                    document.body.classList.remove('classes-navigation-active');
                    isDropdownOpen = false;
                }
            };

            // Show SweetAlert2
            Swal.fire(swalConfig).then((result) => {
                if (result.isConfirmed && config.confirmButtonUrl !== '#') {
                    if (config.confirmButtonUrl.startsWith('http')) {
                        window.open(config.confirmButtonUrl, '_blank');
                    } else {
                        window.location.href = config.confirmButtonUrl;
                    }
                }
                isDropdownOpen = false;
            }).catch((error) => {
                console.error('Sweet Navigation SweetAlert2 error:', error);
                isDropdownOpen = false;
            });
        }

        /**
         * Build HTML for a navigation group (matches original nouranya.blade.php structure)
         */
        function buildGroupHTML(group, currentId) {
            let groupHTML = `
            <div class="program-group" data-program-title="${group.name.toLowerCase()}">
                <div class="program-header" onclick="toggleProgramGroup(this)">
                    <span><i class="fa fa-graduation-cap"></i> ${group.name}</span>
                    <i class="fa fa-chevron-down toggle-icon"></i>
                </div>
                <div class="classes-list">
        `;

            group.items.forEach(function(item) {
                        const isCurrentClass = item.is_current || (currentId && item.id == currentId) ? 'current-class' : '';

                        // Build search data attributes
                        const searchData = item.searchData || {};
                        const searchAttributes = `
                            data-class-name="${searchData.className || item.name.toLowerCase()}"
                            data-class-code="${searchData.classCode || ''}"
                            data-center-name="${searchData.centerName || ''}"
                            data-teachers="${searchData.teachers || ''}"
                        `;

                        groupHTML += `
                <div class="class-item ${isCurrentClass}"
                     data-class-id="${item.id}"
                     ${searchAttributes}>
                    <div class="class-main-info">
                        <div class="class-name">${item.name}</div>
                        ${item.subtitle ? `<div class="class-details">${item.subtitle}</div>` : ''}
                    </div>
                    <div class="class-meta">
                        ${item.count ? `<span class="student-count">${item.count}</span>` : ''}
                        ${item.actions ? buildClassActionsHTML(item.actions) : ''}
                    </div>
                </div>
            `;
        });

        groupHTML += `
                </div>
            </div>
        `;

        return groupHTML;
    }

    /**
     * Build HTML for class actions (matches original nouranya.blade.php structure)
     */
    function buildClassActionsHTML(actions) {
        if (!actions || actions.length === 0) return '';

        let actionsHTML = '<div class="class-actions">';
        actions.forEach(function(action) {
            actionsHTML += `<a href="${action.url}" target="_blank" class="class-action-btn action-${action.type}" title="${action.title}">${action.label}</a>`;
        });
        actionsHTML += '</div>';

        return actionsHTML;
    }

    /**
     * Setup search functionality within the popup (matches original nouranya.blade.php)
     */
    function setupSearchFunctionality() {
        $('#classesSearchInput').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();

            if (searchTerm === '') {
                // Show all groups and items
                $('.program-group').show();
                $('.class-item').show();
                updateGroupCounters();
            } else {
                // Hide all items first
                $('.class-item').hide();
                $('.program-group').hide();

                // Show matching items based on class data attributes
                $('.class-item').each(function() {
                    const $item = $(this);
                    const className = $item.data('class-name') || '';
                    const classCode = $item.data('class-code') || '';
                    const centerName = $item.data('center-name') || '';
                    const teachers = $item.data('teachers') || '';

                    if (className.includes(searchTerm) ||
                        classCode.includes(searchTerm) ||
                        centerName.includes(searchTerm) ||
                        teachers.includes(searchTerm)) {
                        $item.show();
                        $item.closest('.program-group').show();
                    }
                });

                updateGroupCounters();
            }
        });
    }

    /**
     * Setup click handlers for class items (matches original nouranya.blade.php)
     */
    function setupItemClickHandlers() {
        $('.class-item').off('click.sweet-nav').on('click.sweet-nav', function(e) {
            // Don't interfere with action button clicks
            if ($(e.target).hasClass('class-action-btn')) {
                return true;
            }

            // Prevent event from bubbling up to SweetAlert2
            e.stopPropagation();

            // Get the class URL from data attribute or build default
            const classId = $(this).data('class-id');
            if (classId) {
                // Try to get the URL from the first 'show' action button, otherwise use default
                const showActionBtn = $(this).find('.action-show');
                let classUrl;

                if (showActionBtn.length > 0) {
                    classUrl = showActionBtn.attr('href');
                } else {
                    classUrl = `/workplace/education/classes/${classId}`;
                }

                // Always open in new tab
                window.open(classUrl, '_blank');
            }
        });
    }

    /**
     * Setup event handlers for closing the popup (NUCLEAR approach from original)
     */
    function setupEventHandlers() {
        console.log('🎯 DEBUGGING: Setting up event handlers');

        // Test if elements exist
        const backdrop = document.querySelector('.swal2-backdrop');
        const closeBtn = document.querySelector('.swal2-close');
        console.log('🎯 DEBUGGING: Backdrop exists:', !!backdrop);
        console.log('🎯 DEBUGGING: Close button exists:', !!closeBtn);

        // Method 1: Direct event listeners on actual DOM elements
        if (backdrop) {
            backdrop.addEventListener('click', function(e) {
                console.log('🎯 DEBUGGING: Backdrop clicked!');
                if (e.target === backdrop) {
                    console.log('🎯 DEBUGGING: Closing via backdrop click');
                    Swal.close();
                }
            }, true); // Use capture phase
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
                console.log('🎯 DEBUGGING: Close button clicked - FORCE closing!');
                e.preventDefault();
                e.stopPropagation();

                // NUCLEAR OPTION: Force remove SweetAlert2 DOM elements
                const swalContainer = document.querySelector('.swal2-container');
                const swalBackdrop = document.querySelector('.swal2-backdrop');

                if (swalContainer) {
                    console.log('🎯 FORCE: Removing SweetAlert2 container');
                    swalContainer.remove();
                }

                if (swalBackdrop) {
                    console.log('🎯 FORCE: Removing SweetAlert2 backdrop');
                    swalBackdrop.remove();
                }

                // Reset body classes
                document.body.classList.remove('swal2-shown', 'swal2-height-auto', 'classes-navigation-active');
                isDropdownOpen = false;

                console.log('🎯 FORCE: Close button - SweetAlert2 forcefully closed');
            }, true); // Use capture phase
        }

        // Method 2: NUCLEAR ESC key handler - Force close SweetAlert2
        document.addEventListener('keydown', function escHandler(e) {
            console.log('🎯 DEBUGGING: Key pressed:', e.key, e.keyCode);
            if (e.key === 'Escape' || e.keyCode === 27) {
                console.log('🎯 DEBUGGING: ESC key detected, FORCE closing SweetAlert2');
                e.preventDefault();
                e.stopImmediatePropagation();

                // NUCLEAR OPTION: Force remove SweetAlert2 DOM elements
                const swalContainer = document.querySelector('.swal2-container');
                const swalBackdrop = document.querySelector('.swal2-backdrop');

                if (swalContainer) {
                    console.log('🎯 FORCE: Removing SweetAlert2 container');
                    swalContainer.remove();
                }

                if (swalBackdrop) {
                    console.log('🎯 FORCE: Removing SweetAlert2 backdrop');
                    swalBackdrop.remove();
                }

                // Reset body classes
                document.body.classList.remove('swal2-shown', 'swal2-height-auto', 'classes-navigation-active');

                // Clean up event handlers
                document.removeEventListener('keydown', escHandler);

                // Set dropdown state
                isDropdownOpen = false;

                console.log('🎯 FORCE: SweetAlert2 forcefully closed');
            }
        }, true); // Use capture phase

        // Method 3: NUCLEAR outside click handler
        document.addEventListener('click', function outsideClickHandler(e) {
            const popup = document.querySelector('.swal2-popup');
            if (popup && !popup.contains(e.target)) {
                console.log('🎯 DEBUGGING: Outside click detected - FORCE closing!');

                // NUCLEAR OPTION: Force remove SweetAlert2 DOM elements
                const swalContainer = document.querySelector('.swal2-container');
                if (swalContainer) {
                    swalContainer.remove();
                }

                // Reset body classes
                document.body.classList.remove('swal2-shown', 'swal2-height-auto', 'classes-navigation-active');
                isDropdownOpen = false;

                // Clean up event handler
                document.removeEventListener('click', outsideClickHandler);

                console.log('🎯 FORCE: Outside click - SweetAlert2 forcefully closed');
            }
        }, true);
    }

    /**
     * Update group counters based on visible items (matches original nouranya.blade.php)
     */
    function updateGroupCounters() {
        $('.program-group').each(function() {
            const $group = $(this);
            const visibleItems = $group.find('.class-item:visible').length;
            const totalItems = $group.find('.class-item').length;

            if (visibleItems === 0) {
                $group.hide();
            } else {
                $group.show();
                const groupName = $group.find('.program-header span').text().split('(')[0].trim();
                $group.find('.program-header span').html(`<i class="fa fa-graduation-cap"></i> ${groupName} (${visibleItems} of ${totalItems} classes)`);
            }
        });
    }

    /**
     * Show error message using SweetAlert2
     */
    function showErrorMessage(message) {
        isDropdownOpen = false;
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message,
            customClass: {
                popup: 'classes-navigation-alert'
            }
        });
    }

    /**
     * Global function for toggling program groups (matches original nouranya.blade.php)
     * This needs to be global so it can be called from onclick attributes
     */
    window.toggleProgramGroup = function(header) {
        const $header = $(header);
        const $classesList = $header.next('.classes-list');
        const $toggleIcon = $header.find('.toggle-icon');

        if ($classesList.is(':visible')) {
            $classesList.slideUp(window.SweetNavigationConfig.animationDuration);
            $header.addClass('collapsed');
            $toggleIcon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
        } else {
            $classesList.slideDown(window.SweetNavigationConfig.animationDuration);
            $header.removeClass('collapsed');
            $toggleIcon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
        }
    };

    /**
     * Public API - expose the initialization function
     */
    window.initializeSweetNavigation = initializeSweetNavigation;

    /**
     * Auto-initialize when DOM is ready (if jQuery is available)
     */
    $(document).ready(function() {
        if (typeof $ !== 'undefined') {
            initializeSweetNavigation();
        }
    });

})(jQuery);