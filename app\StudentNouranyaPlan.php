<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class StudentNouranyaPlan extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'student_nouranya_plans';
    protected $casts = ['start_date' => 'date', 'created_at' => 'datetime'];

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    protected $fillable = [
        'updated_at',
        'class_id',
        'organization_id',
        'student_id',
        'level_id',
        'plan_year_and_month',
        'start_date',
        'end_date',
        'from_lesson',
        'to_lesson',
        'center_id',
        'status',
        'approved_by',
        'supervisor_comment',
        'created_by',
        'updated_by',
        'delete_reason',
        'deleted_at',
        'talqeen_talaqqi',
        'talaqqi_from_lesson',
        'talqeen_from_lesson',
        'talaqqi_to_lesson',
        'talqeen_to_lesson',
        'from_lesson_line_number',
        'to_lesson_line_number',
    ];

    protected static function boot()
    {


        parent::boot();

        static::creating(function ($model) {
            $model->created_by = is_object(\Auth::guard('employee')->user()) ? \Auth::guard('employee')->user()->id : 1;
            $model->updated_by = NULL;
        });

        static::updating(function ($model) {
            $model->updated_by = is_object(\Auth::guard('employee')->user()) ? \Auth::guard('employee')->user()->id : 1;
        });
    }

    public function center()
    {
        return $this->belongsTo('App\Center');
    }

    public function creator()
    {

        return $this->belongsTo(Employee::class, 'created_by');
    }

    public function updator()
    {

        return $this->belongsTo(Employee::class, 'updated_by');
    }


    public function student()
    {

        return $this->belongsTo(Student::class,'student_id','id');
    }


    public function teacher()
    {

        return $this->belongsTo(Employee::class, 'teacher_id', 'id');
    }

    public function approver()
    {

        return $this->belongsTo(Employee::class, 'approved_by', 'id');
    }


    public function halaqah()
    {


        return $this->belongsTo(Classes::class, 'class_id', 'id', 'student_hefz_plans');
//        return $this->belongsTo('App\Classes');

    }

    function programLevel()
    {

        return $this->belongsTo(ProgramLevel::class, 'level_id', 'id');
    }




}
