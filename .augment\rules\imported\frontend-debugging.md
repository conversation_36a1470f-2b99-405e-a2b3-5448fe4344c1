---
type: "always_apply"
---

- **Systematic Approach**
  - Read the exact error message carefully.
  - Identify the file and line number.
  - Look for **common patterns** (typos, type mismatches, etc.).

- **Common Issue Patterns**
  - **CORS errors** → Usually need proper headers or proxy setup.
  - **Hydration mismatches** → Server/client rendering differences.
  - **Routing problems** → Verify Laravel route names, prefixes, and navigation logic.

- **Debugging Hierarchy**
  1. **Console errors** – address immediate blockers first.
  2. **Network failures** – API/resource loading issues.
  3. **Logic errors** – incorrect behaviour.
  4. **UI/UX issues** – visual or interaction problems.
  5. **Performance optimisations**.

- **Tools & Techniques**
  - Add **strategic console.log** statements to trace execution flow.
  - Use the **browser network panel** for API debugging.
  - Test **responsive breakpoints** across devices.
  - Perform thorough **responsive design testing**.

- **Progressive Problem Solving**
  - Start with the **simplest possible fix**.
  - **Don't over-engineer** solutions.
  - Break complex problems into **smaller parts**.
  - **Always verify** the fix actually works before moving on.

