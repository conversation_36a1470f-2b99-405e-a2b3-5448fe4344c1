/*
 * Description: Create system_health_metrics table for continuous health monitoring
 * Module: <PERSON><PERSON>eeker
 * Author: Optimization & Prevention System
 * Date: 2025-07-28
 * 
 * IMPORTANT: This file must be reviewed and executed by authorized database administrators
 */

-- Create system_health_metrics table
CREATE TABLE IF NOT EXISTS `system_health_metrics` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `metric_type` varchar(50) NOT NULL COMMENT 'Type of metric (email_notifications, job_fetching, etc.)',
    `metric_name` varchar(100) NOT NULL COMMENT 'Specific metric name',
    `value` decimal(10,4) NOT NULL COMMENT 'Metric value',
    `status` enum('healthy','warning','critical','unknown') NOT NULL DEFAULT 'unknown' COMMENT 'Health status',
    `threshold` decimal(10,4) DEFAULT NULL COMMENT 'Threshold value for status determination',
    `metadata` json DEFAULT NULL COMMENT 'Additional metric metadata',
    `recorded_at` timestamp NOT NULL COMMENT 'When the metric was recorded',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_metric_type` (`metric_type`),
    KEY `idx_recorded_at` (`recorded_at`),
    KEY `idx_status` (`status`),
    KEY `idx_type_recorded` (`metric_type`, `recorded_at`),
    KEY `idx_status_recorded` (`status`, `recorded_at`),
    KEY `idx_critical_recent` (`status`, `recorded_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='System health metrics for continuous monitoring';

-- Create index for efficient dashboard queries
CREATE INDEX `idx_latest_by_type` ON `system_health_metrics` (`metric_type`, `recorded_at` DESC);

-- Create index for cleanup operations (MySQL doesn't support conditional WHERE clauses in indexes)
CREATE INDEX `idx_cleanup` ON `system_health_metrics` (`recorded_at`);

-- Insert sample health metrics for reference (optional)
INSERT IGNORE INTO `system_health_metrics` (`metric_type`, `metric_name`, `value`, `status`, `threshold`, `metadata`, `recorded_at`, `created_at`, `updated_at`) VALUES
('email_notifications', 'notifications_sent_last_hour', 5.0000, 'healthy', 1.0000, '{"test": true, "sample": "data"}', NOW(), NOW(), NOW()),
('job_fetching', 'acbar_jobs_fetched_last_sync', 19.0000, 'healthy', 0.0000, '{"provider": "ACBAR", "test": true}', NOW(), NOW(), NOW()),
('api_performance', 'acbar_api_response_time', 6.1200, 'healthy', 30.0000, '{"provider": "ACBAR", "test": true}', NOW(), NOW(), NOW());

-- Mark sample data as test data and clean it up
DELETE FROM `system_health_metrics` WHERE `metadata`->>'$.test' = 'true';

-- Rollback procedure (commented out for safety)
-- DROP TABLE IF EXISTS `system_health_metrics`;
