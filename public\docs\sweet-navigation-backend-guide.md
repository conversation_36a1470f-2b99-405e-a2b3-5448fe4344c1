# Sweet Navigation Component - Backend Implementation Guide

## Overview

This guide provides comprehensive instructions for implementing backend endpoints that work with the Sweet Navigation component. It covers Laravel-specific implementations, best practices, security considerations, and performance optimization.

## Quick Implementation Checklist

- [ ] Create API controller with navigation method
- [ ] Define API routes with proper middleware
- [ ] Implement JSON response format
- [ ] Add authentication and authorization
- [ ] Configure rate limiting
- [ ] Test with frontend component
- [ ] Add error handling and logging

## Laravel Controller Implementation

### Basic Controller Structure

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

final class NavigationController extends Controller
{
    public function getNavigationData(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validated = $request->validate([
                'current_id' => 'nullable|string|max:255',
                'search' => 'nullable|string|max:100',
                'limit' => 'nullable|integer|min:1|max:100'
            ]);
            
            // Fetch and format data
            $groups = $this->fetchNavigationGroups($validated);
            
            return response()->json([
                'success' => true,
                'data' => ['groups' => $groups],
                'message' => 'Navigation data loaded successfully'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Navigation API Error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to load navigation data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
    
    private function fetchNavigationGroups(array $params): array
    {
        // Implementation depends on your data model
        // This is a template - customize for your needs
        
        $currentId = $params['current_id'] ?? null;
        $search = $params['search'] ?? null;
        $limit = $params['limit'] ?? 50;
        
        // Example: Fetch classes grouped by programs
        $query = Program::with(['classes' => function($query) use ($search, $limit) {
            if ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%");
            }
            $query->limit($limit);
        }])->whereHas('classes');
        
        $programs = $query->get();
        
        return $programs->map(function($program) use ($currentId) {
            return [
                'name' => $program->name,
                'items' => $program->classes->map(function($class) use ($currentId) {
                    return [
                        'id' => (string) $class->id,
                        'name' => $class->name,
                        'url' => route('classes.show', $class->id),
                        'is_current' => $currentId == $class->id,
                        'subtitle' => $this->buildItemSubtitle($class),
                        'count' => (string) $class->students_count,
                        'actions' => $this->buildItemActions($class)
                    ];
                })->toArray()
            ];
        })->toArray();
    }
    
    private function buildItemSubtitle($item): string
    {
        // Customize based on your data structure
        $parts = [];
        
        if ($item->teacher) {
            $teacherLink = '<a href="' . route('teachers.show', $item->teacher->id) . '" class="sweet-navigation-link">' . $item->teacher->name . '</a>';
            $parts[] = "Teacher: {$teacherLink}";
        }
        
        if ($item->schedule) {
            $parts[] = "Schedule: {$item->schedule}";
        }
        
        return implode(' | ', $parts);
    }
    
    private function buildItemActions($item): array
    {
        $actions = [];
        
        // Add actions based on user permissions
        if (auth()->user()->can('view', $item)) {
            $actions[] = [
                'type' => 'show',
                'url' => route('classes.show', $item->id),
                'title' => 'View Details',
                'label' => 'V'
            ];
        }
        
        if (auth()->user()->can('viewReport', $item)) {
            $actions[] = [
                'type' => 'report',
                'url' => route('classes.report', $item->id),
                'title' => 'View Report',
                'label' => 'R'
            ];
        }
        
        if (auth()->user()->can('update', $item)) {
            $actions[] = [
                'type' => 'edit',
                'url' => route('classes.edit', $item->id),
                'title' => 'Edit',
                'label' => 'E'
            ];
        }
        
        return $actions;
    }
}
```

### Route Configuration

```php
// routes/api.php
Route::middleware(['auth:sanctum', 'throttle:60,1'])->group(function () {
    Route::prefix('navigation')->name('api.navigation.')->group(function () {
        Route::get('/classes', [NavigationController::class, 'getClassesNavigation'])
            ->name('classes');
        Route::get('/students', [NavigationController::class, 'getStudentsNavigation'])
            ->name('students');
        Route::get('/teachers', [NavigationController::class, 'getTeachersNavigation'])
            ->name('teachers');
    });
});
```

## Data Fetching Strategies

### 1. Eager Loading (Recommended)

```php
// Prevent N+1 queries with eager loading
$programs = Program::with([
    'classes.teachers:id,name',
    'classes.students:id,class_id',
    'classes.center:id,name'
])->whereHas('classes')->get();
```

### 2. Query Optimization

```php
// Use select to limit columns
$classes = Class::select(['id', 'name', 'code', 'program_id', 'teacher_id'])
    ->with(['teacher:id,name', 'program:id,name'])
    ->where('active', true)
    ->orderBy('name')
    ->get();
```

### 3. Caching Strategy

```php
use Illuminate\Support\Facades\Cache;

private function fetchNavigationGroups(array $params): array
{
    $cacheKey = 'navigation_classes_' . auth()->id() . '_' . md5(serialize($params));
    
    return Cache::remember($cacheKey, 300, function() use ($params) {
        return $this->buildNavigationData($params);
    });
}
```

## Security Implementation

### 1. Authentication

```php
// In your controller constructor
public function __construct()
{
    $this->middleware('auth:sanctum');
}

// Or in routes
Route::middleware('auth:sanctum')->group(function () {
    // Navigation routes
});
```

### 2. Authorization

```php
// Check user permissions for each item
private function buildItemActions($item): array
{
    $actions = [];
    
    // Use Laravel policies
    if ($this->authorize('view', $item)) {
        $actions[] = [
            'type' => 'show',
            'url' => route('classes.show', $item->id),
            'title' => 'View Details',
            'label' => 'V'
        ];
    }
    
    return $actions;
}

// Filter items based on permissions
private function filterAuthorizedItems($items)
{
    return $items->filter(function($item) {
        return auth()->user()->can('view', $item);
    });
}
```

### 3. Input Validation

```php
// Use Form Requests for complex validation
class NavigationRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'current_id' => 'nullable|string|max:255',
            'search' => 'nullable|string|max:100|regex:/^[a-zA-Z0-9\s\-_]+$/',
            'limit' => 'nullable|integer|min:1|max:100',
            'type' => 'nullable|in:classes,students,teachers'
        ];
    }
    
    public function messages(): array
    {
        return [
            'search.regex' => 'Search term contains invalid characters',
            'limit.max' => 'Maximum 100 items allowed per request'
        ];
    }
}
```

### 4. Rate Limiting

```php
// In RouteServiceProvider or routes file
Route::middleware(['throttle:navigation'])->group(function () {
    // Navigation routes
});

// In app/Http/Kernel.php
protected $middlewareGroups = [
    'api' => [
        'throttle:api',
        // other middleware
    ],
];

protected $routeMiddleware = [
    'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
];

// Custom rate limiting in config/cache.php
'navigation' => [
    'driver' => 'redis',
    'connection' => 'default',
    'prefix' => 'navigation_throttle',
],
```

## Performance Optimization

### 1. Database Indexing

```sql
-- Add indexes for commonly queried fields
CREATE INDEX idx_classes_active ON classes(active);
CREATE INDEX idx_classes_program_id ON classes(program_id);
CREATE INDEX idx_classes_name ON classes(name);
CREATE INDEX idx_classes_search ON classes(name, code);

-- Composite indexes for complex queries
CREATE INDEX idx_classes_active_program ON classes(active, program_id);
```

### 2. Query Optimization

```php
// Use database-level filtering instead of collection filtering
$classes = Class::select(['id', 'name', 'code', 'program_id'])
    ->where('active', true)
    ->when($search, function($query, $search) {
        return $query->where(function($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('code', 'like', "%{$search}%");
        });
    })
    ->with(['program:id,name'])
    ->orderBy('name')
    ->limit(50)
    ->get();
```

### 3. Response Optimization

```php
// Use API Resources for consistent formatting
class NavigationItemResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => (string) $this->id,
            'name' => $this->name,
            'url' => route('classes.show', $this->id),
            'is_current' => $request->get('current_id') == $this->id,
            'subtitle' => $this->buildSubtitle(),
            'count' => (string) $this->students_count,
            'actions' => $this->buildActions()
        ];
    }
}

// Use in controller
return NavigationItemResource::collection($items);
```

## Error Handling

### 1. Exception Handling

```php
try {
    $groups = $this->fetchNavigationGroups($validated);
    
    return response()->json([
        'success' => true,
        'data' => ['groups' => $groups]
    ]);
    
} catch (ValidationException $e) {
    return response()->json([
        'success' => false,
        'message' => 'Invalid request parameters',
        'errors' => $e->errors()
    ], 422);
    
} catch (AuthorizationException $e) {
    return response()->json([
        'success' => false,
        'message' => 'Unauthorized access'
    ], 403);
    
} catch (ModelNotFoundException $e) {
    return response()->json([
        'success' => false,
        'message' => 'Resource not found'
    ], 404);
    
} catch (\Exception $e) {
    Log::error('Navigation API Error', [
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'request' => $request->all()
    ]);
    
    return response()->json([
        'success' => false,
        'message' => 'Internal server error'
    ], 500);
}
```

### 2. Logging Strategy

```php
// In your controller
use Illuminate\Support\Facades\Log;

private function logNavigationAccess(Request $request, array $result): void
{
    Log::info('Navigation API Access', [
        'user_id' => auth()->id(),
        'endpoint' => $request->path(),
        'parameters' => $request->all(),
        'result_count' => count($result['groups'] ?? []),
        'ip_address' => $request->ip(),
        'user_agent' => $request->userAgent()
    ]);
}
```

## Testing

### 1. Feature Tests

```php
// tests/Feature/NavigationApiTest.php
class NavigationApiTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_authenticated_user_can_access_navigation()
    {
        $user = User::factory()->create();
        $program = Program::factory()->create();
        $class = Class::factory()->create(['program_id' => $program->id]);
        
        $response = $this->actingAs($user)
            ->getJson('/api/navigation/classes');
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'groups' => [
                        '*' => [
                            'name',
                            'items' => [
                                '*' => [
                                    'id',
                                    'name',
                                    'url',
                                    'is_current',
                                    'subtitle',
                                    'count',
                                    'actions'
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
    }
    
    public function test_unauthenticated_user_cannot_access_navigation()
    {
        $response = $this->getJson('/api/navigation/classes');
        
        $response->assertStatus(401);
    }
    
    public function test_navigation_respects_user_permissions()
    {
        $user = User::factory()->create();
        $class = Class::factory()->create();
        
        // User without permissions
        $response = $this->actingAs($user)
            ->getJson('/api/navigation/classes');
        
        $response->assertStatus(200)
            ->assertJsonPath('data.groups.0.items.0.actions', []);
    }
}
```

### 2. Unit Tests

```php
// tests/Unit/NavigationControllerTest.php
class NavigationControllerTest extends TestCase
{
    public function test_build_item_subtitle_formats_correctly()
    {
        $controller = new NavigationController();
        $class = new Class([
            'name' => 'Test Class',
            'schedule' => 'Mon, Wed, Fri 10:00-11:30'
        ]);
        $class->teacher = new Teacher(['name' => 'Dr. Smith']);
        
        $subtitle = $controller->buildItemSubtitle($class);
        
        $this->assertStringContains('Dr. Smith', $subtitle);
        $this->assertStringContains('Mon, Wed, Fri', $subtitle);
    }
}
```

## Deployment Considerations

### 1. Environment Configuration

```env
# .env
NAVIGATION_CACHE_TTL=300
NAVIGATION_RATE_LIMIT=60
NAVIGATION_MAX_ITEMS=100
NAVIGATION_DEBUG=false
```

### 2. Production Optimizations

```php
// In production, use:
- Redis for caching
- Database connection pooling
- CDN for static assets
- Proper error monitoring (Sentry, Bugsnag)
- Performance monitoring (New Relic, DataDog)
```

### 3. Monitoring

```php
// Add monitoring to your controller
use Illuminate\Support\Facades\Cache;

public function getNavigationData(Request $request): JsonResponse
{
    $startTime = microtime(true);
    
    try {
        // Your implementation
        
        $executionTime = microtime(true) - $startTime;
        
        // Log slow queries
        if ($executionTime > 1.0) {
            Log::warning('Slow navigation query', [
                'execution_time' => $executionTime,
                'parameters' => $request->all()
            ]);
        }
        
        return $response;
        
    } catch (\Exception $e) {
        // Error handling
    }
}
```

This backend implementation guide provides a solid foundation for creating robust, secure, and performant navigation endpoints that work seamlessly with the Sweet Navigation frontend component.
