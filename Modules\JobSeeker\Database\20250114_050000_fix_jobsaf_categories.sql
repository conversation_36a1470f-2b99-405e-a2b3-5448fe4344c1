/*
 * Description: Fix Jobs.af categories in provider_job_categories table with correct API identifiers and canonical mappings
 * Module: JobSeeker
 * Author: System
 * Date: 2025-01-14
 * 
 * IMPORTANT: This file must be reviewed and executed by authorized database administrators
 */

-- First, remove any existing Jobs.af categories to avoid conflicts
DELETE FROM provider_job_categories WHERE provider_name = 'jobs.af';

-- Drop the index if it exists (MySQL compatible syntax)
DROP INDEX IF EXISTS idx_provider_job_categories_jobsaf ON provider_job_categories;

-- Insert all correct Jobs.af categories with exact API identifiers and proper canonical category mappings
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at) VALUES

-- IT & Software Categories (canonical_category_id: 10 = IT - Software, 1 = Information Technology, 31 = Information Technology)
('jobs.af', 'IT - Software', 'IT - Software', 10, NOW(), NOW()),
('jobs.af', 'Software engineering', 'Software engineering', 10, NOW(), NOW()),
('jobs.af', 'software development', 'software development', 10, NOW(), NOW()),
('jobs.af', 'software analysis', 'software analysis', 10, NOW(), NOW()),
('jobs.af', 'it software and Hardware', 'it software and Hardware', 1, NOW(), NOW()),
('jobs.af', 'SALES/ software engineering ', 'SALES/ software engineering ', 10, NOW(), NOW()),
('jobs.af', 'Software Development and Data Management', 'Software Development and Data Management', 10, NOW(), NOW()),
('jobs.af', 'Software developer', 'Software developer', 10, NOW(), NOW()),
('jobs.af', 'Sofware developer and data base development', 'Sofware developer and data base development', 10, NOW(), NOW()),
('jobs.af', 'IT - Hardware', 'IT - Hardware', 20, NOW(), NOW()),
('jobs.af', 'Data Security/Protection', 'Data Security/Protection', 31, NOW(), NOW()),
('jobs.af', 'IT Billing', 'IT Billing', 1, NOW(), NOW()),
('jobs.af', 'Computer Science', 'Computer Science', 1, NOW(), NOW()),

-- Business & Management (canonical_category_id: 8 = Human Resources, 2 = Management, 24 = Administration)
('jobs.af', 'Human Resources', 'Human Resources', 8, NOW(), NOW()),
('jobs.af', 'Human Resource Development', 'Human Resource Development', 8, NOW(), NOW()),
('jobs.af', 'Personnel Management', 'Personnel Management', 8, NOW(), NOW()),
('jobs.af', 'Sales/Marketing', 'Sales/Marketing', 22, NOW(), NOW()),
('jobs.af', 'Administrative', 'Administrative', 23, NOW(), NOW()),
('jobs.af', 'Business Administration ', 'Business Administration ', 24, NOW(), NOW()),
('jobs.af', 'Management, Engineering and social ', 'Management, Engineering and social ', 2, NOW(), NOW()),
('jobs.af', 'Customer Service', 'Customer Service', 24, NOW(), NOW()),

-- Transportation & Travel (canonical_category_id: 9 = Transportation, 13 = Travel/Tourism)
('jobs.af', 'Transportation', 'Transportation', 9, NOW(), NOW()),
('jobs.af', 'Travel/Tourism', 'Travel/Tourism', 13, NOW(), NOW()),

-- Security & Safety (canonical_category_id: 15 = Security/Safety, 29 = Security)
('jobs.af', 'Security/Safety', 'Security/Safety', 15, NOW(), NOW()),
('jobs.af', 'Personal Safety', 'Personal Safety', 15, NOW(), NOW()),

-- Consulting & Advisory (canonical_category_id: 2 = Management, 18 = Legal)
('jobs.af', 'Consulting', 'Consulting', 2, NOW(), NOW()),
('jobs.af', 'Adviser', 'Adviser', 2, NOW(), NOW()),
('jobs.af', 'Legal Advisor', 'Legal Advisor', 18, NOW(), NOW()),

-- Education & Research (canonical_category_id: 5 = Education, 11 = Research/Survey, 6 = Translation, 7 = Programme)
('jobs.af', 'Internships', 'Internships', 5, NOW(), NOW()),
('jobs.af', 'Research/Survey', 'Research/Survey', 11, NOW(), NOW()),
('jobs.af', 'Translation', 'Translation', 6, NOW(), NOW()),
('jobs.af', 'Social Science', 'Social Science', 11, NOW(), NOW()),
('jobs.af', 'Human Rights', 'Human Rights', 7, NOW(), NOW()),
('jobs.af', 'Social Mobilization', 'Social Mobilization', 7, NOW(), NOW()),
('jobs.af', 'Sociology', 'Sociology', 11, NOW(), NOW()),
('jobs.af', 'conflict resolution', 'conflict resolution', 7, NOW(), NOW()),
('jobs.af', 'Social activities', 'Social activities', 7, NOW(), NOW()),

-- Creative & Design (canonical_category_id: 4 = Marketing for graphics)
('jobs.af', 'Graphic Designer', 'Graphic Designer', 4, NOW(), NOW()),

-- Industrial & Engineering (canonical_category_id: 26 = Engineering, 19 = Architecture)
('jobs.af', 'Industrial', 'Industrial', 26, NOW(), NOW()),
('jobs.af', 'Electrical Engineering', 'Electrical Engineering', 26, NOW(), NOW()),
('jobs.af', 'Soil Mechanics', 'Soil Mechanics', 26, NOW(), NOW()),
('jobs.af', 'Soil Engineer', 'Soil Engineer', 26, NOW(), NOW()),
('jobs.af', 'Architecture', 'Architecture', 19, NOW(), NOW()),

-- Healthcare & Medical (canonical_category_id: 27 = Health, 16 = Health/Medical)
('jobs.af', 'Nursing', 'Nursing', 16, NOW(), NOW()),
('jobs.af', 'Health/Medical', 'Health/Medical', 16, NOW(), NOW()),
('jobs.af', 'Fitness/Health', 'Fitness/Health', 16, NOW(), NOW()),

-- Finance & Accounting (canonical_category_id: 25 = Finance, 21 = Banking)
('jobs.af', 'Accounting/Finance- Human Resource', 'Accounting/Finance- Human Resource', 25, NOW(), NOW()),
('jobs.af', 'Banking', 'Banking', 21, NOW(), NOW()),

-- Content & Media (canonical_category_id: 17 = Media, 5 = Education for literature)
('jobs.af', 'Editing/Reviewing', 'Editing/Reviewing', 17, NOW(), NOW()),
('jobs.af', 'Literature', 'Literature', 5, NOW(), NOW()),
('jobs.af', 'Pashto Literature', 'Pashto Literature', 5, NOW(), NOW()),
('jobs.af', 'Dari Literature', 'Dari Literature', 5, NOW(), NOW()),
('jobs.af', 'English Literature', 'English Literature', 5, NOW(), NOW()),
('jobs.af', 'Report writing', 'Report writing', 17, NOW(), NOW()),
('jobs.af', 'Media', 'Media', 17, NOW(), NOW()),

-- Quality & Monitoring (canonical_category_id: 2 = Management, 11 = Research/Survey for monitoring)
('jobs.af', 'Quality Management', 'Quality Management', 2, NOW(), NOW()),
('jobs.af', 'Quality Control (QC)', 'Quality Control (QC)', 2, NOW(), NOW()),
('jobs.af', 'Monitoring & Reporting', 'Monitoring & Reporting', 11, NOW(), NOW()),
('jobs.af', 'Monitoring and Evaluation', 'Monitoring and Evaluation', 11, NOW(), NOW()),

-- Environmental & Resources (canonical_category_id: 14 = Agriculture, 7 = Programme for resource management)
('jobs.af', 'Natural Resource Management', 'Natural Resource Management', 7, NOW(), NOW()),
('jobs.af', 'Agriculture', 'Agriculture', 14, NOW(), NOW()),

-- Communication & Political (canonical_category_id: 17 = Media, 11 = Research/Survey, 25 = Finance for economic)
('jobs.af', 'Communication', 'Communication', 17, NOW(), NOW()),
('jobs.af', 'Political Science', 'Political Science', 11, NOW(), NOW()),
('jobs.af', 'Economic Growth', 'Economic Growth', 25, NOW(), NOW());

-- Create index for better performance on Jobs.af category lookups (MySQL compatible syntax)
CREATE INDEX idx_provider_job_categories_jobsaf ON provider_job_categories(provider_name, provider_identifier);

-- Verify the insertions
SELECT COUNT(*) as 'Total Jobs.af Categories' FROM provider_job_categories WHERE provider_name = 'jobs.af';

-- Show sample of inserted categories with their canonical mappings
SELECT 
    pjc.provider_identifier,
    pjc.name,
    jc.name as canonical_category_name,
    pjc.canonical_category_id
FROM provider_job_categories pjc
JOIN job_categories jc ON pjc.canonical_category_id = jc.id
WHERE pjc.provider_name = 'jobs.af'
ORDER BY pjc.provider_identifier 
LIMIT 15;

-- Show categories by canonical category to verify logical groupings
SELECT 
    jc.name as canonical_category,
    COUNT(*) as jobsaf_categories_count,
    GROUP_CONCAT(pjc.provider_identifier SEPARATOR ', ') as jobsaf_categories
FROM provider_job_categories pjc
JOIN job_categories jc ON pjc.canonical_category_id = jc.id
WHERE pjc.provider_name = 'jobs.af'
GROUP BY jc.id, jc.name
ORDER BY COUNT(*) DESC;

-- Rollback procedure (commented out)
-- DELETE FROM provider_job_categories WHERE provider_name = 'jobs.af';
-- DROP INDEX IF EXISTS idx_provider_job_categories_jobsaf; 