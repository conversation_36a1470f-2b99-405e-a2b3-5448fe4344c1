<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Command Schedule Rules - Bulk Edit MVP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --border-color: #e2e8f0;
        }
        
        body { background: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        
        .chess-board-view {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1rem;
            padding: 1rem;
        }
        
        .rule-card {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .rule-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
            transform: translateY(-2px);
        }
        
        .provider-badge {
            position: absolute;
            top: -8px;
            right: 15px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .provider-jobsaf { background: #dbeafe; color: #1e40af; }
        .provider-acbar { background: #dcfce7; color: #166534; }
        
        .inline-edit {
            border: 1px solid transparent;
            background: transparent;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            transition: all 0.2s ease;
            width: 100%;
        }
        
        .inline-edit:focus, .inline-edit:hover {
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        
        .saving-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            background: var(--warning-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .saving-indicator.show { opacity: 1; }
        
        .field-error {
            border-color: var(--danger-color) !important;
            background: #fef2f2 !important;
        }
        
        .error-message {
            color: var(--danger-color);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .clone-superpower {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }
        
        .clone-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            color: white;
            font-weight: 600;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }
        
        .clone-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
            color: white;
        }
        
        .clone-modal {
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(10px);
        }
        
        .clone-panel {
            background: white;
            border-radius: 20px;
            max-width: 500px;
            margin: 5% auto;
            padding: 2rem;
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }
        
        .provider-selector {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 1rem;
            align-items: center;
            margin: 1.5rem 0;
        }
        
        .provider-option {
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .provider-option.selected {
            border-color: var(--primary-color);
            background: rgba(79, 70, 229, 0.1);
        }
        
        .time-offset-selector {
            display: flex;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .offset-btn {
            padding: 0.5rem 1rem;
            border: 2px solid var(--border-color);
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .offset-btn.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }
        
        .stats-bar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #64748b;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <!-- Stats Bar -->
    <div class="stats-bar">
        <div class="d-flex gap-4">
            <div class="stat-item">
                <div class="stat-number" id="totalRules">0</div>
                <div class="stat-label">Total Rules</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="jobsAfRules">0</div>
                <div class="stat-label">Jobs.af</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="acbarRules">0</div>
                <div class="stat-label">ACBAR</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="activeRules">0</div>
                <div class="stat-label">Active</div>
            </div>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary btn-sm" onclick="filterProvider('all')">
                <i class="fas fa-eye"></i> All
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="filterProvider('jobsaf')">
                <i class="fas fa-briefcase"></i> Jobs.af
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="filterProvider('acbar')">
                <i class="fas fa-building"></i> ACBAR
            </button>
        </div>
    </div>

    <!-- Chess Board View -->
    <div class="chess-board-view" id="rulesContainer">
        <!-- Rules will be loaded dynamically -->
    </div>

    <!-- Clone Superpower Button -->
    <div class="clone-superpower">
        <button class="btn clone-btn" onclick="openCloneModal()">
            <i class="fas fa-magic me-2"></i>
            Clone Superpower
        </button>
    </div>

    <!-- Clone Modal -->
    <div class="modal clone-modal" id="cloneModal" style="display:none;">
        <div class="clone-panel">
            <h4 class="text-center mb-4">
                <i class="fas fa-magic text-primary me-2"></i>
                Clone Rules Between Providers
            </h4>
            
            <div class="provider-selector">
                <div class="provider-option" id="fromProvider" onclick="selectFromProvider('jobsaf')">
                    <i class="fas fa-briefcase fa-2x mb-2 text-primary"></i>
                    <div class="fw-bold">Jobs.af</div>
                    <div class="text-muted small" id="fromProviderCount">0 rules</div>
                </div>
                
                <div class="text-center">
                    <i class="fas fa-arrow-right fa-2x text-muted"></i>
                </div>
                
                <div class="provider-option" id="toProvider" onclick="selectToProvider('acbar')">
                    <i class="fas fa-building fa-2x mb-2 text-success"></i>
                    <div class="fw-bold">ACBAR</div>
                    <div class="text-muted small" id="toProviderCount">0 rules</div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label fw-bold">Time Offset</label>
                <div class="time-offset-selector">
                    <button class="offset-btn active" onclick="selectOffset(15)">15 min</button>
                    <button class="offset-btn" onclick="selectOffset(30)">30 min</button>
                    <button class="offset-btn" onclick="selectOffset(60)">60 min</button>
                </div>
            </div>
            
            <div class="d-flex gap-2 justify-content-end">
                <button class="btn btn-secondary" onclick="closeCloneModal()">Cancel</button>
                <button class="btn btn-primary" onclick="executeClone()" id="cloneExecuteBtn">
                    <i class="fas fa-magic me-2"></i>
                    Clone Rules
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let allRules = [];
        let selectedOffset = 15;
        let fromProvider = 'jobsaf';
        let toProvider = 'acbar';
        
        // API Configuration
        const API_BASE = '/admin/jobseeker/command-schedule/api';
        const CSRF_TOKEN = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadBulkData();
        });
        
        // Load all rules data
        async function loadBulkData() {
            try {
                const response = await fetch(`${API_BASE}/bulk-data`, {
                    headers: {
                        'X-CSRF-TOKEN': CSRF_TOKEN,
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    allRules = data.rules;
                    updateStats(data.stats);
                    renderRules(allRules);
                } else {
                    showError('Failed to load rules: ' + data.message);
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }
        
        // Update statistics display
        function updateStats(stats) {
            document.getElementById('totalRules').textContent = stats.total_rules;
            document.getElementById('jobsAfRules').textContent = stats.jobsaf_rules;
            document.getElementById('acbarRules').textContent = stats.acbar_rules;
            document.getElementById('activeRules').textContent = stats.active_rules;
            
            // Update clone modal counts
            document.getElementById('fromProviderCount').textContent = `${stats.jobsaf_rules} rules`;
            document.getElementById('toProviderCount').textContent = `${stats.acbar_rules} rules`;
        }
        
        // Render rules in chess board view
        function renderRules(rules) {
            const container = document.getElementById('rulesContainer');
            container.innerHTML = '';
            
            rules.forEach(rule => {
                const ruleCard = createRuleCard(rule);
                container.appendChild(ruleCard);
            });
        }
        
        // Create individual rule card
        function createRuleCard(rule) {
            const card = document.createElement('div');
            card.className = 'rule-card';
            card.dataset.provider = rule.provider;
            card.dataset.ruleId = rule.id;
            
            card.innerHTML = `
                <div class="saving-indicator" id="saving-${rule.id}">Saving...</div>
                <div class="provider-badge provider-${rule.provider}">${rule.provider.toUpperCase()}</div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Rule Name</label>
                    <input type="text" class="inline-edit" value="${rule.name}" 
                           onchange="saveField(${rule.id}, 'name', this.value)" 
                           onblur="validateField(this, 'required')">
                    <div class="error-message" style="display:none;"></div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <label class="form-label fw-bold">Schedule Time</label>
                        <input type="time" class="inline-edit" value="${rule.schedule_expression}" 
                               onchange="saveField(${rule.id}, 'schedule_time', this.value)">
                    </div>
                    <div class="col-6">
                        <label class="form-label fw-bold">Priority</label>
                        <input type="number" class="inline-edit" value="${rule.priority}" min="1" max="1000"
                               onchange="saveField(${rule.id}, 'priority', this.value)">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Status</label>
                    <select class="inline-edit" onchange="saveField(${rule.id}, 'is_active', this.value)">
                        <option value="1" ${rule.is_active ? 'selected' : ''}>Active</option>
                        <option value="0" ${!rule.is_active ? 'selected' : ''}>Disabled</option>
                    </select>
                </div>
                
                <div class="text-muted small">
                    <i class="fas fa-clock"></i> Next Run: ${rule.next_run_human}
                    ${rule.last_execution ? `<span class="ms-3"><i class="fas fa-history"></i> Last: ${rule.last_execution.started_at}</span>` : ''}
                </div>
            `;
            
            return card;
        }
        
        // Save field inline
        async function saveField(ruleId, field, value) {
            const indicator = document.getElementById(`saving-${ruleId}`);
            indicator.classList.add('show');
            
            try {
                const response = await fetch(`${API_BASE}/update-field`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': CSRF_TOKEN,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        rule_id: ruleId,
                        field: field,
                        value: value
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Update local rule data
                    const rule = allRules.find(r => r.id === ruleId);
                    if (rule) {
                        rule[field] = value;
                        if (data.rule && data.rule.next_run) {
                            rule.next_run_human = data.rule.next_run;
                        }
                    }
                } else {
                    showError('Failed to update field: ' + data.message);
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                indicator.classList.remove('show');
            }
        }
        
        // Field validation
        function validateField(element, type) {
            const errorDiv = element.nextElementSibling;
            if (type === 'required' && !element.value.trim()) {
                element.classList.add('field-error');
                errorDiv.textContent = 'This field is required';
                errorDiv.style.display = 'block';
                return false;
            } else {
                element.classList.remove('field-error');
                errorDiv.style.display = 'none';
                return true;
            }
        }
        
        // Provider filtering
        function filterProvider(provider) {
            const cards = document.querySelectorAll('.rule-card');
            cards.forEach(card => {
                if (provider === 'all' || card.dataset.provider === provider) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
        
        // Clone modal functions
        function openCloneModal() {
            document.getElementById('cloneModal').style.display = 'flex';
        }
        
        function closeCloneModal() {
            document.getElementById('cloneModal').style.display = 'none';
        }
        
        function selectOffset(minutes) {
            selectedOffset = minutes;
            document.querySelectorAll('.offset-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }
        
        function selectFromProvider(provider) {
            fromProvider = provider;
            document.getElementById('fromProvider').classList.add('selected');
            document.getElementById('toProvider').classList.remove('selected');
        }
        
        function selectToProvider(provider) {
            toProvider = provider;
            document.getElementById('toProvider').classList.add('selected');
            document.getElementById('fromProvider').classList.remove('selected');
        }
        
        async function executeClone() {
            if (fromProvider === toProvider) {
                showError('Please select different providers for cloning');
                return;
            }
            
            const btn = document.getElementById('cloneExecuteBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Cloning...';
            btn.disabled = true;
            
            try {
                const response = await fetch(`${API_BASE}/clone-rules`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': CSRF_TOKEN,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        from_provider: fromProvider,
                        to_provider: toProvider,
                        time_offset_minutes: selectedOffset
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess(data.message);
                    closeCloneModal();
                    loadBulkData(); // Reload data
                    
                    // Show success feedback on clone button
                    const cloneBtn = document.querySelector('.clone-btn');
                    cloneBtn.innerHTML = '<i class="fas fa-check me-2"></i>Cloned Successfully!';
                    cloneBtn.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                    
                    setTimeout(() => {
                        cloneBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Clone Superpower';
                        cloneBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    }, 3000);
                } else {
                    showError('Clone failed: ' + data.message);
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }
        
        // Utility functions
        function showError(message) {
            // Simple toast notification - you can replace with your preferred notification system
            alert('Error: ' + message);
        }
        
        function showSuccess(message) {
            // Simple toast notification - you can replace with your preferred notification system
            alert('Success: ' + message);
        }
    </script>
</body>
</html>
