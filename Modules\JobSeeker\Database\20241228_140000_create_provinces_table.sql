-- Create provinces table for Afghanistan
-- This table stores all 34 provinces of Afghanistan for location filtering

CREATE TABLE provinces (
    id int unsigned NOT NULL AUTO_INCREMENT,
    name varchar(100) NOT NULL,
    name_pashto varchar(100) DEFAULT NULL,
    name_dari varchar(100) DEFAULT NULL,
    code varchar(10) DEFAULT NULL,
    is_active tinyint(1) NOT NULL DEFAULT 1,
    created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY provinces_name_unique (name),
    UNIQUE KEY provinces_code_unique (code),
    KEY provinces_is_active_index (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert all 34 provinces of Afghanistan
INSERT INTO provinces (id, name, name_pashto, name_dari, code, is_active) VALUES
(1, 'Kabul', 'کابل', 'کابل', 'KBL', 1),
(2, 'Kandahar', 'کندهار', 'قندهار', 'KDH', 1),
(3, 'Herat', 'هرات', 'هرات', 'HRT', 1),
(4, 'Balkh', 'بلخ', 'بلخ', 'BLK', 1),
(5, 'Nangarhar', 'ننګرهار', 'ننگرهار', 'NGR', 1),
(6, 'Ghazni', 'غزني', 'غزنی', 'GHZ', 1),
(7, 'Kunduz', 'کندز', 'کندز', 'KDZ', 1),
(8, 'Badakhshan', 'بدخشان', 'بدخشان', 'BDS', 1),
(9, 'Takhar', 'تخار', 'تخار', 'TKR', 1),
(10, 'Baghlan', 'بغلان', 'بغلان', 'BGL', 1),
(11, 'Helmand', 'هلمند', 'هلمند', 'HLM', 1),
(12, 'Farah', 'فراه', 'فراه', 'FRH', 1),
(13, 'Faryab', 'فاریاب', 'فاریاب', 'FYB', 1),
(14, 'Jawzjan', 'جوزجان', 'جوزجان', 'JWZ', 1),
(15, 'Badghis', 'بادغیس', 'بادغیس', 'BDG', 1),
(16, 'Ghor', 'غور', 'غور', 'GHR', 1),
(17, 'Parwan', 'پروان', 'پروان', 'PRW', 1),
(18, 'Kapisa', 'کاپیسا', 'کاپیسا', 'KPS', 1),
(19, 'Laghman', 'لغمان', 'لغمان', 'LGM', 1),
(20, 'Kunar', 'کنر', 'کنر', 'KNR', 1),
(21, 'Nuristan', 'نورستان', 'نورستان', 'NRS', 1),
(22, 'Logar', 'لوګر', 'لوگر', 'LGR', 1),
(23, 'Wardak', 'وردک', 'وردک', 'WRD', 1),
(24, 'Bamyan', 'بامیان', 'بامیان', 'BMY', 1),
(25, 'Paktya', 'پکتیا', 'پکتیا', 'PKT', 1),
(26, 'Paktika', 'پکتیکا', 'پکتیکا', 'PKK', 1),
(27, 'Khost', 'خوست', 'خوست', 'KST', 1),
(28, 'Uruzgan', 'اروزګان', 'ارزگان', 'URZ', 1),
(29, 'Zabul', 'زابل', 'زابل', 'ZBL', 1),
(30, 'Daykundi', 'دایکندی', 'دایکندی', 'DYK', 1),
(31, 'Sar-e Pol', 'سرپل', 'سر پل', 'SRP', 1),
(32, 'Samangan', 'سمنګان', 'سمنگان', 'SMG', 1),
(33, 'Panjshir', 'پنجشیر', 'پنجشیر', 'PNJ', 1),
(34, 'Nimroz', 'نیمروز', 'نیمروز', 'NMZ', 1);

-- Verify the insert
SELECT 
    COUNT(*) as total_provinces,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_provinces
FROM provinces; 