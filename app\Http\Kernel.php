<?php

namespace App\Http;

use App\Http\Middleware\CheckMissedClockOutMiddleware;
use App\Http\Middleware\ExecutionTimeMiddleware;
use App\Http\Middleware\IsEmailVerified;
use App\Http\Middleware\TerminateMiddleware;
use App\Http\Middleware\UserRolePermission;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use Modules\Education\Http\Middleware\WriteCurrentClassReportOnlyMiddleware;
use Modules\HumanResource\Http\Middleware\OnlyAjaxMiddleware;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [

        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
//        \App\Http\Middleware\CheckForMaintenanceMode::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,

        # \RenatoMarinho\LaravelPageSpeed\Middleware\TrimUrls::class,
        # \RenatoMarinho\LaravelPageSpeed\Middleware\RemoveQuotes::class,
//        \App\Http\Middleware\CaptureVisitorDetailsMiddleware::class,


    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\ShareAuthDataWithViews::class,
            \App\Http\Middleware\SetLocale::class,
//            \App\Http\Middleware\HttpsProtocolMiddleware::class
//            ExecutionTimeMiddleware::class
        ],

        'api' => [
            'throttle:60,1',
            'bindings',
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $middlewareAliases = [

        'sponsor' => \App\Http\Middleware\RedirectIfNotSponsor::class,
        'sponsor.guest' => \App\Http\Middleware\RedirectIfSponsor::class,
        'superior' => \App\Http\Middleware\RedirectIfNotSuperior::class,
        'superior.guest' => \App\Http\Middleware\RedirectIfSuperior::class,
        'organization' => \App\Http\Middleware\RedirectIfNotOrganization::class,
        'organization.guest' => \App\Http\Middleware\RedirectIfOrganization::class,
        'student' => \App\Http\Middleware\RedirectIfNotStudent::class,
        'student.guest' => \App\Http\Middleware\RedirectIfStudent::class,
        'employee' => \App\Http\Middleware\RedirectIfNotEmployee::class,
        'employee.guest' => \App\Http\Middleware\RedirectIfEmployee::class,
        'guardian' => \App\Http\Middleware\RedirectIfNotGuardian::class,
        'guardian.guest' => \App\Http\Middleware\RedirectIfGuardian::class,
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'center.limited' => \App\Http\Middleware\CenterLimitedMiddleware::class,


        'XSS' => \App\Http\Middleware\XSS::class,
        'SAMiddleware' => \App\Http\Middleware\SAMiddleware::class,
//        'cors' => \App\Http\Middleware\Cors::class,
        'CheckUserMiddleware' => \App\Http\Middleware\CheckUserMiddleware::class,
        'CheckDashboardMiddleware' => \App\Http\Middleware\CheckDashboardMiddleware::class,
        'StudentMiddleware' => \App\Http\Middleware\StudentMiddleware::class,
        'ParentMiddleware' => \App\Http\Middleware\ParentMiddleware::class,
        'only.ajax' => OnlyAjaxMiddleware::class,
        'terminate' => TerminateMiddleware::class,
        'writeCurrentClassReportOnly' => WriteCurrentClassReportOnlyMiddleware::class,
        'userRolePermission' => UserRolePermission::class,
        // 'role' => \Spatie\Permission\Middlewares\RoleMiddleware::class,
        // 'permission' => \Spatie\Permission\Middlewares\PermissionMiddleware::class,
        // 'role_or_permission' => \Spatie\Permission\Middlewares\RoleOrPermissionMiddleware::class,

        'role' => \App\Http\Middleware\EnhancedRoleMiddleware::class,
        'permission' => \App\Http\Middleware\EnhancedPermissionMiddleware::class,
        'role_or_permission' => \App\Http\Middleware\EnhancedRoleOrPermissionMiddleware::class,
        'missedClockOutMiddleware' => CheckMissedClockOutMiddleware::class,
        
        'viewer.restrictions' => \App\Http\Middleware\ViewerRestrictionsMiddleware::class,
        'demo.data' => \App\Http\Middleware\DemoDataMiddleware::class,
        'system.viewer.permission' => \App\Http\Middleware\SystemViewerPermissionMiddleware::class,
        // 'admin' => \App\Http\Middleware\AdminAccessMiddleware::class,
//        'permission' => \App\Http\Middleware\Permission::class,


    ];

    protected $middlewarePriority = [
        \Illuminate\Session\Middleware\StartSession::class,
        \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        \App\Http\Middleware\Authenticate::class,
        \Illuminate\Session\Middleware\AuthenticateSession::class,
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
        \Illuminate\Auth\Middleware\Authorize::class,
    ];
}
