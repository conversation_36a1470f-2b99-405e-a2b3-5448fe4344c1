<?php

namespace Modules\JobSeeker\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Mo<PERSON>les\JobSeeker\Entities\JobNotificationSetup;
use Modules\JobSeeker\Entities\JobNotificationSentJob;
use Modules\JobSeeker\Repositories\JobRepository;
use Modules\JobSeeker\Notifications\JobAlertFcmNotification;
use Mo<PERSON>les\JobSeeker\Notifications\JobAlertNotification;
use Carbon\Carbon;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;

class ProcessJobNotificationForRecipientJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * The notification setup ID
     *
     * @var int
     */
    protected $setupId;
    
    /**
     * The recipient email address
     * 
     * @var string
     */
    protected $recipientEmail;
    
    /**
     * The recipient name (optional)
     * 
     * @var string|null
     */
    protected $recipientName;

    /**
     * Create a new job instance.
     *
     * @param int $setupId
     * @param string $recipientEmail
     * @param string|null $recipientName
     * @return void
     */
    public function __construct(int $setupId, string $recipientEmail, ?string $recipientName = null)
    {
        $this->setupId = $setupId;
        $this->recipientEmail = $recipientEmail;
        $this->recipientName = $recipientName;
        $this->connection = 'job_notifications';
        $this->queue = 'recipient_processors';
    }

    /**
     * Execute the job.
     *
     * @param JobRepository $jobRepository
     * @return void
     */
    public function handle(JobRepository $jobRepository)
    {
        $jobId = $this->job->getJobId(); // Get the ID of this job instance
        Log::info("Processing job notification for recipient", [
            'setup_id' => $this->setupId,
            'recipient' => $this->recipientEmail,
            'job_id' => $jobId
        ]);

        try {
            // Fetch the setup with its categories and jobSeeker
            $setup = JobNotificationSetup::with(['categories', 'jobSeeker'])
                ->find($this->setupId);

            if (!$setup) {
                Log::warning("Job notification setup not found", ['setup_id' => $this->setupId]);
                return;
            }

            // Get category IDs
            $categoryIds = $setup->categories->pluck('id')->toArray();
            if (empty($categoryIds)) {
                Log::info("Setup has no categories, skipping", ['setup_id' => $setup->id]);
                return;
            }

            // Initialize variables to track job processing
            $newJobsForRecipient = collect();
            $chunkSize = 50; // Process 50 jobs at a time - smaller for per-recipient processing
            $totalNewJobs = 0;
            
            // Define a callback function to process each chunk of jobs
            $chunkCallback = function ($jobChunk) use ($jobRepository, $setup, &$newJobsForRecipient, &$totalNewJobs) {
                Log::debug("Processing job chunk for recipient", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'chunk_size' => $jobChunk->count()
                ]);
                
                foreach ($jobChunk as $job) {
                    // Check if this job has already been sent to this recipient
                    if (!$jobRepository->hasJobBeenSentToRecipient($setup->id, $job->id, $this->recipientEmail)) {
                        $newJobsForRecipient->push($job);
                        $totalNewJobs++;
                    }
                }
            };
            
            // Use the repository's chunked method to process jobs in batches
            $jobRepository->getRecentJobsByCategoriesChunked(
                $categoryIds,
                7, // Last 7 days
                $chunkSize,
                $chunkCallback
            );
            
            // If no new jobs were found for this recipient, we're done
            if ($newJobsForRecipient->isEmpty()) {
                Log::info("No new jobs to send to recipient", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail
                ]);
                return;
            }
            
            // Track notification success for each channel independently
            $emailSuccess = false;
            $fcmSuccess = false;
            
            // === EMAIL NOTIFICATION PROCESSING (INDEPENDENT) ===
            try {
                Log::info("Sending email notification to recipient", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'jobs_count' => $newJobsForRecipient->count()
                ]);

                // Recipient name fallback to part before @ in email if not provided
                $name = $this->recipientName ?? explode('@', $this->recipientEmail)[0];

                // Create a notifiable object for the recipient
                $notifiable = new class($this->recipientEmail, $name) {
                    public function __construct(public string $email, public string $name) {}
                    
                    public function routeNotificationForMail() {
                        return $this->email;
                    }
                };

                // Send notification using Laravel's notification system
                $notifiable->notify(new JobAlertNotification($newJobsForRecipient, $setup));
                
                $emailSuccess = true;
                Log::info("Successfully sent job notification email", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'jobs_count' => $newJobsForRecipient->count()
                ]);
                
            } catch (\Exception $emailError) {
                Log::error("Exception occurred while sending email notification", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'error' => $emailError->getMessage(),
                    'trace' => $emailError->getTraceAsString()
                ]);
            }

            // === FCM NOTIFICATION PROCESSING (INDEPENDENT) ===
            try {
                // Check if this specific setup has push notifications enabled
                if ($setup->receive_push_notifications && $setup->jobSeeker) {
                    Log::info("Attempting to send FCM notification", [
                        'setup_id' => $setup->id,
                        'job_seeker_id' => $setup->jobSeeker->id,
                        'jobs_count' => $newJobsForRecipient->count(),
                        'setup_push_enabled' => $setup->receive_push_notifications
                    ]);
                    
                    // Send FCM notification using the dedicated FCM notification class
                    // This will be queued to the 'fcm_notifications' queue
                    $setup->jobSeeker->notify(new JobAlertFcmNotification($newJobsForRecipient, $setup));
                    
                    $fcmSuccess = true;
                    Log::info("FCM notification dispatched successfully", [
                        'setup_id' => $setup->id,
                        'job_seeker_id' => $setup->jobSeeker->id,
                        'queue' => 'fcm_notifications'
                    ]);
                } else {
                    Log::info("FCM notification skipped", [
                        'setup_id' => $setup->id,
                        'job_seeker_id' => $setup->jobSeeker?->id,
                        'setup_push_enabled' => $setup->receive_push_notifications,
                        'has_job_seeker' => $setup->jobSeeker !== null,
                        'reason' => !$setup->receive_push_notifications ? 'push_notifications_disabled_for_setup' : 'no_job_seeker'
                    ]);
                }
            } catch (\Exception $fcmError) {
                Log::error("Exception occurred while dispatching FCM notification", [
                    'setup_id' => $setup->id,
                    'job_seeker_id' => $setup->jobSeeker?->id,
                    'error' => $fcmError->getMessage(),
                    'trace' => $fcmError->getTraceAsString()
                ]);
                // FCM failure should not affect the overall job success
            }

            // === RECORD SENT JOBS (if at least one channel succeeded) ===
            if ($emailSuccess || $fcmSuccess) {
                try {
                    // Record each sent job in a single transaction
                    DB::transaction(function() use ($setup, $newJobsForRecipient) {
                        foreach ($newJobsForRecipient as $job) {
                            JobNotificationSentJob::create([
                                'setup_id' => $setup->id,
                                'job_id' => $job->id,
                                'recipient_email' => $this->recipientEmail,
                                'sent_at' => now(),
                            ]);
                        }
                        
                        // Update the setup's sent count atomically
                        DB::table('job_notification_setups')
                            ->where('id', $setup->id)
                            ->increment('sent_count');
                    });

                    Log::info("Successfully recorded sent jobs", [
                        'setup_id' => $setup->id,
                        'recipient' => $this->recipientEmail,
                        'jobs_count' => $newJobsForRecipient->count(),
                        'email_success' => $emailSuccess,
                        'fcm_success' => $fcmSuccess
                    ]);
                } catch (\Exception $dbError) {
                    Log::error("Failed to record sent jobs in database", [
                        'setup_id' => $setup->id,
                        'recipient' => $this->recipientEmail,
                        'error' => $dbError->getMessage(),
                        'trace' => $dbError->getTraceAsString()
                    ]);
                    // Database recording failure should not fail the entire job
                }
            } else {
                Log::error("Both email and FCM notifications failed", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'email_success' => $emailSuccess,
                    'fcm_success' => $fcmSuccess
                ]);
                
                // If both channels failed, throw an exception to trigger job retry
                throw new \Exception('All notification channels failed for recipient: ' . $this->recipientEmail);
            }

            Log::info("Completed processing job notification for recipient", [
                'setup_id' => $setup->id, 
                'recipient' => $this->recipientEmail,
                'email_success' => $emailSuccess,
                'fcm_success' => $fcmSuccess,
                'jobs_processed' => $newJobsForRecipient->count()
            ]);

        } catch (\Exception $e) {
            Log::error("Error processing job notification for recipient", [
                'setup_id' => $this->setupId,
                'recipient' => $this->recipientEmail,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e; // Rethrow to trigger job retry
        }
    }

    /**
     * Format jobs for email template
     * 
     * @param \Illuminate\Database\Eloquent\Collection $jobs
     * @return array
     */
    protected function formatJobsForEmail($jobs)
    {
        $formattedJobs = [];
        $categoryPriority = [
            'IT - Software' => 1,
            'Software engineering' => 1,
            'software development' => 1, 
            'software development ' => 1,
            'Information Technology' => 1,
            'Leadership' => 2,
            'Management' => 3
        ];

        foreach ($jobs as $job) {
            // Determine job category priority based on position or category
            $priority = 10; // Default low priority
            $position = strtolower($job->position ?? '');
            
            // Check position for keywords
            if (strpos($position, 'developer') !== false || 
                strpos($position, 'engineer') !== false || 
                strpos($position, 'software') !== false || 
                strpos($position, 'it') !== false) {
                $priority = 1; // IT jobs
            } elseif (strpos($position, 'lead') !== false || 
                     strpos($position, 'chief') !== false || 
                     strpos($position, 'head') !== false || 
                     strpos($position, 'director') !== false) {
                $priority = 2; // Leadership jobs
            } elseif (strpos($position, 'manager') !== false || 
                     strpos($position, 'management') !== false) {
                $priority = 3; // Management jobs
            }
            
            $formattedJobs[] = [
                'position' => $job->position ?? '',
                'company_name' => $job->company_name ?? '',
                'locations' => $job->locations ?? '',
                'contract_type' => $job->contract_type ?? '',
                'work_type' => $job->work_type ?? '',
                'publish_date' => $job->publish_date ?? '',
                'salary' => $job->salary ?? '',
                'slug' => $job->slug ?? '',
                'updated_at' => $job->updated_at ?? now()->format('Y-m-d H:i:s'),
                'priority' => $priority // Add priority for sorting
            ];
        }
        
        // Sort formatted jobs by priority (lower number first)
        usort($formattedJobs, function($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });
        
        return $formattedJobs;
    }

    /**
     * Get relative time string (e.g., "2 hours ago", "1 day ago")
     *
     * @param string $dateString
     * @return string
     */
    protected function getTimeAgo($dateString)
    {
        try {
            $date = Carbon::parse($dateString);
            $now = Carbon::now();
            
            if ($date->diffInMinutes($now) < 60) {
                return $date->diffInMinutes($now) . ' minutes ago';
            } elseif ($date->diffInHours($now) < 24) {
                return $date->diffInHours($now) . ' hours ago';
            } else {
                return $date->diffInDays($now) . ' days ago';
            }
        } catch (\Exception $e) {
            Log::warning("Failed to parse date for time ago: {$dateString} - " . $e->getMessage());
            return 'recently';
        }
    }
} 