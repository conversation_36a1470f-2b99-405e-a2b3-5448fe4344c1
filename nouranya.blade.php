@extends('layouts.hound')
@section('mytitle' ,'Nouranya Report')
@section("css")
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" type="text/css" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.4.1/semantic.min.css" type="text/css" rel="stylesheet"/>
    <link href="https://netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.min.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/dropdown.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/search.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/icon.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/grid.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/transition.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/button.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/table.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/modal.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/progress.min.css')}}">
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
    <link href="https://cdn.datatables.net/1.13.2/css/jquery.dataTables.min.css" type="text/css" rel="stylesheet"/>
    <link href="https://cdn.datatables.net/buttons/2.3.4/css/buttons.dataTables.min.css" type="text/css" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr@latest/dist/plugins/monthSelect/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">

    <style>
        table.small-font {
            font-size: 12px;
        }

        .table-cell {
            white-space: nowrap;
        }

        .nouranya-table {
            border: 1px solid rgba(31, 255, 15, 0.471) !important;
        }

        .nouranya-table th,
        .nouranya-table td {
            border: 1px solid rgba(31, 255, 15, 0.471) !important;
        }

        .nouranya-table tbody tr:hover {
            background-color: rgba(31, 255, 15, 0.168) !important;
        }

        .index-text {
            color: #b4eeb0 !important;
        }

        .nouranya-table td:first-child {
            color: #b4eeb0 !important;
        }

        .dropdown-container {
            position: relative;
            border: 2px solid transparent;
            border-radius: 5px;
        }

        @keyframes border-colors {
            0% { border-color: transparent; }
            25% { border-color: #FF5252; }
            50% { border-color: #FFEA00; }
            75% { border-color: #00E676; }
            100% { border-color: #448AFF; }
        }

        .loading {
            animation: border-colors 8s linear infinite;
        }

        @media print {
            .ui.table {
                border-collapse: collapse !important;
            }
            .ui.table th, .ui.table td {
                border: 1px solid black !important;
            }
            .no-print {
                display: none !important;
            }
            .print-header {
                display: block !important;
                text-align: center;
                margin-bottom: 20px;
            }
            .print-header img {
                max-width: 150px;
                margin-bottom: 10px;
            }
            .print-header h2 {
                margin: 5px 0;
                font-size: 18px;
            }
            .print-header p {
                margin: 5px 0;
                font-size: 14px;
            }
        }

        .nav-tabs > li.active > a {
            border-bottom: 2px solid #009933 !important;
        }

        .custom-breadcrumb li + li:before {
            content: "|";
            color: #333;
            padding: 0 5px;
            font-size: 21px;
        }

        .custom-breadcrumb li {
            font-size: 21px;
        }

        .custom-breadcrumb li a {
            font-size: 21px;
        }

        .custom-breadcrumb li.active {
            color: #009933;
        }

        .custom-breadcrumb li.active a {
            color: #009933;
        }

        .print-header {
            display: none;
        }

        /* DataTable Buttons Styling */
        .dt-buttons {
            margin-bottom: 10px;
        }

        .dt-button {
            background-color: #1fff0f !important;
            border: 1px solid #1fff0f !important;
            color: #333 !important;
            font-weight: bold !important;
        }

        .dt-button:hover {
            background-color: #00e676 !important;
            border-color: #00e676 !important;
        }

        /* Attendance Progress Bar Styling */
        .attendance-progress,
        .achievement-progress {
            cursor: pointer !important;
            transition: all 0.2s ease;
        }

        .attendance-progress:hover,
        .achievement-progress:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .attendance-progress:hover::after,
        .achievement-progress:hover::after {
            content: "Click for details";
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            white-space: nowrap;
            z-index: 1000;
        }

        /* Modal Styling */
        .attendance-popup-content {
            font-family: Arial, sans-serif;
        }

        .attendance-popup-content .table td {
            padding: 5px 8px;
            border: none;
            border-bottom: 1px solid #eee;
        }

        .attendance-popup-content .table tr:last-child td {
            border-bottom: none;
        }

        .attendance-popup-content .well {
            padding: 15px;
            margin-bottom: 0;
        }

        /* Unique lesson count styling */
        .text-muted {
            color: #6c757d !important;
            font-style: italic;
        }

        /* SweetAlert2 Custom Styling for Attendance Popup */
        .attendance-sweet-alert .swal2-title {
            color: #009933 !important;
            font-size: 20px !important;
        }

        .attendance-sweet-alert .swal2-content {
            text-align: left !important;
        }

        .attendance-sweet-alert .table {
            font-size: 13px;
        }

        .attendance-sweet-alert .table td {
            padding: 5px 8px;
            border: none;
            border-bottom: 1px solid #eee;
        }

        .attendance-sweet-alert .table tr:last-child td {
            border-bottom: none;
        }

        .attendance-sweet-alert .alert {
            border-radius: 5px;
            padding: 10px 15px;
        }

        .attendance-sweet-alert .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        /* Classes Dropdown Trigger Styling */
        .classes-dropdown-trigger {
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none !important;
        }

        .classes-dropdown-trigger:hover {
            background-color: rgba(31, 255, 15, 0.2) !important;
            border-radius: 4px !important;
            padding: 2px 6px !important;
            transform: scale(1.02);
        }

        .classes-dropdown-trigger:hover .fa-caret-down {
            animation: bounce 0.6s ease-in-out infinite alternate;
        }

        @keyframes bounce {
            0% { transform: translateY(0); }
            100% { transform: translateY(-3px); }
        }

        /* SweetAlert2 Custom Classes Navigation Styling */
        .classes-navigation-alert {
            animation: slideInFromTop 0.3s ease-out !important;
        }
        
        .classes-navigation-alert .swal2-popup {
            margin-top: 80px !important; /* Position below breadcrumb */
            box-shadow: 0 10px 30px rgba(0, 153, 51, 0.2) !important;
            border: 2px solid rgba(0, 153, 51, 0.1) !important;
        }

        .classes-navigation-alert .swal2-title {
            color: #009933 !important;
            font-size: 22px !important;
            margin-bottom: 20px !important;
        }

        .classes-navigation-alert .swal2-content {
            text-align: left !important;
            padding: 0 !important;
        }

        @keyframes slideInFromTop {
            0% {
                opacity: 0;
                transform: translateY(-20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .classes-search-container {
            margin-bottom: 15px;
            position: relative;
        }

        .classes-search-input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .classes-search-input:focus {
            outline: none;
            border-color: #009933;
            box-shadow: 0 0 0 3px rgba(0, 153, 51, 0.1);
        }

        .classes-search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .program-group {
            margin-bottom: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .program-header {
            background: linear-gradient(135deg, #009933, #00b33c);
            color: white;
            padding: 12px 15px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }

        .program-header:hover {
            background: linear-gradient(135deg, #00b33c, #009933);
        }

        .program-header .toggle-icon {
            transition: transform 0.3s ease;
        }

        .program-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .classes-list {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .class-item {
            padding: 12px 15px;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: flex-start; /* Changed from center to flex-start */
        }

        .class-item:last-child {
            border-bottom: none;
        }

        .class-item:hover {
            background: rgba(0, 153, 51, 0.1);
            transform: translateX(5px);
        }

        .class-item.current-class {
            background: rgba(0, 153, 51, 0.2);
            border-left: 4px solid #009933;
            font-weight: bold;
        }

        .class-item.current-class::before {
            content: "👉 ";
            margin-right: 5px;
        }

        .class-main-info {
            display: flex;
            flex-direction: column;
            flex: 1; /* Take up available space */
            text-align: left; /* Ensure left alignment */
        }

        .class-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 3px;
            text-align: left; /* Explicitly set left alignment */
        }

        .class-details {
            font-size: 12px;
            color: #666;
            text-align: left; /* Explicitly set left alignment */
        }

        /* Teacher link styling */
        .teacher-link {
            color: #007bff;
            text-decoration: none;
            cursor: pointer;
        }

        .teacher-link:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        .class-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            font-size: 11px;
            color: #888;
        }

        .student-count {
            background: #009933;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            margin-bottom: 2px;
        }

        .class-actions {
            margin-top: 5px;
        }

        .class-action-btn {
            font-size: 10px;
            padding: 2px 6px;
            margin: 0 2px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            color: white;
            transition: all 0.2s ease;
        }

        .action-report { background: #17a2b8; }
        .action-show { background: #6c757d; }
        .action-reports { background: #ffc107; color: #333; }

        .class-action-btn:hover {
            transform: scale(1.1);
            text-decoration: none;
            color: white;
        }

        .no-classes-found {
            text-align: center;
            padding: 30px;
            color: #666;
            font-style: italic;
        }

        /* Scrollbar styling for classes list */
        .classes-list::-webkit-scrollbar {
            width: 6px;
        }

        .classes-list::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .classes-list::-webkit-scrollbar-thumb {
            background: #009933;
            border-radius: 3px;
        }

        .classes-list::-webkit-scrollbar-thumb:hover {
            background: #007a29;
        }


        /* * ===================================================================
     * SweetAlert2 Conflict Fix for Bootstrap 3 & Semantic UI
     * ===================================================================
     * This forces the SweetAlert2 container to the highest z-index, 
     * preventing Bootstrap/Semantic overlays from blocking clicks.
    */
    body.swal2-shown > [aria-hidden="true"] {
        transition: 0.1s filter;
        filter: blur(5px);
    }
    .swal2-container {
        z-index: 9999 !important;
    }
    </style>
@endsection

@section('content')
    <div class="pull-right text-success no-print">
        <ol class="breadcrumb custom-breadcrumb">
            <li><a href="{{url('workplace')}}">Dashboard</a></li>
            <li class="dropdown-container">
                <a href="{{url('workplace/education/classes/')}}" id="classesDropdownTrigger" 
                   class="classes-dropdown-trigger" 
                   data-current-class-id="{{ request()->route()->parameter('classId') }}">
                    <i class="fa fa-list-ul"></i> Classes
                    <i class="fa fa-caret-down" style="margin-left: 5px;"></i>
                </a>
            </li>
            <li class="active">Nouranya Report</li>
        </ol>
    </div>

    <div class="print-header">
        <img src="{{ asset('images/logo.png') }}" alt="School Logo">
        <h2>{{ config('app.name') }}</h2>
        <h3>Nouranya Class Report</h3>
        <p>Class: <span id="print-class-name"></span></p>
        <p>Month-Year: <span id="print-month-year"></span></p>
    </div>

    <div class="panel-heading no-print">
        <a class="section" target="_blank" href="#">Class Details <i class="external alternate icon"></i></a>
    </div>

    <div class="panel-body">
        @include('modules.education.classes.reports.class.partials.report_tabs')

        <div class="tab-content table-bordered" id="myTabContent">
            <div class="tab-pane fade active in" role="tabpanel">
                <div class="panel-body">
                    <div class="container-fluid">
                        <br>
                        <div class="row no-print">
                            <div class="col-md-3">
                                <label>Month-Year:</label>
                                <div id="formLoader">
                                    <select name="monthlyNouranyaReportMonthYear" class="form-control" id="monthlyNouranyaReportMonthYearList">
                                        <option value="">Select Month-Year</option>
                                        @foreach($monthlyHalaqahReportMonthYearList as $report)
                                            <option value="{{ $report->month . ' ' . $report->year }}">
                                                {{ $report->month . ' ' . $report->year }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-xs-3">
                                <!-- The print button -->
                                <input type="hidden" id="baseUrl"
                                       value="{{ route('education-nouranya-class-tables-pdf',['classId'=>'CLASS_ID','monthYear'=>'MONTH_YEAR']) }}">

                                <a style="display: none;margin-top: 27px;" target="_blank" id="printAllTablesBtn" href=""
                                   class="btn btn-primary">Print all Tables</a>
                            </div>
                        </div>
                        <br>

                        <div id="nouranya-report-intro">
                            <h4>Monthly Nouranya Progress Report</h4>
                            </div>

                        <div class="NouranyaReportTableLoader">
                            <table class="small-font ui inverted table nouranya-table" id="NouranyaReportDatatable" style="width:100%">
                                    <thead style="background-color: rgba(31,255,15,0.168);">
                                    <tr>
                                        <th class="tableColumnHeadingNormalFont">ID</th>
                                        <th class="tableColumnHeadingNormalFont">Student</th>
                                        <th class="tableColumnHeadingNormalFont">Entry1</th>
                                        <th class="tableColumnHeadingNormalFont">Entry2</th>
                                        <th class="tableColumnHeadingNormalFont">Entry3</th>
                                        <th class="tableColumnHeadingNormalFont">Entry4</th>
                                        <th class="tableColumnHeadingNormalFont">Monthly Plan</th>
                                        <th class="tableColumnHeadingNormalFont">Monthly Achievement</th>
                                        <th class="tableColumnHeadingNormalFont">Attendance %</th>
                                        <th class="tableColumnHeadingNormalFont">Achievement %</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                    </div>

                    <br>
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-xs-12">
                                    <h4 class="text-left">Class Summary for the Month:</h4>
                            </div>
                        </div>
                        <br>
                        <div class="row">
                            <div class="col-xs-12">
                                    <div class="NouranyaSummaryTableLoader">
                                        <table id="NouranyaSummaryTable" class="small-font ui inverted table nouranya-table" style="width:100%">
                                        <thead style="background-color: rgba(31,255,15,0.168);">
                                        <tr>
                                                    <th class="tableColumnHeadingNormalFont">No. of Students</th>
                                                    <th class="tableColumnHeadingNormalFont">Avg. Attendance %</th>
                                                    <th class="tableColumnHeadingNormalFont">Avg. Achievement %</th>
                                                    <th class="tableColumnHeadingNormalFont">Total Lessons Planned</th>
                                                    <th class="tableColumnHeadingNormalFont">Total Lessons Completed</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
@endsection

    @include('jssnippets.flatpickr')

    <script src="{{ asset('js/jquery351.min.js')}}"></script>

    <!-- corals js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.4.0/clipboard.min.js"></script>
    <script src="{{ asset('/assets/workplace/hound/vendors/raphael/raphael.min.js') }}"></script>
    <script src="{{ asset('/assets/workplace/hound/vendors/morris.js/morris.min.js') }}"></script>

    <script src="{{ asset('js/semantic/semantic.min.js')}}"></script>
    <script src="{{ asset('js/semantic/components/dropdown.js')}}"></script>
    <script src="{{ asset('js/semantic/components/search.js')}}"></script>
    <script src="{{ asset('js/semantic/components/transition.js')}}"></script>
    <script src="{{ asset('js/semantic/components/progress.min.js')}}"></script>
    <script src="{{ asset('js/semantic/components/form.min.js')}}"></script>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr@latest/dist/plugins/monthSelect/index.js"></script>

    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    @section('js')
        <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
        <script src="https://fullcalendar.io/js/fullcalendar-2.1.1/lib/jquery-ui.custom.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.9.0/fullcalendar.js"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/dataTables.buttons.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/buttons.bootstrap.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/buttons.colVis.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/buttons.flash.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/buttons.html5.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/buttons.print.min.js')}}"></script>
        <script src="https://cdn.datatables.net/select/1.4.0/js/dataTables.select.min.js"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/dataTables.colReorder.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/dataTables.keyTable.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/dataTables.responsive.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/responsive.bootstrap.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/dataTables.searchBuilder.min.js')}}"></script>
        <script src="{{ asset('assets/workplace/hound/js/datatables/searchBuilder.bootstrap.min.js')}}"></script>
    @append

    <script>
$(document).ready(function() {
    let nouranyaReportTable = null;
    let nouranySummaryTable = null;

    // Month-Year selection change handler with improved error handling
    $('#monthlyNouranyaReportMonthYearList').change(function() {
        const monthYear = $(this).val();
        if (monthYear) {
            try {
                // Show loading states
                $('.NouranyaReportTableLoader, .NouranyaSummaryTableLoader').addClass('loading');

                // Update print button URL
                var classId = "{{ request()->route()->parameter('classId') }}";
                var baseUrl = $("#baseUrl").val();
                var newUrl = baseUrl.replace('CLASS_ID', classId).replace('MONTH_YEAR', encodeURIComponent(monthYear));
                $('#printAllTablesBtn').attr('href', newUrl);
                $('#printAllTablesBtn').show();

                // Destroy existing tables if they exist
                if (nouranyaReportTable) {
                    nouranyaReportTable.destroy();
                }
                if (nouranySummaryTable) {
                    nouranySummaryTable.destroy();
                }

                // Initialize student progress table with comprehensive buttons
                nouranyaReportTable = $('#NouranyaReportDatatable').DataTable({
                    processing: true,
                    serverSide: true,
                        searching: false,
                        info: false,
                    autoWidth: true,
                        destroy: true,
                        classes: {
                            "sWrapper": "",
                            "sFilterInput": "",
                            "sLengthSelect": ""
                        },
                        "language": {
                            "lengthMenu": "Show _MENU_ entries",
                            "zeroRecords": "",
                            "emptyTable": "",
                            "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                            "infoEmpty": "Showing 0 to 0 of 0 entries",
                            "infoFiltered": "(filtered from _MAX_ total entries)",
                        },
                        dom: 'Bfrtip',
                        buttons: [
                            {
                            text: '<i class="fa fa-refresh"></i>',
                                titleAttr: 'Reload',
                                action: function (e, dt, node, config) {
                                $(".NouranyaReportTableLoader").addClass("ui loading form");
                                nouranyaReportTable.ajax.reload(null, false);
                            },
                            className: 'button reloadButton'
                        },
                        {
                                extend: 'copyHtml5',
                                text: '<i class="fa fa-files-o"></i>',
                                title: $("#logo_title").val(),
                                titleAttr: 'Copy',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                }
                            },
                            {
                                extend: 'excelHtml5',
                                text: '<i class="fa fa-file-excel-o"></i>',
                                titleAttr: 'Excel',
                                title: $("#logo_title").val(),
                                margin: [10, 10, 10, 0],
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                },
                            },
                            {
                                extend: 'csvHtml5',
                                text: '<i class="fa fa-file-text"></i>',
                                titleAttr: 'CSV',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                }
                            },
                            {
                                extend: 'pdfHtml5',
                                text: '<i class="fa fa-file-pdf-o"></i>',
                                title: $("#logo_title").val(),
                                titleAttr: 'PDF',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                },
                                orientation: 'landscape',
                                pageSize: 'A4',
                                margin: [0, 0, 0, 12],
                                alignment: 'center',
                                header: true,
                                customize: function (doc) {
                                    doc.content.splice(1, 0, {
                                        margin: [0, 0, 0, 12],
                                        alignment: 'center',
                                        image: "data:image/png;base64," + $("#logo_img").val()
                                    });
                                }
                            },
                            {
                                extend: 'print',
                                text: '<i class="fa fa-print"></i>',
                                titleAttr: 'Print',
                                title: '',
                                customize: function (win) {
                                    $(win.document.body)
                                        .css('font-size', '10pt')
                                        .prepend(
                                            '<div style="text-align: center;">' +
                                        '<img src="{{ asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg") }}" style="width: 100%; height: auto;" />' +
                                            '</div>' +
                                        '<div style="text-align: center; font-size: 20px; font-weight: bold; margin-bottom: 10px;">Nouranya Progress Report</div>' +
                                            '<table class="print-table">' +
                                            '<tr>' +
                                        '<td><strong>Class:</strong> ' + '{{ $className }}' + '</td>' +
                                        '<td><strong>Teacher:</strong> ' + '{{ implode(", ", $classTeachers) }}' + '</td>' +
                                        '<td><strong>Month:</strong> ' + $('#monthlyNouranyaReportMonthYearList option:selected').text().split(' ')[0] + '</td>' +
                                            '</tr>' +
                                            '<tr>' +
                                        '<td><strong>Center:</strong> ' + '{{ $center }}' + '</td>' +
                                        '<td><strong>Supervisor:</strong> ' + '{{ $supervisorList }}' + '</td>' +
                                        '<td><strong>Year:</strong> ' + $('#monthlyNouranyaReportMonthYearList option:selected').text().split(' ')[1] + '</td>' +
                                            '</tr>' +
                                            '</table>'
                                        );

                                    // Remove the default date/time
                                    $(win.document.body).find('div.head').remove();
                                    $(win.document.body).find('table')
                                        .addClass('compact')
                                        .css('font-size', 'inherit');

                                // Adding border to the table and td elements
                                    $(win.document.body).find('table')
                                        .attr('style', 'border: 1px solid black !important');
                                    $(win.document.body).find('table td, table th')
                                        .attr('style', 'border: 1px solid black !important');

                                    // Add footer
                                    $(win.document.body).append(
                                        '<div style="position: absolute; bottom: 0; width: 100%; display: flex; justify-content: space-between; font-size: 10pt; font-weight: bold; color: darkgreen !important; margin-top: 20px;">' +
                                        '<div style="text-align: center; text-transform: uppercase; color: darkgreen !important; padding: 0 10px;">' +
                                        'YAYASAN PENDIDIKAN<br>ITQAN 529795-U' +
                                        '</div>' +
                                        '<div style="border-left: 2px solid darkgreen !important; height: 50px;"></div>' +
                                        '<div style="text-align: center; text-transform: uppercase; color: darkgreen !important; padding: 0 10px;">' +
                                        'No. ​56-2, Jalan Jernai 2, Medan Idaman,<br> Setapak 53100 Kuala Lumpur, Malaysia' +
                                        '</div>' +
                                        '<div style="border-left: 2px solid darkgreen !important; height: 50px;"></div>' +
                                        '<div style="text-align: center; color: darkgreen !important; padding: 0 10px;">' +
                                        'www.itqanalquran.org<br><EMAIL>' +
                                        '</div>' +
                                        '</div>'
                                    );
                                }
                            },
                            {
                                extend: 'print',
                                text: 'Print selected',
                                title: '',
                                customize: function (win) {
                                    $(win.document.body)
                                        .css('font-size', '10pt')
                                        .prepend(
                                            '<div style="text-align: center;">' +
                                        '<img src="{{ asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg") }}" style="width: 100%; height: auto;" />' +
                                            '</div>' +
                                        '<div style="text-align: center; font-size: 20px; font-weight: bold; margin-bottom: 10px;">Nouranya Progress Report</div>' +
                                            '<table class="print-table">' +
                                            '<tr>' +
                                        '<td><strong>Class:</strong> ' + '{{ $className }}' + '</td>' +
                                        '<td><strong>Teacher:</strong> ' + '{{ implode(", ", $classTeachers) }}' + '</td>' +
                                        '<td><strong>Month:</strong> ' + $('#monthlyNouranyaReportMonthYearList option:selected').text().split(' ')[0] + '</td>' +
                                            '</tr>' +
                                            '<tr>' +
                                        '<td><strong>Center:</strong> ' + '{{ $center }}' + '</td>' +
                                        '<td><strong>Supervisor:</strong> ' + '{{ $supervisorList }}' + '</td>' +
                                        '<td><strong>Year:</strong> ' + $('#monthlyNouranyaReportMonthYearList option:selected').text().split(' ')[1] + '</td>' +
                                            '</tr>' +
                                            '</table>'
                                        );

                                    // Remove the default date/time
                                    $(win.document.body).find('div.head').remove();
                                    $(win.document.body).find('table')
                                        .addClass('compact')
                                        .css('font-size', 'inherit');

                                // Adding border to the table and td elements
                                    $(win.document.body).find('table')
                                        .attr('style', 'border: 1px solid black !important');
                                    $(win.document.body).find('table td, table th')
                                        .attr('style', 'border: 1px solid black !important');

                                    // Add footer
                                    $(win.document.body).append(
                                        '<div style="position: absolute; bottom: 0; width: 100%; display: flex; justify-content: space-between; font-size: 10pt; font-weight: bold; color: darkgreen !important; margin-top: 20px;">' +
                                        '<div style="text-align: center; text-transform: uppercase; color: darkgreen !important; padding: 0 10px;">' +
                                        'YAYASAN PENDIDIKAN<br>ITQAN 529795-U' +
                                        '</div>' +
                                        '<div style="border-left: 2px solid darkgreen !important; height: 50px;"></div>' +
                                        '<div style="text-align: center; text-transform: uppercase; color: darkgreen !important; padding: 0 10px;">' +
                                        'No. ​56-2, Jalan Jernai 2, Medan Idaman,<br> Setapak 53100 Kuala Lumpur, Malaysia' +
                                        '</div>' +
                                        '<div style="border-left: 2px solid darkgreen !important; height: 50px;"></div>' +
                                        '<div style="text-align: center; color: darkgreen !important; padding: 0 10px;">' +
                                        'www.itqanalquran.org<br><EMAIL>' +
                                        '</div>' +
                                        '</div>'
                                    );
                                }
                            },
                            {
                                extend: 'colvis',
                                text: '<i class="fa fa-columns"></i>',
                                postfixButtons: ['colvisRestore']
                            }
                        ],
                        select: true,
                    ajax: {
                        url: '{{ route("monthly-nouranya-report") }}',
                        data: function(d) {
                            d.classId = '{{ $classId }}';
                            d.classDate = monthYear;
                        },
                        error: function(xhr, error, thrown) {
                            console.error('Error loading Nouranya report data:', error);
                            $('.NouranyaReportTableLoader').removeClass('loading');
                            
                                // Parse the JSON response from the server
                                var jsonResponse = JSON.parse(xhr.responseText);
                                var errorMessage = jsonResponse.message || 'An unknown error occurred';
                            var fullErrorMessage = `There is an error: ${errorMessage} when accessing this URL: {{ route("monthly-nouranya-report") }}`;

                                // Display the error using Toastr with HTML content
                                var toastrElement = toastr.error(`<div id="copyableToastr" style="cursor: pointer;">${fullErrorMessage} <span style="color: blue;">(Click to copy)</span></div>`, 'Error', {
                                    "timeOut": 0,
                                    "extendedTimeOut": 0,
                                    "closeButton": true,
                                    "tapToDismiss": false
                                });
                        },
                        complete: function() {
                            $('.NouranyaReportTableLoader').removeClass('loading');
                        }
                    },
                    columns: [
                            {data: 'DT_RowIndex', name: 'DT_RowIndex'},
                            {data: 'student', name: 'student'},
                        {data: 'entry1', name: 'entry1'},
                        {data: 'entry2', name: 'entry2'},
                        {data: 'entry3', name: 'entry3'},
                        {data: 'entry4', name: 'entry4'},
                        {data: 'monthlyPlan', name: 'monthlyPlan'},
                        {data: 'monthlyAchievement', name: 'monthlyAchievement'},
                        {data: 'attendancePercentage', name: 'attendancePercentage'},
                        {data: 'achievementPercentage', name: 'achievementPercentage'}
                    ],
                    order: [[0, 'asc']],
                    pageLength: 25,
                    rowCallback: function(row, data) {
                        // Add student ID as data attribute to the row
                        $(row).attr('data-student-id', data.student_id);
                    },
                    drawCallback: function() {
                        // Initialize attendance and achievement popups
                        initializeAttendancePopups();
                    }
                });

                // Hide the loading overlay when the table has finished loading
                nouranyaReportTable.on('xhr.dt', function (e, settings, json, xhr) {
                    $(".NouranyaReportTableLoader").removeClass("ui loading form");
                });

                // Initialize summary table with comprehensive buttons
                nouranySummaryTable = $('#NouranyaSummaryTable').DataTable({
                    processing: true,
                    serverSide: true,
                        searching: false,
                    paging: false,
                        info: false,
                    autoWidth: true,
                        destroy: true,
                        classes: {
                            "sWrapper": "",
                            "sFilterInput": "",
                            "sLengthSelect": ""
                        },
                        "language": {
                            "lengthMenu": "Show _MENU_ entries",
                            "zeroRecords": "",
                            "emptyTable": "",
                            "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                            "infoEmpty": "Showing 0 to 0 of 0 entries",
                            "infoFiltered": "(filtered from _MAX_ total entries)",
                        },
                        dom: 'Bfrtip',
                        buttons: [
                            {
                            text: '<i class="fa fa-refresh"></i>',
                                titleAttr: 'Reload',
                                action: function (e, dt, node, config) {
                                $(".NouranyaSummaryTableLoader").addClass("ui loading form");
                                nouranySummaryTable.ajax.reload(null, false);
                            },
                            className: 'button reloadButton'
                        },
                        {
                                extend: 'copyHtml5',
                                text: '<i class="fa fa-files-o"></i>',
                                title: $("#logo_title").val(),
                                titleAttr: 'Copy',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                }
                            },
                            {
                                extend: 'excelHtml5',
                                text: '<i class="fa fa-file-excel-o"></i>',
                                titleAttr: 'Excel',
                                title: $("#logo_title").val(),
                                margin: [10, 10, 10, 0],
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                },
                            },
                            {
                                extend: 'csvHtml5',
                                text: '<i class="fa fa-file-text"></i>',
                                titleAttr: 'CSV',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                }
                            },
                            {
                                extend: 'pdfHtml5',
                                text: '<i class="fa fa-file-pdf-o"></i>',
                                title: $("#logo_title").val(),
                                titleAttr: 'PDF',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                },
                                orientation: 'landscape',
                                pageSize: 'A4',
                                margin: [0, 0, 0, 12],
                                alignment: 'center',
                                header: true,
                                customize: function (doc) {
                                    doc.content.splice(1, 0, {
                                        margin: [0, 0, 0, 12],
                                        alignment: 'center',
                                        image: "data:image/png;base64," + $("#logo_img").val()
                                    });
                                }
                            },
                            {
                                extend: 'print',
                                text: '<i class="fa fa-print"></i>',
                                titleAttr: 'Print',
                                title: '',
                                customize: function (win) {
                                    $(win.document.body)
                                        .css('font-size', '10pt')
                                        .prepend(
                                            '<div style="text-align: center;">' +
                                        '<img src="{{ asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg") }}" style="width: 100%; height: auto;" />' +
                                            '</div>' +
                                        '<div style="text-align: center; font-size: 20px; font-weight: bold; margin-bottom: 10px;">Nouranya Summary Report</div>' +
                                            '<table class="print-table">' +
                                            '<tr>' +
                                        '<td><strong>Class:</strong> ' + '{{ $className }}' + '</td>' +
                                        '<td><strong>Teacher:</strong> ' + '{{ implode(", ", $classTeachers) }}' + '</td>' +
                                        '<td><strong>Month:</strong> ' + $('#monthlyNouranyaReportMonthYearList option:selected').text().split(' ')[0] + '</td>' +
                                            '</tr>' +
                                            '<tr>' +
                                        '<td><strong>Center:</strong> ' + '{{ $center }}' + '</td>' +
                                        '<td><strong>Supervisor:</strong> ' + '{{ $supervisorList }}' + '</td>' +
                                        '<td><strong>Year:</strong> ' + $('#monthlyNouranyaReportMonthYearList option:selected').text().split(' ')[1] + '</td>' +
                                            '</tr>' +
                                            '</table>'
                                        );

                                    // Remove the default date/time
                                    $(win.document.body).find('div.head').remove();
                                    $(win.document.body).find('table')
                                        .addClass('compact')
                                        .css('font-size', 'inherit');

                                // Adding border to the table and td elements
                                    $(win.document.body).find('table')
                                        .attr('style', 'border: 1px solid black !important');
                                    $(win.document.body).find('table td, table th')
                                        .attr('style', 'border: 1px solid black !important');

                                    // Add footer
                                    $(win.document.body).append(
                                        '<div style="position: absolute; bottom: 0; width: 100%; display: flex; justify-content: space-between; font-size: 10pt; font-weight: bold; color: darkgreen !important; margin-top: 20px;">' +
                                        '<div style="text-align: center; text-transform: uppercase; color: darkgreen !important; padding: 0 10px;">' +
                                        'YAYASAN PENDIDIKAN<br>ITQAN 529795-U' +
                                        '</div>' +
                                        '<div style="border-left: 2px solid darkgreen !important; height: 50px;"></div>' +
                                        '<div style="text-align: center; text-transform: uppercase; color: darkgreen !important; padding: 0 10px;">' +
                                        'No. ​56-2, Jalan Jernai 2, Medan Idaman,<br> Setapak 53100 Kuala Lumpur, Malaysia' +
                                        '</div>' +
                                        '<div style="border-left: 2px solid darkgreen !important; height: 50px;"></div>' +
                                        '<div style="text-align: center; color: darkgreen !important; padding: 0 10px;">' +
                                        'www.itqanalquran.org<br><EMAIL>' +
                                        '</div>' +
                                        '</div>'
                                    );
                                }
                            },
                            {
                                extend: 'colvis',
                                text: '<i class="fa fa-columns"></i>',
                                postfixButtons: ['colvisRestore']
                            }
                        ],
                        select: true,
                    ajax: {
                        url: '{{ route("month-end-nouranya-summary-report") }}',
                        data: function(d) {
                            d.classId = '{{ $classId }}';
                            d.classDate = monthYear;
                        },
                        error: function(xhr, error, thrown) {
                            console.error('Error loading summary data:', error);
                            $('.NouranyaSummaryTableLoader').removeClass('loading');
                            
                                // Parse the JSON response from the server
                                var jsonResponse = JSON.parse(xhr.responseText);
                                var errorMessage = jsonResponse.message || 'An unknown error occurred';
                            var fullErrorMessage = `There is an error: ${errorMessage} when accessing this URL: {{ route("month-end-nouranya-summary-report") }}`;

                                // Display the error using Toastr with HTML content
                                var toastrElement = toastr.error(`<div id="copyableToastr" style="cursor: pointer;">${fullErrorMessage} <span style="color: blue;">(Click to copy)</span></div>`, 'Error', {
                                    "timeOut": 0,
                                    "extendedTimeOut": 0,
                                    "closeButton": true,
                                    "tapToDismiss": false
                                });
                        },
                        complete: function() {
                            $('.NouranyaSummaryTableLoader').removeClass('loading');
                        }
                    },
                    columns: [
                        {data: 'noOfStudents', name: 'noOfStudents'},
                        {data: 'avgAttendance', name: 'avgAttendance'},
                        {data: 'avgAchievement', name: 'avgAchievement'},
                        {data: 'totalPlannedLessons', name: 'totalPlannedLessons'},
                        {data: 'totalCompletedLessons', name: 'totalCompletedLessons'}
                    ]
                });

                    // Hide the loading overlay when the table has finished loading
                nouranySummaryTable.on('xhr.dt', function (e, settings, json, xhr) {
                    $(".NouranyaSummaryTableLoader").removeClass("ui loading form");
                });

                // Update print header
                $('#print-month-year').text(monthYear);
            } catch (error) {
                console.error('Error initializing tables:', error);
                alert('An error occurred while loading the report data. Please try again.');
                $('.NouranyaReportTableLoader, .NouranyaSummaryTableLoader').removeClass('loading');
            }
        } else {
            // Clear tables if no month-year selected
            if (nouranyaReportTable) {
                nouranyaReportTable.clear().draw();
            }
            if (nouranySummaryTable) {
                nouranySummaryTable.clear().draw();
            }
            $('#printAllTablesBtn').hide();
        }
    });

    // Function to initialize attendance and achievement popups
    function initializeAttendancePopups() {
        $('.attendance-progress, .achievement-progress').off('click').on('click', function() {
            const detailsData = $(this).data('attendance-details') || $(this).data('achievement-details');
            const popupType = $(this).hasClass('attendance-progress') ? 'attendance' : 'achievement';
            if (detailsData) {
                showDetailsSweetAlert(detailsData, popupType);
            }
        });
    }

    // Function to show details popup using SweetAlert2
    function showDetailsSweetAlert(details, type) {
        let htmlContent = '';
        let title = '';
        
        if (type === 'attendance') {
            title = '<i class="fa fa-calendar-check-o" style="color: #009933;"></i> Attendance Details';
            
            if (details.error) {
                htmlContent = `
                    <div class="alert alert-warning" style="margin-bottom: 15px;">
                        <i class="fa fa-exclamation-triangle"></i> ${details.error}
                    </div>
                `;
            } else {
                htmlContent = `
                    <div style="text-align: left;">
                        <div class="row" style="margin-bottom: 20px;">
                            <div class="col-md-6">
                                <table class="table table-striped table-condensed" style="margin-bottom: 0;">
                                    <tr><td><strong>Total Scheduled Classes:</strong></td><td>${details.total_scheduled}</td></tr>
                                    <tr><td><strong>Classes Attended:</strong></td><td>${details.attended}</td></tr>
                                    <tr style="color: #28a745;"><td><strong>On Time:</strong></td><td>${details.on_time}</td></tr>
                                    <tr style="color: #ffc107;"><td><strong>Late:</strong></td><td>${details.late}</td></tr>
                                    <tr style="color: #dc3545;"><td><strong>Absent:</strong></td><td>${details.absent}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px;">
                                    <h5 style="color: #009933; margin-bottom: 10px;">
                                        <i class="fa fa-calculator"></i> Calculation Logic:
                                    </h5>
                                    <p style="font-size: 12px; margin-bottom: 10px;">${details.calculation}</p>
                                    <hr style="margin: 10px 0;">
                                    <p style="font-size: 11px; color: #6c757d; margin-bottom: 0;">
                                        <strong>Note:</strong> Attendance % = (On Time + Late) ÷ Total Scheduled × 100
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        } else if (type === 'achievement') {
            title = '<i class="fa fa-line-chart" style="color: #009933;"></i> Achievement Details';
            
            htmlContent = `
                <div style="text-align: left;">
                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col-md-6">
                            <table class="table table-striped table-condensed" style="margin-bottom: 0;">
                                <tr><td><strong>Total Reports:</strong></td><td>${details.total_reports}</td></tr>
                                <tr style="color: #28a745;"><td><strong>Completed Reports:</strong></td><td>${details.completed_reports}</td></tr>
                                <tr style="color: #dc3545;"><td><strong>Incomplete Reports:</strong></td><td>${details.incomplete_reports}</td></tr>
                                <tr style="color: #007bff;"><td><strong>Unique Lessons Covered:</strong></td><td>${details.unique_lessons}</td></tr>
                                <tr><td><strong>Lesson Range:</strong></td><td>${details.lesson_range}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px;">
                                <h5 style="color: #009933; margin-bottom: 10px;">
                                    <i class="fa fa-calculator"></i> Calculation Logic:
                                </h5>
                                <p style="font-size: 12px; margin-bottom: 10px;">${details.calculation}</p>
                                <hr style="margin: 10px 0;">
                                <p style="font-size: 11px; color: #6c757d; margin-bottom: 5px;">
                                    <strong>Plan Status:</strong> ${details.plan_info}
                                </p>
                                <p style="font-size: 11px; color: #6c757d; margin-bottom: 0;">
                                    <strong>Note:</strong> Achievement % = Total Reports ÷ Expected Reports × 100
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        Swal.fire({
            title: title,
            html: htmlContent,
            width: '700px',
            showCloseButton: true,
            showConfirmButton: true,
            confirmButtonText: '<i class="fa fa-check"></i> Close',
            confirmButtonColor: '#009933',
            customClass: {
                popup: 'attendance-sweet-alert',
                title: 'attendance-sweet-alert-title',
                content: 'attendance-sweet-alert-content'
            }
        });
    }

    // Set class name for print header
    $('#print-class-name').text('{{ $className }}');

    // Hide print button initially
    $('#printAllTablesBtn').hide();

    // Classes Navigation Dropdown Functionality
    let hoverTimeout;
    let isDropdownOpen = false;

    $('#classesDropdownTrigger').on('mouseenter', function() {
        const currentClassId = $(this).data('current-class-id');
        
        // Clear any existing timeout
        clearTimeout(hoverTimeout);
        
        // Set a small delay to prevent accidental triggers
        hoverTimeout = setTimeout(function() {
            if (!isDropdownOpen) {
                showClassesDropdown(currentClassId);
            }
        }, 500); // 500ms delay before showing dropdown
    });

    $('#classesDropdownTrigger').on('mouseleave', function() {
        // Clear the timeout if mouse leaves before delay completes
        clearTimeout(hoverTimeout);
    });

    function showClassesDropdown(currentClassId) {
        isDropdownOpen = true;

        // Show loading state, positioned at the top
        Swal.fire({
            title: '<i class="fa fa-spinner fa-spin"></i> Loading Classes...',
            html: '<div style="text-align: center; padding: 20px;">Fetching classes grouped by programs...</div>',
            allowOutsideClick: false, // Disallow closing while loading
            showConfirmButton: false,
            position: 'top',
            customClass: {
                popup: 'classes-navigation-alert'
            }
        });

        // Fetch classes data
        $.ajax({
            url: '{{ route("classes.grouped-by-program", ":currentClassId") }}'.replace(':currentClassId', currentClassId || ''),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json'
            },
            success: function(response) {
                if (response.success) {
                    displayClassesDropdown(response.data, currentClassId);
                } else {
                    showErrorMessage('Failed to load classes: ' + (response.message || 'Unknown error occurred'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading classes:', xhr.responseText);
                let errorMessage = 'Error loading classes. Please try again.';
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 500) {
                    errorMessage = 'Server error occurred. Please check if migrations are up to date.';
                } else if (xhr.status === 404) {
                    errorMessage = 'Classes data endpoint not found.';
                }
                
                showErrorMessage(errorMessage);
            }
        });
    }

    function displayClassesDropdown(programsData, currentClassId) {
        let searchHTML = `
            <div class="classes-search-container">
                <input type="text" id="classesSearchInput" class="classes-search-input" 
                       placeholder="Search classes by name, code, center, or teacher...">
                <i class="fa fa-search classes-search-icon"></i>
            </div>
        `;

        let programsHTML = '';
        
        if (programsData.length === 0) {
            programsHTML = '<div class="no-classes-found">No classes found.</div>';
        } else {
            programsData.forEach(function(program) {
                programsHTML += `
                    <div class="program-group" data-program-title="${program.program_title.toLowerCase()}">
                        <div class="program-header" onclick="toggleProgramGroup(this)">
                            <span><i class="fa fa-graduation-cap"></i> ${program.program_title} (${program.classes.length} classes)</span>
                            <i class="fa fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="classes-list">
                `;

                program.classes.forEach(function(classItem) {
                    const isCurrentClass = classItem.is_current ? 'current-class' : '';
                    
                    // Create teacher links
                    let teacherLinksHTML = '';
                    if (classItem.teachers_data && classItem.teachers_data.length > 0) {
                        const teacherLinks = classItem.teachers_data.slice(0, 2).map(teacher => {
                            const employeeShowUrl = '{{ route("employees.show", ":teacherId") }}'.replace(':teacherId', teacher.id);
                            return `<a href="${employeeShowUrl}" target="_blank" class="teacher-link">${teacher.name}</a>`;
                        }).join(', ');
                        
                        if (classItem.teachers_data.length > 2) {
                            teacherLinksHTML = teacherLinks + ` +${classItem.teachers_data.length - 2}`;
                        } else {
                            teacherLinksHTML = teacherLinks;
                        }
                    } else {
                        teacherLinksHTML = 'No teacher assigned';
                    }
                    
                    programsHTML += `
                        <div class="class-item ${isCurrentClass}" 
                             data-class-name="${classItem.name.toLowerCase()}"
                             data-class-code="${classItem.class_code.toLowerCase()}"
                             data-center-name="${classItem.center_name.toLowerCase()}"
                             data-teachers="${classItem.teachers.toLowerCase()}">
                            <div class="class-main-info">
                                <div class="class-name">${classItem.name}</div>
                                <div class="class-details">
                                    <i class="fa fa-code"></i> ${classItem.class_code} | 
                                    <i class="fa fa-map-marker"></i> ${classItem.center_name} | 
                                    <i class="fa fa-user"></i> ${teacherLinksHTML}
                                </div>
                            </div>
                            <div class="class-meta">
                                <span class="student-count">${classItem.student_count} students</span>
                                <div class="class-actions">
                                    <a href="${classItem.report_url}" target="_blank" class="class-action-btn action-report" title="Class Report">R</a>
                                    <a href="${classItem.class_show_url}" target="_blank" class="class-action-btn action-show" title="View Class">V</a>
                                    <a href="${classItem.monthly_plan_url}" target="_blank" class="class-action-btn action-reports" title="Monthly Plan">P</a>
                                </div>
                            </div>
                        </div>
                    `;
                });

                programsHTML += `
                        </div>
                    </div>
                `;
            });
        }

        Swal.fire({
            title: '<i class="fa fa-list-ul"></i> Class Navigation',
            html: searchHTML + '<div id="programsContainer">' + programsHTML + '</div>',
            width: '800px',
            position: 'top', // Position at the top
            showCancelButton: true,
            confirmButtonText: '<i class="fa fa-external-link"></i> Go to Classes Index',
            cancelButtonText: '<i class="fa fa-times"></i> Close',
            allowOutsideClick: true, // Allow closing on outside click
            allowEscapeKey: true, // Allow closing with ESC key
            showCloseButton: true, // Show top-right close button
            customClass: {
                popup: 'classes-navigation-alert'
            },
            didOpen: function() {
                setupSearchFunctionality();
                setupClassItemClick();
                
                // Auto-focus on search input
                setTimeout(() => {
                    $('#classesSearchInput').focus();
                }, 100);
            },
            willClose: function() {
                isDropdownOpen = false;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                window.open('{{ url("workplace/education/classes/") }}', '_blank');
            }
            isDropdownOpen = false;
        });
    }

    function setupSearchFunctionality() {
        $('#classesSearchInput').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            
            if (searchTerm === '') {
                // Show all programs and classes
                $('.program-group').show();
                $('.class-item').show();
                updateProgramCounters();
            } else {
                // Hide all items first
                $('.class-item').hide();
                $('.program-group').hide();
                
                // Show matching classes
                $('.class-item').each(function() {
                    const $item = $(this);
                    const className = $item.data('class-name') || '';
                    const classCode = $item.data('class-code') || '';
                    const centerName = $item.data('center-name') || '';
                    const teachers = $item.data('teachers') || '';
                    
                    if (className.includes(searchTerm) || 
                        classCode.includes(searchTerm) || 
                        centerName.includes(searchTerm) || 
                        teachers.includes(searchTerm)) {
                        $item.show();
                        $item.closest('.program-group').show();
                    }
                });
                
                updateProgramCounters();
            }
        });
    }

    function setupClassItemClick() {
        $('.class-item').on('click', function(e) {
            // Prevent default action if clicking on action buttons
            if ($(e.target).hasClass('class-action-btn')) {
                return;
            }
            
            // Get the report link (unified report URL)
            const reportLink = $(this).find('.action-report').attr('href');
            if (reportLink) {
                window.open(reportLink, '_blank');
            }
        });
    }

    function updateProgramCounters() {
        $('.program-group').each(function() {
            const $group = $(this);
            const visibleClasses = $group.find('.class-item:visible').length;
            const totalClasses = $group.find('.class-item').length;
            
            if (visibleClasses === 0) {
                $group.hide();
            } else {
                $group.show();
                const programTitle = $group.find('.program-header span').text().split('(')[0].trim();
                $group.find('.program-header span').text(`${programTitle} (${visibleClasses} of ${totalClasses} classes)`);
            }
        });
    }

    function showErrorMessage(message) {
        isDropdownOpen = false;
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message,
            customClass: {
                popup: 'classes-navigation-alert'
            }
        });
    }

    // Global function for toggling program groups
    window.toggleProgramGroup = function(header) {
        const $header = $(header);
        const $classesList = $header.next('.classes-list');
        const $toggleIcon = $header.find('.toggle-icon');
        
        if ($classesList.is(':visible')) {
            $classesList.slideUp(300);
            $header.addClass('collapsed');
            $toggleIcon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
        } else {
            $classesList.slideDown(300);
            $header.removeClass('collapsed');
            $toggleIcon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
        }
    };

        });
    </script>
