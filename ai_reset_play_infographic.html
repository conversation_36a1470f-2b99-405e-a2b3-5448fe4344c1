<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Reset Play: An AI Prompting Strategy</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-color: #121212;
            --desk-texture: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjMTgxNzE1Ij48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDVMNSAwWk02IDRMNCA2Wk0tMSAxTDEgLTFaIiBzdHJva2U9IiMyYzI4MjUiIHN0cm9rZS13aWR0aD0iMSI+PC9wYXRoPgo8L3N2Zz4=');
            --text-color: #e1e1e1;
            --text-muted: #9e9e9e;
            --font-family: 'Inter', sans-serif;
            --radius: 24px;
            --c-gold: #E5C585;
            --c-green: #2ECC71;
            --c-red: #E74C3C;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-color);
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 50px;
            box-sizing: border-box;
        }

        .infographic-container {
            width: 100%;
            max-width: 1800px;
            padding: 50px;
            background-color: #1a1a1a;
            background-image: var(--desk-texture);
            border-radius: var(--radius);
            box-shadow: inset 0 0 100px #000, 0 10px 40px rgba(0,0,0,0.4);
            border: 1px solid #333;
        }

        header {
            text-align: center;
            margin-bottom: 70px;
        }

        header h1 {
            font-size: 3.2rem;
            font-weight: 700;
            color: var(--c-gold);
        }

        header p {
            font-size: 1.25rem;
            color: var(--text-muted);
            max-width: 900px;
            margin: 15px auto 0;
        }

        .workflow-grid {
            display: grid;
            grid-template-columns: 1fr 0.5fr 1fr;
            gap: 50px;
            align-items: center;
        }

        .pitch-area {
            padding: 30px;
            border-radius: var(--radius);
            background: rgba(0,0,0,0.2);
            border: 1px solid #333;
            height: 100%;
        }

        .pitch-header {
            font-size: 1.6rem;
            font-weight: 600;
            margin-bottom: 30px;
            color: var(--text-muted);
            text-align: center;
        }

        .play-card {
            background: #2c2c2c;
            border: 1px solid #444;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            animation: fade-in 0.5s ease-out forwards;
            opacity: 0;
        }
        .play-card:nth-child(2) { animation-delay: 0.2s; }
        .play-card:nth-child(3) { animation-delay: 0.4s; }

        .goal-attempt {
            margin-top: 40px;
            text-align: center;
        }
        
        .goal-graphic {
            width: 200px;
            height: 120px;
            border: 4px solid #ccc;
            border-top: none;
            position: relative;
            margin: 0 auto;
        }
        .goal-graphic::before { /* Top bar */
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            height: 4px;
            background: #ccc;
        }

        .ball {
            width: 20px;
            height: 20px;
            background: #fff;
            border-radius: 50%;
            position: absolute;
            box-shadow: 0 0 10px #fff;
        }

        #ball-miss {
            bottom: 10px;
            left: -2px; /* Hit the post */
            animation: strike-post 1s ease-out 0.6s forwards;
        }
        @keyframes strike-post { from { transform: translate(0, 100px) scale(0.5); } to { transform: translate(0, 0) scale(1); } }

        #ball-goal {
            bottom: 10px;
            left: calc(50% - 10px); /* Center */
            animation: score-goal 1s ease-out 0.6s forwards;
        }
        @keyframes score-goal { from { transform: translate(0, 100px) scale(0.5); } to { transform: translate(0, 0) scale(1); } }

        .outcome-text {
            margin-top: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .outcome-text.miss { color: var(--c-red); }
        .outcome-text.goal { color: var(--c-green); }

        .strategy-session {
            text-align: center;
        }

        .playbook {
            width: 100%;
            padding: 25px;
            border-radius: 15px;
            background: linear-gradient(145deg, #4a3a2a, #3a2a1a);
            border: 2px solid var(--c-gold);
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            animation: fade-in 0.5s ease-out 0.8s forwards;
            opacity: 0;
        }
        .playbook:hover { transform: scale(1.05); }
        .playbook h3 { margin-top: 0; color: var(--c-gold); }
        .playbook p { font-size: 0.9rem; color: var(--text-muted); margin-bottom: 5px; }

        @keyframes fade-in { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }

    </style>
</head>
<body>

    <div class="infographic-container">
        <header>
            <h1>The Reset Play</h1>
            <p>The expert strategy for maintaining peak AI accuracy by recognizing context drift and resetting the play with a perfect, consolidated prompt.</p>
        </header>

        <div class="workflow-grid">
            <!-- The First Half: Accuracy Drift -->
            <div class="pitch-area">
                <h2 class="pitch-header">The First Half: Building the Play</h2>
                <div class="play-card"><strong>Play 1:</strong> "Make the register page mobile-first."</div>
                <div class="play-card"><strong>Play 2:</strong> "Good. Now fix the unexpected behavior on Android devices."</div>
                <div class="play-card"><strong>Play 3:</strong> "Okay, now try to add a loading spinner."</div>
                <div class="goal-attempt">
                    <div class="goal-graphic">
                        <div id="ball-miss" class="ball"></div>
                    </div>
                    <p class="outcome-text miss">Result: Hit the Post. (Accuracy Drift Detected)</p>
                </div>
            </div>

            <!-- Half-Time: The Strategy Session -->
            <div class="strategy-session">
                <div class="playbook">
                    <h3>The Playbook</h3>
                    <p><strong>Work Done:</strong> Mobile-first register page created; Android bug partially fixed.</p>
                    <p><strong>Problem:</strong> Spinner logic is conflicting with the Android fix.</p>
                    <p><strong>Next Play:</strong> Refactor the spinner using a modern async function and apply it correctly for both iOS and Android platforms.</p>
                </div>
            </div>

            <!-- The Second Half: Flawless Execution -->
            <div class="pitch-area">
                <h2 class="pitch-header">The Second Half: A Fresh Pitch</h2>
                <div class="play-card"><strong>Play 1 (The Playbook):</strong> "Work Done: Mobile-first page... Problem: Spinner logic... Next Play: Refactor the spinner..."</div>
                <div class="goal-attempt">
                    <div class="goal-graphic">
                        <div id="ball-goal" class="ball"></div>
                    </div>
                    <p class="outcome-text goal">Result: Perfect Goal!</p>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
