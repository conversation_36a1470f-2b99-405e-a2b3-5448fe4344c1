---
type: "always_apply"
---

# Laravel Project Guidelines

This document outlines the key architectural principles and technologies used in this Laravel 10 project. For detailed backend standards, please refer to the `[laravel_backend_development.mdc](mdc:.cursor/rules/laravel_backend_development.mdc)` file.

## Core Technologies

*   **Backend**: [Laravel 10](mdc:https:/laravel.com/docs/10.x), PHP 8.1+
*   **Frontend**: Bootstrap 3, jQuery
*   **Database**: MySQL
*   **Connection Details**:    
        * Database: itqan
        * Username: root
        * Password: (none)
        * Command: mysql -u root itqan
        
*   **Modular Structure**: [nwidart/laravel-modules](mdc:https:/nwidart.com/laravel-modules/v10/introduction)

## Core Development Principles

**🎯 These principles are MANDATORY for all development work and must be followed without exception.**

### Communication & Clarity Principle

*   **Ask for Clarification**: When uncertain about requirements, implementation details, or expected behavior, **ALWAYS ask for clarification instead of making assumptions or guessing**. This prevents:
    *   Wasted development time on incorrect implementations
    *   Introduction of bugs through misunderstood requirements
    *   Costly refactoring when assumptions prove wrong
    *   Degraded code quality from hallucinated solutions

*   **Validate Understanding**: Before implementing complex features, confirm your understanding of the requirements with stakeholders or team members.

### Laravel Architecture Respect

*   **Eloquent Active Record Pattern**: Respect and leverage Laravel's Eloquent ORM as an Active Record pattern implementation:
    *   Models should contain business logic relevant to the entity they represent
    *   Use Eloquent relationships (`hasMany`, `belongsTo`, `belongsToMany`, etc.) instead of manual joins
    *   Leverage Eloquent's built-in methods (`create`, `update`, `delete`, `find`, `where`, etc.)
    *   Implement model events, observers, and accessors/mutators for entity-specific behavior
    *   Use model scopes for reusable query logic
    *   Avoid bypassing Eloquent with raw SQL unless performance critically requires it

*   **Framework Conventions**: Follow Laravel's conventions and patterns rather than fighting against them:
    *   Use dependency injection and service container
    *   Leverage Laravel's built-in features before creating custom solutions
    *   Follow naming conventions for controllers, models, and database tables
    *   Use Laravel's validation, authentication, and authorization systems


**⚠️ NON-COMPLIANCE**: Admin interfaces that don't follow mobile-first principles will be rejected and must be redesigned.

## Project Structure & Conventions

*   **Modular Development**: The project uses the `nwidart/laravel-modules` package. All module-specific backend code (Controllers, Models, Routes) should reside within the corresponding directory under `[Modules/](mdc:Modules)`.
    *   **Example**: Code for the Admission module is located in `[Modules/Admission/](mdc:Modules/Admission)`.
*   **Routing**: Module-specific web routes are defined in `Modules/<ModuleName>/Http/routes.php` (e.g., `[Modules/Admission/Http/routes.php](mdc:Modules/Admission/Http/routes.php)`). API routes are in `Modules/<ModuleName>/Http/api.php`. Ensure the module's `RouteServiceProvider` is configured correctly.
*   **Controllers**: Place controllers within the `Http/Controllers` directory of the respective module. Controllers should be `final` and read-only. Use method injection for dependencies or dedicated Service classes.
*   **Models**: Place Eloquent models within the `Entities` directory of the respective module (e.g., `[Modules/Admission/Entities/](mdc:Modules/Admission/Entities)`). Models should be `final`.
*   **Database Schema/Seeding**: **Do not** use Laravel migrations or seeders. Perform schema changes and initial data seeding directly via MySQL queries. Use Eloquent ORM for all application-level data manipulation (CRUD).
*   **Logging**: Implement extensive logging. Logs are stored in `[storage/logs/](mdc:storage/logs)`. Log method entry/exit points, contextual information, warnings, and errors.
*   **Coding Standards**: Adhere strictly to PSR-12 coding standards. Use `declare(strict_types=1);` and explicit type hints.
*   **Testing**: Write unit and feature tests using PHPUnit. Place tests within the `Tests/` directory of each module or the main `[tests/](mdc:tests)` directory.

## Critical Safety Rules

*   **🚨 NEVER USE RefreshDatabase Trait**: The `use Illuminate\Foundation\Testing\RefreshDatabase;` trait is **STRICTLY PROHIBITED** in all test files and any other code. This trait drops and recreates the entire database schema, which can result in:
    *   **Complete loss of production data** if accidentally run against production database
    *   **Deletion of all database tables, indexes, and stored procedures**
    *   **Irreversible data destruction** that cannot be recovered
    *   **Loss of custom database objects** and configurations
    
    **⚠️ WARNING**: Any code containing `RefreshDatabase` will be immediately rejected and must be rewritten using safe alternatives.

*   **🚨 NEVER TOUCH .env file**: The `.env` file is strictly for environment-specific configuration and should **NEVER** be modified directly by application code or committed to version control. Modifying this file can lead to:
    *   **Production Outages**: Overwriting production settings with development values.
    *   **Security Breaches**: Exposing sensitive credentials if the file is accidentally committed.
    *   **Configuration Instability**: Making it difficult to manage different environment settings reliably.
    
    **Best Practices for Configuration:**
    *   Use the `config()` helper to access environment variables defined in `.env`.
    *   Core application configuration is in `@config/`. Module-specific configuration is in `Modules/<ModuleName>/Config/config.php`.
    
    **⚠️ WARNING**: Any direct modification of the `.env` file via code will be rejected. All configuration should be managed through Laravel's configuration system.

## Key Practices

*   **Validation**: Use Laravel's Form Request validation (`app/Http/Requests/` or module-specific request classes).
*   **Security**: Implement CSRF protection, sanitize inputs, use Eloquent/prepared statements to prevent SQL injection. Use Laravel's authorization features (Policies).
*   **Database Interactions**: 
    *   **Primary Rule**: Use Eloquent ORM for all application-level data manipulation (CRUD operations)
    *   **Eloquent Relationships**: Define and use proper Eloquent relationships instead of manual joins
    *   **Query Optimization**: Use eager loading (`with()`) to prevent N+1 query problems
    *   **Model Logic**: Place entity-specific business logic in models using accessors, mutators, and scopes
    *   **Raw SQL Exception**: Only use raw SQL for schema changes, complex reporting queries, or performance-critical operations where Eloquent's overhead is prohibitive
*   **Performance**:Leverage caching where appropriate. Minify assets.
*   **Services & Repositories**: Employ Service and Repository patterns for complex logic and data abstraction, placing them in `app/Services/`, `app/Repositories/` or module-specific directories.
*   **Error Handling**: Use Laravel's exception handling. Create custom exceptions as needed.
*   **Emailing**: All email sending functionality MUST be handled exclusively through the `[EmailService](mdc:app/Services/EmailService.php)`. This service is critical for maintaining consistent, reliable, and configurable email delivery across the entire application. Direct use of Laravel's `Mail` facade, `PHPMailer`, or any other email sending library is strictly prohibited.