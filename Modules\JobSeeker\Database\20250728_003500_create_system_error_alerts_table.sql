/*
 * Description: Create system_error_alerts table for comprehensive error monitoring and alerting
 * Module: JobSeeker
 * Author: System Error Notification Service
 * Date: 2025-07-28
 * 
 * IMPORTANT: This file must be reviewed and executed by authorized database administrators
 */

-- Create system_error_alerts table
CREATE TABLE IF NOT EXISTS `system_error_alerts` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `category` varchar(50) NOT NULL COMMENT 'Error category (job_fetch_failure, email_send_failure, etc.)',
    `severity` enum('critical','high','medium','low') NOT NULL DEFAULT 'medium' COMMENT 'Alert severity level',
    `title` varchar(255) NOT NULL COMMENT 'Alert title/summary',
    `message` text NOT NULL COMMENT 'Detailed error message',
    `context` json DEFAULT NULL COMMENT 'Additional context data as JSON',
    `exception_details` json DEFAULT NULL COMMENT 'Exception details if applicable',
    `resolved_at` timestamp NULL DEFAULT NULL COMMENT 'When the alert was resolved',
    `resolved_by` varchar(255) DEFAULT NULL COMMENT 'Who resolved the alert',
    `resolution_notes` text DEFAULT NULL COMMENT 'Notes about the resolution',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_severity` (`severity`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_resolved_at` (`resolved_at`),
    KEY `idx_category_severity` (`category`, `severity`),
    KEY `idx_unresolved` (`resolved_at`, `severity`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='System error alerts for monitoring and notification';

-- Create index for efficient querying of recent unresolved alerts
CREATE INDEX `idx_recent_unresolved` ON `system_error_alerts` (`created_at`, `resolved_at`, `severity`);

-- Insert sample alert categories for reference (optional)
INSERT IGNORE INTO `system_error_alerts` (`category`, `severity`, `title`, `message`, `context`, `created_at`, `updated_at`) VALUES
('job_fetch_failure', 'high', 'Sample Job Fetch Failure', 'This is a sample alert for testing purposes', '{"provider": "sample", "test": true}', NOW(), NOW()),
('email_send_failure', 'critical', 'Sample Email Send Failure', 'This is a sample email failure alert', '{"recipient": "<EMAIL>", "test": true}', NOW(), NOW());

-- Mark sample alerts as resolved immediately
UPDATE `system_error_alerts` 
SET `resolved_at` = NOW(), `resolved_by` = 'system', `resolution_notes` = 'Sample data - auto-resolved' 
WHERE `context`->>'$.test' = 'true';

-- Rollback procedure (commented out for safety)
-- DROP TABLE IF EXISTS `system_error_alerts`;
