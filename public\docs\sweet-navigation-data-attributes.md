# Sweet Navigation Component - Data Attributes Specification

## Overview

The Sweet Navigation component uses HTML data attributes to configure its behavior. This document provides a comprehensive specification of all available data attributes, their types, default values, and usage examples.

## Required Data Attributes

### `data-ajax-url`
- **Type**: String (URL)
- **Required**: Yes
- **Description**: The endpoint URL that returns navigation data in JSON format
- **Example**: `data-ajax-url="{{ route('api.navigation.classes') }}"`
- **Notes**: 
  - Must return JSON in the standardized format (see JSON specification)
  - Should handle CSRF tokens and authentication
  - Can include query parameters

### `data-title`
- **Type**: String
- **Required**: Yes
- **Description**: The title displayed in the SweetAlert2 popup header
- **Example**: `data-title="Class Navigation"`
- **Notes**: 
  - Supports HTML content (will be rendered as HTML)
  - Commonly includes Font Awesome icons
  - Keep concise for better UX

## Optional Data Attributes

### `data-current-id`
- **Type**: String/Number
- **Required**: No
- **Default**: `null`
- **Description**: ID of the currently selected item (will be highlighted)
- **Example**: `data-current-id="{{ $currentClassId }}"`
- **Notes**: 
  - Used to highlight the current item in the navigation
  - Compared against `item.id` in the JSON response
  - Can be string or numeric

### `data-confirm-button-text`
- **Type**: String (HTML)
- **Required**: No
- **Default**: `"<i class='fa fa-external-link'></i> Go to Index"`
- **Description**: Custom text for the confirm button
- **Example**: `data-confirm-button-text="<i class='fa fa-users'></i> Go to Students"`
- **Notes**: 
  - Supports HTML content including icons
  - Button appears at bottom of popup
  - If no URL provided, button does nothing

### `data-confirm-button-url`
- **Type**: String (URL)
- **Required**: No
- **Default**: `"#"`
- **Description**: URL to navigate to when confirm button is clicked
- **Example**: `data-confirm-button-url="{{ route('students.index') }}"`
- **Notes**: 
  - Supports both relative and absolute URLs
  - External URLs open in new tab/window
  - Use `#` to disable button action

### `data-width`
- **Type**: String (CSS width value)
- **Required**: No
- **Default**: `"800px"`
- **Description**: Custom width for the popup
- **Example**: `data-width="900px"` or `data-width="80%"`
- **Notes**: 
  - Accepts any valid CSS width value
  - Responsive design should be considered
  - Very large widths may cause UX issues on mobile

## Usage Examples

### Basic Usage
```html
<a href="#" class="sweet-navigation-trigger"
   data-ajax-url="/api/classes/navigation"
   data-title="Class Navigation">
    <i class="fa fa-list"></i> Classes
</a>
```

### Full Configuration
```html
<a href="#" class="sweet-navigation-trigger"
   data-ajax-url="{{ route('api.navigation.students') }}"
   data-title="<i class='fa fa-users'></i> Student Navigation"
   data-current-id="{{ $currentStudentId }}"
   data-confirm-button-text="<i class='fa fa-plus'></i> Add New Student"
   data-confirm-button-url="{{ route('students.create') }}"
   data-width="900px">
    <i class="fa fa-users"></i> Browse Students
</a>
```

### Laravel Blade Integration
```html
<!-- In breadcrumb navigation -->
<ol class="breadcrumb">
    <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="dropdown-container">
        <a href="#" class="sweet-navigation-trigger"
           data-ajax-url="{{ route('api.navigation.classes', ['current' => $class->id]) }}"
           data-title="Class Navigation"
           data-current-id="{{ $class->id }}"
           data-confirm-button-url="{{ route('classes.index') }}">
            <i class="fa fa-list-ul"></i> Classes
            <i class="fa fa-caret-down"></i>
        </a>
    </li>
    <li class="active">{{ $class->name }}</li>
</ol>
```

### Multiple Triggers on Same Page
```html
<!-- Each trigger works independently -->
<div class="navigation-menu">
    <a href="#" class="btn btn-primary sweet-navigation-trigger"
       data-ajax-url="/api/programs/navigation"
       data-title="Programs">
        <i class="fa fa-graduation-cap"></i> Programs
    </a>
    
    <a href="#" class="btn btn-success sweet-navigation-trigger"
       data-ajax-url="/api/teachers/navigation"
       data-title="Teachers">
        <i class="fa fa-user"></i> Teachers
    </a>
    
    <a href="#" class="btn btn-info sweet-navigation-trigger"
       data-ajax-url="/api/centers/navigation"
       data-title="Centers">
        <i class="fa fa-building"></i> Centers
    </a>
</div>
```

## Validation Rules

### Client-Side Validation
The JavaScript component performs the following validations:

1. **Required Attributes Check**:
   - `data-ajax-url` must be present and non-empty
   - `data-title` must be present and non-empty

2. **URL Validation**:
   - `data-ajax-url` should be a valid URL format
   - `data-confirm-button-url` should be a valid URL format (if provided)

3. **Type Validation**:
   - All attributes are treated as strings
   - Numeric values are converted to strings

### Server-Side Considerations
When implementing the backend endpoint:

1. **CSRF Protection**: Include CSRF token in AJAX headers
2. **Authentication**: Verify user permissions for navigation data
3. **Input Validation**: Validate any query parameters
4. **Rate Limiting**: Consider rate limiting for navigation endpoints

## Error Handling

### Missing Required Attributes
```javascript
// Console error will be logged:
"Sweet Navigation: data-ajax-url is required"
```

### Invalid AJAX URL
```javascript
// Error popup will be shown with message:
"Navigation data endpoint not found."
```

### Malformed JSON Response
```javascript
// Error popup will be shown with message:
"Failed to load navigation data: Invalid response format"
```

## Best Practices

### Performance
1. **Cache Navigation Data**: Cache frequently accessed navigation data
2. **Limit Items**: Keep navigation items under 100 per group for performance
3. **Lazy Loading**: Consider pagination for very large datasets

### UX/UI
1. **Consistent Styling**: Use consistent icons and text across triggers
2. **Loading States**: The component automatically shows loading states
3. **Keyboard Navigation**: Component supports ESC key and focus management
4. **Mobile Friendly**: Test on mobile devices, consider responsive widths

### Security
1. **Authorization**: Ensure users can only access navigation data they're authorized to see
2. **Input Sanitization**: Sanitize all data returned in JSON responses
3. **HTTPS**: Use HTTPS for all navigation endpoints in production

### Accessibility
1. **Alt Text**: Provide meaningful text content, not just icons
2. **ARIA Labels**: Consider adding ARIA labels for screen readers
3. **Keyboard Support**: Component supports keyboard navigation (ESC, Tab)
4. **Color Contrast**: Ensure sufficient color contrast in custom styling

## Troubleshooting

### Common Issues

1. **Popup Not Appearing**:
   - Check browser console for JavaScript errors
   - Verify `data-ajax-url` is correct and accessible
   - Ensure SweetAlert2 is loaded

2. **AJAX Errors**:
   - Check network tab for failed requests
   - Verify CSRF token is included
   - Check server logs for backend errors

3. **Styling Issues**:
   - Ensure `sweet-navigation.css` is loaded
   - Check for CSS conflicts with other frameworks
   - Verify SweetAlert2 override styles are applied

4. **Multiple Instances Interfering**:
   - Each trigger should work independently
   - Check for global JavaScript conflicts
   - Verify proper event namespacing

### Debug Mode
Enable debug mode in the configuration:
```javascript
window.SweetNavigationConfig.debug = true;
```

This will log additional information to the browser console for troubleshooting.
