--
-- Create provider_job_categories table for dynamic category mapping
-- This enables flexible provider-specific category handling without hardcoded configuration
--

CREATE TABLE provider_job_categories (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    provider_name varchar(100) NOT NULL COMMENT 'Provider identifier (e.g., jobs.af, acbar)',
    name varchar(255) NOT NULL COMMENT 'Human-readable category name',
    provider_identifier varchar(255) NOT NULL COMMENT 'The actual value sent to provider API',
    canonical_category_id int unsigned NOT NULL COMMENT 'FK to job_categories table',
    created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY provider_job_categories_provider_identifier_unique (provider_name, provider_identifier),
    KEY provider_job_categories_provider_name_index (provider_name),
    KEY provider_job_categories_canonical_category_id_index (canonical_category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better query performance
CREATE INDEX provider_job_categories_name_index ON provider_job_categories (name);
CREATE INDEX provider_job_categories_compound_provider_canonical ON provider_job_categories (provider_name, canonical_category_id);

-- Insert comments explaining the table structure
ALTER TABLE provider_job_categories COMMENT = 'Maps provider-specific category identifiers to canonical categories for dynamic translation';

-- Verify table creation
DESCRIBE provider_job_categories; 