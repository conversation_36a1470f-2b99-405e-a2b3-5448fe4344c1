# Sweet Navigation - Master Layout Integration Guide

## Overview

This guide explains how to integrate the Sweet Navigation component into the master Blade layout (`resources/views/layouts/hound.blade.php`) so that all views extending this layout can use the navigation functionality.

## Integration Options

### Option 1: Global Integration (Recommended)

Add Sweet Navigation assets to the master layout so they're available on all pages.

#### Step 1: Add CSS to Head Section

Add this after the existing CSS includes (around line 40):

```blade
<!-- Sweet Navigation CSS -->
<link rel="stylesheet" href="{{ asset('css/components/sweet-navigation.css') }}">
```

#### Step 2: Add JavaScript Before Closing Body Tag

Add this after the existing scripts (around line 3700, before the final `</body>` tag):

```blade
<!-- Sweet Navigation JavaScript -->
<script src="{{ asset('js/components/sweet-navigation.js') }}"></script>

<script>
    $(document).ready(function() {
        // Initialize Sweet Navigation component globally
        if (typeof initializeSweetNavigation === 'function') {
            initializeSweetNavigation();
            console.log('Sweet Navigation: Initialized globally');
        }
    });
</script>
```

#### Complete Integration Code

```blade
{{-- Add to head section after line 40 --}}
<!-- Sweet Navigation CSS -->
<link rel="stylesheet" href="{{ asset('css/components/sweet-navigation.css') }}">

{{-- Add before closing body tag around line 3700 --}}
<!-- Sweet Navigation JavaScript -->
<script src="{{ asset('js/components/sweet-navigation.js') }}"></script>

<script>
    $(document).ready(function() {
        // Initialize Sweet Navigation component globally
        if (typeof initializeSweetNavigation === 'function') {
            initializeSweetNavigation();
            console.log('Sweet Navigation: Initialized globally');
        }
        
        // Optional: Configure global settings
        window.SweetNavigationConfig = window.SweetNavigationConfig || {};
        window.SweetNavigationConfig.debug = {{ config('app.debug') ? 'true' : 'false' }};
    });
</script>
```

### Option 2: Conditional Integration

Only load Sweet Navigation assets when needed using a Blade directive.

#### Step 1: Create Blade Directive

Add to `app/Providers/AppServiceProvider.php`:

```php
use Illuminate\Support\Facades\Blade;

public function boot()
{
    Blade::directive('sweetNavigation', function () {
        return '<?php echo view("components.sweet-navigation-assets")->render(); ?>';
    });
}
```

#### Step 2: Use in Views

In views that need Sweet Navigation:

```blade
@extends('layouts.hound')

@section('css')
    @sweetNavigation
@endsection

@section('content')
    <!-- Your content with navigation triggers -->
    <a href="#" class="sweet-navigation-trigger"
       data-ajax-url="{{ route('api.navigation.classes') }}"
       data-title="Class Navigation">
        <i class="fa fa-list"></i> Classes
    </a>
@endsection
```

### Option 3: Stack-Based Integration (Most Flexible)

Modify the master layout to support stack-based asset inclusion.

#### Step 1: Add Stacks to Master Layout

Add to the head section (around line 40):

```blade
<!-- Additional CSS -->
@stack('head-styles')
```

Add before closing body tag (around line 3700):

```blade
<!-- Additional JavaScript -->
@stack('body-scripts')
```

#### Step 2: Use Sweet Navigation Assets Component

The existing `sweet-navigation-assets.blade.php` component already uses stacks, so you can simply include it in any view:

```blade
@extends('layouts.hound')

@section('content')
    {{-- Include Sweet Navigation assets --}}
    @include('components.sweet-navigation-assets')
    
    {{-- Your navigation triggers --}}
    <a href="#" class="sweet-navigation-trigger"
       data-ajax-url="{{ route('api.navigation.classes') }}"
       data-title="Class Navigation">
        <i class="fa fa-list"></i> Classes
    </a>
@endsection
```

## Recommended Implementation

Based on the existing `hound.blade.php` structure, here's the recommended approach:

### 1. Modify Master Layout

Add these changes to `resources/views/layouts/hound.blade.php`:

```blade
{{-- Around line 40, after existing CSS includes --}}
<!-- Sweet Navigation CSS -->
<link rel="stylesheet" href="{{ asset('css/components/sweet-navigation.css') }}">

{{-- Add stacks support in head section --}}
@stack('head-styles')

{{-- Around line 3700, before closing body tag --}}
<!-- Sweet Navigation JavaScript -->
<script src="{{ asset('js/components/sweet-navigation.js') }}"></script>

{{-- Add stacks support before closing body --}}
@stack('body-scripts')

<script>
    $(document).ready(function() {
        // Initialize Sweet Navigation globally
        if (typeof initializeSweetNavigation === 'function') {
            initializeSweetNavigation();
            
            @if(config('app.debug'))
                console.log('Sweet Navigation: Initialized globally');
            @endif
        }
        
        // Global configuration
        window.SweetNavigationConfig = window.SweetNavigationConfig || {};
        window.SweetNavigationConfig.debug = {{ config('app.debug') ? 'true' : 'false' }};
        window.SweetNavigationConfig.hoverDelay = 200;
    });
</script>
```

### 2. Usage in Views

After the master layout is updated, you can use Sweet Navigation in any view:

```blade
@extends('layouts.hound')

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="panel panel-default card-view">
                <div class="panel-heading">
                    <div class="pull-left">
                        <h6 class="panel-title txt-dark">
                            <a href="#" class="sweet-navigation-trigger"
                               data-ajax-url="{{ route('api.navigation.classes') }}"
                               data-title="Class Navigation"
                               data-current-id="{{ $class->id ?? '' }}">
                                <i class="fa fa-list-ul"></i> Classes
                                <i class="fa fa-caret-down"></i>
                            </a>
                        </h6>
                    </div>
                </div>
                <div class="panel-body">
                    <!-- Your content -->
                </div>
            </div>
        </div>
    </div>
@endsection
```

### 3. Breadcrumb Integration

For breadcrumb navigation (like the original nouranya.blade.php):

```blade
@extends('layouts.hound')

@section('content')
    <div class="pull-right text-success no-print">
        <ol class="breadcrumb custom-breadcrumb">
            <li><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="dropdown-container">
                <a href="#" class="sweet-navigation-trigger" 
                   data-ajax-url="{{ route('api.navigation.classes') }}"
                   data-title="Class Navigation"
                   data-current-id="{{ request()->route()->parameter('classId') }}"
                   data-confirm-button-text="<i class='fa fa-external-link'></i> Go to Classes Index"
                   data-confirm-button-url="{{ route('classes.index') }}">
                    <i class="fa fa-list-ul"></i> Classes
                    <i class="fa fa-caret-down" style="margin-left: 5px;"></i>
                </a>
            </li>
            <li class="active">Current Page</li>
        </ol>
    </div>
    
    <!-- Rest of your content -->
@endsection
```

## Implementation Steps

### Step 1: Update Master Layout

```bash
# Backup the original file
cp resources/views/layouts/hound.blade.php resources/views/layouts/hound.blade.php.backup
```

Edit `resources/views/layouts/hound.blade.php` and add the CSS and JavaScript as shown above.

### Step 2: Test Integration

Create a test view to verify the integration works:

```blade
{{-- resources/views/test-sweet-navigation.blade.php --}}
@extends('layouts.hound')

@section('mytitle', 'Sweet Navigation Test')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-12">
                <div class="panel panel-default card-view">
                    <div class="panel-heading">
                        <h6 class="panel-title txt-dark">Sweet Navigation Test</h6>
                    </div>
                    <div class="panel-body">
                        <p>Test the Sweet Navigation component:</p>
                        
                        <a href="#" class="btn btn-primary sweet-navigation-trigger"
                           data-ajax-url="{{ route('api.sweet-navigation.classes') }}"
                           data-title="Test Navigation">
                            <i class="fa fa-list"></i> Test Navigation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
```

### Step 3: Add Route for Test

```php
// routes/web.php
Route::get('/test-sweet-navigation', function () {
    return view('test-sweet-navigation');
})->name('test.sweet-navigation');
```

### Step 4: Verify Assets

Ensure the asset files exist:
- `public/css/components/sweet-navigation.css`
- `public/js/components/sweet-navigation.js`

### Step 5: Test in Browser

Visit `/test-sweet-navigation` and verify:
1. No console errors
2. Sweet Navigation CSS loads
3. Sweet Navigation JS loads
4. Hover triggers work
5. Navigation popup appears

## Troubleshooting

### Assets Not Loading

```bash
# Clear Laravel cache
php artisan cache:clear
php artisan config:clear

# Ensure assets exist
ls -la public/css/components/sweet-navigation.css
ls -la public/js/components/sweet-navigation.js
```

### JavaScript Errors

Check browser console for:
- jQuery conflicts
- SweetAlert2 conflicts
- Missing dependencies

### CSS Conflicts

The component CSS includes Bootstrap 3 compatibility. If you encounter styling issues:

```css
/* Add to your custom CSS */
.sweet-navigation-alert .swal2-popup {
    /* Override with higher specificity */
    margin-top: 80px !important;
}
```

## Performance Considerations

### Lazy Loading (Optional)

For better performance, you can lazy-load the component:

```javascript
// In master layout
function loadSweetNavigation() {
    if (window.sweetNavigationLoaded) return;
    
    // Load CSS
    const css = document.createElement('link');
    css.rel = 'stylesheet';
    css.href = '{{ asset("css/components/sweet-navigation.css") }}';
    document.head.appendChild(css);
    
    // Load JS
    const script = document.createElement('script');
    script.src = '{{ asset("js/components/sweet-navigation.js") }}';
    script.onload = function() {
        initializeSweetNavigation();
        window.sweetNavigationLoaded = true;
    };
    document.body.appendChild(script);
}

// Load when first trigger is hovered
$(document).on('mouseenter', '.sweet-navigation-trigger', function() {
    loadSweetNavigation();
});
```

### Caching

Enable browser caching for assets:

```php
// In your web server configuration or .htaccess
# Cache CSS and JS files for 1 year
<FilesMatch "\.(css|js)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
</FilesMatch>
```

This integration approach ensures that Sweet Navigation is available globally while maintaining flexibility for individual views to customize behavior as needed.
