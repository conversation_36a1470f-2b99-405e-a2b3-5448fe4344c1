<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Classes;
use App\Student;
use App\StudentHefzReport;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Modules\EducationalReports\Exports\HalaqahReportsExport;
use Modules\EducationalReports\Exports\HalaqahPlansExport;
use Carbon\Carbon;

final class HalaqahExcelExportController extends Controller
{
    /**
     * Export halaqah reports to Excel with Monthly Plans and Daily Reports sheets (Multi-parameter version)
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportMulti(Request $request)
    {
        try {
            // Validate required parameters
            if (!$request->has('monthYear') || empty($request->monthYear)) {
                return response()->json([
                    'error' => 'Month-Year parameter is required'
                ], 400);
            }

            // Parse classes and centers
            $classIds = $this->parseIds($request->get('classes', ''));
            $centerIds = $this->parseIds($request->get('centers', ''));
            
            if (empty($classIds)) {
                return response()->json([
                    'error' => 'At least one class must be selected'
                ], 400);
            }

            // Enforce limits
            if (count($classIds) > 5) {
                return response()->json([
                    'error' => 'Maximum 5 classes allowed'
                ], 400);
            }

            if (count($centerIds) > 5) {
                return response()->json([
                    'error' => 'Maximum 5 centers allowed'
                ], 400);
            }

            // Parse month-year
            $monthYear = $request->monthYear;
            $parts = explode(' ', $monthYear);

            if (count($parts) !== 2) {
                return response()->json([
                    'error' => 'Invalid month-year format'
                ], 400);
            }

            $monthName = $parts[0];
            $year = (int) $parts[1];

            try {
                $monthNumber = Carbon::parse($monthName . ' 1')->month;
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Invalid month name: ' . $monthName
                ], 400);
            }

            // Validate classes exist and user has permission
            $classes = Classes::with('center')->whereIn('id', $classIds)->get();
            if ($classes->count() !== count($classIds)) {
                return response()->json([
                    'error' => 'One or more classes not found'
                ], 404);
            }

            // Validate student if provided
            $studentId = $request->get('studentId');
            $student = null;
            if ($studentId) {
                $student = Student::find($studentId);
                if (!$student) {
                    return response()->json([
                        'error' => 'Student not found'
                    ], 404);
                }
            }

            // Check if there's any data to export
            $hasData = $this->checkMultiDataAvailability($classIds, $monthNumber, $year, $studentId);
            if (!$hasData) {
                return response()->json([
                    'error' => 'No data available for export with the specified filters',
                    'details' => 'No records found for the selected classes, month-year' . ($studentId ? ', and student' : '')
                ], 404);
            }

            // Set memory and time limits for large exports
            ini_set('memory_limit', '1024M');
            set_time_limit(600); // 10 minutes

            // Get filters
            $filters = [
                'classIds' => $classIds,
                'centerIds' => $centerIds,
                'year' => $year,
                'month' => $monthNumber,
                'monthName' => $monthName,
                'studentId' => $studentId,
                'classes' => $classes,
                'student' => $student,
                'exportType' => $request->get('exportType', 'reports') // 'plans' or 'reports'
            ];

            // Create enhanced filename
            $filename = $this->generateMultiFilename($classes, $monthName, $year, $student, $filters['exportType']);

            // Export using the appropriate Excel export class
            if ($filters['exportType'] === 'plans') {
                return Excel::download(new HalaqahPlansExport($filters), $filename);
            } else {
                return Excel::download(new HalaqahReportsExport($filters), $filename);
            }

        } catch (\Exception $e) {
            \Log::error('Error exporting multi halaqah reports to Excel: ' . $e->getMessage(), [
                'classes' => $request->get('classes'),
                'centers' => $request->get('centers'),
                'monthYear' => $request->get('monthYear'),
                'studentId' => $request->get('studentId'),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to export reports',
                'message' => $e->getMessage(),
                'details' => config('app.debug') ? $e->getTraceAsString() : 'Enable debug mode for details'
            ], 500);
        }
    }

    /**
     * Export halaqah reports to Excel with Monthly Plans and Daily Reports sheets (Single class - backward compatibility)
     * 
     * @param int $classId
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(int $classId, Request $request)
    {
        try {
            // Validate required parameters
            if (!$request->has('monthYear') || empty($request->monthYear)) {
                return response()->json([
                    'error' => 'Month-Year parameter is required'
                ], 400);
            }

            // Parse month-year
            $monthYear = $request->monthYear;
            $parts = explode(' ', $monthYear);

            if (count($parts) !== 2) {
                return response()->json([
                    'error' => 'Invalid month-year format'
                ], 400);
            }

            $monthName = $parts[0];
            $year = (int) $parts[1];

            try {
                $monthNumber = Carbon::parse($monthName . ' 1')->month;
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Invalid month name: ' . $monthName
                ], 400);
            }

            // Validate class exists
            $class = Classes::with('center')->find($classId);
            if (!$class) {
                return response()->json([
                    'error' => 'Class not found'
                ], 404);
            }

            // Validate student if provided
            $studentId = $request->get('studentId');
            $student = null;
            if ($studentId) {
                $student = Student::find($studentId);
                if (!$student) {
                    return response()->json([
                        'error' => 'Student not found'
                    ], 404);
                }
            }

            // Check if there's any data to export
            $hasData = $this->checkDataAvailability($classId, $monthNumber, $year, $studentId);
            if (!$hasData) {
                return response()->json([
                    'error' => 'No data available for export with the specified filters',
                    'details' => 'No hefz records found for the selected class, month-year' . ($studentId ? ', and student' : '')
                ], 404);
            }

            // Create enhanced filename
            $filename = $this->generateFilename($class, $monthName, $year, $student);

            // Set memory and time limits for large exports
            ini_set('memory_limit', '512M');
            set_time_limit(300); // 5 minutes

            // Get filters
            $filters = [
                'classId' => $classId,
                'year' => $year,
                'month' => $monthNumber,
                'monthName' => $monthName,
                'studentId' => $studentId,
                'class' => $class,
                'student' => $student
            ];

            // Export using the Excel export class
            return Excel::download(new HalaqahReportsExport($filters), $filename);

        } catch (\Exception $e) {
            \Log::error('Error exporting halaqah reports to Excel: ' . $e->getMessage(), [
                'classId' => $classId,
                'monthYear' => $request->get('monthYear'),
                'studentId' => $request->get('studentId'),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to export reports',
                'message' => $e->getMessage(),
                'details' => config('app.debug') ? $e->getTraceAsString() : 'Enable debug mode for details'
            ], 500);
        }
    }

    /**
     * Check if there's data available for export
     */
    private function checkDataAvailability($classId, $month, $year, $studentId = null)
    {
        $query = StudentHefzReport::where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('hefz_from_surat')
            ->whereNotNull('hefz_from_ayat')
            ->whereNotNull('hefz_to_surat')
            ->whereNotNull('hefz_to_ayat');

        if ($studentId) {
            $query->where('student_id', $studentId);
        }

        return $query->exists();
    }

    /**
     * Generate descriptive filename for export
     */
    private function generateFilename($class, $monthName, $year, $student = null)
    {
        $filename = 'Halaqah_Report';

        // Add center name if available
        if ($class->center && $class->center->name) {
            $centerName = str_replace([' ', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $class->center->name);
            $filename .= '_' . $centerName;
        }

        // Add class name
        $className = str_replace([' ', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $class->name);
        $filename .= '_' . $className;

        // Add month and year
        $filename .= '_' . $monthName . '_' . $year;

        // Add student name if filtering by specific student
        if ($student) {
            $studentName = str_replace([' ', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $student->full_name);
            $filename .= '_' . $studentName;
        }

        // Add timestamp to avoid conflicts
        $filename .= '_' . date('Y-m-d_H-i-s');

        return $filename . '.xlsx';
    }

    /**
     * Parse comma-separated IDs
     */
    private function parseIds(string $ids): array
    {
        if (empty($ids) || $ids === 'all') {
            return [];
        }
        
        return array_filter(array_map('intval', explode(',', $ids)));
    }

    /**
     * Check if there's data available for multi-class export
     */
    private function checkMultiDataAvailability(array $classIds, int $month, int $year, $studentId = null): bool
    {
        // Check for any student with active plans in the selected classes and month
        $hasPlans = StudentHefzPlan::whereIn('class_id', $classIds)
            ->where('plan_year_and_month', sprintf('%d-%02d', $year, $month))
            ->where('status', 'active')
            ->when($studentId, function($query, $studentId) {
                return $query->where('student_id', $studentId);
            })
            ->exists();

        $hasRevisionPlans = StudentRevisionPlan::whereIn('class_id', $classIds)
            ->where('plan_year_and_month', sprintf('%d-%02d', $year, $month))
            ->where('status', 'active')
            ->when($studentId, function($query, $studentId) {
                return $query->where('student_id', $studentId);
            })
            ->exists();

        return $hasPlans || $hasRevisionPlans;
    }

    /**
     * Generate descriptive filename for multi-export
     */
    private function generateMultiFilename($classes, string $monthName, int $year, $student = null, string $exportType = 'reports'): string
    {
        $filename = 'halaqah_' . $exportType;

        // Get unique center names
        $centerNames = $classes->pluck('center.name')->filter()->unique()->values();
        $classNames = $classes->pluck('name')->values();

        // Add centers (max 3, then "and_More")
        if ($centerNames->count() > 0) {
            $centerPart = $centerNames->take(3)->map(function($name) {
                return str_replace([' ', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $name);
            })->join('_');
            
            if ($centerNames->count() > 3) {
                $centerPart .= '_and_More';
            }
            
            $filename .= '_' . $centerPart;
        }

        // Add classes (max 3, then "and_More")
        if ($classNames->count() > 0) {
            $classPart = $classNames->take(3)->map(function($name) {
                return str_replace([' ', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $name);
            })->join('_');
            
            if ($classNames->count() > 3) {
                $classPart .= '_and_More';
            }
            
            $filename .= '_' . $classPart;
        }

        // Add month and year
        $filename .= '_' . $monthName . '_' . $year;

        // Add student name if filtering by specific student
        if ($student) {
            $studentName = str_replace([' ', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $student->full_name);
            $filename .= '_' . $studentName;
        }

        // Add timestamp to avoid conflicts
        $filename .= '_' . date('Y-m-d_H-i-s');

        return $filename . '.xlsx';
    }
}
