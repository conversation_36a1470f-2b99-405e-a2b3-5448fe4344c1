--
-- Rename provider_locations to provider_job_locations and add canonical relationship
-- This clarifies that these are job location mappings, not provider locations
--

-- Step 1: Rename the table to be more descriptive
RENAME TABLE provider_locations TO provider_job_locations;

-- Step 2: Add canonical_location_id foreign key column
ALTER TABLE provider_job_locations 
ADD COLUMN canonical_location_id int unsigned NOT NULL COMMENT 'FK to job_locations table' AFTER provider_identifier;

-- Step 3: Create foreign key constraint
ALTER TABLE provider_job_locations 
ADD CONSTRAINT fk_provider_job_locations_canonical_location_id 
FOREIGN KEY (canonical_location_id) REFERENCES job_locations(id) ON DELETE RESTRICT;

-- Step 4: Create compound index for efficient queries
CREATE INDEX provider_job_locations_canonical_provider ON provider_job_locations (canonical_location_id, provider_name);

-- Step 5: Update table comment
ALTER TABLE provider_job_locations COMMENT = 'Maps provider-specific job location identifiers to canonical locations for admin interface';

-- Step 6: Update unique constraint to include canonical_location_id for better data integrity
ALTER TABLE provider_job_locations DROP INDEX provider_locations_provider_identifier_unique;
ALTER TABLE provider_job_locations 
ADD CONSTRAINT provider_job_locations_provider_canonical_unique 
UNIQUE KEY (provider_name, canonical_location_id);

-- Verify changes
DESCRIBE provider_job_locations; 