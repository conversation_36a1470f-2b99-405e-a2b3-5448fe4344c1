<!DOCTYPE html>
<html>
<head>
    <title>Student Filter - Side Drawer Design</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            background: #f1f5f9;
            padding: 20px;
            color: #334155;
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Filter System Styling */
        .filter-system {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 24px;
            margin-bottom: 24px;
        }

        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .filter-title h3 {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
        }

        .progress-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f8fafc;
            border-radius: 8px;
            padding: 6px 12px;
            font-size: 14px;
            color: #64748b;
        }

        .progress-value {
            font-weight: 600;
            color: #1e40af;
        }

        /* Filter Cards - Horizontal Layout */
        .filter-row {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .filter-card {
            flex: 1;
            min-width: 240px;
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .filter-card.active {
            background: white;
            border-color: #10b981;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
        }

        .filter-card-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .filter-icon {
            width: 32px;
            height: 32px;
            background: #dbeafe;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1e40af;
        }

        .filter-label {
            font-weight: 500;
            color: #475569;
        }

        .filter-value {
            font-size: 14px;
            color: #1e293b;
            min-height: 20px;
        }

        .filter-value.selected {
            color: #10b981;
            font-weight: 500;
        }

        /* Student Selection Button - Special Treatment */
        .student-filter-card {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .student-filter-card:hover {
            background: #edf7f5;
            border-color: #b6f6e8;
        }

        .student-count {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: #dcfce7;
            color: #166534;
            border-radius: 20px;
            padding: 2px 10px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        /* Selection Panel - Side Drawer */
        .selection-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 380px;
            height: 100vh;
            background: white;
            box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            transition: right 0.3s ease;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .selection-panel.open {
            right: 0;
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            background: white;
        }

        .panel-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .panel-title h4 {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .close-panel {
            background: none;
            border: none;
            font-size: 20px;
            color: #64748b;
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-panel:hover {
            background: #f8fafc;
            color: #1e293b;
        }

        .panel-subtitle {
            color: #64748b;
            font-size: 14px;
        }

        .panel-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }

        .search-box {
            position: relative;
            margin-bottom: 20px;
        }

        .search-box input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
        }

        .filter-options {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .filter-option {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            white-space: nowrap;
        }

        .filter-option.active {
            background: #dcfce7;
            border-color: #a7f3d0;
            color: #166534;
        }

        .student-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .student-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .student-item:hover {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .student-item.selected {
            border-color: #10b981;
            background: #f0fdf4;
            position: relative;
        }

        .student-item.selected::after {
            content: "✓";
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #10b981;
            font-weight: bold;
        }

        .student-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #cbd5e1;
            border-radius: 4px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .student-item.selected .student-checkbox {
            border-color: #10b981;
            background: #10b981;
        }

        .student-checkbox::after {
            content: "";
            display: none;
            width: 8px;
            height: 4px;
            border: 2px solid white;
            border-top: none;
            border-right: none;
            transform: rotate(-45deg);
        }

        .student-item.selected .student-checkbox::after {
            display: block;
        }

        .student-info {
            flex: 1;
        }

        .student-name {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 3px;
        }

        .student-meta {
            font-size: 12px;
            color: #64748b;
        }

        .selection-summary {
            padding: 16px 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
        }

        .summary-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .selected-count {
            font-weight: 500;
            color: #1e293b;
        }

        .selected-count.highlight {
            color: #10b981;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 16px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-outline {
            background: white;
            border: 1px solid #e2e8f0;
            color: #475569;
        }

        .btn-outline:hover {
            background: #f8fafc;
            border-color: #cbd5e1;
        }

        .btn-primary {
            background: #10b981;
            color: white;
        }

        .btn-primary:hover {
            background: #0da272;
        }

        .btn-secondary {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #f1f5f9;
        }

        /* Overlay */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Action Bar */
        .action-bar {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 20px 0;
        }

        .main-btn {
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .btn-generate {
            background: #3b82f6;
            color: white;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-generate:hover {
            background: #2563eb;
        }

        .btn-reset {
            background: white;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }

        .btn-reset:hover {
            background: #f8fafc;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #94a3b8;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-weight: 500;
            margin-bottom: 8px;
            color: #475569;
        }

        .empty-description {
            font-size: 14px;
            color: #64748b;
            max-width: 280px;
            margin: 0 auto;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .selection-panel {
                width: 100%;
                right: -100%;
            }
            
            .filter-row {
                flex-direction: column;
            }
            
            .filter-card {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="filter-system">
            <div class="filter-header">
                <div class="filter-title">
                    <i class="fas fa-filter" style="color: #3b82f6;"></i>
                    <h3>Report Filters</h3>
                    <span class="progress-indicator">
                        <span>Required filters completed:</span>
                        <span class="progress-value">3/3</span>
                    </span>
                </div>
            </div>
            
            <div class="filter-row">
                <div class="filter-card active">
                    <div class="filter-card-header">
                        <div class="filter-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="filter-label">Center</div>
                    </div>
                    <div class="filter-value selected">Idaman Male (10 classes)</div>
                </div>
                
                <div class="filter-card active">
                    <div class="filter-card-header">
                        <div class="filter-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="filter-label">Class</div>
                    </div>
                    <div class="filter-value selected">Abu Bakr Alsiddiq</div>
                </div>
                
                <div class="filter-card active">
                    <div class="filter-card-header">
                        <div class="filter-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="filter-label">Period</div>
                    </div>
                    <div class="filter-value selected">July 2025</div>
                </div>
                
                <div class="filter-card student-filter-card" id="openStudentPanel">
                    <div class="filter-card-header">
                        <div class="filter-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="filter-label">Students</div>
                    </div>
                    <div class="filter-value">
                        <span>Selected students</span>
                        <span class="student-count" id="studentCount">4</span>
                    </div>
                </div>
            </div>
            
            <div class="action-bar">
                <button class="main-btn btn-reset">
                    <i class="fas fa-redo"></i> Reset All
                </button>
                <button class="main-btn btn-generate">
                    <i class="fas fa-chart-bar"></i> Generate Report
                </button>
            </div>
        </div>
    </div>
    
    <!-- Student Selection Panel -->
    <div class="selection-panel" id="studentPanel">
        <div class="panel-header">
            <div class="panel-title">
                <h4>Student Selection</h4>
                <button class="close-panel" id="closePanel">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="panel-subtitle">Select students for your report</div>
        </div>
        
        <div class="panel-body">
            <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input type="text" placeholder="Search students by name, ID, or class...">
            </div>
            
            <div class="filter-options">
                <div class="filter-option active">All Students</div>
                <div class="filter-option">Class A</div>
                <div class="filter-option">Class B</div>
                <div class="filter-option">Top Performers</div>
                <div class="filter-option">Absentees</div>
            </div>
            
            <div class="student-list" id="studentList">
                <!-- Student items will be populated here -->
            </div>
            
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-users-slash"></i>
                </div>
                <h4 class="empty-title">No students found</h4>
                <p class="empty-description">Try adjusting your search or filter criteria to find students</p>
            </div>
        </div>
        
        <div class="selection-summary">
            <div class="summary-content">
                <div class="selected-count"><span id="selectedCount">4</span> students selected</div>
                <div class="action-buttons">
                    <button class="btn btn-outline" id="clearSelection">Clear</button>
                    <button class="btn btn-primary" id="applySelection">Apply</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="overlay" id="overlay"></div>

    <script>
        // Mock student data
        const students = [
            { id: 1, name: "Abdullah Gasan Halaqah", class: "Class A", section: "Science", attendance: "95%" },
            { id: 2, name: "Abdurrahman Oleolo", class: "Class A", section: "Science", attendance: "88%" },
            { id: 3, name: "Basm Taufeeq Abdu", class: "Class B", section: "Arts", attendance: "92%" },
            { id: 4, name: "Faouaz Chaibou Issoufou", class: "Class B", section: "Arts", attendance: "76%" },
            { id: 5, name: "Heba Mhd Eid Mustafa", class: "Class A", section: "Science", attendance: "89%" },
            { id: 6, name: "Mohammed Al-Mansoori", class: "Class C", section: "Commerce", attendance: "94%" },
            { id: 7, name: "Aisha Bint Khalid", class: "Class A", section: "Science", attendance: "97%" },
            { id: 8, name: "Yusuf Ibn Ali", class: "Class B", section: "Arts", attendance: "85%" },
            { id: 9, name: "Fatima Al-Hassan", class: "Class C", section: "Commerce", attendance: "91%" },
            { id: 10, name: "Omar Al-Farooq", class: "Class A", section: "Science", attendance: "82%" }
        ];

        // State
        let selectedStudents = [1, 2, 3, 4]; // Pre-select first 4 students

        // DOM Elements
        const studentPanel = document.getElementById('studentPanel');
        const overlay = document.getElementById('overlay');
        const openStudentPanelBtn = document.getElementById('openStudentPanel');
        const closePanelBtn = document.getElementById('closePanel');
        const studentListEl = document.getElementById('studentList');
        const emptyStateEl = document.getElementById('emptyState');
        const selectedCountEl = document.getElementById('selectedCount');
        const studentCountEl = document.getElementById('studentCount');
        const clearSelectionBtn = document.getElementById('clearSelection');
        const applySelectionBtn = document.getElementById('applySelection');

        // Open panel
        openStudentPanelBtn.addEventListener('click', () => {
            studentPanel.classList.add('open');
            overlay.classList.add('active');
            renderStudentList();
        });

        // Close panel
        function closePanel() {
            studentPanel.classList.remove('open');
            overlay.classList.remove('active');
        }

        closePanelBtn.addEventListener('click', closePanel);
        overlay.addEventListener('click', closePanel);

        // Render student list
        function renderStudentList() {
            studentListEl.innerHTML = '';
            
            if (students.length === 0) {
                emptyStateEl.style.display = 'block';
                return;
            }
            
            emptyStateEl.style.display = 'none';
            
            students.forEach(student => {
                const isSelected = selectedStudents.includes(student.id);
                const studentEl = document.createElement('div');
                studentEl.className = `student-item ${isSelected ? 'selected' : ''}`;
                studentEl.dataset.id = student.id;
                
                studentEl.innerHTML = `
                    <div class="student-checkbox"></div>
                    <div class="student-info">
                        <div class="student-name">${student.name}</div>
                        <div class="student-meta">${student.class} • ${student.section} • Attendance: ${student.attendance}</div>
                    </div>
                `;
                
                studentEl.addEventListener('click', () => {
                    toggleStudentSelection(student.id);
                });
                
                studentListEl.appendChild(studentEl);
            });
            
            updateSelectionCount();
        }

        // Toggle student selection
        function toggleStudentSelection(id) {
            const index = selectedStudents.indexOf(id);
            if (index === -1) {
                selectedStudents.push(id);
            } else {
                selectedStudents.splice(index, 1);
            }
            
            renderStudentList();
        }

        // Update selection count
        function updateSelectionCount() {
            selectedCountEl.textContent = selectedStudents.length;
            studentCountEl.textContent = selectedStudents.length;
        }

        // Clear selection
        clearSelectionBtn.addEventListener('click', () => {
            selectedStudents = [];
            renderStudentList();
        });

        // Apply selection
        applySelectionBtn.addEventListener('click', closePanel);

        // Initialize
        renderStudentList();
    </script>
</body>
</html>