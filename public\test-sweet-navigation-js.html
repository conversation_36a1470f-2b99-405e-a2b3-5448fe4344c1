<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sweet Navigation JavaScript Test</title>
    
    <!-- CSRF Token for testing -->
    <meta name="csrf-token" content="test-token">
    
    <!-- Bootstrap 3 for compatibility testing -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <!-- Our Sweet Navigation CSS -->
    <link rel="stylesheet" href="css/components/sweet-navigation.css">
    
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-trigger {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #009933;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .test-trigger:hover {
            background: #007a29;
            color: white;
            text-decoration: none;
        }
        .breadcrumb-style {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .breadcrumb-style a {
            color: #009933;
            text-decoration: none;
        }
        .breadcrumb-style a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sweet Navigation JavaScript Component Test</h1>
        <p class="lead">This page tests the JavaScript functionality of the Sweet Navigation component.</p>
        
        <div class="test-section">
            <h3>Test 1: Basic Navigation Trigger</h3>
            <p>Hover over the link below to trigger the navigation popup:</p>
            
            <div class="breadcrumb-style">
                <span>Dashboard > </span>
                <a href="#" class="sweet-navigation-trigger" 
                   data-ajax-url="/test-navigation-data.json"
                   data-title="Class Navigation"
                   data-current-id="2">
                    <i class="fa fa-list-ul"></i> Classes
                    <i class="fa fa-caret-down" style="margin-left: 5px;"></i>
                </a>
                <span> > Current Page</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Custom Configuration</h3>
            <p>Test with custom button text and URL:</p>
            
            <a href="#" class="test-trigger sweet-navigation-trigger"
               data-ajax-url="/test-navigation-data.json"
               data-title="Student Navigation"
               data-current-id="1"
               data-confirm-button-text="<i class='fa fa-users'></i> Go to Students"
               data-confirm-button-url="https://example.com/students"
               data-width="900px">
                <i class="fa fa-users"></i> Browse Students
            </a>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Error Handling</h3>
            <p>Test with invalid AJAX URL to see error handling:</p>
            
            <a href="#" class="test-trigger sweet-navigation-trigger"
               data-ajax-url="/invalid-endpoint"
               data-title="Error Test">
                <i class="fa fa-exclamation-triangle"></i> Test Error Handling
            </a>
        </div>
        
        <div class="test-section">
            <h3>Test 4: Multiple Triggers</h3>
            <p>Test multiple navigation triggers on the same page:</p>
            
            <a href="#" class="test-trigger sweet-navigation-trigger"
               data-ajax-url="/test-navigation-data.json"
               data-title="Programs"
               data-current-id="3">
                <i class="fa fa-graduation-cap"></i> Programs
            </a>
            
            <a href="#" class="test-trigger sweet-navigation-trigger"
               data-ajax-url="/test-navigation-data.json"
               data-title="Teachers"
               data-current-id="4">
                <i class="fa fa-user"></i> Teachers
            </a>
            
            <a href="#" class="test-trigger sweet-navigation-trigger"
               data-ajax-url="/test-navigation-data.json"
               data-title="Centers"
               data-current-id="5">
                <i class="fa fa-building"></i> Centers
            </a>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <p><strong>JavaScript Functionality Checklist:</strong></p>
                <ul>
                    <li>✓ Hover triggers show loading state</li>
                    <li>✓ AJAX calls are made with proper headers</li>
                    <li>✓ SweetAlert2 popup displays with custom styling</li>
                    <li>✓ Search functionality filters items</li>
                    <li>✓ Group collapse/expand works</li>
                    <li>✓ Item clicks navigate to URLs</li>
                    <li>✓ Action buttons work independently</li>
                    <li>✓ ESC key closes popup</li>
                    <li>✓ Outside click closes popup</li>
                    <li>✓ Close button works</li>
                    <li>✓ Error handling displays appropriate messages</li>
                    <li>✓ Multiple triggers work independently</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Manual Test Instructions</h3>
            <ol>
                <li><strong>Hover Test</strong>: Hover over any navigation trigger - should show loading state after brief delay</li>
                <li><strong>Search Test</strong>: Type in search box to filter items</li>
                <li><strong>Group Toggle</strong>: Click group headers to collapse/expand</li>
                <li><strong>Item Click</strong>: Click on items to navigate (will open in new tab/window)</li>
                <li><strong>Action Buttons</strong>: Click small action buttons - should work independently</li>
                <li><strong>Close Methods</strong>: Test ESC key, outside click, and close button</li>
                <li><strong>Error Handling</strong>: Try the error test trigger to see error messages</li>
                <li><strong>Multiple Triggers</strong>: Test that multiple triggers work without interference</li>
            </ol>
        </div>
    </div>

    <!-- jQuery and SweetAlert2 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Our Sweet Navigation JavaScript -->
    <script src="js/components/sweet-navigation.js"></script>
    
    <script>
        // Mock server for testing - intercept AJAX calls
        $(document).ajaxSend(function(event, xhr, settings) {
            console.log('AJAX Request:', settings.url);
            
            // Mock successful response for test-navigation-data.json
            if (settings.url === '/test-navigation-data.json') {
                setTimeout(function() {
                    const mockData = {
                        success: true,
                        data: {
                            groups: [
                                {
                                    name: "Mathematics Program",
                                    items: [
                                        {
                                            id: "1",
                                            name: "Algebra 101",
                                            url: "https://example.com/classes/1",
                                            is_current: false,
                                            subtitle: "Teacher: John Doe | Schedule: Mon, Wed, Fri 10:00-11:30",
                                            count: "25",
                                            actions: [
                                                {type: "report", url: "https://example.com/report/1", title: "Class Report", label: "R"},
                                                {type: "show", url: "https://example.com/show/1", title: "View Class", label: "V"}
                                            ]
                                        },
                                        {
                                            id: "2",
                                            name: "Geometry Advanced",
                                            url: "https://example.com/classes/2",
                                            is_current: true,
                                            subtitle: "Teacher: Jane Smith | Schedule: Tue, Thu 14:00-15:30",
                                            count: "18",
                                            actions: [
                                                {type: "report", url: "https://example.com/report/2", title: "Class Report", label: "R"},
                                                {type: "show", url: "https://example.com/show/2", title: "View Class", label: "V"}
                                            ]
                                        }
                                    ]
                                },
                                {
                                    name: "Science Program",
                                    items: [
                                        {
                                            id: "3",
                                            name: "Physics 101",
                                            url: "https://example.com/classes/3",
                                            is_current: false,
                                            subtitle: "Teacher: Bob Wilson | Schedule: Daily 09:00-10:00",
                                            count: "32"
                                        },
                                        {
                                            id: "4",
                                            name: "Chemistry Lab",
                                            url: "https://example.com/classes/4",
                                            is_current: false,
                                            subtitle: "Teacher: Alice Brown | Schedule: Wed, Fri 16:00-17:30",
                                            count: "15"
                                        }
                                    ]
                                }
                            ]
                        }
                    };
                    
                    // Trigger success callback
                    if (settings.success) {
                        settings.success(mockData);
                    }
                }, 500); // Simulate network delay
                
                // Prevent actual AJAX call
                xhr.abort();
            }
            
            // Mock error response for invalid endpoints
            if (settings.url === '/invalid-endpoint') {
                setTimeout(function() {
                    // Trigger error callback
                    if (settings.error) {
                        settings.error({
                            status: 404,
                            responseJSON: {
                                message: "Navigation endpoint not found"
                            }
                        }, 'error', 'Not Found');
                    }
                }, 300);
                
                // Prevent actual AJAX call
                xhr.abort();
            }
        });
        
        // Log initialization
        console.log('Sweet Navigation Test Page Loaded');
        console.log('Available triggers:', $('.sweet-navigation-trigger').length);
        
        // Test configuration override
        window.SweetNavigationConfig.hoverDelay = 100; // Faster for testing
        
        // Re-initialize after config change
        initializeSweetNavigation();
    </script>
</body>
</html>
