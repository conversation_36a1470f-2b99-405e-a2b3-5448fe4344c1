<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Student;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

final class HalaqahStudentsWithRecordsController extends Controller
{
    /**
     * Get students who have hefz records for the specified class
     * 
     * @param int $classId
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudents(int $classId, Request $request): JsonResponse
    {
        try {
            // Get students who have hefz records OR ijazasanad memorization records with the required fields
            $query = Student::where(function ($q) use ($classId) {
                $q->whereHas('hefz', function ($subQ) use ($classId) {
                    $subQ->where('class_id', $classId)
                         ->whereNotNull('hefz_from_surat')
                         ->whereNotNull('hefz_from_ayat')
                         ->whereNotNull('hefz_to_surat')
                         ->whereNotNull('hefz_to_ayat');
                })
                ->orWhereHas('ijazaMemorizationReport', function ($subQ) use ($classId) {
                    $subQ->where('class_id', $classId)
                         ->whereNotNull('hefz_from_surat')
                         ->whereNotNull('hefz_from_ayat')
                         ->whereNotNull('hefz_to_surat')
                         ->whereNotNull('hefz_to_ayat');
                });
            });

            // Apply month-year filter if provided
            if ($request->has('monthYear') && !empty($request->monthYear)) {
                $monthYear = $request->monthYear;
                $parts = explode(' ', $monthYear);

                if (count($parts) === 2) {
                    $monthName = $parts[0];
                    $year = (int) $parts[1];

                    // Convert month name to number
                    $monthNumber = Carbon::parse($monthName . ' 1')->month;

                    $query->where(function ($q) use ($classId, $year, $monthNumber) {
                        $q->whereHas('hefz', function ($subQ) use ($classId, $year, $monthNumber) {
                            $subQ->where('class_id', $classId)
                                 ->whereYear('created_at', $year)
                                 ->whereMonth('created_at', $monthNumber)
                                 ->whereNotNull('hefz_from_surat')
                                 ->whereNotNull('hefz_from_ayat')
                                 ->whereNotNull('hefz_to_surat')
                                 ->whereNotNull('hefz_to_ayat');
                        })
                        ->orWhereHas('ijazaMemorizationReport', function ($subQ) use ($classId, $year, $monthNumber) {
                            $subQ->where('class_id', $classId)
                                 ->whereYear('created_at', $year)
                                 ->whereMonth('created_at', $monthNumber)
                                 ->whereNotNull('hefz_from_surat')
                                 ->whereNotNull('hefz_from_ayat')
                                 ->whereNotNull('hefz_to_surat')
                                 ->whereNotNull('hefz_to_ayat');
                        });
                    });
                }
            }

            $students = $query->select('id', 'full_name')
                             ->orderBy('full_name')
                             ->get();

            return response()->json($students);
            
        } catch (\Exception $e) {
            \Log::error('Error fetching students with hefz records: ' . $e->getMessage());
            
            return response()->json([
                'error' => 'Failed to fetch students',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
