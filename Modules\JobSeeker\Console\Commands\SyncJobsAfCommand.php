<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Mo<PERSON>les\JobSeeker\Services\JobsAfService;
use Mo<PERSON>les\JobSeeker\Entities\CommandScheduleExecution;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Modules\JobSeeker\Entities\JobCategory;

class SyncJobsAfCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:sync-jobs-af {--category=* : Optional specific category IDs to sync} {--schedule-rule-id= : Optional schedule rule ID for custom filters}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync jobs from jobs.af, optionally filtered by category (--category=*) or schedule rule (--schedule-rule-id=), track health metrics, and send notifications';

    /**
     * @var JobsAfService
     */
    protected JobsAfService $jobsAfService;

    /**
     * Create a new command instance.
     *
     * @param JobsAfService $jobsAfService
     */
    public function __construct(JobsAfService $jobsAfService)
    {
        parent::__construct();
        $this->jobsAfService = $jobsAfService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $categoryIds = $this->option('category'); // Add this line
        $scheduleRuleId = $this->option('schedule-rule-id') ? (int) $this->option('schedule-rule-id') : null;
        $executionId = null;
        $startTime = Carbon::now();
        
        try {
            $this->info('Starting jobs.af synchronization...');
            Log::info('Starting jobs.af synchronization via command', [
                'category_ids' => $categoryIds,
                'schedule_rule_id' => $scheduleRuleId,
                'start_time' => $startTime->toDateTimeString()
            ]);

            // Validate category IDs if provided
            if (!empty($categoryIds)) {
                $validCategoryIds = JobCategory::pluck('id')->toArray();
                $invalidIds = array_diff($categoryIds, $validCategoryIds);
                if (!empty($invalidIds)) {
                    $this->error('Invalid category IDs provided: ' . implode(', ', $invalidIds));
                    Log::error('Jobs.af sync aborted due to invalid category IDs', [
                        'invalid_ids' => $invalidIds,
                        'provided_ids' => $categoryIds
                    ]);
                    return 1; // Skip service call
                }
            }

            // ALWAYS create execution record for health tracking
            $execution = CommandScheduleExecution::create([
                'schedule_rule_id' => $scheduleRuleId, // Can be null for manual executions
                'command' => 'jobseeker:sync-jobs-af',
                'status' => 'running',
                'started_at' => $startTime,
                'output' => json_encode([
                    'categories' => $categoryIds,
                    'schedule-rule-id' => $scheduleRuleId,
                    'manual_execution' => !$scheduleRuleId
                ])
            ]);
            $executionId = $execution->id;
            $this->info("Created execution record with ID: {$executionId}");
            
            if ($scheduleRuleId) {
                $this->info("Using custom filters for schedule rule ID: {$scheduleRuleId}");
            } else {
                $this->info("Manual execution - using default filters");
            }

            // Fetch and notify jobs from the API using JobsAfService
            $this->info('Fetching and processing jobs from jobs.af API...');
            $apiStats = $this->jobsAfService->fetchAndNotifyJobs($categoryIds, $scheduleRuleId);
            
            if (!$apiStats['success']) {
                throw new \Exception($apiStats['error'] ?? 'Unknown error occurred during job sync');
            }
            
            $this->info('Jobs.af API synchronization completed successfully');
            
            // Display API sync statistics including health metrics
            $this->displaySyncResults($apiStats);
            
            // Get additional database statistics
            $dbStats = $this->getDatabaseStatistics();
            $this->displayDatabaseSummary($dbStats);
            
            // Log comprehensive results
            Log::info('Jobs.af synchronization completed via command', [
                'api_stats' => $apiStats,
                'db_stats' => $dbStats,
                'execution_id' => $executionId
            ]);
            
            // Complete execution record with health metrics
            if ($executionId) {
                $this->completeExecution($executionId, $apiStats, true);
            }
            
            // Handle notifications
            $this->handleNotifications($apiStats);
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Error during jobs.af synchronization: ' . $e->getMessage());
            Log::error('Error during jobs.af synchronization via command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'execution_id' => $executionId
            ]);
            
            // Complete execution record with error
            if ($executionId) {
                $errorStats = [
                    'success' => false,
                    'jobs_fetched' => 0,
                    'jobs_by_category' => [],
                    'error_types' => ['COMMAND_ERROR' => 1],
                    'api_response_time' => 0,
                    'created' => 0,
                    'updated' => 0,
                    'errors' => 1
                ];
                $this->completeExecution($executionId, $errorStats, false, $e->getMessage());
            }
            
            return 1;
        }
    }

    /**
     * Display synchronization results including health metrics
     * 
     * @param array $apiStats
     * @return void
     */
    protected function displaySyncResults(array $apiStats): void
    {
        $this->info('API Synchronization Results:');
        $this->line('  - Jobs created: ' . $apiStats['created']);
        $this->line('  - Jobs updated: ' . $apiStats['updated']);
        $this->line('  - Jobs processed: ' . $apiStats['processed']);
        $this->line('  - Errors: ' . $apiStats['errors']);
        $this->line('  - Non-English skipped: ' . ($apiStats['non_english_skipped'] ?? 0));
        $this->line('  - Location filtered: ' . ($apiStats['location_filtered'] ?? 0));
        $this->line('  - Missed jobs found: ' . ($apiStats['missed'] ?? 0));
        
        // Health dashboard metrics
        if (isset($apiStats['jobs_fetched'])) {
            $this->info('Health Dashboard Metrics:');
            $this->line('  - Total jobs fetched: ' . $apiStats['jobs_fetched']);
            $this->line('  - API response time: ' . round($apiStats['api_response_time'] ?? 0, 2) . 's');
            
            if (!empty($apiStats['jobs_by_category'])) {
                $this->line('  - Jobs by category:');
                foreach ($apiStats['jobs_by_category'] as $category => $count) {
                    $this->line("    • {$category}: {$count}");
                }
            }
            
            if (!empty($apiStats['error_types'])) {
                $this->line('  - Error breakdown:');
                foreach ($apiStats['error_types'] as $errorType => $count) {
                    $this->line("    • {$errorType}: {$count}");
                }
            }
        }
    }

    /**
     * Get database statistics
     * 
     * @return array
     */
    protected function getDatabaseStatistics(): array
    {
        return [
            'total_jobs' => \Modules\JobSeeker\Entities\Job::where('source', 'jobs.af')->count(),
            'recent_jobs' => \Modules\JobSeeker\Entities\Job::where('source', 'jobs.af')
                ->where('publish_date', '>=', now()->subDays(7))
                ->count()
        ];
    }

    /**
     * Display database summary
     * 
     * @param array $dbStats
     * @return void
     */
    protected function displayDatabaseSummary(array $dbStats): void
    {
        $this->info('');
        $this->info('=== JOBS.AF SYNCHRONIZATION SUMMARY ===');
        $this->line('  - Total jobs.af jobs in database: ' . $dbStats['total_jobs']);
        $this->line('  - Jobs published in last 7 days: ' . $dbStats['recent_jobs']);
        $this->line('  - API synchronization completed successfully');
        $this->line('  - Run "php artisan jobseeker:fetch-jobs-af-descriptions" to update job descriptions');
    }

    /**
     * Complete execution record with health metrics
     * 
     * @param int $executionId
     * @param array $stats
     * @param bool $success
     * @param string|null $errorMessage
     * @return void
     */
    protected function completeExecution(int $executionId, array $stats, bool $success, ?string $errorMessage = null): void
    {
        try {
            $execution = CommandScheduleExecution::find($executionId);
            if (!$execution) {
                Log::warning('Execution record not found for completion', ['execution_id' => $executionId]);
                return;
            }

            // Format health metrics using the service
            $healthMetrics = $this->jobsAfService->formatExecutionStats($stats);
            
            $execution->markCompleted(
                $success ? 0 : 1,                            // $exitCode (int): 0 = success, 1 = failure
                $errorMessage,                                // $output (string): error message or null
                null,                                         // $memoryUsageMb (float): not tracked yet
                $healthMetrics['jobs_fetched'],               // $jobsFetched (int)
                $healthMetrics['jobs_by_category'],           // $jobsByCategory (array)
                $healthMetrics['error_type'],                 // $errorType (string)
                $healthMetrics['error_details']               // $errorDetails (array)
            );

            $this->info("Execution record {$executionId} completed with health metrics");
            
        } catch (\Exception $e) {
            Log::error('Error completing execution record', [
                'execution_id' => $executionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle job notifications
     * 
     * @param array $apiStats
     * @return void
     */
    protected function handleNotifications(array $apiStats): void
    {
        if ($apiStats['created'] > 0 || $apiStats['updated'] > 0) {
            $this->info('Job notifications will be triggered automatically via event system');
            Log::info('Jobs.af sync completed - notifications handled via JobProcessedEvent', [
                'created_jobs' => $apiStats['created'],
                'updated_jobs' => $apiStats['updated']
            ]);
        } else {
            $this->info('No new or updated jobs found');
            Log::info('No new or updated jobs found during jobs.af sync');
        }
    }
} 