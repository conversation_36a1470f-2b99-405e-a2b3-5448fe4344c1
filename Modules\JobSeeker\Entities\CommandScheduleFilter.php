<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * CommandScheduleFilter model
 * 
 * Manages per-rule filter configurations for jobs.af API requests.
 * Each schedule rule can have custom filters for categories, locations,
 * companies, experience levels, search terms, and work types.
 */
final class CommandScheduleFilter extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'command_schedule_filters';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'schedule_rule_id',
        'categories',
        'locations',
        'companies',
        'experience_levels',
        'search_term',
        'work_type',
        'is_default',
        'min_page',
        'max_page',
        // ACBAR-specific fields for extended functionality
        'max_retries',
        'timeout',
        'base_delay'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'categories' => 'array',
        'locations' => 'array',
        'companies' => 'array',
        'experience_levels' => 'array',
        'is_default' => 'boolean',
        'min_page' => 'integer',
        'max_page' => 'integer',
        'max_retries' => 'integer',
        'timeout' => 'integer',
        'base_delay' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the schedule rule that owns this filter
     *
     * @return BelongsTo
     */
    public function scheduleRule(): BelongsTo
    {
        return $this->belongsTo(CommandScheduleRule::class, 'schedule_rule_id');
    }

    /**
     * Scope to get default filters
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to get filters for a specific schedule rule
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $ruleId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForRule($query, int $ruleId)
    {
        return $query->where('schedule_rule_id', $ruleId);
    }

    /**
     * Get filters formatted for jobs.af API request
     * NOTE: This method returns raw database values, not provider-specific identifiers
     * Use FilterRepository::getDynamicProviderFilters() for translated API requests
     *
     * @return array
     */
    public function getApiFilters(): array
    {
        return [
            'searchFilters' => [
                'searchTerm' => $this->search_term ?: '',
                'categories' => $this->categories ?: [],
                'workType' => $this->work_type ?: '',
                'companies' => $this->companies ?: [],
                'experienceLevels' => $this->experience_levels ?: [],
                'locations' => $this->locations ?: [] // FIXED: No hardcoded default location
            ]
        ];
    }

    /**
     * Merge with default filters to ensure completeness
     *
     * @param array $defaultFilters
     * @return array
     */
    public function mergeWithDefaults(array $defaultFilters): array
    {
        $apiFilters = $this->getApiFilters();
        
        // Merge search filters, preferring rule-specific values
        $searchFilters = array_merge(
            $defaultFilters['searchFilters'] ?? [],
            $apiFilters['searchFilters']
        );

        // If any array filter is empty, use default
        if (empty($searchFilters['categories'])) {
            $searchFilters['categories'] = $defaultFilters['searchFilters']['categories'] ?? [];
        }
        
        if (empty($searchFilters['locations'])) {
            $searchFilters['locations'] = $defaultFilters['searchFilters']['locations'] ?? [];
        }

        return [
            'page' => $defaultFilters['page'] ?? 1,
            'searchFilters' => $searchFilters
        ];
    }

    /**
     * Validate filter data before saving
     *
     * @param array $filterData
     * @return array Validation errors (empty if valid)
     */
    public static function validateFilterData(array $filterData): array
    {
        $errors = [];

        // Validate categories
        if (isset($filterData['categories']) && !is_array($filterData['categories'])) {
            $errors['categories'] = 'Categories must be an array';
        }

        // Validate locations
        if (isset($filterData['locations']) && !is_array($filterData['locations'])) {
            $errors['locations'] = 'Locations must be an array';
        }

        // Validate companies
        if (isset($filterData['companies']) && !is_array($filterData['companies'])) {
            $errors['companies'] = 'Companies must be an array';
        }

        // Validate experience levels
        if (isset($filterData['experience_levels']) && !is_array($filterData['experience_levels'])) {
            $errors['experience_levels'] = 'Experience levels must be an array';
        }

        // Validate search term length
        if (isset($filterData['search_term']) && strlen($filterData['search_term']) > 255) {
            $errors['search_term'] = 'Search term cannot exceed 255 characters';
        }

        // Validate work type
        $validWorkTypes = ['', 'Remote', 'On-site', 'Hybrid'];
        if (isset($filterData['work_type']) && !in_array($filterData['work_type'], $validWorkTypes)) {
            $errors['work_type'] = 'Invalid work type. Must be one of: ' . implode(', ', $validWorkTypes);
        }

        // Validate ACBAR-specific numeric fields
        if (isset($filterData['max_retries']) && (!is_numeric($filterData['max_retries']) || $filterData['max_retries'] < 1 || $filterData['max_retries'] > 10)) {
            $errors['max_retries'] = 'Max retries must be a number between 1 and 10';
        }

        if (isset($filterData['timeout']) && (!is_numeric($filterData['timeout']) || $filterData['timeout'] < 10 || $filterData['timeout'] > 300)) {
            $errors['timeout'] = 'Timeout must be a number between 10 and 300 seconds';
        }

        if (isset($filterData['base_delay']) && (!is_numeric($filterData['base_delay']) || $filterData['base_delay'] < 100 || $filterData['base_delay'] > 10000)) {
            $errors['base_delay'] = 'Base delay must be a number between 100 and 10000 milliseconds';
        }

        return $errors;
    }

    /**
     * Check if this filter has any specific configurations
     *
     * @return bool
     */
    public function hasSpecificFilters(): bool
    {
        return !empty($this->categories) ||
               !empty($this->locations) ||
               !empty($this->companies) ||
               !empty($this->experience_levels) ||
               !empty($this->search_term) ||
               !empty($this->work_type);
    }

    /**
     * Get summary of filter configuration
     *
     * @return array
     */
    public function getSummary(): array
    {
        return [
            'categories_count' => count($this->categories ?? []),
            'locations_count' => count($this->locations ?? []),
            'companies_count' => count($this->companies ?? []),
            'experience_levels_count' => count($this->experience_levels ?? []),
            'has_search_term' => !empty($this->search_term),
            'has_work_type' => !empty($this->work_type),
            'is_default' => $this->is_default,
            'has_specific_filters' => $this->hasSpecificFilters()
        ];
    }
} 