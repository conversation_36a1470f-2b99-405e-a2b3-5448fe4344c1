---
type: "always_apply"
---

# Laravel Project Guidelines

This document outlines the key architectural principles and technologies used in this Laravel 10 project. For detailed backend standards, please refer to the `[laravel_backend_development.mdc](mdc:.cursor/rules/laravel_backend_development.mdc)` file.

## Core Technologies

*   **Backend**: [Laravel 10](mdc:https:/laravel.com/docs/10.x), PHP 8.1+
*   **Frontend**: Bootstrap 3, jQuery
*   **Database**: MySQL
*   **Connection Details**:    
        * Database: itqan
        * Username: root
        * Password: (none)
        * Command: mysql -u root itqan
        
*   **Modular Structure**: [nwidart/laravel-modules](mdc:https:/nwidart.com/laravel-modules/v10/introduction)

## Core Development Principles

**🎯 These principles are MANDATORY for all development work and must be followed without exception.**

### Communication & Clarity Principle

*   **Ask for Clarification**: When uncertain about requirements, implementation details, or expected behavior, **ALWAYS ask for clarification instead of making assumptions or guessing**. This prevents:
    *   Wasted development time on incorrect implementations
    *   Introduction of bugs through misunderstood requirements
    *   Costly refactoring when assumptions prove wrong
    *   Degraded code quality from hallucinated solutions

*   **Validate Understanding**: Before implementing complex features, confirm your understanding of the requirements with stakeholders or team members.

### Laravel Architecture Respect

*   **Eloquent Active Record Pattern**: Respect and leverage Laravel's Eloquent ORM as an Active Record pattern implementation:
    *   Models should contain business logic relevant to the entity they represent
    *   Use Eloquent relationships (`hasMany`, `belongsTo`, `belongsToMany`, etc.) instead of manual joins
    *   Leverage Eloquent's built-in methods (`create`, `update`, `delete`, `find`, `where`, etc.)
    *   Implement model events, observers, and accessors/mutators for entity-specific behavior
    *   Use model scopes for reusable query logic
    *   Avoid bypassing Eloquent with raw SQL unless performance critically requires it

*   **Framework Conventions**: Follow Laravel's conventions and patterns rather than fighting against them:
    *   Use dependency injection and service container
    *   Leverage Laravel's built-in features before creating custom solutions
    *   Follow naming conventions for controllers, models, and database tables
    *   Use Laravel's validation, authentication, and authorization systems

**⚠️ NON-COMPLIANCE**: Admin interfaces that don't follow mobile-first principles will be rejected and must be redesigned.

## Project Structure & Conventions

*   **Modular Development**: The project uses the `nwidart/laravel-modules` package. All module-specific backend code (Controllers, Models, Routes) should reside within the corresponding directory under `[Modules/](mdc:Modules)`.
    *   **Example**: Code for the Admission module is located in `[Modules/Admission/](mdc:Modules/Admission)`.
*   **Routing**: Module-specific web routes are defined in `Modules/<ModuleName>/Http/routes.php` (e.g., `[Modules/Admission/Http/routes.php](mdc:Modules/Admission/Http/routes.php)`). API routes are in `Modules/<ModuleName>/Http/api.php`. Ensure the module's `RouteServiceProvider` is configured correctly.
*   **Controllers**: Place controllers within the `Http/Controllers` directory of the respective module. Controllers should be `final` and read-only. Use method injection for dependencies or dedicated Service classes.
*   **Models**: Place Eloquent models within the `Entities` directory of the respective module (e.g., `[Modules/Admission/Entities/](mdc:Modules/Admission/Entities)`). Models should be `final`.
*   **Database Schema/Seeding**: 
    * **NEVER** use Laravel migrations or seeders
    * **NEVER** run direct MySQL commands (e.g., `mysql -u root itqan`) for:
      * **DELETE** operations - Risk of unintended data loss
      * **UPDATE** operations - Can bypass application logic
      * **INSERT** operations - Can violate data integrity
      * Schema changes or any other data manipulation
    * **ALWAYS** write schema changes and data seeding as SQL files
    * Store SQL files in the module's `Modules/<ModuleName>/Database/` directory with clear naming conventions
    * Use Eloquent ORM for all application-level data manipulation (CRUD)
    * SQL files must be reviewed and executed by authorized database administrators
    * Include proper documentation and rollback procedures in SQL files
*   **Logging**: Implement extensive logging. `
*   **Services & Repositories**: Employ Service and Repository patterns for complex logic and data abstraction, placing them in `app/Services/`, `app/Repositories/` or module-specific directories.
*   **Error Handling**: Use Laravel's exception handling. Create custom exceptions as needed.
*   **Emailing**: All email sending functionality MUST be handled exclusively through the `[EmailService](mdc:app/Services/EmailService.php)`. This service is critical for maintaining consistent, reliable, and configurable email delivery across the entire application. Direct use of Laravel's `Mail` facade, `PHPMailer`, or any other email sending library is strictly prohibited.


*   **Emailing**: All email sending functionality MUST be handled exclusively through the `[EmailService](mdc:app/Services/EmailService.php)`. This service is critical for maintaining consistent, reliable, and configurable email delivery across the entire application. Direct use of Laravel's `Mail` facade, `PHPMailer`, or any other email sending library is strictly prohibited.

