---
title: Laravel Project Guidelines
---

# Laravel Project Guidelines

This document outlines the key architectural principles, coding standards, and technologies used in this Laravel project.

## Core Technologies & Dependencies

- **Backend**: Laravel 10+, PHP 8.1+
- **Frontend**: Bootstrap 3, jQuery
- **Database**: MySQL
- **Modular Structure**: nwidart/laravel-modules

## Core Development Principles

**🎯 These principles are MANDATORY for all development work.**

### Communication & Clarity

- **Ask for Clarification**: When uncertain about requirements or implementation details, ALWAYS ask for clarification
- **Validate Understanding**: Confirm understanding of complex features before implementation
- **Document Decisions**: Record important architectural decisions and their rationale

### Code Quality Standards

- **Follow PSR Standards**: Adhere to PSR-1, PSR-2, and PSR-12
- **Type Safety**:
  - Use typed properties over docblocks
  - Always specify return types including `void`
  - Use short nullable syntax: `?Type` not `Type|null`
  - Document iterables with generics

### Laravel Architecture Principles

- **Follow Laravel Conventions First**: Only deviate with clear justification
- **Eloquent Active Record Pattern**:
  - Models contain relevant business logic
  - Use Eloquent relationships over manual joins
  - Leverage built-in methods (`create`, `update`, `delete`, etc.)
  - Implement model events and observers
  - Use model scopes for reusable queries

### Naming Conventions

- **Classes**: PascalCase (`UserController`, `OrderStatus`)
- **Methods/Variables**: camelCase (`getUserName`, `$firstName`)
- **Routes**: kebab-case (`/open-source`, `/user-profile`)
- **Config**: 
  - Files: kebab-case (`pdf-generator.php`)
  - Keys: snake_case (`chrome_path`)
- **Database**: snake_case for columns and tables

## Project Structure

### Modular Development

- Use `nwidart/laravel-modules` package structure
- Module-specific code goes in corresponding `Modules/<ModuleName>/` directory
- Follow module directory structure:
  ```
  Modules/<ModuleName>/
    ├── Http/
    │   ├── Controllers/
    │   ├── routes.php
    │   └── api.php
    ├── Entities/
    ├── Services/
    ├── Repositories/
    └── Database/
  ```

### Component Guidelines

- **Controllers**:
  - Place in `Http/Controllers`
  - Make `final` and read-only
  - Use method injection or service classes
  - Follow CRUD naming (`index`, `create`, `store`, etc.)

- **Models**:
  - Place in `Entities` directory
  - Make `final` to prevent inheritance
  - Use typed properties
  - Implement proper relationships

- **Database**:
  - NEVER use Laravel migrations
  - Store SQL files in `Modules/<ModuleName>/Database/`
  - Include rollback procedures
  - Use Eloquent for CRUD operations

- **Services & Repositories**:
  - Place in module-specific directories
  - Make services final and read-only
  - Use for complex business logic
  - Keep controllers thin

### Code Organization

- **Control Flow**:
  - Handle error conditions first (happy path last)
  - Avoid else statements
  - Use early returns
  - Always use curly brackets

- **Type Declarations**:
  - Always use return type declarations
  - Document collections with generics
  - Import class names in docblocks
  - Use one-line docblocks when possible

### Critical Requirements

- **Email Handling**: Use `EmailService` exclusively
- **Error Handling**: Use Laravel's exception system
- **Authorization**: Implement proper middleware
- **Mobile First**: All admin interfaces must be responsive
- **SQL Operations**: Never run direct MySQL commands
- **Documentation**: Maintain clear inline documentation

### Testing Standards

- Keep test classes focused
- Use descriptive test method names
- Follow arrange-act-assert pattern
- Avoid `RefreshDatabase`, use `DatabaseTransactions`

## Security & Performance

- Never store credentials in code
- Use proper CSRF protection
- Implement XSS prevention
- Follow SQL injection prevention practices
- Use caching appropriately
- Optimize database queries

**⚠️ NON-COMPLIANCE**: Violations of these guidelines will result in rejected code reviews.


*   **Emailing**: All email sending functionality MUST be handled exclusively through the `[EmailService](mdc:app/Services/EmailService.php)`. This service is critical for maintaining consistent, reliable, and configurable email delivery across the entire application. Direct use of Laravel's `Mail` facade, `PHPMailer`, or any other email sending library is strictly prohibited.


*   **Emailing**: All email sending functionality MUST be handled exclusively through the `[EmailService](mdc:app/Services/EmailService.php)`. This service is critical for maintaining consistent, reliable, and configurable email delivery across the entire application. Direct use of Laravel's `Mail` facade, `PHPMailer`, or any other email sending library is strictly prohibited.

