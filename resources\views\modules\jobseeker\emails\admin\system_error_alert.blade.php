<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Error Alert - {{ $alert->title }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .email-container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .email-header {
            padding: 30px;
            text-align: center;
            color: white;
            background: linear-gradient(135deg, {{ $severity_color }} 0%, {{ $severity_color }}dd 100%);
        }
        
        .email-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 700;
        }
        
        .email-header .severity-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .email-header .alert-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.9;
        }
        
        .email-body {
            padding: 40px;
        }
        
        .alert-summary {
            background: #f8f9fa;
            border-left: 5px solid {{ $severity_color }};
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }
        
        .alert-summary h2 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 20px;
        }
        
        .alert-summary .meta-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .meta-item i {
            color: {{ $severity_color }};
            width: 16px;
        }
        
        .meta-item strong {
            color: #2c3e50;
        }
        
        .error-details {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .error-details h3 {
            margin: 0 0 15px 0;
            color: #c53030;
            font-size: 16px;
        }
        
        .error-message {
            background: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .context-section {
            margin: 25px 0;
        }
        
        .context-section h3 {
            color: #2d3748;
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .context-data {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .action-buttons {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: {{ $severity_color }};
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .email-footer {
            background: #2d3748;
            color: #a0aec0;
            padding: 25px;
            text-align: center;
            font-size: 14px;
        }
        
        .email-footer a {
            color: #63b3ed;
            text-decoration: none;
        }
        
        .timestamp {
            color: #718096;
            font-size: 12px;
            margin-top: 10px;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .email-header, .email-body {
                padding: 20px;
            }
            
            .alert-summary .meta-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <div class="alert-icon">
                <i class="{{ $category_icon }}"></i>
            </div>
            <h1>System Error Alert</h1>
            <div class="severity-badge">{{ $alert->getFormattedSeverity() }} Priority</div>
        </div>
        
        <div class="email-body">
            <div class="alert-summary">
                <h2>{{ $alert->title }}</h2>
                <p>{{ $alert->message }}</p>
                
                <div class="meta-info">
                    <div class="meta-item">
                        <i class="fas fa-tag"></i>
                        <span><strong>Category:</strong> {{ $alert->getFormattedCategory() }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span><strong>Severity:</strong> {{ $alert->getFormattedSeverity() }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span><strong>Occurred:</strong> {{ $alert->created_at->format('M d, Y H:i:s T') }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-hashtag"></i>
                        <span><strong>Alert ID:</strong> {{ $alert->id }}</span>
                    </div>
                </div>
            </div>
            
            @if($alert->message)
            <div class="error-details">
                <h3><i class="fas fa-bug"></i> Error Message</h3>
                <div class="error-message">{{ $alert->message }}</div>
            </div>
            @endif
            
            @if($alert->context && !empty($alert->context))
            <div class="context-section">
                <h3><i class="fas fa-info-circle"></i> Context Information</h3>
                <div class="context-data">{{ json_encode($alert->context, JSON_PRETTY_PRINT) }}</div>
            </div>
            @endif
            
            @if($alert->exception_details)
            <div class="context-section">
                <h3><i class="fas fa-code"></i> Exception Details</h3>
                <div class="context-data">
Class: {{ $alert->exception_details['class'] ?? 'Unknown' }}
File: {{ $alert->exception_details['file'] ?? 'Unknown' }}:{{ $alert->exception_details['line'] ?? 'Unknown' }}
Message: {{ $alert->exception_details['message'] ?? 'No message' }}
                </div>
            </div>
            @endif
            
            <div class="action-buttons">
                <a href="{{ config('app.url') }}/admin/jobseeker/system-alerts" class="btn btn-primary">
                    <i class="fas fa-external-link-alt"></i> View in Dashboard
                </a>
                <a href="{{ config('app.url') }}/admin/jobseeker/command-schedule" class="btn btn-secondary">
                    <i class="fas fa-cogs"></i> Check System Status
                </a>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
                <h4 style="margin: 0 0 10px 0; color: #856404;">
                    <i class="fas fa-lightbulb"></i> Next Steps
                </h4>
                <ul style="margin: 0; padding-left: 20px; color: #856404;">
                    <li>Check the attached error report for detailed information</li>
                    <li>Review recent system logs for related issues</li>
                    <li>Verify system resources and connectivity</li>
                    <li>Contact technical support if the issue persists</li>
                </ul>
            </div>
        </div>
        
        <div class="email-footer">
            <p>
                This alert was automatically generated by the ITQAN JobSeeker monitoring system.<br>
                <a href="{{ config('app.url') }}">{{ config('app.name') }}</a> | 
                <a href="mailto:<EMAIL>">Technical Support</a>
            </p>
            <div class="timestamp">
                Generated on {{ now()->format('F j, Y \a\t g:i A T') }}
            </div>
        </div>
    </div>
</body>
</html>
