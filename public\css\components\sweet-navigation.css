/**
 * Sweet Navigation Component CSS
 * Extracted from nouranya.blade.php for reusable navigation functionality
 * 
 * Usage: Add class "sweet-navigation-trigger" to any element with appropriate data attributes
 * Include this CSS file and the corresponding JS file for full functionality
 */


/* ===================================================================
 * CORE SWEET NAVIGATION STYLES
 * ===================================================================
 */


/* SweetAlert2 Custom Classes Navigation Styling */

.classes-navigation-alert {
    animation: slideInFromTop 0.3s ease-out !important;
}

.classes-navigation-alert .swal2-popup {
    margin-top: 80px !important;
    /* Position below breadcrumb */
    box-shadow: 0 10px 30px rgba(0, 153, 51, 0.2) !important;
    border: 2px solid rgba(0, 153, 51, 0.1) !important;
}

.classes-navigation-alert .swal2-title {
    color: #009933 !important;
    font-size: 22px !important;
    margin-bottom: 20px !important;
}

.classes-navigation-alert .swal2-content {
    text-align: left !important;
    padding: 0 !important;
}

@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}


/* ===================================================================
 * SEARCH CONTAINER STYLES (Original nouranya.blade.php structure)
 * ===================================================================
 */

.classes-search-container {
    margin-bottom: 15px;
    position: relative;
}

.classes-search-input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.classes-search-input:focus {
    outline: none;
    border-color: #009933;
    box-shadow: 0 0 0 3px rgba(0, 153, 51, 0.1);
}

.classes-search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}


/* Legacy support for sweet-navigation classes */

.sweet-navigation-search-container {
    margin-bottom: 15px;
    position: relative;
}

.sweet-navigation-search-input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.sweet-navigation-search-input:focus {
    outline: none;
    border-color: #009933;
    box-shadow: 0 0 0 3px rgba(0, 153, 51, 0.1);
}

.sweet-navigation-search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}


/* ===================================================================
 * PROGRAM GROUP STYLES
 * ===================================================================
 */

.sweet-navigation-group {
    margin-bottom: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.sweet-navigation-group-header {
    background: linear-gradient(135deg, #009933, #00b33c);
    color: white;
    padding: 12px 15px;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.sweet-navigation-group-header:hover {
    background: linear-gradient(135deg, #00b33c, #009933);
}

.sweet-navigation-group-header .toggle-icon {
    transition: transform 0.3s ease;
}

.sweet-navigation-group-header.collapsed .toggle-icon {
    transform: rotate(-90deg);
}


/* ===================================================================
 * ITEMS LIST STYLES
 * ===================================================================
 */

.sweet-navigation-items-list {
    max-height: 300px;
    overflow-y: auto;
    background: #f8f9fa;
}

.sweet-navigation-item {
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.sweet-navigation-item:last-child {
    border-bottom: none;
}

.sweet-navigation-item:hover {
    background: rgba(0, 153, 51, 0.1);
    transform: translateX(5px);
}

.sweet-navigation-item.current-item {
    background: rgba(0, 153, 51, 0.2);
    border-left: 4px solid #009933;
    font-weight: bold;
}

.sweet-navigation-item.current-item::before {
    content: "👉 ";
    margin-right: 5px;
}


/* ===================================================================
 * ITEM CONTENT STYLES
 * ===================================================================
 */

.sweet-navigation-item-main-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    text-align: left;
}

.sweet-navigation-item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
    text-align: left;
}

.sweet-navigation-item-details {
    font-size: 12px;
    color: #666;
    text-align: left;
}


/* Teacher/Link styling */

.sweet-navigation-link {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
}

.sweet-navigation-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.sweet-navigation-item-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 11px;
    color: #888;
}

.sweet-navigation-count {
    background: #009933;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    margin-bottom: 2px;
}


/* ===================================================================
 * ACTION BUTTONS STYLES
 * ===================================================================
 */

.sweet-navigation-item-actions {
    margin-top: 5px;
}

.sweet-navigation-action-btn {
    font-size: 10px;
    padding: 2px 6px;
    margin: 0 2px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    text-decoration: none;
    color: white;
    transition: all 0.2s ease;
}

.sweet-navigation-action-report {
    background: #17a2b8;
}

.sweet-navigation-action-show {
    background: #6c757d;
}

.sweet-navigation-action-reports {
    background: #ffc107;
    color: #333;
}

.sweet-navigation-action-btn:hover {
    transform: scale(1.1);
    text-decoration: none;
    color: white;
}


/* ===================================================================
 * UTILITY STYLES
 * ===================================================================
 */

.sweet-navigation-no-items-found {
    text-align: center;
    padding: 30px;
    color: #666;
    font-style: italic;
}


/* Scrollbar styling for items list */

.sweet-navigation-items-list::-webkit-scrollbar {
    width: 6px;
}

.sweet-navigation-items-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sweet-navigation-items-list::-webkit-scrollbar-thumb {
    background: #009933;
    border-radius: 3px;
}

.sweet-navigation-items-list::-webkit-scrollbar-thumb:hover {
    background: #007a29;
}


/* ===================================================================
 * SWEETALERT2 OVERRIDE STYLES - CRITICAL FOR COMPATIBILITY
 * ===================================================================
 * This MUST be loaded to override all framework conflicts
 * Ensures SweetAlert2 works with Bootstrap 3 & Semantic UI
 */


/* Scoped styles for classes navigation alert only - avoid global conflicts */


/* Only apply to classes navigation alert container */

.classes-navigation-alert.swal2-container {
    z-index: 999999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: auto !important;
}


/* Only apply backdrop styles when classes navigation is active */


/* Use a more compatible approach instead of :has() */

body.classes-navigation-active .swal2-backdrop {
    z-index: 999998 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}


/* Scoped popup styles */

.classes-navigation-alert .swal2-popup {
    z-index: 999999 !important;
    pointer-events: auto !important;
    position: relative !important;
}


/* Scoped close button styles */

.classes-navigation-alert .swal2-close {
    z-index: 1000000 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
}


/* FORCE all classes navigation alert children to be interactive - scoped only */

.classes-navigation-alert *,
.classes-navigation-alert .swal2-container *,
.classes-navigation-alert .swal2-popup * {
    pointer-events: auto !important;
}


/* Specific fix for classes-navigation-alert only */

.classes-navigation-alert {
    pointer-events: auto !important;
}

.classes-navigation-alert .swal2-close {
    pointer-events: auto !important;
    cursor: pointer !important;
    display: block !important;
    visibility: visible !important;
}


/* Override framework interference - scoped to classes navigation only */

.classes-navigation-alert .swal2-container,
.classes-navigation-alert .swal2-backdrop,
.classes-navigation-alert .swal2-popup,
.classes-navigation-alert .swal2-close {
    -webkit-user-select: auto !important;
    -moz-user-select: auto !important;
    -ms-user-select: auto !important;
    user-select: auto !important;
}


/* ===================================================================
 * ORIGINAL NOURANYA.BLADE.PHP CLASSES STRUCTURE
 * ===================================================================
 */


/* Program Group Styles */

.program-group {
    margin-bottom: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.program-header {
    background: linear-gradient(135deg, #009933, #00b33c);
    color: white;
    padding: 12px 15px;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.program-header:hover {
    background: linear-gradient(135deg, #00b33c, #009933);
}

.program-header .toggle-icon {
    transition: transform 0.3s ease;
}

.program-header.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.classes-list {
    background: #f9f9f9;
    max-height: 400px;
    overflow-y: auto;
}


/* Class Item Styles */

.class-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.class-item:last-child {
    border-bottom: none;
}

.class-item:hover {
    background: #f0f8ff;
    transform: translateX(3px);
}

.class-item.current-class {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    border-left: 4px solid #009933;
    font-weight: bold;
}

.class-main-info {
    flex: 1;
}

.class-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.class-details {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.class-details i {
    margin-right: 4px;
    color: #009933;
}

.class-details .teacher-link {
    color: #0066cc;
    text-decoration: none;
}

.class-details .teacher-link:hover {
    text-decoration: underline;
}

.class-meta {
    display: flex;
    align-items: center;
    gap: 10px;
}

.student-count {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 4px 8px;
    border-radius: 12px;
    white-space: nowrap;
}

.class-actions {
    display: flex;
    gap: 5px;
}

.class-action-btn {
    display: inline-block;
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 4px;
    color: white;
    text-decoration: none;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.class-action-btn:hover {
    transform: scale(1.1);
    text-decoration: none;
    color: white;
}

.action-report {
    background: #28a745;
}

.action-report:hover {
    background: #218838;
}

.action-show {
    background: #007bff;
}

.action-show:hover {
    background: #0056b3;
}

.action-plan {
    background: #ffc107;
    color: #212529 !important;
}

.action-plan:hover {
    background: #e0a800;
    color: #212529 !important;
}


/* No classes found message */

.no-classes-found {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}


/* Responsive adjustments */

@media (max-width: 768px) {
    .class-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    .class-meta {
        width: 100%;
        justify-content: space-between;
    }
}