<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;
use Mo<PERSON><PERSON>\JobSeeker\Entities\Job;
use Mo<PERSON>les\JobSeeker\Entities\JobNotificationSetup;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;

final class JobAlertNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param Collection<int, Job> $jobs Collection of Job models relevant to the recipient
     * @param JobNotificationSetup $setup The notification setup instance
     * @return void
     */
    public function __construct(
        private readonly Collection $jobs,
        private readonly JobNotificationSetup $setup
    ) {
        $this->queue = 'job_notifications';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        // Email-only notification - FCM is handled by JobAlertFcmNotification
        Log::info('JobAlertNotification: Email-only notification channel', [
            'notifiable_id' => $notifiable->id ?? 'unknown',
            'notifiable_type' => get_class($notifiable),
            'setup_id' => $this->setup->id,
            'jobs_count' => $this->jobs->count()
        ]);
        
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     * Uses EmailService for robust email delivery with circuit breaker protection.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        $correlationId = uniqid('job_alert_notification_', true);
        
        Log::info('JobAlertNotification: Preparing email notification via EmailService', [
            'correlation_id' => $correlationId,
            'notifiable_id' => $notifiable->id,
            'notifiable_email' => $notifiable->email,
            'jobs_count' => $this->jobs->count(),
            'setup_name' => $this->setup->name
        ]);

        // Early return if no jobs to prevent empty emails
        if ($this->jobs->isEmpty()) {
            Log::warning('JobAlertNotification: No jobs to notify, skipping email', [
                'correlation_id' => $correlationId,
                'notifiable_id' => $notifiable->id,
                'setup_id' => $this->setup->id
            ]);
            
            // Return a dummy MailMessage that won't be sent
            return (new MailMessage)
                ->subject('No Jobs Available')
                ->line('No jobs found for notification.');
        }

        $subject = $this->getEmailSubject();
        
        try {
            // Use EmailService for robust email delivery
            $emailService = app(EmailService::class);
            
            $result = $emailService->sendEmail(
                [
                    'email' => $notifiable->email,
                    'name' => $notifiable->name ?? explode('@', $notifiable->email)[0]
                ],
                $subject,
                'jobseeker::emails.jobs.jobseeker_notification',
                [
                    'jobs' => $this->formatJobsForEmail(),
                    'jobSeeker' => $notifiable,
                    'setup' => $this->setup,
                    'currentDate' => now()->format('F j, Y'),
                ],
                [], // attachments
                []  // cc
            );
            
            if ($result['success']) {
                Log::info('JobAlertNotification: Email sent successfully via EmailService', [
                    'correlation_id' => $correlationId,
                    'notifiable_id' => $notifiable->id,
                    'subject' => $subject,
                    'jobs_count' => $this->jobs->count(),
                    'email_service_correlation_id' => $result['correlation_id'] ?? null
                ]);
            } else {
                Log::error('JobAlertNotification: Email failed via EmailService', [
                    'correlation_id' => $correlationId,
                    'notifiable_id' => $notifiable->id,
                    'error' => $result['message'] ?? 'Unknown error',
                    'email_service_correlation_id' => $result['correlation_id'] ?? null
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('JobAlertNotification: Exception calling EmailService', [
                'correlation_id' => $correlationId,
                'notifiable_id' => $notifiable->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        // Return a dummy MailMessage since we've already sent the email via EmailService
        // This prevents Laravel's mail system from trying to send it again
        return (new MailMessage)
            ->subject($subject)
            ->line('Email sent via EmailService');
    }

    /**
     * Format jobs for email display.
     *
     * @return array
     */
    private function formatJobsForEmail(): array
    {
        return $this->jobs->map(function (Job $job) {
            return [
                'id' => $job->id,
                'title' => $job->title ?? $job->position,
                'position' => $job->position,
                'company' => $job->company ?? $job->company_name,
                'company_name' => $job->company_name,
                'location' => $job->location ?? $job->locations,
                'locations' => $job->locations,
                'publish_date' => $job->publish_date?->format('Y-m-d'),
                'salary' => $job->salary,
                'contract_type' => $job->contract_type,
                'work_type' => $job->work_type,
                'slug' => $job->slug,
                'url' => $job->url,
                'source' => $job->source,
            ];
        })->toArray();
    }

    /**
     * Get the email subject for this notification.
     *
     * @return string
     */
    private function getEmailSubject(): string
    {
        $jobCount = $this->jobs->count();

        // For single job notifications, include job title and company name
        if ($jobCount === 1) {
            $job = $this->jobs->first();
            $jobTitle = $job->position ?? 'Job Position';
            $companyName = $job->company_name ?? 'Company';
            return "Job Opportunity: {$jobTitle} at {$companyName}";
        }

        // For multiple jobs, use generic alert format
        $pluralJobs = $jobCount > 1 ? 'jobs' : 'job';
        return "Job Alert: {$jobCount} new {$pluralJobs} matching your criteria";
    }

    /**
     * Get the setup ID for this notification.
     *
     * @return int|null
     */
    public function getSetupId(): ?int
    {
        return $this->setup->id;
    }

    /**
     * Get the job IDs for this notification.
     *
     * @return array
     */
    public function getJobIds(): array
    {
        return $this->jobs->pluck('id')->toArray();
    }
} 