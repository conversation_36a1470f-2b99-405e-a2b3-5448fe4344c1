<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Events;

use Illuminate\Queue\SerializesModels;
use Modules\JobSeeker\Entities\Job;

final class JobProcessedEvent
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param Job $job The processed job with canonical categories assigned
     * @return void
     */
    public function __construct(public readonly Job $job)
    {
        // The job property is now public and readonly
    }
} 