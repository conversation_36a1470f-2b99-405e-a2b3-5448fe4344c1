/*
 * Fix command_schedule_filters table creation and verification
 * 
 * This script ensures the command_schedule_filters table exists with proper structure,
 * foreign key constraints, and indexes for the JobSeeker module's CommandSchedule system.
 * 
 * Module: JobSeeker
 * Author: System Administrator  
 * Date: 2025-01-07
 * 
 * IMPORTANT: This file must be executed by authorized database administrators
 */

-- Step 1: Drop table if it exists with issues and recreate properly
DROP TABLE IF EXISTS command_schedule_filters;

-- Step 2: Create command_schedule_filters table with comprehensive structure
CREATE TABLE command_schedule_filters (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    schedule_rule_id bigint unsigned NOT NULL,
    categories json DEFAULT NULL COMMENT 'Array of job categories to filter by',
    locations json DEFAULT NULL COMMENT 'Array of province/location names to filter by', 
    companies json DEFAULT NULL COMMENT 'Array of company names to filter by',
    experience_levels json DEFAULT NULL COMMENT 'Array of experience levels (Entry, Mid, Senior, etc.)',
    search_term varchar(255) DEFAULT NULL COMMENT 'Search term for job title/description',
    work_type varchar(50) DEFAULT NULL COMMENT 'Work type filter (Remote, On-site, Hybrid)',
    is_default tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Marks the fallback filter configuration',
    -- ACBAR-specific fields for extended functionality
    max_retries int DEFAULT NULL COMMENT 'Maximum retry attempts for ACBAR commands',
    timeout int DEFAULT NULL COMMENT 'Timeout in seconds for API requests',
    base_delay int DEFAULT NULL COMMENT 'Base delay in milliseconds between requests',
    created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Primary key
    PRIMARY KEY (id),
    
    -- Unique constraint to ensure one filter per schedule rule
    UNIQUE KEY command_schedule_filters_rule_unique (schedule_rule_id),
    
    -- Index for performance on default filter lookups
    KEY command_schedule_filters_is_default_index (is_default),
    
    -- Foreign key constraint to ensure referential integrity
    CONSTRAINT command_schedule_filters_schedule_rule_id_foreign 
        FOREIGN KEY (schedule_rule_id) 
        REFERENCES command_schedule_rules (id) 
        ON DELETE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 3: Create additional indexes for JSON column searches (MySQL 5.7+ syntax)
-- Note: These indexes will be created if MySQL version supports them
SET @sql = IF(
    (SELECT VERSION() REGEXP '^[8-9]|^5\\.[7-9]') = 1,
    'CREATE INDEX command_schedule_filters_categories_index ON command_schedule_filters ((CAST(categories->>\'$\' AS CHAR(255) ARRAY)))',
    'SELECT "Skipping JSON index creation - MySQL version not compatible" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(
    (SELECT VERSION() REGEXP '^[8-9]|^5\\.[7-9]') = 1,
    'CREATE INDEX command_schedule_filters_locations_index ON command_schedule_filters ((CAST(locations->>\'$\' AS CHAR(255) ARRAY)))',
    'SELECT "Skipping JSON index creation - MySQL version not compatible" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 4: Create default filter configuration using existing schedule rule
-- Note: This will be handled by the application layer to avoid foreign key constraints

-- Step 5: Verification queries
SELECT 'Table structure verification:' as verification_step;
DESCRIBE command_schedule_filters;

SELECT 'Index verification:' as verification_step;
SHOW INDEX FROM command_schedule_filters;

SELECT 'Foreign key verification:' as verification_step;
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 'command_schedule_filters' 
AND TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL;

SELECT 'Default filter setup will be handled by application layer' as default_filter_note;

SELECT 'Table creation completed successfully!' as status; 