<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>JobSeeker Portal - @yield('title', 'Dashboard')</title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('favicon.ico') }}">
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon">
    
    <!-- Bootstrap 5 CSS (CoreUI depends on this) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    
    <!-- Font Awesome 6 CSS (Can be used with CoreUI) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@5.0.0/dist/css/coreui.min.css">
    
    <!-- Optional: CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@3.0.1/css/all.min.css">

    <!-- Toastr CSS - CDN version -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.12.4/dist/sweetalert2.min.css">
    
    <!-- Firebase SDK for Push Notifications -->
    <script>
        // Check if service workers are supported
        if ('serviceWorker' in navigator) {
            // Register the Firebase messaging service worker
            navigator.serviceWorker.register('/firebase-messaging-sw.js')
                .then((registration) => {
                    console.log('Firebase SW registered successfully:', registration);
                })
                .catch((error) => {
                    console.log('Firebase SW registration failed:', error);
                });
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        /* Bootstrap 5.3 Color Mode Variables */
        :root {
            --bs-border-radius-xl: 12px;

            --itqan-primary: #6c757d;
            --itqan-primary-dark: #5a6268;
            --itqan-sidebar-bg: #f8f9fa;
            --itqan-sidebar-active: #6c757d;
            --itqan-sidebar-hover: #e9ecef;
            
            /* Override CoreUI variables for unified gray/white theme */
            --cui-sidebar-bg: var(--itqan-sidebar-bg);
            --cui-sidebar-color: #495057;
            --cui-sidebar-width: 250px;
            --cui-sidebar-width-collapsed: 56px;
            --cui-sidebar-brand-bg: var(--itqan-sidebar-bg);
            --cui-sidebar-brand-color: #495057;
            --cui-header-bg: var(--itqan-sidebar-bg);
            --cui-header-color: #495057;
        }

        .rounded-xl {
            border-radius: var(--bs-border-radius-xl) !important;
        }

        /* Dark mode color overrides */
        [data-bs-theme="dark"] {
            /* II. Color Palette & Bootstrap Variable Overrides */
            --bs-body-bg: #111217;
            --bs-tertiary-bg: #1C1D22;
            --bs-primary: #3B82F6;
            --bs-primary-rgb: 59, 130, 246;
            --bs-body-color: #F0F0F0;
            --bs-secondary-color: #8A8F98;
            --bs-border-color: #33343A;
            --bs-custom-hover-bg: #2A2B30; /* Custom variable */
            
            --bs-heading-color: var(--bs-body-color);
            --bs-link-color: var(--bs-primary);
            --bs-link-hover-color: color-mix(in srgb, var(--bs-primary) 80%, white);


            /* Component specific overrides */
            --bs-card-bg: var(--bs-tertiary-bg);
            --bs-card-border-width: 0;
            --bs-card-cap-bg: transparent;

            --bs-btn-secondary-bg: #33343A;
            --bs-btn-secondary-color: #F0F0F0;
            --bs-btn-secondary-border-color: #33343A;
            --bs-btn-secondary-hover-bg: #4A4B50;
            --bs-btn-secondary-hover-border-color: #4A4B50;

            --bs-nav-pills-link-active-bg: var(--bs-custom-hover-bg);

            /* Overriding existing custom vars to maintain unified gray theme in dark mode */
            --itqan-primary: #adb5bd;
            --itqan-sidebar-bg: var(--bs-tertiary-bg);
            --itqan-sidebar-hover: var(--bs-custom-hover-bg);
            --itqan-sidebar-active: #adb5bd;
            --cui-sidebar-bg: var(--itqan-sidebar-bg);
            --cui-sidebar-color: var(--bs-body-color);
            --cui-header-bg: var(--bs-tertiary-bg);
            --cui-sidebar-brand-bg: var(--bs-tertiary-bg);
            --cui-sidebar-brand-color: var(--bs-body-color);


            /* Form Controls to match the new theme */
            --bs-form-control-bg: #33343A;
            --bs-form-control-color: var(--bs-body-color);
            --bs-form-control-border-color: #4A4B50;
            
            --bs-input-group-addon-bg: #33343A;
            --bs-input-group-addon-color: var(--bs-secondary-color);
            --bs-input-group-addon-border-color: #4A4B50;
            
            --bs-dropdown-bg: var(--bs-tertiary-bg);
            --bs-dropdown-link-hover-bg: var(--bs-custom-hover-bg);
            --bs-dropdown-border-color: var(--bs-border-color);
            --bs-dropdown-divider-bg: var(--bs-border-color);

            /* Override page-specific CSS variables for dark mode */
            --primary-color: var(--bs-primary);
            --primary-dark: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --secondary-color: var(--bs-secondary-color);
            --light-bg: var(--bs-tertiary-bg);
            --border-color: var(--bs-border-color);
            --text-muted: var(--bs-secondary-color);
        }

        /* Global dark mode overrides for all pages */
        [data-bs-theme="dark"] .page-header {
            background: linear-gradient(135deg, var(--bs-primary) 0%, #2563eb 100%) !important;
        }

        [data-bs-theme="dark"] .stats-card {
            background: var(--bs-tertiary-bg) !important;
            border-color: var(--bs-border-color) !important;
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .stats-number {
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .stats-label {
            color: var(--bs-secondary-color) !important;
        }

        [data-bs-theme="dark"] .table th {
            background: var(--bs-tertiary-bg) !important;
            color: var(--bs-body-color) !important;
            border-color: var(--bs-border-color) !important;
        }

        [data-bs-theme="dark"] .table td {
            border-color: var(--bs-border-color) !important;
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .table tbody tr:hover {
            background-color: var(--bs-custom-hover-bg) !important;
        }

        [data-bs-theme="dark"] .modal-header {
            background: var(--bs-tertiary-bg) !important;
            border-color: var(--bs-border-color) !important;
        }

        [data-bs-theme="dark"] .modal-content {
            background: var(--bs-body-bg) !important;
            border-color: var(--bs-border-color) !important;
        }

        [data-bs-theme="dark"] .modal-title {
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .conditional-group {
            background-color: var(--bs-tertiary-bg) !important;
            border-color: var(--bs-border-color) !important;
        }

        [data-bs-theme="dark"] .time-slot-group {
            background-color: var(--bs-tertiary-bg) !important;
            border-color: var(--bs-border-color) !important;
        }

        [data-bs-theme="dark"] .cron_human_readable {
            background-color: var(--bs-tertiary-bg) !important;
        }

        [data-bs-theme="dark"] .cron_human_readable.text-success {
            background-color: rgba(16, 185, 129, 0.1) !important;
            border-left-color: var(--success-color) !important;
        }

        [data-bs-theme="dark"] .cron_human_readable.text-danger {
            background-color: rgba(239, 68, 68, 0.1) !important;
            border-left-color: var(--danger-color) !important;
        }

        /* DataTables dark mode overrides */
        [data-bs-theme="dark"] .dataTables_wrapper {
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .dataTables_filter input {
            background: var(--bs-form-control-bg) !important;
            border-color: var(--bs-border-color) !important;
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .dataTables_length select {
            background: var(--bs-form-control-bg) !important;
            border-color: var(--bs-border-color) !important;
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .dataTables_info {
            color: var(--bs-secondary-color) !important;
        }

        [data-bs-theme="dark"] .pagination .page-link {
            background: var(--bs-tertiary-bg) !important;
            border-color: var(--bs-border-color) !important;
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .pagination .page-link:hover {
            background: var(--bs-custom-hover-bg) !important;
            border-color: var(--bs-primary) !important;
        }

        [data-bs-theme="dark"] .pagination .page-item.active .page-link {
            background: var(--bs-primary) !important;
            border-color: var(--bs-primary) !important;
        }

        /* Additional DataTables and table fixes */
        [data-bs-theme="dark"] .table-responsive {
            background: var(--bs-tertiary-bg) !important;
        }

        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_length,
        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_filter,
        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_info,
        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate {
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_length label,
        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_filter label {
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_scroll {
            background: var(--bs-tertiary-bg) !important;
        }

        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_scrollBody {
            background: var(--bs-tertiary-bg) !important;
        }

        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_scrollHead {
            background: var(--bs-tertiary-bg) !important;
        }

        [data-bs-theme="dark"] .dataTables_wrapper .dataTables_scrollFoot {
            background: var(--bs-tertiary-bg) !important;
        }

        /* Force table background */
        [data-bs-theme="dark"] #scheduleRulesTable,
        [data-bs-theme="dark"] .dataTable {
            background: var(--bs-tertiary-bg) !important;
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] #scheduleRulesTable tbody,
        [data-bs-theme="dark"] .dataTable tbody {
            background: var(--bs-tertiary-bg) !important;
        }

        [data-bs-theme="dark"] #scheduleRulesTable thead,
        [data-bs-theme="dark"] .dataTable thead {
            background: var(--bs-tertiary-bg) !important;
        }

        /* Fix any remaining white backgrounds */
        [data-bs-theme="dark"] .bg-white {
            background: var(--bs-tertiary-bg) !important;
        }

        [data-bs-theme="dark"] .bg-light {
            background: var(--bs-tertiary-bg) !important;
        }

        [data-bs-theme="dark"] .text-dark {
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .text-muted {
            color: var(--bs-secondary-color) !important;
        }

        /* Dropdown menus */
        [data-bs-theme="dark"] .dropdown-menu {
            background: var(--bs-tertiary-bg) !important;
            border-color: var(--bs-border-color) !important;
        }

        [data-bs-theme="dark"] .dropdown-item {
            color: var(--bs-body-color) !important;
        }

        [data-bs-theme="dark"] .dropdown-item:hover,
        [data-bs-theme="dark"] .dropdown-item:focus {
            background: var(--bs-custom-hover-bg) !important;
            color: var(--bs-body-color) !important;
        }

        /* Button group fixes */
        [data-bs-theme="dark"] .btn-group-actions {
            color: var(--bs-body-color) !important;
        }

        /* Card body fixes */
        [data-bs-theme="dark"] .card-body {
            background: var(--bs-tertiary-bg) !important;
            color: var(--bs-body-color) !important;
        }

        /* Further refinements for dark mode */
        [data-bs-theme="dark"] .sidebar {
            background: var(--itqan-sidebar-bg) !important;
            border-right: 1px solid var(--bs-border-color);
        }

        [data-bs-theme="dark"] .sidebar-nav .nav-link.active {
            background: var(--itqan-sidebar-active) !important;
            border-left-color: var(--itqan-sidebar-active);
        }

        [data-bs-theme="dark"] .header {
            box-shadow: none;
            border-bottom: 1px solid var(--bs-border-color);
        }

        [data-bs-theme="dark"] .btn-outline-secondary {
             --bs-btn-color: var(--bs-secondary-color);
             --bs-btn-border-color: var(--bs-border-color);
             --bs-btn-hover-bg: var(--bs-custom-hover-bg);
             --bs-btn-hover-border-color: var(--bs-border-color);
             --bs-btn-active-bg: #33343A;
             --bs-btn-active-border-color: #33343A;
        }

        [data-bs-theme="dark"] .card {
            box-shadow: none;
        }
        
        [data-bs-theme="dark"] .form-control:focus {
            border-color: var(--itqan-primary);
            box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
        }

        [data-bs-theme="dark"] .footer {
            background-color: var(--bs-body-bg);
            border-top: 1px solid var(--bs-border-color);
        }

        [data-bs-theme="dark"] .user-account-menu {
            background: var(--bs-tertiary-bg);
            box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.25);
        }

        [data-bs-theme="dark"] .breadcrumb {
             background-color: var(--bs-tertiary-bg);
        }
        
        [data-bs-theme="dark"] .table {
            --bs-table-bg: var(--bs-tertiary-bg);
            --bs-table-border-color: var(--bs-border-color);
            --bs-table-striped-bg: #2c3136; /* Needs a better var */
            --bs-table-hover-bg: var(--bs-custom-hover-bg);
            color: var(--bs-body-color);
        }

        /* Sidebar collapse/expand transitions */
        .sidebar {
            transition: width 0.3s ease, transform 0.3s ease;
            background: var(--itqan-sidebar-bg) !important;
            box-shadow: 2px 0 4px rgba(0,0,0,0.08);
            border-right: 1px solid var(--bs-border-color);
        }
        
        .sidebar.sidebar-collapsed {
            width: var(--cui-sidebar-width-collapsed) !important;
        }
        
        .sidebar.sidebar-collapsed .sidebar-brand-full,
        .sidebar.sidebar-collapsed .nav-link-text,
        .sidebar.sidebar-collapsed .sidebar-user-info .user-details {
            opacity: 0;
            visibility: hidden;
        }
        
        .sidebar.sidebar-collapsed .sidebar-brand-narrow {
            opacity: 1;
            visibility: visible;
        }

        /* Wrapper margin transitions */
        .wrapper {
            transition: margin-left 0.3s ease;
            margin-left: var(--cui-sidebar-width);
        }
        
        .wrapper.sidebar-collapsed {
            margin-left: var(--cui-sidebar-width-collapsed);
        }
        
        /* Debug theme helper */
        .theme-display {
            position: fixed;
            bottom: 10px;
            right: 10px;
            padding: 5px 10px;
            background: rgba(0,0,0,0.5);
            color: white;
            font-size: 12px;
            z-index: 9999;
            border-radius: 4px;
        }

        /* Sidebar styling */
        .sidebar-nav .nav-link {
            color: var(--cui-sidebar-color) !important;
            padding: 0.8rem 1rem;
            display: flex;
            align-items: center;
            transition: all 0.3s;
            white-space: nowrap;
            border-radius: 6px;
            margin: 2px 8px;
        }
        
        .sidebar-nav .nav-link:hover {
            color: var(--itqan-sidebar-active) !important;
            background-color: var(--itqan-sidebar-hover) !important;
        }
        
        .sidebar-nav .nav-link.active {
            color: var(--itqan-sidebar-active) !important;
            background: var(--itqan-sidebar-hover) !important;
            font-weight: 600;
            border-left: 3px solid var(--itqan-sidebar-active);
        }
        
        .sidebar-nav .nav-icon {
            margin-right: 0.5rem;
            flex: 0 0 1.5rem;
            text-align: center;
        }
        
        .sidebar-nav .nav-group-toggle::after {
            margin-left: auto;
        }
        
        .sidebar-nav .nav-group.show .nav-group-toggle {
            color: var(--itqan-sidebar-active) !important;
            background-color: var(--itqan-sidebar-hover);
        }
        
        .sidebar-nav .nav-group-items {
            padding-left: 1rem;
        }
        
        .sidebar-nav .nav-title {
            padding: 0.8rem 1rem;
            margin-top: 0.5rem;
            font-size: 80%;
            font-weight: 700;
            color: var(--bs-secondary-color);
            text-transform: uppercase;
        }
        
        .sidebar-brand {
            background-color: var(--cui-sidebar-brand-bg);
            color: var(--cui-sidebar-brand-color);
            padding: 1rem;
            height: 60px;
            position: relative;
            overflow: hidden;
            border-bottom: 1px solid var(--bs-border-color);
            font-weight: 600;
        }
        
        .sidebar-brand-narrow {
            opacity: 0;
            visibility: hidden;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        /* Header styling */
        .header {
            background: var(--cui-header-bg) !important;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header .nav-link {
            color: white !important;
            padding: 0.5rem 1rem;
        }
        
        .header .nav-link:hover {
            color: rgba(255,255,255,0.75) !important;
        }
        
        .header .header-toggler {
            color: white !important;
        }
        
        .avatar-img {
            object-fit: cover;
        }

        /* Desktop sidebar toggle button */
        .sidebar-toggle-desktop {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            padding: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .sidebar-toggle-desktop:hover {
            color: rgba(255,255,255,0.75);
            transform: scale(1.1);
        }






        /* Global Loader Styles */
        .global-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .loader-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(2px);
        }

        .loader-content {
            position: relative;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: var(--bs-body-bg);
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            text-align: center;
            border: 1px solid var(--bs-border-color);
        }

        .loader-spinner {
            margin-bottom: 1rem;
        }

        .loader-spinner .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        .loader-text {
            font-size: 1rem;
            color: var(--bs-body-color);
            font-weight: 500;
        }
        
        /* Content area & breadcrumb styling */
        .breadcrumb {
            padding: 0.75rem 1rem;
            background-color: var(--bs-secondary-bg);
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
        
        .breadcrumb a {
            color: var(--itqan-primary);
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .content-wrapper {
            padding: 1.5rem;
        }
        
        /* Footer styling */
        .footer {
            background-color: var(--bs-secondary-bg);
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-top: 1px solid var(--bs-border-color);
        }
        
        .data-source-info {
            font-size: 12px;
            color: var(--bs-secondary-color);
        }

        /* User info in sidebar */
        .sidebar-user-info {
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 1px solid var(--bs-border-color);
            transition: all 0.3s ease;
        }
        
        .sidebar-user-info .user-details {
            transition: all 0.3s ease;
        }
        
        .sidebar-user-info .user-name {
            color: var(--cui-sidebar-color);
            font-weight: 500;
        }
        
        .sidebar-user-info .user-status {
            font-size: 0.75rem;
            color: var(--bs-secondary-color);
        }
        
        .sidebar-user-info .online-status {
            color: #4caf50;
            margin-right: 0.25rem;
        }

        /* User Account Management Section */
        .sidebar-user-account {
            margin-top: auto;
            border-top: 1px solid var(--bs-border-color);
            background: var(--itqan-sidebar-hover);
        }
        
        .user-account-dropdown {
            position: relative;
        }
        
        .user-account-toggle {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 1rem;
            background: none;
            border: none;
            color: var(--cui-sidebar-color);
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .user-account-toggle:hover {
            background-color: var(--itqan-sidebar-hover);
            color: var(--itqan-sidebar-active);
        }
        
        .user-account-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 0.75rem;
            border: 2px solid rgba(255,255,255,0.2);
            flex-shrink: 0;
        }
        
        .user-account-info {
            flex-grow: 1;
            min-width: 0; /* Allow text truncation */
        }
        
        .user-account-info h6 {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--cui-sidebar-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .user-account-actions {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }
        
        .action-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: 4px;
            color: var(--bs-secondary-color);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }
        
        .action-icon:hover {
            background-color: var(--itqan-sidebar-hover);
            color: var(--itqan-sidebar-active);
            transform: translateY(-1px);
        }
        
        .action-icon:active {
            transform: translateY(0);
        }
        
        .user-account-chevron {
            margin-left: auto;
            transition: transform 0.3s ease;
            flex-shrink: 0;
        }
        
        .user-account-dropdown.show .user-account-chevron {
            transform: rotate(180deg);
        }
        
        .user-account-menu {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background: var(--itqan-sidebar-bg);
            border: 1px solid var(--bs-border-color);
            border-radius: 8px 8px 0 0;
            box-shadow: 0 -4px 12px rgba(0,0,0,0.15);
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .user-account-dropdown.show .user-account-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-account-menu .dropdown-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--cui-sidebar-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }
        
        .user-account-menu .dropdown-item:hover {
            background-color: var(--itqan-sidebar-hover);
            color: var(--itqan-sidebar-active);
        }
        
        .user-account-menu .dropdown-item i {
            width: 1.5rem;
            margin-right: 0.5rem;
            text-align: center;
        }
        
        .user-account-menu .dropdown-divider {
            border-color: var(--bs-border-color);
            margin: 0.25rem 0;
        }

        .body.flex-grow-1 {
            background-color: var(--bs-body-bg);
        }
        
        [data-bs-theme="dark"] .body.flex-grow-1 {
            background-color: var(--bs-body-bg);
        }
        
        /* Bootstrap card overrides for better styling */
        .card {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            background-color: var(--bs-body-bg);
            border: 1px solid var(--bs-border-color);
        }
        
        .card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .card .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--bs-secondary-bg);
            border-bottom: 1px solid var(--bs-border-color);
        }
        
        .card .card-header .card-tools {
            display: flex;
            gap: 5px;
        }
        
        .card-header {
            border-bottom: 1px solid var(--bs-border-color);
            padding: 0.75rem 1.25rem;
            background-color: var(--bs-secondary-bg);
        }
        
        .card-header-collapsible {
            cursor: pointer;
        }
        
        .card-collapsible .card-body {
            transition: all 0.3s ease;
        }
        
        /* Button styles to match CoreUI */
        .btn-primary {
            background-color: var(--itqan-primary);
            border-color: var(--itqan-primary-dark);
        }
        
        .btn-primary:hover {
            background-color: var(--itqan-primary-dark);
            border-color: var(--itqan-primary-dark);
        }

        /* Fix for CoreUI dropdown misalignment */
        .dropdown-menu {
            --bs-dropdown-min-width: 10rem;
            --bs-dropdown-padding-x: 0;
            --bs-dropdown-padding-y: 0.5rem;
            --bs-dropdown-border-width: 1px;
        }
        
        /* Form control adjustments */
        .form-control:focus {
            border-color: var(--itqan-primary);
            box-shadow: 0 0 0 0.25rem rgba(70, 56, 194, 0.25);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container-lg {
                max-width: 100%;
                padding-left: 15px;
                padding-right: 15px;
            }
            
            .wrapper {
                margin-left: 0 !important;
            }
            
            .wrapper.sidebar-collapsed {
                margin-left: 0 !important;
            }
            
            .sidebar-toggle-desktop {
                display: none;
            }
            
            .body {
                padding: 1rem !important;
            }
            
            /* Mobile adjustments for user account section */
            .user-account-toggle {
                padding: 0.75rem;
            }
            
            .user-account-info h6 {
                font-size: 0.8rem;
            }
            
            .action-icon {
                width: 24px;
                height: 24px;
                font-size: 0.75rem;
            }
            
            .user-account-menu {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                border-radius: 0;
            }
            
            /* Mobile sidebar overlay */
            .sidebar {
                box-shadow: 4px 0 8px rgba(0,0,0,0.1);
            }
        }
        
        /* Collapsible card styles */
        .collapse-indicator::after {
            content: "\f077"; /* fa-chevron-up */
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            margin-left: 5px;
            transition: transform 0.3s;
        }
        
        .collapsed .collapse-indicator::after {
            transform: rotate(180deg);
        }
        
        /* Info box styling (for alerts that use close button) */
        .info-box {
            position: relative;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.25rem;
            background-color: var(--bs-info-bg-subtle);
            color: var(--bs-info-text-emphasis);
            border-color: var(--bs-info-border-subtle);
        }
        
        .info-box .btn-close {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            padding: 0.5rem;
            background-color: transparent;
            border: 0;
            font-size: 1rem;
        }

        /* Global loader styles */
        .global-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .global-loader.show {
            visibility: visible;
            opacity: 1;
        }
        
        .loader-content {
            background: var(--bs-body-bg);
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            text-align: center;
            min-width: 200px;
            border: 1px solid var(--bs-border-color);
        }
        
        .loader-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--bs-secondary-bg);
            border-top: 4px solid var(--itqan-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loader-text {
            color: var(--bs-body-color);
            font-weight: 500;
        }
    </style>
    
    <!-- JavaScript Data -->
    <script type="text/javascript">
        window.AppConfig = {
            user: {
                id: {{ (auth()->check() ? auth()->id() : (auth()->guard('job_seeker')->check() ? auth()->guard('job_seeker')->id() : 'null')) }},
                authenticated: {{ (auth()->check() || auth()->guard('job_seeker')->check()) ? 'true' : 'false' }},
            },
            urls: {
                base: "{{ url('/') }}",
                api: "{{ url('/api') }}",
                workplace: "{{ url('/workplace') }}",
                assets: "{{ asset('/') }}",
                current: "{{ url()->current() }}"
            },
            csrf: {
                token: "{{ csrf_token() }}",
                field: "{{ csrf_field() }}"
            },
            env: "{{ app()->environment() }}",
            debug: {{ config('app.debug') ? 'true' : 'false' }}
        };
    </script>
    

    
    @stack('styles')
    @yield('styles')
</head>
<body>
    <!-- Global Loader -->
    <div class="global-loader" id="global-loader" style="display: none;">
        <div class="loader-backdrop"></div>
        <div class="loader-content">
            <div class="loader-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <div class="loader-text">Loading...</div>
        </div>
    </div>

    <!-- CoreUI App Container -->
    <div class="sidebar sidebar-fixed d-flex flex-column" id="sidebar">
        <!-- Logo and branding -->
        <div class="sidebar-brand d-flex align-items-center">
            <span class="sidebar-brand-full fs-5 fw-bold">{{ config('app.name', 'ITQAN') }}</span>
            <span class="sidebar-brand-narrow fs-5 fw-bold">Q</span>
        </div>
        
      
        
        <!-- Navigation -->
        <ul class="sidebar-nav flex-grow-1" data-coreui="navigation">
           
            
            <li class="nav-item">
                <a class="nav-link {{ request()->is('dashboard*') ? 'active' : '' }}" href="{{ url('/dashboard') }}">
                    <i class="nav-icon fa-solid fa-gauge"></i>
                    <span class="nav-link-text">Dashboard</span>
                </a>
            </li>
            <li class="nav-group {{ request()->is('jobseeker/admin*') ? 'show' : '' }}">
                <a class="nav-link nav-group-toggle" href="#">
                    <i class="nav-icon fa-solid fa-briefcase"></i>
                    <span class="nav-link-text">Admin</span>
                </a>
                <ul class="nav-group-items">
                    {{-- <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.jobseeker.jobsaf_schedule.*') ? 'active' : '' }}" 
                            href="{{ route('admin.jobseeker.jobsaf_schedule.index') }}">
                             <i class="nav-icon fas fa-calendar-alt"></i>
                             <span class="nav-link-text">Schedule Management</span>
                         </a>
                    </li> --}}
                    <!-- add the new Schedule Management -->

                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.jobseeker.command_schedule.*') ? 'active' : '' }}" 
                            href="{{ route('admin.jobseeker.command_schedule.index') }}">
                             <i class="nav-icon fas fa-calendar-alt"></i>
                             <span class="nav-link-text">Schedule Management</span>
                         </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.jobseeker.email_control_board.*') ? 'active' : '' }}" 
                            href="{{ route('admin.jobseeker.email_control_board.index') }}">
                             <i class="nav-icon fas fa-envelope-circle-check"></i>
                             <span class="nav-link-text">Email Control Board</span>
                         </a>
                    </li>
                </ul>

            </li>
            <li class="nav-group {{ request()->is('jobseeker/jobs*') ? 'show' : '' }}">
                <a class="nav-link nav-group-toggle" href="#">
                    <i class="nav-icon fa-solid fa-briefcase"></i>
                    <span class="nav-link-text">Jobs</span>
                </a>
                <ul class="nav-group-items">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->is('jobseeker/jobs') && !request()->is('jobseeker/notifications*') && !request()->is('jobseeker/recipients*') && !request()->is('jobseeker/monitoring*') ? 'active' : '' }}" href="{{ url('/jobseeker/jobs') }}">
                            <i class="nav-icon fa-solid fa-list"></i>
                            <span class="nav-link-text">Jobs Board</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->is('jobseeker/notifications*') ? 'active' : '' }}" href="{{ url('/jobseeker/jobs/notifications') }}">
                            <i class="nav-icon fa-solid fa-bell"></i>
                            <span class="nav-link-text">Job Notifications</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->is('jobseeker/recipients*') ? 'active' : '' }}" href="{{ url('/jobseeker/recipients') }}">
                            <i class="nav-icon fa-solid fa-users"></i>
                            <span class="nav-link-text">Recipients</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->is('jobseeker/monitoring*') ? 'active' : '' }}" href="{{ url('/jobseeker/monitoring') }}">
                            <i class="nav-icon fa-solid fa-chart-line"></i>
                            <span class="nav-link-text">Queue Monitor</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->is('jobseeker/operations*') ? 'active' : '' }}" href="{{ url('/jobseeker/operations') }}">
                            <i class="nav-icon fa-solid fa-cogs"></i>
                            <span class="nav-link-text">Job Operations</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <!-- Add more menu items as needed -->
        </ul>
        
        <!-- User Account Management Section -->
        @if(Auth::check() || Auth::guard('job_seeker')->check())
        <div class="sidebar-user-account">
            <div class="user-account-dropdown" id="userAccountDropdown">
                <button class="user-account-toggle" type="button" onclick="toggleUserAccountMenu()">
                    <img src="{{ (Auth::check() ? Auth::user()->image : Auth::guard('job_seeker')->user()->image) ?? asset('assets/img/user.png') }}"
                         class="user-account-avatar" alt="User Avatar">
                    <div class="user-account-info">
                        <h6>{{ Auth::check() ? Auth::user()->name : Auth::guard('job_seeker')->user()->name }}</h6>
                    </div>
                    <div class="user-account-actions">
                        <!-- Profile Icon -->
                        <a href="{{ route('jobseeker.profile') }}" class="action-icon" title="View Profile" onclick="event.stopPropagation();">
                            <i class="fa-solid fa-user"></i>
                        </a>
                        
                        <!-- Logout Icon -->
                        <a href="{{ route('jobseeker.logout') }}" class="action-icon" title="Logout"
                           onclick="event.preventDefault(); event.stopPropagation(); document.getElementById('logout-form-sidebar').submit();">
                            <i class="fa-solid fa-right-from-bracket"></i>
                        </a>
                    </div>
                    <i class="fa-solid fa-chevron-up user-account-chevron"></i>
                </button>
                
                <div class="user-account-menu" id="userAccountMenu">
                    <a href="{{ route('jobseeker.account.settings') }}" class="dropdown-item">
                        <i class="fa-solid fa-cog"></i>
                        <span class="nav-link-text">Account Settings</span>
                    </a>
                    <a href="{{ route('jobseeker.account.password') }}" class="dropdown-item">
                        <i class="fa-solid fa-key"></i>
                        <span class="nav-link-text">Change Password</span>
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="{{ route('jobseeker.account.security') }}" class="dropdown-item">
                        <i class="fa-solid fa-shield-alt"></i>
                        <span class="nav-link-text">Security Settings</span>
                    </a>
                </div>
                
                <form id="logout-form-sidebar" action="{{ route('jobseeker.logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </div>
        </div>
        @endif
        
        <!-- Sidebar toggler visible on mobile -->
        <button class="sidebar-toggler" type="button" data-coreui-toggle="unfoldable"></button>
    </div>

        <div class="wrapper d-flex flex-column min-vh-100 bg-light" style="margin-left: var(--cui-sidebar-width);">
        <!-- Main Content -->
        <div class="body flex-grow-1 px-3 py-4">
            <div class="container-lg">
                <!-- Page title -->
                @hasSection('page_title')
                <div class="row mb-4">
                    <div class="col">
                        <h1 class="mb-0">
                            @yield('page_title')
                            @hasSection('page_subtitle')
                            <small class="text-muted">@yield('page_subtitle')</small>
                            @endif
                        </h1>
                    </div>
                </div>
                @endif
                
                <!-- Flash messages -->
                @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
                    <button type="button" class="btn-close" data-coreui-dismiss="alert" aria-label="Close"></button>
                </div>
                @endif
                
                @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i> {{ session('error') }}
                    <button type="button" class="btn-close" data-coreui-dismiss="alert" aria-label="Close"></button>
                </div>
                @endif
                
                <!-- Main content -->
                @yield('content')
            </div>
        </div>


    </div>

    <!-- Global Loader -->
    <div id="global-loader" class="global-loader" style="display: none;">
        <div class="loader-backdrop"></div>
        <div class="loader-content">
            <div class="loader-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <div class="loader-text">Loading...</div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js" integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g=" crossorigin="anonymous"></script>
    
    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>
    
    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@5.0.0/dist/js/coreui.bundle.min.js"></script>
    
    <!-- Toastr JS - CDN version -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.12.4/dist/sweetalert2.all.min.js"></script>
    
    <!-- Application Scripts -->
    <script>
        // Enhanced Toastr config for JobSeeker module
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: "toast-top-center",
            timeOut: 1500, // 1.5 seconds maximum
            extendedTimeOut: 500,
            preventDuplicates: true,
            showDuration: 300,
            hideDuration: 300,
            showEasing: 'swing',
            hideEasing: 'linear',
            showMethod: 'fadeIn',
            hideMethod: 'fadeOut',
            tapToDismiss: true,
            newestOnTop: true
        };
        
        // Global loader functions
        window.showLoader = function(text = 'Loading...') {
            const loader = document.getElementById('global-loader');
            const loaderText = loader ? loader.querySelector('.loader-text') : null;
            if (loader && loaderText) {
                loaderText.textContent = text;
                loader.style.display = 'flex';
            }
        };
        
        window.hideLoader = function() {
            const loader = document.getElementById('global-loader');
            if (loader) {
                loader.style.display = 'none';
            }
        };
        
        // Global toaster notification functions for JobSeeker module (DRY principle)
        window.showToast = {
            success: function(message, title = 'Success') {
                toastr.success(message, title);
            },
            error: function(message, title = 'Error') {
                toastr.error(message, title);
            },
            warning: function(message, title = 'Warning') {
                toastr.warning(message, title);
            },
            info: function(message, title = 'Info') {
                toastr.info(message, title);
            }
        };
        
        // Global SweetAlert2 wrapper functions
        window.showAlert = {
            confirm: function(options = {}) {
                const defaultOptions = {
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, proceed!',
                    cancelButtonText: 'Cancel'
                };
                return Swal.fire({...defaultOptions, ...options});
            },
            success: function(message, title = 'Success!') {
                return Swal.fire(title, message, 'success');
            },
            error: function(message, title = 'Error!') {
                return Swal.fire(title, message, 'error');
            },
            warning: function(message, title = 'Warning!') {
                return Swal.fire(title, message, 'warning');
            },
            info: function(message, title = 'Info') {
                return Swal.fire(title, message, 'info');
            }
        };
        
        @if(session('toastr_success'))
            toastr.success("{{ session('toastr_success') }}", "Success");
        @endif
        
        @if(session('toastr_error'))
            toastr.error("{{ session('toastr_error') }}", "Error");
        @endif
        
        @if(session('toastr_warning'))
            toastr.warning("{{ session('toastr_warning') }}", "Warning");
        @endif
        
        @if(session('toastr_info'))
            toastr.info("{{ session('toastr_info') }}", "Info");
        @endif

        // Global AJAX loader setup
        $(document).ready(function() {
            // Show loader on AJAX start
            $(document).ajaxStart(function() {
                showLoader('Processing request...');
            });

            // Hide loader on AJAX complete
            $(document).ajaxStop(function() {
                hideLoader();
            });

            // Handle AJAX errors globally
            $(document).ajaxError(function(event, xhr, settings, thrownError) {
                hideLoader();
                if (xhr.status !== 422) { // Don't show error for validation errors
                    showToast.error('An error occurred while processing your request. Please try again.');
                }
            });
        });
        
        // Sidebar Management
        const SidebarManager = {
            init() {
                this.sidebar = document.getElementById('sidebar');
                this.wrapper = document.querySelector('.wrapper');
                this.isCollapsed = this.getStoredState();
                
                this.applySidebarState();
                this.bindEvents();
                
                console.log('Sidebar Manager initialized');
            },
            
            getStoredState() {
                try {
                    return localStorage.getItem('sidebar-collapsed') === 'true';
                } catch (e) {
                    return false;
                }
            },
            
            setStoredState(collapsed) {
                try {
                    localStorage.setItem('sidebar-collapsed', collapsed.toString());
                } catch (e) {
                    console.warn('Failed to store sidebar state:', e);
                }
            },
            
            applySidebarState() {
                if (window.innerWidth <= 768) return; // Don't apply on mobile
                
                if (this.isCollapsed) {
                    this.sidebar.classList.add('sidebar-collapsed');
                    this.wrapper.classList.add('sidebar-collapsed');
                } else {
                    this.sidebar.classList.remove('sidebar-collapsed');
                    this.wrapper.classList.remove('sidebar-collapsed');
                }
            },
            
            toggle() {
                if (window.innerWidth <= 768) return; // Don't toggle on mobile
                
                this.isCollapsed = !this.isCollapsed;
                this.setStoredState(this.isCollapsed);
                this.applySidebarState();
                
                console.log(`Sidebar ${this.isCollapsed ? 'collapsed' : 'expanded'}`);
            },
            
            bindEvents() {
                // Handle window resize
                window.addEventListener('resize', () => {
                    this.applySidebarState();
                });
            }
        };
        
        // User Account Dropdown Management
        const UserAccountManager = {
            init() {
                this.dropdown = document.getElementById('userAccountDropdown');
                this.menu = document.getElementById('userAccountMenu');
                
                if (this.dropdown && this.menu) {
                    this.bindEvents();
                    console.log('User Account Manager initialized');
                }
            },
            
            toggle() {
                this.dropdown.classList.toggle('show');
            },
            
            close() {
                this.dropdown.classList.remove('show');
            },
            
            bindEvents() {
                // Close on outside click
                document.addEventListener('click', (e) => {
                    if (!this.dropdown.contains(e.target)) {
                        this.close();
                    }
                });
                
                // Close on escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.close();
                    }
                });
            }
        };
        
        // Global Functions (for onclick handlers)
        window.toggleSidebar = function() {
            SidebarManager.toggle();
        };
        
        window.toggleUserAccountMenu = function() {
            UserAccountManager.toggle();
        };
        
        // Document ready handler
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded and parsed');
            
            // Initialize all managers (ThemeManager removed)
            SidebarManager.init();
            UserAccountManager.init();
            
            // Initialize CoreUI components
            console.log('Initializing CoreUI components');
            
            // Initialize sidebar
            try {
                const sidebar = coreui.Sidebar.getInstance(document.getElementById('sidebar'));
                if (!sidebar) {
                    console.log('Sidebar instance not found, creating new instance');
                    new coreui.Sidebar(document.getElementById('sidebar'));
                }
            } catch (e) {
                console.error('Error initializing sidebar:', e);
            }
            
            // Initialize dropdowns
            try {
                const dropdownElementList = [].slice.call(document.querySelectorAll('[data-coreui-toggle="dropdown"]'));
                dropdownElementList.forEach(function(dropdownToggleEl) {
                    new coreui.Dropdown(dropdownToggleEl);
                });
            } catch (e) {
                console.error('Error initializing dropdowns:', e);
            }
            
            // Active menu state
            try {
                const currentPath = window.location.pathname;
                const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');
                
                navLinks.forEach(link => {
                    const href = link.getAttribute('href');
                    if (href && (href === currentPath || currentPath.startsWith(href) && href !== '/')) {
                        link.classList.add('active');
                        
                        // Find parent nav-groups if any and open them
                        let parentGroup = link.closest('.nav-group');
                        while (parentGroup) {
                            parentGroup.classList.add('show');
                            parentGroup = parentGroup.parentElement.closest('.nav-group');
                        }
                    }
                });
            } catch (e) {
                console.error('Error setting active menu state:', e);
            }
            
            // Initialize collapsible cards
            try {
                document.querySelectorAll('.card-header-collapsible').forEach(header => {
                    header.addEventListener('click', function() {
                        const cardBody = this.closest('.card').querySelector('.card-body');
                        const wasCollapsed = this.classList.contains('collapsed');
                        
                        if (wasCollapsed) {
                            this.classList.remove('collapsed');
                            $(cardBody).slideDown(300);
                        } else {
                            this.classList.add('collapsed');
                            $(cardBody).slideUp(300);
                        }
                    });
                });
            } catch (e) {
                console.error('Error initializing collapsible cards:', e);
            }
        });
    </script>
    
    <!-- Firebase SDK Scripts (using compat version for better browser support) -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js"></script>
    
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBqJ8K9X7Y5Z3A2B4C6D8E0F2G4H6I8J0K",
            authDomain: "jobseeker-push-notification.firebaseapp.com",
            projectId: "jobseeker-push-notification",
            storageBucket: "jobseeker-push-notification.appspot.com",
            messagingSenderId: "111391222403786823992",
            appId: "1:111391222403786823992:web:abc123def456ghi789jkl"
        };

        // VAPID key for web push notifications
        const vapidKey = "BNdxKJqSDk7rFbqL8ubn7h5BIN9wTpckHVQweY4CL7oKj0-rWJQQFN4JRUVbXO2uMJcka-2spVcOjFrVjqNjPAw";

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        
        // Initialize messaging (with error handling)
        let messaging = null;
        try {
            messaging = firebase.messaging();
        } catch (error) {
            console.error('Failed to initialize Firebase messaging:', error);
        }

        // Push Notification Manager Class
        class PushNotificationManager {
            constructor() {
                this.isSupported = this.checkSupport();
                this.isPermissionGranted = false;
                this.deviceToken = null;
                this.authToken = null;
                this.csrfToken = this.getCsrfToken();
                this.tokenRetrievalAttempted = false;
                this.tokenRetrievalFailed = false;
                this.tokenRetrievalInProgress = false;
                
                if (this.isSupported) {
                    this.init();
                    this.addNotificationStyles();
                }
            }
            
            checkSupport() {
                const hasBasicSupport = 'serviceWorker' in navigator && 'Notification' in window && 'PushManager' in window;
                const hasFirebase = typeof firebase !== 'undefined';
                const hasMessaging = messaging !== null;
                return hasBasicSupport && hasFirebase && hasMessaging;
            }
            
            async init() {
                if (!this.isSupported) {
                    console.warn('Push notifications are not supported in this browser');
                    return;
                }
                
                this.authToken = this.getAuthToken();
                
                // Check current permission status
                this.isPermissionGranted = Notification.permission === 'granted';
                
                if (this.isPermissionGranted) {
                    this.setupForegroundMessageHandling();
                    
                    // Only attempt token retrieval once per page load
                    if (!this.tokenRetrievalAttempted && !this.tokenRetrievalFailed) {
                        this.tokenRetrievalAttempted = true;
                        try {
                            await this.getDeviceToken();
                        } catch (error) {
                            console.warn('Initial token retrieval failed:', error);
                            this.tokenRetrievalFailed = true;
                        }
                    }
                }
                
                console.log('Push notification manager initialized');
            }
            
            async requestPermission() {
                if (!this.isSupported) {
                    throw new Error('Push notifications are not supported');
                }
                
                if (Notification.permission === 'denied') {
                    this.handlePermissionDenied();
                    return false;
                }
                
                try {
                    console.log('Requesting notification permission...');
                    const permission = await Notification.requestPermission();
                    console.log('Permission result:', permission);
                    
                    if (permission === 'granted') {
                        this.isPermissionGranted = true;
                        this.showNotificationStatus('success', 'Push notifications enabled successfully!');
                        
                        // Setup foreground message handling first
                        this.setupForegroundMessageHandling();
                        
                        // Try to get device token, but don't fail if it doesn't work immediately
                        if (!this.tokenRetrievalAttempted && !this.tokenRetrievalFailed) {
                            this.tokenRetrievalAttempted = true;
                            try {
                                await this.getDeviceToken();
                            } catch (tokenError) {
                                console.warn('Failed to get device token immediately:', tokenError);
                                this.tokenRetrievalFailed = true;
                                // Schedule a single retry after a longer delay
                                setTimeout(() => {
                                    if (!this.deviceToken) {
                                        this.tokenRetrievalFailed = false;
                                        this.getDeviceToken().catch(e => {
                                            console.warn('Retry failed:', e);
                                            this.tokenRetrievalFailed = true;
                                        });
                                    }
                                }, 5000);
                            }
                        }
                        
                        return true;
                    } else {
                        this.handlePermissionDenied();
                        return false;
                    }
                } catch (error) {
                    console.error('Error requesting notification permission:', error);
                    
                    // Check if permission was actually granted despite the error
                    if (Notification.permission === 'granted') {
                        console.log('Permission was granted despite error, continuing...');
                        this.isPermissionGranted = true;
                        this.setupForegroundMessageHandling();
                        this.showNotificationStatus('success', 'Push notifications enabled successfully!');
                        
                        // Try to get token in background (only if not already attempted)
                        if (!this.tokenRetrievalAttempted && !this.tokenRetrievalFailed) {
                            this.tokenRetrievalAttempted = true;
                            setTimeout(() => {
                                this.getDeviceToken().catch(e => {
                                    console.warn('Background token retrieval failed:', e);
                                    this.tokenRetrievalFailed = true;
                                });
                            }, 3000);
                        }
                        
                        return true;
                    }
                    
                    this.showNotificationStatus('error', 'Failed to enable push notifications. Please try again.');
                    throw error;
                }
            }
            
            async getDeviceToken() {
                if (!this.isPermissionGranted) {
                    console.warn('Cannot get device token: permission not granted');
                    return null;
                }
                
                if (!messaging) {
                    console.warn('Firebase messaging not initialized');
                    return null;
                }
                
                // If we already have a token, return it
                if (this.deviceToken) {
                    console.log('Using existing device token');
                    return this.deviceToken;
                }
                
                // Prevent multiple simultaneous attempts
                if (this.tokenRetrievalInProgress) {
                    console.log('Token retrieval already in progress, skipping...');
                    return null;
                }
                
                this.tokenRetrievalInProgress = true;
                
                try {
                    console.log('Attempting to get FCM token...');
                    
                    // Add a longer delay to ensure service worker is fully ready
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    
                    // Check if service worker is ready
                    if ('serviceWorker' in navigator) {
                        const registration = await navigator.serviceWorker.ready;
                        console.log('Service worker is ready:', registration);
                    }
                    
                    const token = await messaging.getToken({ vapidKey });
                    
                    if (token) {
                        console.log('FCM device token obtained successfully');
                        this.deviceToken = token;
                        
                        // Try to register with backend
                        try {
                            await this.registerDeviceToken(token);
                            console.log('Device token registered with backend');
                        } catch (registerError) {
                            console.warn('Failed to register token with backend:', registerError);
                            // Don't throw here, token retrieval was successful
                        }
                        
                        return token;
                    } else {
                        console.warn('No registration token available from Firebase');
                        return null;
                    }
                } catch (error) {
                    console.error('Error getting device token:', error);
                    
                    // Check if it's a specific Firebase error
                    if (error.code === 'messaging/failed-service-worker-registration') {
                        console.warn('Service worker registration failed');
                        throw new Error('Service worker not ready, please try again');
                    } else if (error.name === 'AbortError') {
                        console.warn('Token request was aborted, likely due to timing issues');
                        throw new Error('Token request timed out, please try again');
                    }
                    
                    throw error;
                } finally {
                    this.tokenRetrievalInProgress = false;
                }
            }
            
            async registerDeviceToken(token, platform = 'web') {
                try {
                    console.log('Registering device token with backend...');
                    
                    const response = await fetch('/api/jobseeker/device-tokens/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': this.csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        },
                        credentials: 'same-origin',
                        body: JSON.stringify({
                            device_token: token,
                            platform: platform
                        })
                    });
                    
                    console.log('Response status:', response.status);
                    
                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('HTTP error:', response.status, errorText);
                        
                        if (response.status === 401 || response.status === 403) {
                            console.error('Authentication failed - user may not be logged in as job seeker');
                        }
                        
                        return false;
                    }
                    
                    const data = await response.json();
                    console.log('Registration response:', data);
                    
                    if (data.success) {
                        console.log('Device token registered successfully');
                        return true;
                    } else {
                        console.error('Failed to register device token:', data.message);
                        return false;
                    }
                } catch (error) {
                    console.error('Error registering device token:', error);
                    return false;
                }
            }
            
            setupForegroundMessageHandling() {
                if (messaging) {
                    messaging.onMessage((payload) => {
                        console.log('Message received in foreground:', payload);
                        
                        const jobsCount = payload.data?.jobs_count || '0';
                        const setupName = payload.data?.setup_name || 'Job Alert';
                        
                        this.showJobAlert(jobsCount, setupName, payload);
                    });
                }
            }
            
            showJobAlert(jobsCount, setupName, payload) {
                // Create custom notification UI
                const alertDiv = document.createElement('div');
                alertDiv.className = 'job-alert-notification';
                alertDiv.innerHTML = `
                    <div class="alert alert-success alert-dismissible position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 350px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        <div class="d-flex align-items-center">
                            <i class="fa-solid fa-briefcase fa-2x text-success me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">New Jobs Available!</h6>
                                <p class="mb-2">${jobsCount} new job${jobsCount !== '1' ? 's' : ''} found for "${setupName}"</p>
                                <a href="/jobseeker/jobs/notifications" class="btn btn-sm btn-success">
                                    <i class="fa-solid fa-eye me-1"></i>View Jobs
                                </a>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(alertDiv);
                
                // Auto-remove after 10 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 10000);
                
                // Also show browser notification if permission is granted
                if (Notification.permission === 'granted') {
                    const notification = new Notification(`${jobsCount} New Job${jobsCount !== '1' ? 's' : ''} Available`, {
                        body: `New opportunities found for "${setupName}"`,
                        icon: '/favicon.ico',
                        badge: '/favicon.ico',
                        tag: 'job-alert',
                        data: payload.data
                    });
                    
                    notification.onclick = function() {
                        window.focus();
                        window.location.href = '/jobseeker/jobs/notifications';
                        notification.close();
                    };
                    
                    setTimeout(() => notification.close(), 8000);
                }
            }
            
            addNotificationStyles() {
                if (document.getElementById('push-notification-styles')) return;
                
                const style = document.createElement('style');
                style.id = 'push-notification-styles';
                style.textContent = `
                    .job-alert-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 9999;
                        animation: slideInRight 0.5s ease-out;
                    }
                    
                    @keyframes slideInRight {
                        from {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                        to {
                            transform: translateX(0);
                            opacity: 1;
                        }
                    }
                    
                    .job-alert-notification .alert {
                        margin: 0;
                        border: none;
                        border-radius: 8px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    }
                `;
                document.head.appendChild(style);
            }
            
            handlePermissionDenied() {
                this.showNotificationStatus('warning', 'Push notifications are blocked. Please enable them in your browser settings.');
            }
            
            showNotificationStatus(type, message) {
                if (typeof toastr !== 'undefined') {
                    toastr[type](message);
                } else {
                    console.log(`${type.toUpperCase()}: ${message}`);
                }
            }
            
            getAuthToken() {
                // For session-based auth, we don't need a token
                return null;
            }
            
            getCsrfToken() {
                const metaToken = document.querySelector('meta[name="csrf-token"]');
                return metaToken ? metaToken.getAttribute('content') : null;
            }
            
            isJobSeekerLoggedIn() {
                // For session-based auth, we can check if we're on a job seeker page
                return document.querySelector('meta[name="csrf-token"]') !== null;
            }
            
            async triggerPermissionAfterSetupCreation() {
                if (!this.isSupported) {
                    console.warn('Push notifications not supported');
                    return false;
                }
                
                if (Notification.permission === 'granted') {
                    console.log('Push notifications already enabled');
                    // Try to get token if not already available
                    if (!this.deviceToken) {
                        setTimeout(() => {
                            this.getDeviceToken().catch(e => console.warn('Token retrieval failed:', e));
                        }, 1000);
                    }
                    return true;
                }
                
                if (Notification.permission === 'denied') {
                    console.log('Push notifications are blocked');
                    return false;
                }
                
                // Show a friendly prompt before requesting permission
                const userWantsNotifications = confirm(
                    'Would you like to enable push notifications to receive instant job alerts even when this page is closed?'
                );
                
                if (userWantsNotifications) {
                    try {
                        return await this.requestPermission();
                    } catch (error) {
                        console.warn('Permission request failed, but checking if permission was granted:', error);
                        // Check if permission was granted despite the error
                        return Notification.permission === 'granted';
                    }
                }
                
                return false;
            }
        }

        // Initialize push notification manager when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Only initialize on job seeker pages
            if (document.querySelector('meta[name="csrf-token"]')) {
                window.pushNotificationManager = new PushNotificationManager();
                console.log('Push notification manager attached to window');
            }
        });
    </script>
    
    @stack('scripts')
    @yield('scripts')
</body>
</html> 