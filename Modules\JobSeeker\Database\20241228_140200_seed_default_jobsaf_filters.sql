-- Seed default jobs.af filters configuration
-- This preserves the existing default behavior as a fallback when no rule-specific filters exist

-- Create a special default filter entry (rule_id = 0 indicates system default)
INSERT INTO command_schedule_filters (
    schedule_rule_id,
    categories,
    locations,
    companies,
    experience_levels,
    search_term,
    work_type,
    is_default,
    created_at,
    updated_at
) VALUES (
    0, -- Special ID for system default (not linked to any specific rule)
    JSON_ARRAY(
        'IT - Software',
        'Management',
        'Leadership',
        'Software engineering',
        'software development ',
        'software development',
        'Information Technology',
        'Administrative',
        'Sales/Marketing',
        'Consulting',
        'Security/Safety',
        'Travel/Tourism',
        'Transportation',
        'Human Resources',
        'Internships',
        'Research/Survey',
        'Translation',
        'Industrial',
        'Nursing',
        'Customer Service',
        'Business Administration',
        'Social Science',
        'Human Rights',
        'Computer Science',
        'Graphic Designer'
    ),
    JSON_ARRAY('Kabul'), -- Default to Kabul only
    JSON_ARRAY(), -- No company filters by default
    JSON_ARRAY(), -- No experience level filters by default
    '', -- No search term by default
    '', -- No work type filter by default
    1, -- Mark as default
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Verify the default filter was inserted
SELECT 
    id,
    schedule_rule_id,
    JSON_LENGTH(categories) as categories_count,
    JSON_EXTRACT(locations, '$[0]') as primary_location,
    search_term,
    work_type,
    is_default,
    created_at
FROM command_schedule_filters 
WHERE is_default = 1;

-- Show the structure for verification
SELECT 
    'Default filter configuration created successfully' as status,
    COUNT(*) as default_filters_count
FROM command_schedule_filters 
WHERE is_default = 1; 